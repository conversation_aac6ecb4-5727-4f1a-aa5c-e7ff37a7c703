import{j as e}from"./@react-google-maps/api-ee55a349.js";import{t as i}from"./i18next-7389dd8c.js";import{L as d}from"./vendor-b16525a8.js";import"./index-09a1718e.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";const V=({taskData:l})=>{const a=t=>{if(!/^\d{1,2}(AM|PM) - \d{1,2}(AM|PM)$/.test(t))return i("user.nearby.open");const[f,x]=t.split(" - "),r=new Date,[p,n]=x.split(/(?=[AP]M)/),o=new Date(r);let[s,m]=p.split(":").map(Number);return n==="PM"&&s!==12?s+=12:n==="AM"&&s===12&&(s=0),o.setHours(s,m||0,0),r>o?i("user.nearby.closed"):`${i("user.nearby.open")} ${t}`};return e.jsx("div",{className:" flex h-full max-h-[calc(100vh-250px)] w-full flex-col gap-4 overflow-y-auto px-5 py-5 ",children:l.length===0?e.jsx("div",{className:"mt-5 w-full text-center font-['Poppins'] text-2xl font-medium text-[#b4b4b4]",dangerouslySetInnerHTML:{__html:i("user.nearby.no_res2")}}):l.map((t,C)=>e.jsxs(d,{to:`/user/view-nearby/${t.id}`,className:"inline-flex items-start justify-start gap-[5px]",children:[e.jsx("div",{className:"flex h-11 w-11 flex-shrink-0 items-center justify-center rounded-full bg-[#50a8f9]/10 ",children:e.jsxs("svg",{width:"19",height:"22",viewBox:"0 0 19 22",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M13.9298 12.4291C14.6786 11.4418 15.0744 10.2633 15.0744 9.02095C15.0744 7.49777 14.477 6.06991 13.3922 5.00036C12.3077 3.93106 10.8702 3.3536 9.34684 3.37561C7.87057 3.3965 6.47972 3.98887 5.43049 5.0436C4.38131 6.09829 3.79613 7.49227 3.78271 8.96869C3.77123 10.2344 4.16931 11.4341 4.93391 12.4382C6.17516 14.0681 6.85879 16.0647 6.85879 18.0601V20.2435C6.85879 21.212 7.6467 21.9999 8.61519 21.9999H10.2489C11.2174 21.9999 12.0053 21.212 12.0053 20.2435V18.0603C12.0054 16.0428 12.666 14.0955 13.9298 12.4291ZM5.50983 11.9996C4.84348 11.1246 4.49655 10.0788 4.50654 8.9753C4.53062 6.32423 6.70653 4.13693 9.35707 4.09943C10.6867 4.08158 11.9385 4.5837 12.884 5.51591C13.8297 6.4483 14.3505 7.69312 14.3505 9.021C14.3505 10.104 14.0056 11.1313 13.353 11.9918C12.0214 13.7476 11.3045 15.7961 11.2751 17.9242H7.58165C7.55139 15.8168 6.81835 13.7179 5.50983 11.9996ZM7.58266 19.9982V19.7052L11.2815 20.129V20.2435C11.2815 20.3039 11.2752 20.3626 11.2653 20.4201L7.58266 19.9982ZM7.58266 18.9766V18.648H11.2815V19.4004L7.58266 18.9766ZM8.61519 21.2761C8.22638 21.2761 7.88736 21.0599 7.71127 20.7416L10.8272 21.0985C10.6621 21.2105 10.463 21.2761 10.249 21.2761H8.61519V21.2761Z",fill:"#2B2B2B"}),e.jsx("path",{d:"M5.84937 9.56534C5.82114 9.37592 5.80767 9.18135 5.80946 8.98706C5.81129 8.78717 5.65074 8.62367 5.45085 8.62184C5.25232 8.6201 5.08747 8.78056 5.08564 8.98045C5.08351 9.21257 5.09958 9.44527 5.13341 9.67213C5.16019 9.85175 5.31462 9.98074 5.49096 9.98074C5.50871 9.98074 5.52671 9.97944 5.54481 9.97674C5.74248 9.9472 5.87885 9.76305 5.84937 9.56534Z",fill:"#2B2B2B"}),e.jsx("path",{d:"M7.97926 15.063C8.01671 15.063 8.05483 15.0571 8.09238 15.0448C8.28228 14.9824 8.3856 14.7778 8.32315 14.588C7.92203 13.3681 7.3242 12.2317 6.54628 11.2102C6.41772 11.0413 6.30378 10.8615 6.2077 10.6755C6.11591 10.4979 5.89755 10.4284 5.71995 10.5202C5.54241 10.612 5.47287 10.8304 5.56466 11.008C5.68 11.231 5.81647 11.4466 5.97042 11.6488C6.6994 12.606 7.25963 13.671 7.63552 14.8141C7.68561 14.9664 7.82715 15.063 7.97926 15.063Z",fill:"#2B2B2B"}),e.jsx("path",{d:"M13.2314 10.2603C13.0478 10.1812 12.8349 10.266 12.7558 10.4496C12.6411 10.7158 12.4928 10.9698 12.3148 11.2045C12.194 11.3637 12.2252 11.5908 12.3845 11.7116C12.4498 11.7612 12.5267 11.7851 12.6029 11.7851C12.7123 11.7851 12.8204 11.7357 12.8916 11.6418C13.1048 11.3606 13.2828 11.0558 13.4206 10.7359C13.4997 10.5523 13.415 10.3394 13.2314 10.2603Z",fill:"#2B2B2B"}),e.jsx("path",{d:"M9.42834 5.40197C11.4239 5.40197 13.0474 7.02552 13.0474 9.0211C13.0474 9.13455 13.0422 9.24888 13.0319 9.36088C13.0137 9.55995 13.1602 9.73609 13.3593 9.75438C13.3705 9.75539 13.3817 9.75593 13.3928 9.75593C13.5777 9.75593 13.7355 9.61487 13.7528 9.42705C13.765 9.29313 13.7713 9.15656 13.7713 9.0211C13.7713 6.62638 11.8231 4.6781 9.42834 4.6781C9.22846 4.6781 9.06641 4.84015 9.06641 5.04004C9.06641 5.23992 9.22846 5.40197 9.42834 5.40197Z",fill:"#2B2B2B"}),e.jsx("path",{d:"M9.42834 2.75072C9.62823 2.75072 9.79028 2.58867 9.79028 2.38878V0.361937C9.79028 0.162051 9.62823 0 9.42834 0C9.22846 0 9.06641 0.162051 9.06641 0.361937V2.38878C9.06641 2.58867 9.22846 2.75072 9.42834 2.75072Z",fill:"#2B2B2B"}),e.jsx("path",{d:"M5.7668 3.46596C5.83388 3.58211 5.95549 3.64702 6.08057 3.64702C6.14196 3.64702 6.20421 3.63139 6.2612 3.59847C6.43431 3.49853 6.49366 3.27717 6.39372 3.10407L5.38078 1.3494C5.28079 1.17625 5.05948 1.11694 4.88638 1.21688C4.71327 1.31683 4.65392 1.53819 4.75386 1.71129L5.7668 3.46596Z",fill:"#2B2B2B"}),e.jsx("path",{d:"M17.1631 13.1318L15.4084 12.1189C15.2353 12.0189 15.0139 12.0782 14.914 12.2514C14.8141 12.4245 14.8734 12.6459 15.0465 12.7458L16.8012 13.7587C16.8582 13.7917 16.9204 13.8073 16.9818 13.8073C17.1069 13.8073 17.2286 13.7423 17.2956 13.6262C17.3956 13.4531 17.3362 13.2318 17.1631 13.1318Z",fill:"#2B2B2B"}),e.jsx("path",{d:"M1.69352 5.03658L3.44818 6.04951C3.50518 6.08243 3.56738 6.09806 3.62881 6.09806C3.7539 6.09806 3.87556 6.03311 3.94259 5.917C4.04253 5.7439 3.98322 5.52254 3.81007 5.42259L2.0554 4.40965C1.8823 4.30966 1.66094 4.36902 1.561 4.54217C1.46106 4.71527 1.52037 4.93658 1.69352 5.03658Z",fill:"#2B2B2B"}),e.jsx("path",{d:"M18.1505 8.72217H16.1237C15.9238 8.72217 15.7617 8.88422 15.7617 9.08411C15.7617 9.28399 15.9238 9.44604 16.1237 9.44604H18.1505C18.3504 9.44604 18.5124 9.28399 18.5124 9.08411C18.5124 8.88422 18.3504 8.72217 18.1505 8.72217Z",fill:"#2B2B2B"}),e.jsx("path",{d:"M3.09495 9.08411C3.09495 8.88422 2.9329 8.72217 2.73302 8.72217H0.706175C0.506289 8.72217 0.344238 8.88422 0.344238 9.08411C0.344238 9.28399 0.506289 9.44604 0.706175 9.44604H2.73302C2.93295 9.44604 3.09495 9.28399 3.09495 9.08411Z",fill:"#2B2B2B"}),e.jsx("path",{d:"M15.2278 6.09803C15.2892 6.09803 15.3514 6.0824 15.4084 6.04948L17.1631 5.03654C17.3362 4.9366 17.3955 4.71524 17.2956 4.54214C17.1957 4.36904 16.9743 4.30968 16.8012 4.40962L15.0465 5.42256C14.8734 5.5225 14.8141 5.74386 14.914 5.91697C14.981 6.03308 15.1028 6.09803 15.2278 6.09803Z",fill:"#2B2B2B"}),e.jsx("path",{d:"M3.44842 12.1189L1.69375 13.1318C1.52065 13.2317 1.46129 13.4531 1.56123 13.6262C1.62826 13.7423 1.74992 13.8073 1.87501 13.8073C1.93639 13.8073 1.99865 13.7916 2.05564 13.7587L3.81031 12.7458C3.98341 12.6458 4.04277 12.4245 3.94282 12.2514C3.84293 12.0782 3.62142 12.0189 3.44842 12.1189Z",fill:"#2B2B2B"}),e.jsx("path",{d:"M12.5956 3.59847C12.6526 3.63139 12.7148 3.64702 12.7762 3.64702C12.9013 3.64702 13.023 3.58207 13.09 3.46596L14.103 1.71129C14.2029 1.53819 14.1435 1.31683 13.9704 1.21688C13.7974 1.11694 13.576 1.17625 13.476 1.3494L12.4631 3.10407C12.3631 3.27717 12.4225 3.49853 12.5956 3.59847Z",fill:"#2B2B2B"})]})}),e.jsxs("div",{className:"inline-flex w-[283px] flex-col items-start justify-center pb-2",children:[e.jsx("div",{className:"inline-flex items-center justify-start gap-1",children:e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:t.business_name})}),e.jsxs("div",{className:"inline-flex items-center justify-start gap-1",children:[e.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:t.address}),e.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:"•"}),e.jsx("div",{className:"text-right font-['Poppins'] text-xs font-medium text-[#8080a3]",children:a(t.hours)})]})]})]},C))})};export{V as default};
