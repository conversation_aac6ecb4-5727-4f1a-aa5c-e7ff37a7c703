<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>chumpchange</title>
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/ios/180.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/ios/32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/ios/16.png" />

    <link rel="manifest" href="/manifest.json" />
    <meta name="theme-color" content="#ffffff" />
    <script type="module" crossorigin src="/assets/index-cf5e6bc7.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-f36d475e.js">
    <link rel="modulepreload" crossorigin href="/assets/@react-google-maps/api-afbf18d5.js">
    <link rel="modulepreload" crossorigin href="/assets/react-confirm-alert-2487dba8.js">
    <link rel="modulepreload" crossorigin href="/assets/moment-a9aaa855.js">
    <link rel="modulepreload" crossorigin href="/assets/@react-pdf-viewer/core-9d395990.js">
    <link rel="modulepreload" crossorigin href="/assets/qr-scanner-cf010ec4.js">
    <link rel="modulepreload" crossorigin href="/assets/@headlessui/react-46b39f71.js">
    <link rel="modulepreload" crossorigin href="/assets/@stripe/stripe-js-6b714a86.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/fontawesome-svg-core-4fa3e289.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/react-fontawesome-eb6bfecd.js">
    <link rel="modulepreload" crossorigin href="/assets/@stripe/react-stripe-js-b3bb6c9d.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/free-solid-svg-icons-0a9c4907.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/free-regular-svg-icons-0a88e957.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/free-brands-svg-icons-fae0dcac.js">
    <link rel="modulepreload" crossorigin href="/assets/i18next-7389dd8c.js">
    <link rel="modulepreload" crossorigin href="/assets/react-i18next-1e3e6bc5.js">
    <link rel="stylesheet" href="/assets/index-d64010cc.css">
  </head>
  <body>
    <div id="root"></div>
    <div id="portal"></div>

    
  </body>
</html>
