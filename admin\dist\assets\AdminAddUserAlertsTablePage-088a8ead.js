import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as i,b as j}from"./vendor-4f06b3f4.js";import{u as A}from"./react-hook-form-f3d72793.js";import{o as I}from"./yup-2324a46a.js";import{c as N,a as l}from"./yup-17027d7a.js";import{G as w,A as E,M as R,s as k,t as D}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as m}from"./MkdInput-ff3aa862.js";import{I as M}from"./InteractiveButton-8f7d74ee.js";import"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";const me=({setSidebar:x})=>{const{dispatch:c}=i.useContext(w),h=N({message:l(),service_alert_id:l(),service_id:l(),location_id:l(),user_id:l(),status:l()}).required(),{dispatch:v}=i.useContext(E),[f,T]=i.useState({}),[g,p]=i.useState(!1),S=j(),{register:a,handleSubmit:_,setError:b,setValue:C,formState:{errors:r}}=A({resolver:I(h)});i.useState([]);const y=async s=>{let u=new R;p(!0);try{for(let d in f){let o=new FormData;o.append("file",f[d].file);let n=await u.uploadImage(o);s[d]=n.url}u.setTable("user_alerts");const t=await u.callRestAPI({message:s.message,service_alert_id:s.service_alert_id,service_id:s.service_id,location_id:s.location_id,user_id:s.user_id,status:s.status},"POST");if(!t.error)k(c,"Added"),S("/admin/user_alerts"),x(!1),c({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(t.validation){const d=Object.keys(t.validation);for(let o=0;o<d.length;o++){const n=d[o];b(n,{type:"manual",message:t.validation[n]})}}p(!1)}catch(t){p(!1),console.log("Error",t),b("message",{type:"manual",message:t.message}),D(v,t.message)}};return i.useEffect(()=>{c({type:"SETPATH",payload:{path:"user_alerts"}})},[]),e.jsxs("div",{className:" mx-auto rounded  p-5 shadow-md",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add User Alerts"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:_(y),children:[e.jsx(m,{type:"text",page:"add",name:"message",errors:r,label:"Message",placeholder:"Message",register:a,className:""}),e.jsx(m,{type:"number",page:"add",name:"service_alert_id",errors:r,label:"Service Alert Id",placeholder:"Service Alert Id",register:a,className:""}),e.jsx(m,{type:"number",page:"add",name:"service_id",errors:r,label:"Service Id",placeholder:"Service Id",register:a,className:""}),e.jsx(m,{type:"number",page:"add",name:"location_id",errors:r,label:"Location Id",placeholder:"Location Id",register:a,className:""}),e.jsx(m,{type:"number",page:"add",name:"user_id",errors:r,label:"User Id",placeholder:"User Id",register:a,className:""}),e.jsx(m,{type:"text",page:"add",name:"status",errors:r,label:"Status",placeholder:"Status",register:a,className:""}),e.jsx(M,{type:"submit",loading:g,disabled:g,className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",children:"Submit"})]})]})};export{me as default};
