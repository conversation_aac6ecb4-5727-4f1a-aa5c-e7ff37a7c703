import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as o,b as T,r as d,h as D}from"./vendor-4f06b3f4.js";import{u as C}from"./react-hook-form-f3d72793.js";import{o as F}from"./yup-2324a46a.js";import{c as L,a as f}from"./yup-17027d7a.js";import{M,A as G,G as O,t as B,s as H}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as v}from"./MkdInput-ff3aa862.js";import{I as U}from"./InteractiveButton-8f7d74ee.js";import{S as $}from"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";let c=new M;const ye=a=>{const{dispatch:y}=o.useContext(G),I=L({service_id:f(),provider_id:f(),price:f(),status:f()}).required(),{dispatch:x}=o.useContext(O),[h,q]=o.useState({}),[g,S]=o.useState(!1),[j,b]=o.useState(!1),E=T(),[K,P]=d.useState(0),[V,k]=d.useState(0),[z,w]=d.useState(""),[J,N]=d.useState(""),{register:m,handleSubmit:A,setError:_,setValue:n,formState:{errors:p}}=C({resolver:F(I)}),s=D();d.useEffect(function(){(async function(){try{b(!0),c.setTable("provider_skill");const e=await c.callRestAPI({id:a.activeId?a.activeId:Number(s==null?void 0:s.id)},"GET");e.error||(n("service_id",e.model.service_id),n("provider_id",e.model.provider_id),n("price",e.model.price),n("status",e.model.status),P(e.model.service_id),k(e.model.provider_id),w(e.model.price),N(e.model.status),b(!1))}catch(e){b(!1),console.log("error",e),B(y,e.message)}})()},[]);const R=async e=>{S(!0);try{c.setTable("provider_skill");for(let l in h){let i=new FormData;i.append("file",h[l].file);let u=await c.uploadImage(i);e[l]=u.url}const r=await c.callRestAPI({id:a.activeId?a.activeId:Number(s==null?void 0:s.id),service_id:e.service_id,provider_id:e.provider_id,price:e.price,status:e.status},"PUT");if(!r.error)H(x,"Updated"),E("/admin/provider_skill"),x({type:"REFRESH_DATA",payload:{refreshData:!0}}),a.setSidebar(!1);else if(r.validation){const l=Object.keys(r.validation);for(let i=0;i<l.length;i++){const u=l[i];_(u,{type:"manual",message:r.validation[u]})}}S(!1)}catch(r){S(!1),console.log("Error",r),_("service_id",{type:"manual",message:r.message})}};return o.useEffect(()=>{x({type:"SETPATH",payload:{path:"provider_skill"}})},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Provider Skill"}),j?t.jsx($,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:A(R),children:[t.jsx(v,{type:"number",page:"edit",name:"service_id",errors:p,label:"Service Id",placeholder:"Service Id",register:m,className:""}),t.jsx(v,{type:"number",page:"edit",name:"provider_id",errors:p,label:"Provider Id",placeholder:"Provider Id",register:m,className:""}),t.jsx(v,{type:"text",page:"edit",name:"price",errors:p,label:"Price",placeholder:"Price",register:m,className:""}),t.jsx(v,{type:"text",page:"edit",name:"status",errors:p,label:"Status",placeholder:"Status",register:m,className:""}),t.jsx(U,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:g,disable:g,children:"Submit"})]})]})};export{ye as default};
