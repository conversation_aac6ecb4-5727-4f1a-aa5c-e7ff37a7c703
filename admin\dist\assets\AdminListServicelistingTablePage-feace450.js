import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as s,b,r as w}from"./vendor-4f06b3f4.js";import{M as v,A as j,G as E,l as D,m as A}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{L as r}from"./index-6416aa2c.js";import{M as c}from"./index-d97c616d.js";import{M}from"./index-68c82eaa.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";new v;const I=[{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"End Date",accessor:"end_date",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Start Date",accessor:"start_date",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"User Id",accessor:"user_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Description",accessor:"description",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Rate",accessor:"rate",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Duration",accessor:"duration",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Service Id",accessor:"service_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],Q=()=>{s.useContext(j),s.useContext(E);const p=b(),[m,a]=s.useState(!1),[o,i]=s.useState(!1),[f,x]=s.useState(),[h,u]=s.useState({}),S=w.useRef(null),[R,g]=s.useState([]),l=(t,d,n=[])=>{switch(t){case"add":a(d);break;case"edit":i(d),g(n),x(n[0]);break}};return e.jsxs(e.Fragment,{children:[e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"flex flex-wrap gap-4 bg-white px-14 shadow ",children:e.jsxs("div",{className:"mt-4 inline-flex h-[154px] w-[261px] flex-col items-center justify-center gap-6 rounded-lg border border-[#e4e6eb] bg-white p-4 shadow",children:[e.jsx("div",{className:" font-['Inter'] text-3xl font-semibold leading-[38px] text-[#0f1728]",children:h.total}),e.jsx("div",{className:" font-['Poppins'] text-base font-medium leading-normal text-[#0f1728]",children:"Total Service listings"})]})}),e.jsx("div",{className:"overflow-x-auto  rounded bg-white p-5 shadow",children:e.jsx(r,{children:e.jsx(M,{setResult:u,columns:I,tableRole:"admin",table:"servicelisting",actionId:"id",actions:{view:{show:!0,action:t=>{p("/admin/view-service_listing/"+t.id,{state:t})},multiple:!1},edit:{show:!0,multiple:!1,action:t=>l("edit",!0,t)},delete:{show:!1,action:null,multiple:!1},select:{show:!1,action:null,multiple:!1},add:{show:!1,action:()=>l("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!0,action:null,multiple:!0}},actionPostion:"ontable",refreshRef:S,join:"service"})})})]}),e.jsx(r,{children:e.jsx(c,{isModalActive:m,closeModalFn:()=>a(!1),children:e.jsx(D,{setSidebar:a})})}),o&&e.jsx(r,{children:e.jsx(c,{isModalActive:o,closeModalFn:()=>i(!1),children:e.jsx(A,{activeId:f,setSidebar:i})})})]})};export{Q as default};
