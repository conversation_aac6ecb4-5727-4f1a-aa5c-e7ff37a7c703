import React, { useId } from "react";
import MoonLoader from "react-spinners/MoonLoader";

const GreenButton = ({
  loading = false,
  disabled,
  children,
  type = "button",
  className,
  loaderclasses,
  onClick,
  color = "#ffffff",
}) => {
  const override = {
    borderColor: "#ffffff",
  };
  const id = useId();
  return (
    <button
      type={type}
      disabled={disabled}
      className={`flex h-12 w-full items-center justify-center gap-5 rounded-2xl bg-[#219653] font-['Poppins'] text-base font-semibold text-white disabled:opacity-25 ${className}`}
      onClick={onClick}
    >
      <>
        <MoonLoader
          color={color}
          loading={loading}
          cssOverride={override}
          size={20}
          className={loaderclasses}
          // aria-label="Loading Spinner"
          data-testid={id}
        />

        <span>{children}</span>
      </>
    </button>
  );
};

export default GreenButton;
