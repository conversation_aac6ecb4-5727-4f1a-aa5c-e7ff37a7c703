<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>chumpchange</title>
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/ios/180.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/ios/32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/ios/16.png" />

    <link rel="manifest" href="/manifest.json" />
    <meta name="theme-color" content="#ffffff" />
    <script type="module" crossorigin src="/assets/index-09a1718e.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-b16525a8.js">
    <link rel="modulepreload" crossorigin href="/assets/@react-google-maps/api-ee55a349.js">
    <link rel="modulepreload" crossorigin href="/assets/react-confirm-alert-c06b7fb4.js">
    <link rel="modulepreload" crossorigin href="/assets/moment-a9aaa855.js">
    <link rel="modulepreload" crossorigin href="/assets/qr-scanner-cf010ec4.js">
    <link rel="modulepreload" crossorigin href="/assets/@headlessui/react-518241d3.js">
    <link rel="modulepreload" crossorigin href="/assets/@stripe/stripe-js-6b714a86.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/fontawesome-svg-core-4fa3e289.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/react-fontawesome-88fe485e.js">
    <link rel="modulepreload" crossorigin href="/assets/@stripe/react-stripe-js-20049f1e.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/free-solid-svg-icons-0a9c4907.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/free-regular-svg-icons-0a88e957.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/free-brands-svg-icons-fae0dcac.js">
    <link rel="modulepreload" crossorigin href="/assets/i18next-7389dd8c.js">
    <link rel="modulepreload" crossorigin href="/assets/react-i18next-4a61273e.js">
    <link rel="stylesheet" href="/assets/index-8ff8a2aa.css">
  </head>
  <body>
    <div id="root"></div>
    <div id="portal"></div>

    
  </body>
</html>
