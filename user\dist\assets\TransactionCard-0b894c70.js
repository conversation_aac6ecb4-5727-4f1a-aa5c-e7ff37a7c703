import{j as i}from"./@react-google-maps/api-ee55a349.js";import{t as n}from"./i18next-7389dd8c.js";import"./vendor-b16525a8.js";import{f as t}from"./index-09a1718e.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";const N=({taskData:r})=>i.jsx("div",{className:" flex h-full max-h-[calc(100vh-250px)] w-full flex-col gap-4 overflow-y-auto px-5 py-5 ",children:r.length===0?i.jsx("div",{className:"mt-5 w-full text-center font-['Poppins'] text-2xl font-medium text-[#b4b4b4]",dangerouslySetInnerHTML:{__html:n("provider.earnings.no_tran")}}):r.map((e,l)=>e!=null&&e.recharge_id?i.jsxs("div",{className:"inline-flex items-start justify-start gap-[5px]",children:[i.jsx("div",{className:"flex h-11 w-11 flex-shrink-0 items-center justify-center rounded-full bg-[#50a8f9]/10",children:i.jsx("svg",{width:"23",height:"21",viewBox:"0 0 23 21",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{d:"M16.9552 0.295471L12.4866 2.72816L5.0828 6.77447L0.614258 9.23339L2.54433 12.7238V10C2.54433 9.66339 2.67806 9.34056 2.91608 9.10253C3.15411 8.8645 3.47694 8.73078 3.81357 8.73078H4.50149L4.52687 8.62501L14.338 3.25701L15.0784 3.46855C15.0987 3.53624 15.1207 3.61493 15.1571 3.68009C15.4482 4.21316 16.1048 4.42132 16.6379 4.13024C16.7022 4.0947 16.7436 4.0177 16.7969 3.97116L17.5636 4.20893L19.2559 7.30332L19.0443 8.06993C18.9775 8.09024 18.8979 8.08686 18.8328 8.12239C18.5849 8.25778 18.4182 8.48455 18.3302 8.73078H21.5828L19.3879 4.73778L16.9552 0.295471ZM16.0303 5.84878C15.7825 5.9018 15.5604 6.03846 15.4014 6.23583C15.2424 6.4332 15.1562 6.67926 15.1571 6.9327C15.1571 7.53855 15.6352 8.0437 16.2419 8.0437C16.8477 8.0437 17.352 7.53855 17.352 6.9327C17.352 6.32686 16.8477 5.84878 16.241 5.84878C16.1657 5.84878 16.1023 5.83355 16.0303 5.84878ZM11.9053 6.58916C11.2591 6.57019 10.6196 6.72538 10.0539 7.03847C9.31695 7.44124 8.76526 8.05724 8.46741 8.73078H14.602C14.5527 8.47173 14.4634 8.22193 14.3372 7.99039C13.8583 7.11547 12.93 6.61963 11.9053 6.58916ZM3.81357 10V21H22.4289V10H3.81357ZM7.54172 11.3488H18.7008L19.2559 11.9039C19.2415 11.9741 19.2026 12.0409 19.2026 12.1154C19.2026 12.7221 19.7069 13.2264 20.3136 13.2264C20.388 13.2264 20.4549 13.1866 20.5251 13.1731L21.0802 13.7282V17.2719L20.5251 17.8269C20.4549 17.8125 20.388 17.7736 20.3136 17.7736C19.7069 17.7736 19.2026 18.2779 19.2026 18.8846C19.2026 18.9591 19.2415 19.0259 19.2559 19.0962L18.7008 19.6512H7.54172L6.98664 19.0962C7.00103 19.0259 7.03995 18.9591 7.03995 18.8846C7.03995 18.2779 6.53564 17.7736 5.92895 17.7736C5.85449 17.7736 5.78764 17.8134 5.71741 17.8269L5.16233 17.2719V13.7282L5.71741 13.1731C5.78764 13.1875 5.85449 13.2264 5.92895 13.2264C6.53564 13.2264 7.03995 12.7221 7.03995 12.1154C7.03995 12.0409 7.00103 11.9741 6.98664 11.9039L7.54172 11.3488ZM13.1213 12.618C11.2868 12.618 9.7891 13.9042 9.7891 15.5C9.7891 17.0959 11.2868 18.382 13.1213 18.382C14.9557 18.382 16.4534 17.0959 16.4534 15.5C16.4534 13.9042 14.9557 12.618 13.1213 12.618ZM7.62126 14.389C7.01541 14.389 6.51026 14.8942 6.51026 15.5C6.51026 16.1059 7.01541 16.611 7.62126 16.611C8.2271 16.611 8.73226 16.1059 8.73226 15.5C8.73226 14.8942 8.2271 14.389 7.62126 14.389ZM18.6213 14.389C18.0146 14.389 17.5103 14.8933 17.5103 15.5C17.5103 16.1067 18.0146 16.611 18.6213 16.611C19.2279 16.611 19.7323 16.1067 19.7323 15.5C19.7323 14.8933 19.2279 14.389 18.6213 14.389Z",fill:"#56CCF2"})})}),i.jsxs("div",{className:"inline-flex w-[283px] flex-col items-start justify-center pb-2",children:[i.jsxs("div",{className:"inline-flex items-center justify-start gap-1",children:[i.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:e==null?void 0:e.recharge_name}),i.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:"→"}),i.jsxs("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:["$",e==null?void 0:e.amount]})]}),i.jsx("div",{className:"inline-flex items-center justify-start gap-1",children:i.jsx("div",{className:"text-right font-['Poppins'] text-xs font-medium text-[#8080a3]",children:t(e==null?void 0:e.create_at)})}),i.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:n("provider.earnings.paid")})]})]},l):i.jsxs("div",{className:"inline-flex items-start justify-start gap-[5px]",children:[i.jsx("div",{className:"flex h-11 w-11 flex-shrink-0 items-center justify-center rounded-full bg-[#50a8f9]/10",children:i.jsx("svg",{width:"23",height:"21",viewBox:"0 0 23 21",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{d:"M16.9552 0.295471L12.4866 2.72816L5.0828 6.77447L0.614258 9.23339L2.54433 12.7238V10C2.54433 9.66339 2.67806 9.34056 2.91608 9.10253C3.15411 8.8645 3.47694 8.73078 3.81357 8.73078H4.50149L4.52687 8.62501L14.338 3.25701L15.0784 3.46855C15.0987 3.53624 15.1207 3.61493 15.1571 3.68009C15.4482 4.21316 16.1048 4.42132 16.6379 4.13024C16.7022 4.0947 16.7436 4.0177 16.7969 3.97116L17.5636 4.20893L19.2559 7.30332L19.0443 8.06993C18.9775 8.09024 18.8979 8.08686 18.8328 8.12239C18.5849 8.25778 18.4182 8.48455 18.3302 8.73078H21.5828L19.3879 4.73778L16.9552 0.295471ZM16.0303 5.84878C15.7825 5.9018 15.5604 6.03846 15.4014 6.23583C15.2424 6.4332 15.1562 6.67926 15.1571 6.9327C15.1571 7.53855 15.6352 8.0437 16.2419 8.0437C16.8477 8.0437 17.352 7.53855 17.352 6.9327C17.352 6.32686 16.8477 5.84878 16.241 5.84878C16.1657 5.84878 16.1023 5.83355 16.0303 5.84878ZM11.9053 6.58916C11.2591 6.57019 10.6196 6.72538 10.0539 7.03847C9.31695 7.44124 8.76526 8.05724 8.46741 8.73078H14.602C14.5527 8.47173 14.4634 8.22193 14.3372 7.99039C13.8583 7.11547 12.93 6.61963 11.9053 6.58916ZM3.81357 10V21H22.4289V10H3.81357ZM7.54172 11.3488H18.7008L19.2559 11.9039C19.2415 11.9741 19.2026 12.0409 19.2026 12.1154C19.2026 12.7221 19.7069 13.2264 20.3136 13.2264C20.388 13.2264 20.4549 13.1866 20.5251 13.1731L21.0802 13.7282V17.2719L20.5251 17.8269C20.4549 17.8125 20.388 17.7736 20.3136 17.7736C19.7069 17.7736 19.2026 18.2779 19.2026 18.8846C19.2026 18.9591 19.2415 19.0259 19.2559 19.0962L18.7008 19.6512H7.54172L6.98664 19.0962C7.00103 19.0259 7.03995 18.9591 7.03995 18.8846C7.03995 18.2779 6.53564 17.7736 5.92895 17.7736C5.85449 17.7736 5.78764 17.8134 5.71741 17.8269L5.16233 17.2719V13.7282L5.71741 13.1731C5.78764 13.1875 5.85449 13.2264 5.92895 13.2264C6.53564 13.2264 7.03995 12.7221 7.03995 12.1154C7.03995 12.0409 7.00103 11.9741 6.98664 11.9039L7.54172 11.3488ZM13.1213 12.618C11.2868 12.618 9.7891 13.9042 9.7891 15.5C9.7891 17.0959 11.2868 18.382 13.1213 18.382C14.9557 18.382 16.4534 17.0959 16.4534 15.5C16.4534 13.9042 14.9557 12.618 13.1213 12.618ZM7.62126 14.389C7.01541 14.389 6.51026 14.8942 6.51026 15.5C6.51026 16.1059 7.01541 16.611 7.62126 16.611C8.2271 16.611 8.73226 16.1059 8.73226 15.5C8.73226 14.8942 8.2271 14.389 7.62126 14.389ZM18.6213 14.389C18.0146 14.389 17.5103 14.8933 17.5103 15.5C17.5103 16.1067 18.0146 16.611 18.6213 16.611C19.2279 16.611 19.7323 16.1067 19.7323 15.5C19.7323 14.8933 19.2279 14.389 18.6213 14.389Z",fill:"#56CCF2"})})}),i.jsxs("div",{className:"inline-flex w-[283px] flex-col items-start justify-center pb-2",children:[i.jsxs("div",{className:"inline-flex items-center justify-start gap-1",children:[i.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:n("provider.earnings.account_activation")}),i.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:"→"}),i.jsxs("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:["$",e==null?void 0:e.plan_amount]})]}),i.jsx("div",{className:"inline-flex items-center justify-start gap-1",children:i.jsxs("div",{className:"text-right font-['Poppins'] text-xs font-medium text-[#8080a3]",children:[n("provider.earnings.from")," ",t(e==null?void 0:e.create_at)," ",n("provider.earnings.to")," ",t(e==null?void 0:e.end_at)]})}),i.jsxs("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:[(e==null?void 0:e.plan_duration)==1&&n("provider.earnings.monthly"),(e==null?void 0:e.plan_duration)>1&&(e==null?void 0:e.plan_duration)<12&&(e==null?void 0:e.plan_duration)+" "+n("provider.earnings.Months"),(e==null?void 0:e.plan_duration)==12&&n("provider.earnings.yearly")]})]})]},l))});export{N as default};
