import{j as d}from"./@react-google-maps/api-afbf18d5.js";import"./vendor-f36d475e.js";const u=({onSort:p,columns:i,actions:e,actionPostion:y,areAllRowsSelected:t,handleSelectAll:h})=>d.jsx(d.Fragment,{children:d.jsx("tr",{children:i.map((r,s)=>{var x,l,j,w,k,f,g;if((r==null?void 0:r.accessor)===""){if([(x=e==null?void 0:e.select)==null?void 0:x.show,(l=e==null?void 0:e.view)==null?void 0:l.show,(j=e==null?void 0:e.edit)==null?void 0:j.show,(w=e==null?void 0:e.delete)==null?void 0:w.show].includes(!0))return d.jsxs("th",{scope:"col",className:`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 ${r.isSorted?"cursor-pointer":""} `,onClick:r.isSorted?()=>p(s):void 0,children:[r.header==="Action"&&((k=e==null?void 0:e.select)!=null&&k.show)?d.jsx("input",{type:"checkbox",disabled:!((f=e==null?void 0:e.select)!=null&&f.multiple),id:"select_all_rows",className:"mr-3",checked:t,onChange:h}):null,y==="ontable"&&r.header,d.jsx("span",{children:r.isSorted?r.isSortedDesc?" ▼":" ▲":""})]},s)}else return d.jsxs("th",{scope:"col",className:`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 ${r.isSorted?"cursor-pointer":""} `,onClick:r.isSorted?()=>p(s):void 0,children:[r.header==="Action"&&((g=e==null?void 0:e.select)!=null&&g.show)?d.jsx("input",{type:"checkbox",id:"select_all_rows",className:"mr-3",checked:t,onChange:h}):null,r.header,d.jsx("span",{children:r.isSorted?r.isSortedDesc?" ▼":" ▲":""})]},s);return null})})});export{u as default};
