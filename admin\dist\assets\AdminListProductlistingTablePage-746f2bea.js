import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as s,b,r as w}from"./vendor-4f06b3f4.js";import{M as j,A as E,G as v,j as A,k as D}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{L as o}from"./index-6416aa2c.js";import{M as c}from"./index-d97c616d.js";import{M as P}from"./index-68c82eaa.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";new j;const I=[{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"User Id",accessor:"user_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Product Name",accessor:"product_name",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Product Type Id",accessor:"product_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Price",accessor:"price",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Quantity",accessor:"quantity",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Item Condition",accessor:"item_condition",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Description",accessor:"description",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Product Images",accessor:"product_pics",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},notfilter:!0},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],O=()=>{s.useContext(E),s.useContext(v);const p=b(),[m,f]=s.useState({}),[u,a]=s.useState(!1),[r,i]=s.useState(!1),[x,h]=s.useState(),g=w.useRef(null),[M,S]=s.useState([]),d=(t,l,n=[])=>{switch(t){case"add":a(l);break;case"edit":i(l),S(n),h(n[0]);break}};return e.jsxs(e.Fragment,{children:[e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"flex flex-wrap gap-4 bg-white px-14 shadow ",children:e.jsxs("div",{className:"mt-4 inline-flex h-[154px] w-[261px] flex-col items-center justify-center gap-6 rounded-lg border border-[#e4e6eb] bg-white p-4 shadow",children:[e.jsx("div",{className:" font-['Inter'] text-3xl font-semibold leading-[38px] text-[#0f1728]",children:m.total}),e.jsx("div",{className:" font-['Poppins'] text-base font-medium leading-normal text-[#0f1728]",children:"Total Product listings"})]})}),e.jsx("div",{className:"overflow-x-auto  rounded bg-white p-5 shadow",children:e.jsx(o,{children:e.jsx(P,{setResult:f,columns:I,tableRole:"admin",table:"productlisting",actionId:"id",actions:{view:{show:!0,action:t=>{p("/admin/view-product_listing/"+t.id,{state:t})},multiple:!1},edit:{show:!0,multiple:!1,action:t=>d("edit",!0,t)},delete:{show:!1,action:null,multiple:!1},select:{show:!1,action:null,multiple:!1},add:{show:!1,action:()=>d("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!0,action:null,multiple:!0}},actionPostion:"ontable",refreshRef:g,join:"product"})})})]}),e.jsx(o,{children:e.jsx(c,{isModalActive:u,closeModalFn:()=>a(!1),children:e.jsx(A,{setSidebar:a})})}),r&&e.jsx(o,{children:e.jsx(c,{isModalActive:r,closeModalFn:()=>i(!1),children:e.jsx(D,{activeId:x,setSidebar:i})})})]})};export{O as default};
