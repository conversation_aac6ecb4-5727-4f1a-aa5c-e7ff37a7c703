import{j as t}from"./@react-google-maps/api-afbf18d5.js";import{h as g,r as s,R as v,L as j}from"./vendor-f36d475e.js";import"./index-5deedf4a.js";import{M as w,A as b,G as k}from"./index-cf5e6bc7.js";import{P as C}from"./index-55e4d382.js";import{L as l}from"./index-010bc024.js";import{S as y}from"./index-65bc3378.js";import{t as r}from"./i18next-7389dd8c.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-1e3e6bc5.js";const c=new w,J=()=>{g(),s.useContext(b);const{state:E,dispatch:n}=s.useContext(k),[i,a]=s.useState("active"),[d,p]=s.useState([]),[x,o]=s.useState(!1),[m,f]=s.useState([]),h=async()=>{try{const e=await c.callRawAPI("/v3/api/custom/chumpchange/provider/listing/active",{},"GET");e.error||p(e.data)}catch(e){console.error("Error fetching active tasks:",e)}},u=async()=>{try{const e=await c.callRawAPI("/v3/api/custom/chumpchange/provider/listing/expired",{},"GET");e.error||f(e.data)}catch(e){return console.error("Error fetching expired tasks:",e),[]}};return v.useEffect(()=>{n({type:"SETPATH",payload:{path:"listings"}}),(async()=>(o(!0),await h(),await u(),o(!1)))()},[]),t.jsxs(C,{className:"",children:[t.jsxs("div",{className:" mt-2 flex items-center justify-between px-10 ",children:[t.jsx("div",{className:"font-['Poppins'] text-lg font-bold text-black",children:r("provider.listing.title")}),t.jsx(j,{to:"/provider/crate-listing",className:"",children:t.jsxs("svg",{width:"43",height:"43",viewBox:"0 0 43 43",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[t.jsx("rect",{width:"43",height:"43",rx:"21.5",fill:"#E1E2E3"}),t.jsx("path",{d:"M20.1667 14.3333C20.1667 13.597 20.7636 13 21.5 13C22.2364 13 22.8333 13.597 22.8333 14.3333V27.6667C22.8333 28.403 22.2364 29 21.5 29C20.7636 29 20.1667 28.403 20.1667 27.6667V14.3333Z",fill:"#56CCF2"}),t.jsx("path",{d:"M14.8333 22.3333C14.097 22.3333 13.5 21.7364 13.5 21C13.5 20.2636 14.097 19.6667 14.8333 19.6667H28.1667C28.903 19.6667 29.5 20.2636 29.5 21C29.5 21.7364 28.903 22.3333 28.1667 22.3333H14.8333Z",fill:"#56CCF2"})]})})]}),t.jsx("div",{className:" mt-14 px-5 ",children:t.jsxs("div",{className:" grid h-12 w-full grid-cols-2 items-center rounded-2xl bg-[#8181a4]/20 px-1",children:[t.jsx("button",{className:`flex h-10 items-center justify-center rounded-2xl font-['Poppins'] text-sm font-medium ${i==="active"?"bg-black text-white":"text-black"}`,onClick:()=>a("active"),children:r("provider.listing.active")}),t.jsx("button",{className:`flex h-10 items-center justify-center rounded-2xl font-['Poppins'] text-sm font-medium ${i==="expired"?"bg-black text-white":"text-black"}`,onClick:()=>a("expired"),children:r("provider.listing.expired")})]})}),t.jsxs("div",{className:" relative left-0 mt-6 flex w-full flex-1 flex-col items-center justify-start overflow-hidden rounded-tl-[32px] rounded-tr-[32px] bg-white",children:[t.jsx("div",{className:" flex h-7 w-full max-w-[335px] items-center justify-center border-b bg-white ",children:t.jsx("div",{className:" h-1 w-[50px] rounded bg-[#f2f2f7]"})}),x?t.jsx("div",{className:"flex h-full w-full items-center justify-center pt-10 ",children:t.jsx(y,{})}):i==="active"?t.jsx(l,{taskData:d}):t.jsx(l,{taskData:m,expired:!0})]})]})};export{J as default};
