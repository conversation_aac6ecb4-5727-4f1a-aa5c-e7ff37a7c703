import{j as e}from"./@react-google-maps/api-ee55a349.js";import{i as E,r as a,d as W}from"./vendor-b16525a8.js";import"./index-9ef1c042.js";import{M as V,A as F,G as Z,s as b}from"./index-09a1718e.js";import{B as q}from"./index-d54cffea.js";import{u as H}from"./user-a875fff3.js";import{u as T}from"./react-hook-form-b6ed2679.js";import{o as R}from"./yup-3990215a.js";import{c as G,a as i}from"./yup-f828ae80.js";import{A as $}from"./index-4ee87ce5.js";import{M as w}from"./index-243c4859.js";import{S as d}from"./index-5a645c18.js";import{u as O}from"./react-i18next-4a61273e.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./@hookform/resolvers-3e831b4a.js";const m=new V,K=[{name:"first_name",placeholder:"first_name_label",label:"first_name_label",icon:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M10.1331 9.05841C10.0498 9.05008 9.9498 9.05008 9.85814 9.05841C7.8748 8.99175 6.2998 7.36675 6.2998 5.36675C6.2998 3.32508 7.9498 1.66675 9.9998 1.66675C12.0415 1.66675 13.6998 3.32508 13.6998 5.36675C13.6915 7.36675 12.1165 8.99175 10.1331 9.05841Z",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M5.9666 12.1333C3.94993 13.4833 3.94993 15.6833 5.9666 17.0249C8.25827 18.5583 12.0166 18.5583 14.3083 17.0249C16.3249 15.6749 16.3249 13.4749 14.3083 12.1333C12.0249 10.6083 8.2666 10.6083 5.9666 12.1333Z",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})},{name:"last_name",placeholder:"last_name_label",label:"last_name_label",icon:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M10.1331 9.05841C10.0498 9.05008 9.9498 9.05008 9.85814 9.05841C7.8748 8.99175 6.2998 7.36675 6.2998 5.36675C6.2998 3.32508 7.9498 1.66675 9.9998 1.66675C12.0415 1.66675 13.6998 3.32508 13.6998 5.36675C13.6915 7.36675 12.1165 8.99175 10.1331 9.05841Z",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M5.9666 12.1333C3.94993 13.4833 3.94993 15.6833 5.9666 17.0249C8.25827 18.5583 12.0166 18.5583 14.3083 17.0249C16.3249 15.6749 16.3249 13.4749 14.3083 12.1333C12.0249 10.6083 8.2666 10.6083 5.9666 12.1333Z",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})},{name:"phone",type:"phone",placeholder:"phone_number_label",label:"phone_number_label",icon:e.jsxs("svg",{width:"20",height:"22",viewBox:"0 0 20 22",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M18 6V16C18 20 17 21 13 21H7C3 21 2 20 2 16V6C2 2 3 1 7 1H13C17 1 18 2 18 6Z",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M12 4.5H8",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M10.0002 18.1C10.8562 18.1 11.5502 17.406 11.5502 16.55C11.5502 15.694 10.8562 15 10.0002 15C9.14415 15 8.4502 15.694 8.4502 16.55C8.4502 17.406 9.14415 18.1 10.0002 18.1Z",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),length:15},{name:"email",placeholder:"email_label",label:"email_label",icon:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M14.1665 17.0834H5.83317C3.33317 17.0834 1.6665 15.8334 1.6665 12.9167V7.08341C1.6665 4.16675 3.33317 2.91675 5.83317 2.91675H14.1665C16.6665 2.91675 18.3332 4.16675 18.3332 7.08341V12.9167C18.3332 15.8334 16.6665 17.0834 14.1665 17.0834Z",stroke:"#8181A4",strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M14.1668 7.5L11.5585 9.58333C10.7002 10.2667 9.29183 10.2667 8.43349 9.58333L5.8335 7.5",stroke:"#8181A4",strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"})]})}],ye=()=>{const{t:o}=O();E();const{state:v,dispatch:k}=a.useContext(F),{state:z,dispatch:p}=a.useContext(Z),[C,u]=a.useState(!1),[_,h]=a.useState(!0),[y,n]=a.useState(!1),[J,N]=a.useState(!1),[Q,L]=a.useState(null),[l,c]=a.useState(null),[f,x]=a.useState();console.log("state >>",v);const A=G({first_name:i().required(),last_name:i().required(),phone:i().required(),email:i().required().email()}).required();W();const{register:S,handleSubmit:M,setValue:r,setError:X,formState:{errors:P,isValid:g}}=T({resolver:R(A),mode:"onChange"});a.useEffect(()=>{N(g)},[g]);const I=async s=>{try{u(!0);const t=await m.callRawAPI("/v3/api/custom/chumpchange/provider/update-profile",{firstName:s.first_name,lastName:s.last_name,phone:s.phone,email:s.email,operating_city:f,photo:l},"POST");u(!1),b(p,"Profile updated successfully")}catch{b(p,"Failed to update profile",5e3,"error")}};a.useEffect(()=>{(async()=>{h(!0);const t=await m.callRawAPI("/v3/api/custom/chumpchange/provider/data",{},"GET");console.log("result >>",t),t.error||(L(t.data),r("first_name",t.data.first_name),r("last_name",t.data.last_name),r("phone",t.data.phone),r("email",t.data.email),r("operating_city",t.data.operating_city),x(t.data.operating_city),c(t.data.photo),k({type:"UPDATE_PROFILE",payload:{first_name:t.data.first_name,last_name:t.data.last_name,photo:t.data.photo,user_data:{...t.data}}})),h(!1)})()},[r]);const U=async s=>{console.log("e >>",s.target.files[0]);try{n(!0);const t=new FormData;t.append("file",s.target.files[0]);const j=await m.uploadImage(t);console.log("result >>",j),c(j.url),n(!1)}catch(t){n(!1),console.log("error >>",t)}};console.log("imageUrl >> ",e.jsx("imageUrl",{}));const B=s=>s.replace(/_/g," ").replace(/ label/i,""),D=K.map(s=>({...s,placeholder:B(s.placeholder)}));return e.jsxs(e.Fragment,{children:[y&&e.jsx(w,{showCloseButton:!1,children:e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsxs("div",{className:"text-lg font-semibold",children:[o("loading.adding"),"..."]}),e.jsx("div",{className:"mt-12",children:e.jsx(d,{})})]})}),C&&e.jsx(w,{showCloseButton:!1,children:e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsxs("div",{className:"text-lg font-semibold",children:[o("loading.updating"),"..."]}),e.jsx("div",{className:"mt-12",children:e.jsx(d,{})})]})}),_?e.jsx("div",{className:"flex min-h-screen w-full items-center justify-center",children:e.jsx(d,{})}):e.jsxs("form",{onSubmit:M(I),className:"p-5",children:[e.jsxs("div",{className:"relative flex w-full items-center justify-between ",children:[e.jsx("div",{className:" ",children:e.jsx(q,{})}),e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:o("user.edit_profile.title")}),e.jsx("div",{className:" ",children:e.jsx("button",{className:"relative flex h-10 w-[67px] items-center justify-center rounded-[32px] bg-white/60 font-['Poppins'] text-sm font-semibold text-[#828282] backdrop-blur-[20px]",children:o("buttons.save")})})]}),e.jsxs("div",{className:" mt-5 flex flex-col items-center justify-center",children:[e.jsx("img",{className:"relative h-20 w-20 rounded-[100px]",src:l||H}),e.jsxs("div",{className:"mt-5 flex w-full items-center justify-around gap-2",children:[e.jsx("button",{type:"button",onClick:()=>{c(null),console.log(l)},className:"relative flex h-12 w-[110px] items-center justify-center rounded-2xl bg-[#ff0000]/5 text-center font-['Poppins'] text-sm font-semibold text-[#f95050]",children:o("buttons.remove")}),e.jsxs("div",{className:"relative flex h-12 w-[110px] items-center justify-center rounded-2xl border-2 bg-[#50a8f9]/10 text-center font-['Poppins'] text-sm font-semibold text-[#56ccf2]",children:[e.jsx("input",{type:"file",className:" absolute left-0 top-[-100%] h-[200%] w-full cursor-pointer opacity-0",onChange:U}),o("buttons.change")]})]})]}),e.jsxs("div",{className:" mt-7 flex flex-col gap-4",children:[D.map((s,t)=>e.jsxs("div",{className:"w-full",children:[e.jsx("div",{className:"mb-1 px-2 font-['Poppins'] text-sm font-medium text-[#8080a3] ",children:o(`user.edit_profile.${s.label}`)}),e.jsx($,{type:s.type,name:s.name,placeholder:s.placeholder,errors:P,register:S,icon:s.icon})]},t)),e.jsxs("div",{className:"w-full",children:[e.jsx("div",{className:"mb-1 px-2 font-['Poppins'] text-sm font-medium text-[#8080a3] ",children:o("user.edit_profile.city_o")}),e.jsx("input",{type:"text",value:f,placeholder:o("user.add_address.enter_city"),onChange:s=>x(s.target.value),className:"relative mt-1 h-12 w-full rounded-2xl border-none bg-white text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "})]})]})]})]})};export{ye as default};
