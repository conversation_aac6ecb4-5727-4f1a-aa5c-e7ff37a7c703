import{j as e}from"./@react-google-maps/api-ee55a349.js";import{i as ke,u as Ne,d as Se,r as c,R as d}from"./vendor-b16525a8.js";import"./index-9ef1c042.js";import{M as Ce,A as ye,G as _e,n as qe,s as $,t as Pe}from"./index-09a1718e.js";import{B as z,a as Le}from"./index-d54cffea.js";import{D as K}from"./index-6b1be6a4.js";import{u as De}from"./react-hook-form-b6ed2679.js";import{o as Me}from"./yup-3990215a.js";import{c as He,a as x}from"./yup-f828ae80.js";import{M as Ae}from"./index-243c4859.js";import{S as T}from"./index-5a645c18.js";import{P as Ee}from"./index-d152a0de.js";import{t as n}from"./i18next-7389dd8c.js";import{A as Fe}from"./@vis.gl/react-google-maps-934eb5c3.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";import"./@hookform/resolvers-3e831b4a.js";const N=new Ce,ht=()=>{var I,W,Z,U,G;ke();const q=Ne(),O=Se(),{state:Ve,dispatch:J}=c.useContext(ye),{state:Re,dispatch:Q}=c.useContext(_e),[P,L]=c.useState([]),[X,D]=c.useState(!1),[h,f]=d.useState(null),[g,j]=d.useState(null),[Y,M]=d.useState(""),[ee,H]=d.useState(""),[te,A]=d.useState(""),[S,w]=d.useState(""),[se,C]=d.useState(!1),[ne,y]=d.useState(!0),[ie,oe]=d.useState([]),[ae,re]=d.useState([]),[Be,le]=c.useState([]),[ce,b]=c.useState(!1),[E,F]=c.useState({name:"Select"}),[V,R]=c.useState({name:"Select"}),[v,_]=c.useState([]),k=c.useRef(null);d.useEffect(()=>{if(window.google&&window.google.maps&&window.google.maps.places){const t=new window.google.maps.Map(document.createElement("div"));k.current=new window.google.maps.places.PlacesService(t)}},[]);const de=He({price:x().required(n("user.create_request.errors.price")),time:x().required(n("user.create_request.errors.time")),endDate:x().required(n("user.create_request.errors.endDate")),address:x().required(n("user.create_request.errors.location")),images:x().required(n("user.create_request.errors.images")),description:x().required(n("user.create_request.errors.description")),skill:x().required(n("user.create_request.errors.skill"))}).required(),{register:m,handleSubmit:ue,setError:Ie,setValue:r,formState:{errors:s,isValid:We}}=De({resolver:Me(de),mode:"onChange"});console.log(s),c.useEffect(()=>{const t=new URLSearchParams(q.search),i=t.get("time");i?r("time",i):r("time",""),r("skill",t.get("skill")||""),r("price",t.get("price")||""),r("endDate",t.get("endDate")||""),r("days",t.get("days")||""),r("description",t.get("description")||""),r("address",t.get("address")||""),r("location",t.get("location")||""),L(t.get("images")?t.get("images").split(","):[]),t.get("skill")&&F({name:t.get("skill")||"Select",id:t.get("service_id")||0}),t.get("location")&&R({name:t.get("location")||"Select",id:t.get("location_id")||0}),f(t.get("lat")||""),j(t.get("lng")||""),M(t.get("city")||""),H(t.get("state")||""),A(t.get("country")||""),w(t.get("address")||"")},[q.search,r]);const me=t=>{r("skill",t.name)},pe=t=>{r("location",t.name),w(t.name),f(t.latitude||""),j(t.longtitude||"")},xe=async t=>{if(console.log("data",t),!h&&!g){$(Q,n("user.create_request.errors.select_address"),4e3,"error");return}const i=new URLSearchParams({price:t.price,time:t.time,endDate:t.endDate,location:t.location,description:t.description,skill:t.skill,location_id:V.id,service_id:E.id,images:P.join(","),lat:h,lng:g,city:Y,state:ee,country:te,address:S}).toString();O(`/user/helping-hand-request/confirm?${i}`)},he=async t=>{const i=Array.from(t.target.files);D(!0);const o=[];for(const a of i){const u=new FormData;u.append("file",a);try{const p=await N.uploadImage(u);o.push(p.url)}catch{$("Error uploading image","error")}}L(a=>{const u=[...a,...o].join(",");return r("images",u,{shouldValidate:!0}),[...a,...o]}),D(!1)},fe=async()=>{var t,i;try{y(!0);const o=await N.callRawAPI("/v3/api/custom/chumpchange/users/services",{},"GET"),a=await N.callRawAPI("/v3/api/custom/chumpchange/common/address/search",{},"GET");if(console.log("services",o),console.log("locations",a),!o.error){let u=[{name:"Select"}];(t=o==null?void 0:o.data)==null||t.map(l=>{u.push({name:l==null?void 0:l.name,price:l==null?void 0:l.price,id:l.id})}),oe(u);let p=[{name:"Select"}];(i=a==null?void 0:a.data)==null||i.map(l=>{p.push({...l,name:l==null?void 0:l.description,id:l.id})}),re(p),y(!1)}}catch(o){y(!1),console.log("error",o),Pe(J,o.message)}};d.useEffect(()=>{(async()=>fe())()},[]);const ge=async t=>{const i=t.target.value;w(i),i?(we(i),b(!0)):(le([]),b(!1))},je=(t,i,o,a,u,p)=>{console.log(t,i),f(t||""),j(i||""),M(a||""),H(u||""),A(p||""),w(o||""),C(!1)},B=t=>{t.target.closest(".modal-content")===null&&b(!1)};c.useEffect(()=>(document.addEventListener("mousedown",B),()=>{document.removeEventListener("mousedown",B)}),[]);const we=t=>{t.length>2?window.google&&new window.google.maps.places.AutocompleteService().getPlacePredictions({input:t},o=>_(o||[])):_([])},ve=t=>new Promise((i,o)=>{k.current&&k.current.getDetails({placeId:t},(a,u)=>{u===window.google.maps.places.PlacesServiceStatus.OK&&a.geometry?i({address:a.formatted_address,lat:a.geometry.location.lat(),lng:a.geometry.location.lng()}):o(`Failed to fetch details for placeId: ${t}`)})}),be=async t=>{try{const i=new window.google.maps.Map(document.createElement("div"));k.current=new window.google.maps.places.PlacesService(i);const o=await ve(t.place_id);console.log("details >> ",o),w(o==null?void 0:o.address),f(o.lat||""),j(o.lng||""),b(!1),_([])}catch(i){console.log("error >>",i)}};return console.log("predictions >> ",v),e.jsxs(e.Fragment,{children:[se&&e.jsxs("div",{className:"fixed left-0 top-0 z-[9999999] min-h-screen w-full bg-[#F2F2F7] ",children:[e.jsx("div",{className:"p-5",children:e.jsxs("div",{className:"relative flex w-full items-center justify-between ",children:[e.jsx(z,{onClick:()=>C(!1)}),e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:n("user.create_request.pick_location")}),e.jsx("div",{className:""})]})}),e.jsx("div",{className:" h-[calc(100vh-88px)] ",children:e.jsx(Ee,{handleChoose:je,lat:h?Number(h):"",lng:g?Number(g):""})})]}),ne?e.jsx("div",{className:"flex min-h-screen w-full items-center justify-center",children:e.jsx(T,{})}):e.jsxs("form",{onSubmit:ue(xe),className:"p-5",children:[X&&e.jsx(Ae,{showCloseButton:!1,children:e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsxs("div",{className:"text-lg font-semibold",children:[n("loading.uploading"),"..."]}),e.jsx("div",{className:"mt-12",children:e.jsx(T,{})})]})}),e.jsxs("div",{className:"relative flex w-full items-center justify-center ",children:[e.jsx("div",{className:" absolute left-0 top-0 ",children:e.jsx(z,{link:"/user/dashboard"})}),e.jsxs("div",{className:"",children:[e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:n("user.create_request.title")}),e.jsx("div",{className:"text-center font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:n("user.create_request.sub_title")})]})]}),e.jsxs("div",{className:"mt-7",children:[(s==null?void 0:s.description)&&e.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:e.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:(I=s==null?void 0:s.description)==null?void 0:I.message})}),(s==null?void 0:s.time)&&e.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:e.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:(W=s==null?void 0:s.time)==null?void 0:W.message})}),(s==null?void 0:s.images)&&e.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:e.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:(Z=s==null?void 0:s.images)==null?void 0:Z.message})}),(s==null?void 0:s.endDate)&&e.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:e.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:(U=s==null?void 0:s.endDate)==null?void 0:U.message})}),(s==null?void 0:s.price)&&e.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:e.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:(G=s==null?void 0:s.price)==null?void 0:G.message})}),e.jsx("div",{className:"max-w-[321px] font-['Poppins'] text-base font-normal tracking-tight text-black",children:n("user.create_request.desc")}),e.jsx("div",{className:" mb-3 mt-5 px-2 font-['Poppins'] text-[22px] font-medium tracking-wide text-black",children:n("user.create_request.hand_with")}),e.jsx("div",{className:"",children:e.jsx(K,{services:ie,onServiceSelect:me,selectedService:E,setSelectedService:F})}),e.jsxs("div",{className:"mt-6",children:[e.jsx("div",{className:" mb-3 px-2 font-['Poppins'] text-[22px] font-medium tracking-wide text-black ",children:n("user.create_request.offer_rate")}),e.jsx("input",{type:"text",placeholder:n("user.create_request.p_offer_rate"),...m("price",{onChange:qe}),className:"relative h-12 w-full rounded-2xl border-none bg-white text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("div",{className:" px-2 font-['Poppins'] text-[22px] font-medium tracking-wide text-black",children:n("user.create_request.describe")}),e.jsxs("div",{className:"relative mt-3 h-[104px] w-full overflow-hidden rounded-2xl bg-white",children:[e.jsx("span",{className:"absolute left-[14px] top-[14px] z-[99] h-5 w-5 ",children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M7.08366 15.8332H6.66699C3.33366 15.8332 1.66699 14.9998 1.66699 10.8332V6.6665C1.66699 3.33317 3.33366 1.6665 6.66699 1.6665H13.3337C16.667 1.6665 18.3337 3.33317 18.3337 6.6665V10.8332C18.3337 14.1665 16.667 15.8332 13.3337 15.8332H12.917C12.6587 15.8332 12.4087 15.9582 12.2503 16.1665L11.0003 17.8332C10.4503 18.5665 9.55033 18.5665 9.00033 17.8332L7.75033 16.1665C7.61699 15.9832 7.30866 15.8332 7.08366 15.8332Z",stroke:"#8181A4",strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M13.3301 9.16667H13.3375",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M9.99607 9.16667H10.0036",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M6.66209 9.16667H6.66957",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),e.jsx("textarea",{...m("description"),type:"text",placeholder:n("user.create_request.p_describe"),className:" absolute left-0 top-0 h-full w-full border-none bg-white  pl-[40px] pt-[14px] text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "})]})]}),e.jsx("div",{className:"mt-10 flex items-center justify-between ",children:e.jsx("div",{className:"font-['Poppins'] text-lg font-medium text-black",children:n("user.create_request.images")})}),e.jsx("div",{className:"scrollbar-hide flex w-full overflow-x-auto",children:e.jsxs("div",{className:" mt-5 flex gap-2",children:[P.map((t,i)=>e.jsxs("div",{className:" relative w-[120px]",children:[e.jsx("img",{className:"h-[127px] w-[120px] rounded-[20px] object-cover ",src:t}),e.jsx("div",{className:" flex items-center justify-center ",children:e.jsx("button",{onClick:()=>handleRemoveImage(i),children:e.jsxs("svg",{width:"25",height:"25",viewBox:"0 0 25 25",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M21.875 6.22933C18.4062 5.88558 14.9167 5.7085 11.4375 5.7085C9.375 5.7085 7.3125 5.81266 5.25 6.021L3.125 6.22933",stroke:"#FD5D5D",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M8.85449 5.17725L9.08366 3.81266C9.25033 2.82308 9.37533 2.0835 11.1357 2.0835H13.8649C15.6253 2.0835 15.7607 2.86475 15.917 3.82308L16.1462 5.17725",stroke:"#FD5D5D",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M19.6351 9.521L18.958 20.0106C18.8434 21.646 18.7497 22.9168 15.8434 22.9168H9.15592C6.24967 22.9168 6.15592 21.646 6.04134 20.0106L5.36426 9.521",stroke:"#FD5D5D",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M10.7607 17.1875H14.2295",stroke:"#FD5D5D",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M9.89551 13.021H15.1038",stroke:"#FD5D5D",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})})})]},i)),e.jsxs("div",{className:"relative flex h-[127px] w-[120px] flex-col items-center justify-center rounded-[20px] bg-[#e0e0e0]",children:[e.jsx("input",{type:"file",className:"absolute left-0 top-[-100%] h-[200%] w-full cursor-pointer opacity-0",onChange:he,multiple:!0}),e.jsxs("svg",{width:"48",height:"48",viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"47",height:"47",rx:"23.5",fill:"#56CCF2"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"47",height:"47",rx:"23.5",stroke:"white"}),e.jsx("path",{d:"M22.1667 16.3333C22.1667 15.597 22.7636 15 23.5 15C24.2364 15 24.8333 15.597 24.8333 16.3333V29.6667C24.8333 30.403 24.2364 31 23.5 31C22.7636 31 22.1667 30.403 22.1667 29.6667V16.3333Z",fill:"white"}),e.jsx("path",{d:"M16.8333 24.3333C16.097 24.3333 15.5 23.7364 15.5 23C15.5 22.2636 16.097 21.6667 16.8333 21.6667H30.1667C30.903 21.6667 31.5 22.2636 31.5 23C31.5 23.7364 30.903 24.3333 30.1667 24.3333H16.8333Z",fill:"white"})]}),e.jsx("div",{className:"mt-2 text-center font-['Poppins'] text-xs font-medium text-black",children:n("user.create_request.tap_here")}),e.jsx("div",{className:"text-center font-['Poppins'] text-xs font-medium text-black",children:n("user.create_request.upload")})]})]})}),e.jsxs("div",{className:"mt-[35px]",children:[e.jsx("div",{className:" px-2 font-['Poppins'] text-[22px] font-medium tracking-wide text-black",children:n("user.create_request.select_time")}),e.jsxs("div",{className:" mt-3 flex justify-between ",children:[e.jsxs("div",{className:"",children:[e.jsx("input",{type:"time",...m("time"),className:" h-12 w-full rounded-2xl border-none bg-white outline-none focus:border-none focus:outline-none focus:ring-0 "}),e.jsx("div",{className:"mt-[10px] text-center font-['Poppins'] text-base font-medium text-[#8080a3] ",children:n("user.create_request.time")})]}),e.jsxs("div",{className:"",children:[e.jsx("input",{type:"date",...m("endDate"),className:" h-12 w-full rounded-2xl border-none bg-white outline-none focus:border-none focus:outline-none focus:ring-0 "}),e.jsx("div",{className:"mt-[10px] text-center font-['Poppins'] text-base font-medium text-[#8080a3] ",children:n("user.create_request.date")})]})]})]}),e.jsx("div",{className:" mb-3 mt-5 px-2 font-['Poppins'] text-[22px] font-medium tracking-wide text-black ",children:n("user.create_request.con_location")}),e.jsx("div",{className:"  ",children:e.jsx(K,{services:ae,onServiceSelect:pe,selectedService:V,setSelectedService:R})}),e.jsxs("div",{className:" relative mt-4 ",children:[e.jsx(Fe,{apiKey:N._google_api_key,libraries:["places"],children:e.jsx("input",{type:"text",...m("address",{onChange:ge}),value:S.toLowerCase()==="select"?"":S,placeholder:n("user.create_request.address"),className:"relative h-12 w-full rounded-2xl border-none bg-white text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "})}),ce&&e.jsx("div",{className:"modal-content absolute bottom-[100%] z-[999999] max-h-[400px] w-full overflow-y-auto rounded-md border border-gray-300 bg-white",children:v.length>0?v==null?void 0:v.map((t,i)=>e.jsx("div",{className:"cursor-pointer p-2 hover:bg-gray-200",onClick:()=>be(t),children:t==null?void 0:t.description},i)):""})]}),e.jsxs("div",{className:"mt-4",children:[e.jsx("input",{type:"text",...m("lat",{onChange:t=>f(t.target.value)}),value:h,placeholder:n("user.create_request.latitude"),className:"relative hidden h-12 w-full rounded-2xl border-none bg-white text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "}),e.jsx("input",{type:"text",...m("lng",{onChange:t=>j(t.target.value)}),value:g,placeholder:n("user.create_request.longitude"),className:"relative mt-4 hidden h-12 w-full rounded-2xl border-none bg-white text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "})]}),e.jsxs("button",{type:"button",onClick:()=>C(!0),className:"relative mt-2 flex h-12 w-full items-center gap-4 rounded-2xl bg-white px-5",children:[e.jsx("svg",{width:"28",height:"28",viewBox:"0 0 28 28",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M14 8.90909C11.2 8.90909 8.90909 11.2 8.90909 14C8.90909 16.8 11.2 19.0909 14 19.0909C16.8 19.0909 19.0909 16.8 19.0909 14C19.0909 11.2 16.8 8.90909 14 8.90909ZM25.3273 12.7273C24.6909 7.38182 20.4909 3.18182 15.2727 2.67273V0H12.7273V2.67273C7.38182 3.18182 3.18182 7.38182 2.67273 12.7273H0V15.2727H2.67273C3.30909 20.6182 7.50909 24.8182 12.7273 25.3273V28H15.2727V25.3273C20.6182 24.6909 24.8182 20.4909 25.3273 15.2727H28V12.7273H25.3273ZM14 22.9091C9.03636 22.9091 5.09091 18.9636 5.09091 14C5.09091 9.03636 9.03636 5.09091 14 5.09091C18.9636 5.09091 22.9091 9.03636 22.9091 14C22.9091 18.9636 18.9636 22.9091 14 22.9091Z",fill:"#56CCF2"})}),e.jsx("div",{className:"font-['Poppins'] text-sm font-normal text-black",children:n("user.create_request.cur_location")})]})]}),e.jsx("div",{className:"mt-[58px]",children:e.jsx(Le,{type:"submit",className:"uppercase opacity-[0.85] ",children:n("buttons.next")})})]})]})};export{ht as default};
