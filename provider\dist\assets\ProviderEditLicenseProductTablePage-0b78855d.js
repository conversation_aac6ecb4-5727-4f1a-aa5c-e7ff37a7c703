import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{R as r,u as q,d as I,r as v,h as $}from"./vendor-f36d475e.js";import{u as G}from"./react-hook-form-ff037c98.js";import{o as U}from"./yup-afe5cf51.js";import{c as V,a as S}from"./yup-2f6e2476.js";import{M as z,A as F,G as H,n as K,t as O,s as N}from"./index-cf5e6bc7.js";import"./react-quill-3f3a006b.js";/* empty css                   */import"./InteractiveButton-303096ac.js";import"./index-bec80226.js";import{B as J,a as Q,R as W}from"./index-895fa99b.js";import X from"./DropDownSelection-d1fecb01.js";import{M as _}from"./index-bf8d79cc.js";import{u as Y}from"./react-i18next-1e3e6bc5.js";import{L as Z}from"./@mantine/core-ee88fb98.js";import"./@hookform/resolvers-eb417cd0.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./@craftjs/core-01ce56b9.js";import"./MoonLoader-4d8718ee.js";import"./@emotion/serialize-460cad7f.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-47271980.js";let m=new z;const qe=ee=>{const{dispatch:y}=r.useContext(F),P=V({ambulant_id:S().required(),price:S().required()}).required(),{dispatch:p}=r.useContext(H);r.useState({});const[w,i]=r.useState(!1),[te,u]=r.useState(!1),[E,d]=r.useState(!1),c=q().state,[f,k]=r.useState([]),[L,x]=r.useState({name:"Select"});console.log("product >>",c);const{t:o}=Y(),R=I();v.useState(0),v.useState(0);const{register:C,handleSubmit:T,setError:b,setValue:h,formState:{errors:l}}=G({resolver:U(P)}),j=$();v.useEffect(()=>{h("price",c.amount),h("ambulant_id",c.ambulant_id),x(f.find(s=>s.id==c.ambulant_id))},[c,f]);const A=async()=>{var s;try{u(!0),m.setTable("ambulant");const t=await m.callRestAPI({filter:['status,eq,"active"']},"GETALL");if(console.log("services",t),!t.error){let n=[{name:"Select"}];(s=t==null?void 0:t.list)==null||s.map(a=>{n.push({name:a==null?void 0:a.name,price:a==null?void 0:a.price,id:a.id})}),k(n),u(!1)}}catch(t){u(!1),console.log("error",t),O(y,t.message)}};r.useEffect(()=>{p({type:"SETPATH",payload:{path:"license_product"}}),A()},[]);const B=async s=>{i(!0);try{const t=await m.callRawAPI(`/v3/api/custom/chumpchange/provider/product/edit/${j.id}`,{ambulant_id:s.ambulant_id,amount:s.price},"PUT");if(!t.error)N(p,o("toast.updated"));else if(t.validation){const n=Object.keys(t.validation);for(let a=0;a<n.length;a++){const g=n[a];b(g,{type:"manual",message:t.validation[g]})}}i(!1)}catch(t){i(!1),console.log("Error",t),b("ambulant_id",{type:"manual",message:t.message})}},M=s=>{h("ambulant_id",s.id),x(s)},D=async()=>{d(!1);try{i(!0),(await m.callRawAPI(`/v3/api/custom/chumpchange/provider/product/delete/${j.id}`,{},"DELETE")).error||(N(p,o("toast.removed")),R("/provider/profile")),i(!1)}catch(s){console.log("Error",s),i(!1)}};return e.jsxs("div",{className:"p-5",children:[w&&e.jsx(_,{showCloseButton:!1,children:e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsxs("div",{className:"text-lg font-semibold",children:[o("loading.uploading"),"..."]}),e.jsx("div",{className:"mt-12",children:e.jsx(Z,{})})]})}),E&&e.jsx(_,{closeModal:()=>d(!1),children:e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsxs("div",{className:"text-lg font-semibold",children:[" ",o("provider.add_product.sure"),"?"]}),e.jsxs("div",{className:"mt-12 flex w-full justify-evenly gap-5 px-5",children:[e.jsx("button",{onClick:D,className:"text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:o("buttons.remove")}),e.jsx("button",{onClick:()=>d(!1),className:"relative flex h-12 w-[163px] items-center justify-center rounded-2xl bg-[#8181a4]/20 text-center font-['Poppins'] text-sm font-medium text-[#8080a3]",children:o("buttons.cancel")})]})]})}),e.jsxs("div",{className:"relative flex w-full items-center justify-center ",children:[e.jsx("div",{className:" absolute left-0 top-0 ",children:e.jsx(J,{})}),e.jsx("div",{className:"mt-3 text-center font-['Poppins'] text-lg font-medium text-black",children:o("provider.add_product.e_title")})]}),((l==null?void 0:l.ambulant_id)||(l==null?void 0:l.price))&&e.jsx("div",{className:"mt-10 flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:e.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium capitalize text-[#f95050]",children:o("provider.add_product.missing")})}),e.jsxs("form",{className:" w-full ",onSubmit:T(B),children:[e.jsx("div",{className:"mt-[50px] font-['Poppins'] text-[22px] font-medium text-black",children:o("provider.add_product.product_name")}),e.jsxs("div",{className:"mt-[27px] w-full ",children:[e.jsx("div",{className:"mb-1 px-2 font-['Poppins'] text-sm font-medium text-[#8080a3] ",children:o("provider.add_product.s_product")}),e.jsx(X,{services:f,onServiceSelect:M,selectedService:L,setSelectedService:x})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("div",{className:" px-2 font-['Poppins'] text-[22px] font-bold tracking-wide text-black",children:o("provider.add_product.price")}),e.jsx("input",{type:"text",placeholder:o("provider.add_product.p_price"),...C("price",{onChange:s=>K(s,10)}),className:"relative mt-3 h-12 w-full rounded-2xl border-none bg-white text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "})]}),e.jsxs("div",{className:"mt-[35px]",children:[e.jsx(Q,{type:"submit",children:"Save"}),e.jsx(W,{onClick:()=>d(!0),className:"mt-[17px]",children:"Remove"})]})]})]})};export{qe as default};
