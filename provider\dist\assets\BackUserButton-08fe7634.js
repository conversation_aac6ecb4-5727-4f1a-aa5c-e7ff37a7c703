import{j as t}from"./@react-google-maps/api-afbf18d5.js";import{d as o}from"./vendor-f36d475e.js";const n=({link:e,onClick:s})=>{const r=o();return t.jsx("button",{type:"button",onClick:()=>s?s():r(e||-1),className:"relative flex h-12 w-12 cursor-pointer items-center justify-center rounded-[32px] bg-[#8181a4]/20 backdrop-blur-[20px]",children:t.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[t.jsx("path",{d:"M6.42226 13.1128C5.85925 12.4982 5.85925 11.5019 6.42226 10.8873L12.5389 4.21097C13.1019 3.59643 14.0147 3.59643 14.5777 4.21097C15.1408 4.82551 15.1408 5.82188 14.5777 6.43642L8.46113 13.1128C7.89811 13.7273 6.98528 13.7273 6.42226 13.1128Z",fill:"#8181A4"}),t.jsx("path",{d:"M14.5777 19.7892C14.0147 20.4037 13.1019 20.4037 12.5389 19.7892L6.42226 13.1128C5.85925 12.4982 5.85925 11.5019 6.42226 10.8873C6.98528 10.2728 7.89811 10.2728 8.46113 10.8873L14.5777 17.5637C15.1408 18.1782 15.1408 19.1746 14.5777 19.7892Z",fill:"#8181A4"})]})})};export{n as default};
