import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as a,b as c}from"./vendor-4f06b3f4.js";import{X as u}from"./@uppy/xhr-upload-57e1d45c.js";import{u as f,D as h}from"./@uppy/react-ed4c83d4.js";import{M as x,A as b,G as g,s as E}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{a as j}from"./@uppy/core-10860ef6.js";import"./@uppy/aws-s3-e4097d1c.js";import"./@craftjs/core-4ea9888f.js";import"./@uppy/aws-s3-multipart-fe0b9775.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@uppy/dashboard-f719adde.js";import"./@uppy/audio-a0565fb1.js";import"./@uppy/compressor-af9f85a3.js";import"./@uppy/drag-drop-b912c52e.js";import"./@uppy/progress-bar-70880302.js";import"./@uppy/file-input-ee919754.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";let i=new x;const W=({setSidebar:p})=>{const{dispatch:m}=a.useContext(b),d=c(),{dispatch:r}=a.useContext(g),l=f(()=>{let e=new j;return e.use(u,{id:"XHRUpload",method:"post",formData:!0,limit:0,fieldName:"file",allowedMetaFields:["caption","size"],headers:i.getHeader(),endpoint:i.uploadUrl()}),e.on("file-added",o=>{e.setFileMeta(o.id,{size:o.size,caption:""})}),e.on("upload-success",async(o,s)=>{s.status,s.body,console.log("response",s),E(r,"Uploaded"),d("/admin/photo")}),e.on("upload-error",(o,s,n)=>{n.status==401&&tokenExpireError(m,"TOKEN_EXPIRED")}),e});return a.useEffect(()=>{r({type:"SETPATH",payload:{path:"photo"}})},[]),t.jsxs("div",{className:"relative p-4 flex-auto",children:[t.jsxs("div",{className:"flex items-center pb-3 gap-4 border-b border-b-[#E0E0E0] justify-between",children:[t.jsx("div",{className:"flex items-center gap-3",children:t.jsx("span",{className:"text-lg font-semibold",children:"Add Photo"})}),t.jsx("div",{className:"flex items-center gap-4",children:t.jsx("button",{className:"flex items-center py-2 px-3 border border-[#C6C6C6] rounded-md shadow-sm hover:bg-[#F4F4F4]",onClick:()=>p(!1),children:"Cancel"})})]}),t.jsx(h,{uppy:l})]})};export{W as default};
