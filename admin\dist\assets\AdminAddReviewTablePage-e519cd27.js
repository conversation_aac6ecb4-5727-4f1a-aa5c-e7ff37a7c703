import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as o,b as S}from"./vendor-4f06b3f4.js";import{u as I}from"./react-hook-form-f3d72793.js";import{o as R}from"./yup-2324a46a.js";import{c as A,a as d}from"./yup-17027d7a.js";import{G as N,A as _,M as E,s as D,t as k}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as m}from"./MkdInput-ff3aa862.js";import{I as C}from"./InteractiveButton-8f7d74ee.js";import"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";const de=({setSidebar:x})=>{const{dispatch:p}=o.useContext(N),h=A({rating:d(),description:d(),client_id:d(),provider_id:d(),job_id:d()}).required(),{dispatch:v}=o.useContext(_),[f,P]=o.useState({}),[b,c]=o.useState(!1),j=S(),{register:r,handleSubmit:y,setError:g,setValue:T,formState:{errors:s}}=I({resolver:R(h)});o.useState([]);const w=async a=>{let u=new E;c(!0);try{for(let l in f){let i=new FormData;i.append("file",f[l].file);let n=await u.uploadImage(i);a[l]=n.url}u.setTable("review");const t=await u.callRestAPI({rating:a.rating,description:a.description,client_id:a.client_id,provider_id:a.provider_id,job_id:a.job_id},"POST");if(!t.error)D(p,"Added"),j("/admin/review"),x(!1),p({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(t.validation){const l=Object.keys(t.validation);for(let i=0;i<l.length;i++){const n=l[i];g(n,{type:"manual",message:t.validation[n]})}}c(!1)}catch(t){c(!1),console.log("Error",t),g("rating",{type:"manual",message:t.message}),k(v,t.message)}};return o.useEffect(()=>{p({type:"SETPATH",payload:{path:"review"}})},[]),e.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Review"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:y(w),children:[e.jsx(m,{type:"number",page:"add",name:"rating",errors:s,label:"Rating",placeholder:"Rating",register:r,className:""}),e.jsx(m,{type:"text",page:"add",name:"description",errors:s,label:"Description",placeholder:"Description",register:r,className:""}),e.jsx(m,{type:"text",page:"add",name:"client_id",errors:s,label:"Client Id",placeholder:"Client Id",register:r,className:""}),e.jsx(m,{type:"text",page:"add",name:"provider_id",errors:s,label:"Provider Id",placeholder:"Provider Id",register:r,className:""}),e.jsx(m,{type:"text",page:"add",name:"job_id",errors:s,label:"Job Id",placeholder:"Job Id",register:r,className:""}),e.jsx(C,{type:"submit",loading:b,disabled:b,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{de as default};
