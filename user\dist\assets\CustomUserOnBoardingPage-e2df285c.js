import{j as e}from"./@react-google-maps/api-ee55a349.js";import{i as a,r,d,R as x}from"./vendor-b16525a8.js";import"./index-9ef1c042.js";import{M as c,A as C,G as m}from"./index-09a1718e.js";import{A as p}from"./index-dd254604.js";import{t}from"./i18next-7389dd8c.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";new c;const _=()=>{a();const[s,i]=r.useState(1);r.useContext(C),r.useContext(m);const n=d();x.useEffect(()=>{},[]);const o=()=>{s<3?i(s+1):n("/user/dashboard")},l=()=>{switch(s){case 1:return e.jsxs(e.Fragment,{children:[e.jsx("h3",{className:"mt-10 text-center font-['Poppins'] text-[26px] font-semibold text-black",children:t("user.onboarding.first_page.title")}),e.jsx("div",{className:"mt-6 flex items-center justify-center",children:e.jsx("div",{className:"flex h-[162px] w-[162px] items-center justify-center rounded-full border-4 border-white bg-white/75 backdrop-blur-2xl",children:e.jsxs("svg",{width:"100",height:"100",viewBox:"0 0 100 100",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M79.1663 58.3333C85.3747 52.25 91.6663 44.9583 91.6663 35.4167C91.6663 29.3388 89.2519 23.5098 84.9542 19.2121C80.6565 14.9144 74.8275 12.5 68.7497 12.5C61.4163 12.5 56.2497 14.5833 49.9997 20.8333C43.7497 14.5833 38.583 12.5 31.2497 12.5C25.1718 12.5 19.3429 14.9144 15.0451 19.2121C10.7474 23.5098 8.33301 29.3388 8.33301 35.4167C8.33301 45 14.583 52.2917 20.833 58.3333L49.9997 87.5L79.1663 58.3333Z",stroke:"#56CCF2",strokeWidth:"4",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M50 20.8333L37.6667 33.1666C36.8201 34.007 36.1482 35.0067 35.6897 36.108C35.2312 37.2092 34.9951 38.3903 34.9951 39.5833C34.9951 40.7762 35.2312 41.9573 35.6897 43.0586C36.1482 44.1598 36.8201 45.1595 37.6667 45.9999C41.0834 49.4166 46.5417 49.5416 50.1667 46.2916L58.7917 38.3749C60.9536 36.4132 63.7683 35.3265 66.6875 35.3265C69.6068 35.3265 72.4215 36.4132 74.5834 38.3749L86.9167 49.4583M75 62.4999L66.6667 54.1666M62.5 74.9999L54.1667 66.6666",stroke:"#56CCF2",strokeWidth:"4",strokeLinecap:"round",strokeLinejoin:"round"})]})})}),e.jsx("p",{className:"mt-10 text-center font-['Poppins'] text-sm font-medium text-[#2b2b2b]",children:t("user.onboarding.first_page.desc")})]});case 2:return e.jsxs(e.Fragment,{children:[e.jsx("h3",{className:"mt-10 text-center font-['Poppins'] text-[26px] font-semibold text-black",children:t("user.onboarding.sec_page.title")}),e.jsx("div",{className:"mt-6 flex items-center justify-center",children:e.jsx("div",{className:"flex h-[162px] w-[162px] items-center justify-center rounded-full border-4 border-white bg-white/75 backdrop-blur-2xl",children:e.jsxs("svg",{width:"100",height:"100",viewBox:"0 0 100 100",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M65.625 93.75C60.6805 93.75 55.847 92.2838 51.7358 89.5368C47.6245 86.7897 44.4202 82.8853 42.528 78.3171C40.6358 73.7489 40.1407 68.7223 41.1054 63.8728C42.07 59.0232 44.451 54.5687 47.9473 51.0723C51.4437 47.576 55.8982 45.195 60.7478 44.2304C65.5973 43.2657 70.6239 43.7608 75.1921 45.653C79.7603 47.5452 83.6647 50.7495 86.4118 54.8608C89.1588 58.972 90.625 63.8055 90.625 68.75C90.625 75.3804 87.9911 81.7393 83.3027 86.4277C78.6143 91.1161 72.2554 93.75 65.625 93.75ZM65.625 50C61.9166 50 58.2915 51.0997 55.2081 53.16C52.1246 55.2202 49.7214 58.1486 48.3023 61.5747C46.8831 65.0008 46.5118 68.7708 47.2353 72.408C47.9588 76.0451 49.7445 79.386 52.3668 82.0083C54.989 84.6305 58.3299 86.4163 61.9671 87.1397C65.6042 87.8632 69.3742 87.4919 72.8003 86.0728C76.2264 84.6536 79.1548 82.2504 81.2151 79.167C83.2753 76.0835 84.375 72.4584 84.375 68.75C84.375 63.7772 82.3996 59.0081 78.8833 55.4918C75.367 51.9754 70.5978 50 65.625 50Z",fill:"#56CCF2"}),e.jsx("path",{d:"M70.5938 78.125L62.5 70.0312V56.25H68.75V67.4688L75 73.7188L70.5938 78.125Z",fill:"#56CCF2"}),e.jsx("path",{d:"M87.5 18.75C87.5 17.0924 86.8415 15.5027 85.6694 14.3306C84.4973 13.1585 82.9076 12.5 81.25 12.5H68.75V6.25H62.5V12.5H37.5V6.25H31.25V12.5H18.75C17.0924 12.5 15.5027 13.1585 14.3306 14.3306C13.1585 15.5027 12.5 17.0924 12.5 18.75V81.25C12.5 82.9076 13.1585 84.4973 14.3306 85.6694C15.5027 86.8415 17.0924 87.5 18.75 87.5H31.25V81.25H18.75V18.75H31.25V25H37.5V18.75H62.5V25H68.75V18.75H81.25V37.5H87.5V18.75Z",fill:"#56CCF2"})]})})}),e.jsx("p",{className:"mt-10 text-center font-['Poppins'] text-sm font-medium text-[#2b2b2b]",children:t("user.onboarding.sec_page.desc")})]});case 3:return e.jsxs(e.Fragment,{children:[e.jsx("h3",{className:"mt-10 text-center font-['Poppins'] text-[26px] font-semibold text-black",children:t("user.onboarding.trd_page.title")}),e.jsx("div",{className:"mt-6 flex items-center justify-center",children:e.jsx("div",{className:"flex h-[162px] w-[162px] items-center justify-center rounded-full border-4 border-white bg-white/75 backdrop-blur-2xl",children:e.jsx("svg",{width:"100",height:"100",viewBox:"0 0 100 100",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M18.848 77.7344C18.848 79.4629 20.2445 80.8594 21.973 80.8594H78.0277C79.7563 80.8594 81.1527 79.4629 81.1527 77.7344V54.9805C81.1527 37.7734 67.2074 23.8281 50.0004 23.8281C32.7934 23.8281 18.848 37.7734 18.848 54.9805V77.7344ZM25.8793 54.9805C25.8793 41.6602 36.6801 30.8594 50.0004 30.8594C63.3207 30.8594 74.1215 41.6602 74.1215 54.9805V73.8281H39.4535V57.1289C39.4535 56.5918 39.0141 56.1523 38.477 56.1523H34.1801C33.643 56.1523 33.2035 56.5918 33.2035 57.1289V73.8281H25.8793V54.9805ZM21.182 30.3223L25.0492 26.4551C25.352 26.1523 25.352 25.6543 25.0492 25.3516L18.4184 18.7207C18.2715 18.5753 18.0732 18.4937 17.8666 18.4937C17.66 18.4937 17.4617 18.5753 17.3148 18.7207L13.4477 22.5879C13.3023 22.7347 13.2207 22.933 13.2207 23.1396C13.2207 23.3463 13.3023 23.5446 13.4477 23.6914L20.0785 30.3223C20.3813 30.625 20.8695 30.625 21.182 30.3223ZM86.5727 22.5879L82.7055 18.7207C82.5586 18.5753 82.3604 18.4937 82.1537 18.4937C81.9471 18.4937 81.7488 18.5753 81.602 18.7207L74.9711 25.3516C74.8257 25.4984 74.7441 25.6967 74.7441 25.9033C74.7441 26.11 74.8257 26.3082 74.9711 26.4551L78.8383 30.3223C79.141 30.625 79.6391 30.625 79.9418 30.3223L86.5727 23.6914C86.8754 23.3789 86.8754 22.8906 86.5727 22.5879ZM81.2504 87.1094H18.7504C17.0219 87.1094 15.6254 88.5059 15.6254 90.2344V92.5781C15.6254 93.0078 15.977 93.3594 16.4066 93.3594H83.5941C84.0238 93.3594 84.3754 93.0078 84.3754 92.5781V90.2344C84.3754 88.5059 82.9789 87.1094 81.2504 87.1094ZM47.266 17.5781H52.7348C53.1645 17.5781 53.516 17.2266 53.516 16.7969V7.42188C53.516 6.99219 53.1645 6.64062 52.7348 6.64062H47.266C46.8363 6.64062 46.4848 6.99219 46.4848 7.42188V16.7969C46.4848 17.2266 46.8363 17.5781 47.266 17.5781Z",fill:"#56CCF2"})})})}),e.jsx("p",{className:"mt-10 text-center font-['Poppins'] text-sm font-medium text-[#2b2b2b]",children:t("user.onboarding.trd_page.desc")})]});default:return null}};return e.jsx("div",{className:"min-h-screen w-full bg-[#56CCF2]",children:e.jsxs(p,{className:"flex flex-col",children:[e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{onClick:()=>n("/user/dashboard"),className:"h-12 w-[75px] rounded-[100px] bg-[#8181a4]/40 text-center font-['Poppins'] text-sm font-semibold text-[#e1e2e3]",children:t("buttons.skip")})}),e.jsxs("div",{className:"flex h-full w-full flex-grow flex-col justify-center",children:[e.jsx("div",{className:"transform transition-transform duration-500",children:l()}),e.jsx("div",{className:"mt-10 flex justify-center pb-6",children:e.jsx("button",{className:"h-14 w-[120px] rounded-[100px] bg-[#f2f2f2] text-center font-['Poppins'] text-base font-semibold text-black",onClick:o,children:t("buttons.next")})})]})]})})};export{_ as default};
