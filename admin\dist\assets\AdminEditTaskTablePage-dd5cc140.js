import{j as a}from"./@react-google-maps/api-ac2f9d6f.js";import{R as d,b as G,r as t,h as U}from"./vendor-4f06b3f4.js";import{u as q}from"./react-hook-form-f3d72793.js";import{o as B}from"./yup-2324a46a.js";import{c as H,a as b}from"./yup-17027d7a.js";import{M as $,A as K,G as V,t as z,s as J}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as x}from"./MkdInput-ff3aa862.js";import{I as Q}from"./InteractiveButton-8f7d74ee.js";import{S as W}from"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";let c=new $;const Fe=l=>{const{dispatch:y}=d.useContext(K),I=H({status:b(),provider_status:b()}).required(),{dispatch:u}=d.useContext(V),[v,X]=d.useState({}),[S,p]=d.useState(!1),[E,f]=d.useState(!1),j=G(),[Y,k]=t.useState(0),[Z,w]=t.useState(""),[ee,T]=t.useState(0),[te,P]=t.useState(0),[se,N]=t.useState(0),[oe,A]=t.useState(""),[ae,D]=t.useState(""),[re,R]=t.useState(0),[ie,L]=t.useState(""),[de,C]=t.useState(""),[le,F]=t.useState(""),{register:_,handleSubmit:M,setError:g,setValue:s,formState:{errors:h}}=q({resolver:B(I)}),r=U();t.useEffect(function(){(async function(){try{f(!0),c.setTable("task");const e=await c.callRestAPI({id:l.activeId?l.activeId:Number(r==null?void 0:r.id)},"GET");e.error||(s("user_id",e.model.user_id),s("title",e.model.title),s("service_id",e.model.service_id),s("location_id",e.model.location_id),s("offer",e.model.offer),s("image",e.model.image),s("description",e.model.description),s("provider_id",e.model.provider_id),s("status",e.model.status),s("provider_status",e.model.provider_status),s("start_datetime",e.model.start_datetime),k(e.model.user_id),w(e.model.title),T(e.model.service_id),P(e.model.location_id),N(e.model.offer),A(e.model.image),D(e.model.description),R(e.model.provider_id),L(e.model.status),C(e.model.provider_status),F(e.model.start_datetime),f(!1))}catch(e){f(!1),console.log("error",e),z(y,e.message)}})()},[]);const O=async e=>{p(!0);try{c.setTable("task");for(let m in v){let i=new FormData;i.append("file",v[m].file);let n=await c.uploadImage(i);e[m]=n.url}const o=await c.callRestAPI({id:l.activeId?l.activeId:Number(r==null?void 0:r.id),status:e.status,provider_status:e.provider_status},"PUT");if(!o.error)J(u,"Updated"),j("/admin/job_requests"),u({type:"REFRESH_DATA",payload:{refreshData:!0}}),l.setSidebar(!1);else if(o.validation){const m=Object.keys(o.validation);for(let i=0;i<m.length;i++){const n=m[i];g(n,{type:"manual",message:o.validation[n]})}}p(!1)}catch(o){p(!1),console.log("Error",o),g("user_id",{type:"manual",message:o.message})}};return d.useEffect(()=>{u({type:"SETPATH",payload:{path:"task"}})},[]),a.jsxs("div",{className:" mx-auto rounded   p-5 shadow-md",children:[a.jsx("h4",{className:"text-2xl font-medium",children:"Edit Task"}),E?a.jsx(W,{}):a.jsxs("form",{className:" w-full max-w-lg",onSubmit:M(O),children:[a.jsx(x,{type:"select",page:"edit",name:"status",errors:h,label:"Status",placeholder:"Status",register:_,className:"",options:["created","accepted","in-progress","completed","cancelled"]}),a.jsx(x,{type:"select",page:"edit",name:"provider_status",errors:h,label:"Provider Status",placeholder:"Provider Status",register:_,className:"",options:["created","accepted","in-progress","completed","cancelled"]}),a.jsx(Q,{type:"submit",className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",loading:S,disable:S,children:"Submit"})]})]})};export{Fe as default};
