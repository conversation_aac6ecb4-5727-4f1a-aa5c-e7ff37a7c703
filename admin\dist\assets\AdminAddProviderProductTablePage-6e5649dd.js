import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as r,b as j}from"./vendor-4f06b3f4.js";import{u as A}from"./react-hook-form-f3d72793.js";import{o as P}from"./yup-2324a46a.js";import{c as w,a as u}from"./yup-17027d7a.js";import{G as I,A as E,M as N,s as T,t as R}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as c}from"./MkdInput-ff3aa862.js";import{I as k}from"./InteractiveButton-8f7d74ee.js";import"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";const me=({setSidebar:h})=>{const{dispatch:d}=r.useContext(I),x=w({product_type_id:u(),amount:u(),provider_id:u()}).required(),{dispatch:g}=r.useContext(E),[f,D]=r.useState({}),[b,m]=r.useState(!1),v=j(),{register:p,handleSubmit:_,setError:y,setValue:C,formState:{errors:l}}=A({resolver:P(x)});r.useState([]);const S=async s=>{let n=new N;m(!0);try{for(let a in f){let o=new FormData;o.append("file",f[a].file);let i=await n.uploadImage(o);s[a]=i.url}n.setTable("provider_product");const e=await n.callRestAPI({product_type_id:s.product_type_id,amount:s.amount,provider_id:s.provider_id},"POST");if(!e.error)T(d,"Added"),v("/admin/provider_product"),h(!1),d({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(e.validation){const a=Object.keys(e.validation);for(let o=0;o<a.length;o++){const i=a[o];y(i,{type:"manual",message:e.validation[i]})}}m(!1)}catch(e){m(!1),console.log("Error",e),y("product_type_id",{type:"manual",message:e.message}),R(g,e.message)}};return r.useEffect(()=>{d({type:"SETPATH",payload:{path:"provider_product"}})},[]),t.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Add Provider Product"}),t.jsxs("form",{className:" w-full max-w-lg",onSubmit:_(S),children:[t.jsx(c,{type:"number",page:"add",name:"product_type_id",errors:l,label:"Product Type Id",placeholder:"Product Type Id",register:p,className:""}),t.jsx(c,{type:"number",page:"add",name:"amount",errors:l,label:"Amount",placeholder:"Amount",register:p,className:""}),t.jsx(c,{type:"number",page:"add",name:"provider_id",errors:l,label:"Provider Id",placeholder:"Provider Id",register:p,className:""}),t.jsx(k,{type:"submit",loading:b,disabled:b,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{me as default};
