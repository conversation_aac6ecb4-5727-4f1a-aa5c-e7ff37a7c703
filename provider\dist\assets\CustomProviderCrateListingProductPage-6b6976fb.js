import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{h as Q,u as J,d as X,R as p,r as o}from"./vendor-f36d475e.js";import"./index-5deedf4a.js";import{M as Y,A as ee,G as te,n as S,t as se,s as ie}from"./index-cf5e6bc7.js";import{B as oe,R as re}from"./index-895fa99b.js";import g from"./DropDownSelection-d1fecb01.js";import{u as ne}from"./react-hook-form-ff037c98.js";import{o as ae}from"./yup-afe5cf51.js";import{c as ce,a as m}from"./yup-2f6e2476.js";import{A as le}from"./index-ad319f83.js";import{M as de}from"./index-bf8d79cc.js";import{S as D}from"./index-65bc3378.js";import{t as s}from"./i18next-7389dd8c.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-1e3e6bc5.js";import"./@hookform/resolvers-eb417cd0.js";const pe=[{name:"Select"},{name:"Good"},{name:"Bad"},{name:"Excellent"}],u=new Y,Ue=()=>{Q();const k=J(),P=X(),[q,M]=p.useState([]),[A,x]=p.useState(!0),{state:me,dispatch:F}=o.useContext(ee);o.useContext(te);const[n,h]=o.useState({name:"Select"}),[w,f]=o.useState({name:"Select"}),[ue,W]=o.useState(!1),[j,v]=o.useState([]),[E,C]=o.useState(!1),[I,b]=o.useState(!1),[H,L]=o.useState({name:"Select City"}),[R,B]=o.useState([]),[xe,V]=p.useState(),G=ce({name:m().required("Product name is required"),price:m().required("Asking price is required"),quantity:m().required("Quantity is required"),description:m().required("Description is required")}).required(),{register:d,handleSubmit:U,setError:he,setValue:a,formState:{errors:N,isValid:_}}=ne({resolver:ae(G),mode:"onChange"});o.useEffect(()=>{W(_)},[_]),o.useEffect(()=>{const t=new URLSearchParams(k.search);V(t.get("republishId")||""),a("name",t.get("name")||""),a("price",t.get("price")||""),a("quantity",t.get("quantity")||""),a("description",t.get("description")||""),a("city",t.get("city")||""),h({name:t.get("type")||"Select",id:t.get("product_id")}),f({name:t.get("condition")||"Select"}),v(t.get("images")?t.get("images").split(","):[])},[k.search,a]);const Z=t=>{h(t)},T=t=>{f(t)},$=t=>{if(j.length==0){C(!0);return}const i=new URLSearchParams({type:n.name,product_id:n.id,name:t.name,price:t.price,quantity:t.quantity,condition:w.name,description:t.description,city:t.city,images:j.join(",")}).toString();P(`/provider/crate-listing/product/confirm?${i}`)},z=async()=>{var t;try{x(!0);const i=await u.callRawAPI("/v3/api/custom/chumpchange/provider/list_products",{},"GET");if(console.log("services",i),!i.error){let l=[{name:"Select"}];(t=i==null?void 0:i.data)==null||t.map(r=>{l.push({name:r==null?void 0:r.name,price:r==null?void 0:r.price,id:r.id})}),M(l),u.setTable("city");const c=await u.callRestAPI({},"GETALL");B(c.list),x(!1)}}catch(i){x(!1),console.log("error",i),se(F,i.message)}};p.useEffect(()=>{(async()=>z())()},[]);const K=async t=>{C(!1);const i=Array.from(t.target.files);b(!0);const l=[];for(const c of i){const r=new FormData;r.append("file",c);try{const y=await u.uploadImage(r);l.push(y.url)}catch{ie(s("provider.cl_product.error_uploading_image"),"error")}}v(c=>[...c,...l]),b(!1)},O=t=>{v(i=>i.filter((l,c)=>c!==t))};return e.jsxs("form",{onSubmit:U($),className:"p-5",children:[I&&e.jsx(de,{showCloseButton:!1,children:e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsxs("div",{className:"text-lg font-semibold",children:[s("loading.uploading"),"..."]}),e.jsx("div",{className:"mt-12",children:e.jsx(D,{})})]})}),e.jsxs("div",{className:"relative flex w-full items-center justify-center ",children:[e.jsx("div",{className:" absolute left-0 top-0 ",children:e.jsx(oe,{})}),e.jsxs("div",{className:"",children:[e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:s("provider.cl_product.title")}),e.jsx("div",{className:"text-center font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:n!=null&&n.name?n==null?void 0:n.name:s("provider.cl_product.select")})]})]}),A?e.jsx("div",{className:"flex min-h-screen w-full items-center justify-center",children:e.jsx(D,{})}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mt-7",children:[(Object.keys(N).length>0||E)&&e.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:e.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:s("provider.cl_product.please_fill_required")})}),e.jsx("div",{className:"max-w-[321px] font-['Poppins'] text-base font-normal tracking-tight text-black",children:s("provider.cl_product.info")}),e.jsx("div",{className:"mt-5 px-2 font-['Poppins'] text-[22px] font-medium text-black",children:s("provider.cl_product.what_selling")}),e.jsxs("div",{className:"mt-2",children:[e.jsx("div",{className:"px-2 font-['Poppins'] text-base font-medium text-[#8080a3]",children:s("provider.cl_product.select_product_type")}),e.jsx(g,{services:q,onServiceSelect:Z,selectedService:n,setSelectedService:h})]}),e.jsxs("div",{className:"mt-5",children:[e.jsx("div",{className:"mb-1 px-2 font-['Poppins'] text-sm font-medium text-[#8080a3] ",children:s("provider.cl_product.product_name")}),e.jsx(le,{type:"text",name:"name",placeholder:s("provider.cl_product.p_product_name"),errors:N,register:d,icon:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M3.475 12.75L7.25 16.525C8.8 18.075 11.317 18.075 12.875 16.525L16.534 12.867C18.084 11.317 18.084 8.8 16.534 7.242L12.75 3.475C11.959 2.684 10.867 2.259 9.75 2.317L5.584 2.517C3.917 2.592 2.592 3.917 2.509 5.575L2.309 9.742C2.259 10.867 2.684 11.959 3.475 12.75Z",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.916 10C9.067 10 10 9.067 10 7.917C10 6.766 9.067 5.834 7.916 5.834C6.766 5.834 5.833 6.766 5.833 7.917C5.833 9.067 6.766 10 7.916 10Z",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})})]}),e.jsxs("div",{className:"mt-5",children:[e.jsx("div",{className:"mb-1 px-2 font-['Poppins'] text-sm font-medium text-[#8080a3] ",children:s("provider.cl_product.asking_price")}),e.jsx("input",{type:"text",name:"price",placeholder:s("provider.cl_product.p_asking_price"),className:"relative h-12 w-full rounded-2xl border-none bg-white text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 ",...d("price",{onChange:S})})]}),e.jsxs("div",{className:"mt-5",children:[e.jsx("div",{className:"mb-1 px-2 font-['Poppins'] text-sm font-medium text-[#8080a3] ",children:s("provider.cl_product.quantity_available")}),e.jsx("input",{type:"text",name:"quantity",placeholder:s("provider.cl_product.p_quantity_available"),className:"relative h-12 w-full rounded-2xl border-none bg-white text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 ",...d("quantity",{onChange:S})})]}),e.jsxs("div",{className:"mt-5",children:[e.jsx("div",{className:"mb-1 px-2 font-['Poppins'] text-base font-medium text-[#8080a3]",children:s("provider.cl_product.condition")}),e.jsx(g,{services:pe,onServiceSelect:T,selectedService:w,setSelectedService:f})]}),e.jsxs("div",{className:"mt-5",children:[e.jsx("div",{className:"mb-1 px-2 font-['Poppins'] text-sm font-medium text-[#8080a3] ",children:s("provider.cl_product.description")}),e.jsxs("div",{className:"relative h-[104px] w-full overflow-hidden rounded-2xl bg-white",children:[e.jsx("span",{className:"absolute left-[14px] top-[14px] z-[99] h-5 w-5 ",children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M7.08366 15.8332H6.66699C3.33366 15.8332 1.66699 14.9998 1.66699 10.8332V6.6665C1.66699 3.33317 3.33366 1.6665 6.66699 1.6665H13.3337C16.667 1.6665 18.3337 3.33317 18.3337 6.6665V10.8332C18.3337 14.1665 16.667 15.8332 13.3337 15.8332H12.917C12.6587 15.8332 12.4087 15.9582 12.2503 16.1665L11.0003 17.8332C10.4503 18.5665 9.55033 18.5665 9.00033 17.8332L7.75033 16.1665C7.61699 15.9832 7.30866 15.8332 7.08366 15.8332Z",stroke:"#8181A4",strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M13.3301 9.16667H13.3375",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M9.99607 9.16667H10.0036",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M6.66209 9.16667H6.66957",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),e.jsx("textarea",{type:"text",name:"description",placeholder:s("provider.cl_product.p_description"),className:" absolute left-0 top-0 h-full w-full border-none bg-white  pl-[40px] pt-[14px] text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 ",...d("description")})]})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("div",{className:" px-2 font-['Poppins'] text-[22px] font-bold tracking-wide text-black",children:s("provider.s_listing.city_name")}),e.jsx(g,{services:R,onServiceSelect:t=>{L(t),a("city",t.name)},selectedService:H,setSelectedService:L})]})]}),e.jsxs("div",{className:"mt-10 flex items-center justify-between ",children:[e.jsx("div",{className:"font-['Poppins'] text-lg font-medium text-black",children:s("provider.cl_product.product_images")}),e.jsxs("div",{className:" relative flex w-24 items-center gap-1 font-['Poppins'] text-lg font-medium text-[#56ccf2]",children:[e.jsx("input",{type:"file",className:"absolute left-0 top-[-100%] h-[200%] w-full cursor-pointer opacity-0",onChange:K,multiple:!0}),s("provider.cl_product.upload"),e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M7.50033 18.3332H12.5003C16.667 18.3332 18.3337 16.6665 18.3337 12.4998V7.49984C18.3337 3.33317 16.667 1.6665 12.5003 1.6665H7.50033C3.33366 1.6665 1.66699 3.33317 1.66699 7.49984V12.4998C1.66699 16.6665 3.33366 18.3332 7.50033 18.3332Z",stroke:"#56CCF2",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.49967 8.33333C8.42015 8.33333 9.16634 7.58714 9.16634 6.66667C9.16634 5.74619 8.42015 5 7.49967 5C6.5792 5 5.83301 5.74619 5.83301 6.66667C5.83301 7.58714 6.5792 8.33333 7.49967 8.33333Z",stroke:"#56CCF2",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M2.22461 15.7918L6.33294 13.0335C6.99128 12.5918 7.94128 12.6418 8.53294 13.1501L8.80794 13.3918C9.45794 13.9501 10.5079 13.9501 11.1579 13.3918L14.6246 10.4168C15.2746 9.85846 16.3246 9.85846 16.9746 10.4168L18.3329 11.5835",stroke:"#56CCF2",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})]})]}),e.jsx("div",{className:"scrollbar-hide flex w-full overflow-x-auto",children:e.jsx("div",{className:" mt-5 flex gap-2",children:j.map((t,i)=>e.jsxs("div",{className:" relative w-[120px]",children:[e.jsx("img",{className:"h-[127px] w-[120px] rounded-[20px]",src:t}),e.jsx("div",{className:" flex items-center justify-center ",children:e.jsx("button",{onClick:()=>O(i),children:e.jsxs("svg",{width:"25",height:"25",viewBox:"0 0 25 25",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M21.875 6.22933C18.4062 5.88558 14.9167 5.7085 11.4375 5.7085C9.375 5.7085 7.3125 5.81266 5.25 6.021L3.125 6.22933",stroke:"#FD5D5D",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M8.85449 5.17725L9.08366 3.81266C9.25033 2.82308 9.37533 2.0835 11.1357 2.0835H13.8649C15.6253 2.0835 15.7607 2.86475 15.917 3.82308L16.1462 5.17725",stroke:"#FD5D5D",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M19.6351 9.521L18.958 20.0106C18.8434 21.646 18.7497 22.9168 15.8434 22.9168H9.15592C6.24967 22.9168 6.15592 21.646 6.04134 20.0106L5.36426 9.521",stroke:"#FD5D5D",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M10.7607 17.1875H14.2295",stroke:"#FD5D5D",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M9.89551 13.021H15.1038",stroke:"#FD5D5D",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})})})]},i))})}),e.jsx("div",{className:"mt-[58px]",children:e.jsx(re,{type:"submit",className:"uppercase",children:s("buttons.next")})})]}),e.jsx("div",{className:"mt-[26px] font-['Poppins'] text-base font-normal tracking-tight text-black",children:s("provider.cl_product.interested_parties_contact")})]})};export{Ue as default};
