import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{M as n,A as m}from"./index-cf5e6bc7.js";import{r as s,R as l}from"./vendor-f36d475e.js";import{u as p}from"./user-a875fff3.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./react-i18next-1e3e6bc5.js";const d=new n,A=({})=>{const{state:o,dispatch:a}=s.useContext(m),[t,r]=l.useState(o.userDetails);return s.useEffect(()=>{(async()=>{const i=await d.callRawAPI("/v3/api/custom/chumpchange/provider/data",{},"GET");i.error||(r(i.data),a({type:"UPDATE_PROFILE",payload:{first_name:i.data.first_name,last_name:i.data.last_name,photo:i.data.photo,user_data:{...i.data}}}))})()},[]),e.jsxs("div",{className:"relative mx-auto mt-14 flex h-[108px] justify-center gap-4 sm:gap-7",children:[e.jsxs("div",{className:"",children:[e.jsx("img",{className:" h-20 w-20 rounded-[80px] object-cover ",src:t!=null&&t.photo?t==null?void 0:t.photo:p}),t!=null&&t.averageRating?e.jsxs("div",{className:" mt-2 flex items-center justify-center gap-1",children:[e.jsx("div",{className:" font-['Poppins'] text-base font-light leading-normal text-[#2b2b2b]",children:t!=null&&t.averageRating?Number(t==null?void 0:t.averageRating).toFixed(2):0}),e.jsx("div",{className:" h-5 w-5",children:e.jsx("svg",{width:"17",height:"17",viewBox:"0 0 17 17",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5.1875 4.875L8 0.1875L10.8125 4.875L16.4375 6.75L13 10.5L13.9375 16.125L8 13.9375L1.75 15.8125L3.3125 10.8125L0.1875 6.75L5.1875 4.875Z",fill:"#56CCF2"})})})]}):""]}),e.jsxs("div",{className:"flex flex-col items-start justify-start gap-1.5 ",children:[e.jsxs("div",{className:" relative font-['Poppins'] text-[28px] font-semibold leading-7 text-black ",children:[t==null?void 0:t.first_name," ",t==null?void 0:t.last_name,t!=null&&t.admin_verify?e.jsx("span",{className:" absolute right-[-21px] top-[-7px] ",children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{width:"20",height:"20",rx:"10",fill:"#56CCF2"}),e.jsx("path",{d:"M5.3335 10L8.44095 13.5L14.6668 6.5",stroke:"white",strokeWidth:"3",strokeLinecap:"round",strokeLinejoin:"round"})]})}):""]}),e.jsxs("div",{className:" font-['Poppins'] text-sm font-medium text-[#8080a3]",children:["Chiripero #",t.id?t.id:localStorage.getItem("user_id")]}),e.jsx("div",{className:" text-center font-['Poppins'] text-sm font-medium text-[#8080a3]",children:t==null?void 0:t.operating_city})]})]})};export{A as default};
