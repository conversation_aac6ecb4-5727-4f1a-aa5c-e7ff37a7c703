import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as n,b as re}from"./vendor-4f06b3f4.js";import{M as ne,G as ie,A as oe,t as v,g as m,s as h}from"./index-06b5b6dd.js";import{o as ce}from"./yup-2324a46a.js";import{u as le}from"./react-hook-form-f3d72793.js";import{c as de,a as g}from"./yup-17027d7a.js";import{P as ue}from"./index-19801678.js";import{B as pe,R as me,A as he,a as ge,b as xe}from"./index.esm-1a4cea12.js";import{S as fe}from"./index-2d8231e7.js";import"./react-confirm-alert-525c3702.js";import"./moment-a9aaa855.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@hookform/resolvers-1aa18522.js";import"./react-icons-e5379072.js";let S=new ne;const N=[{header:"Customer",accessor:"userEmail"},{header:"Plan",accessor:"planName"},{header:"Starts",accessor:"currentPeriodStart",type:"timestamp"},{header:"Ends",accessor:"currentPeriodEnd",type:"timestamp"},{header:"type",accessor:"planType",mapping:{recurring:"Recurring",life_time:"Lifetime"}},{header:"Usage Type",accessor:"isMetered",mapping:{0:"Upfront",1:"Metered"}},{header:"Price",accessor:"planAmount",type:"currency"},{header:"Has Trial",accessor:"trialDays"},{header:"Status",accessor:"status"},{header:"Action",accessor:""}],He=()=>{const{dispatch:l}=n.useContext(ie),{dispatch:x}=n.useContext(oe);n.useState("");const[$,q]=n.useState([]),[o,C]=n.useState(10),[P,z]=n.useState(0),[ye,B]=n.useState(0),[d,I]=n.useState(0),[M,U]=n.useState(!1),[G,H]=n.useState(!1),[R,k]=n.useState(!1),[E,_]=n.useState(!1),[i,f]=n.useState([]),[T,y]=n.useState([]),[K,V]=n.useState(""),[A,J]=n.useState("eq"),[Q,D]=n.useState(!1);re();const b=n.useRef(null),W=de({customer_email:g(),plan_name:g(),sub_status:g(),plan_type:g()}),{register:be,handleSubmit:X,formState:{errors:we}}=le({resolver:ce(W)}),F=(t,a,s)=>{const r=a==="eq"&&isNaN(s)?`"${s}"`:s,u=`${t},${a},${r}`;y(w=>[...w.filter(p=>!p.includes(t)),u]),V(s)};function Y(t){(async function(){C(t),await c(1,t)})()}function Z(){(async function(){await c(d-1>1?d-1:1,o)})()}function ee(){(async function(){await c(d+1<=P?d+1:1,o)})()}async function c(t,a,s){D(!0);try{const r=await S.getStripeSubscriptions({page:t,limit:a},`filter=${s.toString()}`),{list:u,total:w,limit:O,num_pages:p,page:j}=r;q(u),C(+O),z(+p),I(+j),B(+w),U(+j>1),H(+j+1<=+p)}catch(r){console.log("ERROR",r),v(x,r.message)}D(!1)}const te=t=>{const a=m(t.customer_email),s=m(t.plan_name),r=m(t.sub_status),u=m(t.plan_type);c(1,o,{customer_email:a,plan_name:s,sub_status:r,plan_type:u})},se=async t=>{console.log(t);try{const a=await S.adminCancelStripeSubscription(t,{});h(l,a.message,3e3),c(1,o)}catch(a){console.log("ERROR",a),h(l,a.message),v(x,a.message)}},ae=async(t,a)=>{console.log(t);try{const s=await S.adminCreateUsageCharge(t,a);h(l,s.message,3e3),c(1,o)}catch(s){console.log("ERROR",s),h(l,s.message),v(x,s.message)}};n.useEffect(()=>{l({type:"SETPATH",payload:{path:"subscriptions"}});const a=setTimeout(async()=>{await c(1,o,T)},700);return()=>{clearTimeout(a)}},[K,T,A]);const L=t=>{b.current&&!b.current.contains(t.target)&&k(!1)};return n.useEffect(()=>(document.addEventListener("mousedown",L),()=>{document.removeEventListener("mousedown",L)}),[]),e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"flex items-center justify-between py-3",children:e.jsx("form",{className:"relative rounded bg-white",onSubmit:X(te),children:e.jsxs("div",{className:"flex items-center gap-4 text-gray-700 text-nowrap",children:[e.jsxs("div",{className:"relative",ref:b,children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-1",onClick:()=>k(!R),children:[e.jsx(pe,{}),e.jsx("span",{children:"Filters"}),i.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:i.length})]}),R&&e.jsx("div",{className:"absolute z-10 mt-4 w-[500px] min-w-[90%] top-fill left-0 filter-form-holder bg-white border border-gray-200 rounded-md shadow-lg",children:e.jsxs("div",{className:"p-4",children:[i==null?void 0:i.map((t,a)=>e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:" mb-3 w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",children:t}),e.jsxs("select",{className:"mb-3 w-[40%] rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:s=>{J(s.target.value)},children:[e.jsx("option",{value:"eq",selected:!0,children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),e.jsx("input",{type:"text",placeholder:"Enter value...",className:" mb-3 w-1/3 rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:s=>F(t,A,s.target.value)}),e.jsx("div",{className:"w-1/12 mt-[-10px]",children:e.jsx(me,{className:" cursor-pointer text-xl",onClick:()=>{f(s=>s.filter(r=>r!==t)),y(s=>s.filter(r=>!r.includes(t)))}})})]},a)),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out ",onClick:()=>{_(!E)},children:[e.jsx(he,{}),"Add filter"]}),E&&e.jsx("div",{className:"absolute top-11 z-10 bg-white px-5 py-3 text-gray-600 shadow-md",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:N.slice(0,-1).map(t=>e.jsx("li",{className:`${i.includes(t.header)?"cursor-not-allowed text-gray-400":"cursor-pointer"}`,onClick:()=>{i.includes(t.header)||f(a=>[...a,t.header]),_(!1)},children:t.header},t.header))})}),i.length>0&&e.jsx("div",{onClick:()=>{f([]),y([])},className:"inline-block cursor-pointer  rounded px-6  py-2.5 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out",children:"Clear all filter"})]})]})})]}),e.jsxs("div",{className:" flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-2 py-1 focus-within:border-gray-400",children:[e.jsx(ge,{className:"text-xl text-gray-200"}),e.jsx("input",{type:"text",placeholder:"search...",className:"border-none p-0 placeholder:text-left  focus:outline-none",style:{boxShadow:"0 0 transparent"},onInput:t=>{var a;return F("name","cs",(a=t.target)==null?void 0:a.value)}}),e.jsx(xe,{className:"text-lg text-gray-200"})]})]})})}),Q?e.jsx(fe,{}):e.jsx("div",{className:"overflow-x-auto border-b border-gray-200 shadow ",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:N.map((t,a)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},a))})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:$.map((t,a)=>e.jsx("tr",{children:N.map((s,r)=>{if(s.accessor=="")return e.jsxs("td",{className:"whitespace-nowrap px-6 py-4",children:[t.status!=="canceled"?e.jsx("button",{onClick:()=>se(t.subId),type:"button",className:"mx-1 inline-block rounded-full bg-red-600 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-red-700 hover:shadow-lg focus:bg-red-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-red-800 active:shadow-lg",children:"Cancel"}):"",t.isMetered===1?e.jsx("button",{onClick:()=>ae(t.subId,1),type:"button",className:"mx-1 inline-block rounded-full bg-red-600 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-red-700 hover:shadow-lg focus:bg-red-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-red-800 active:shadow-lg",children:"Create Charge"}):""]},r);if(s.mapping)return e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s.mapping[t[s.accessor]]},r);if(t.planType==="recurring"&&s.type==="timestamp")return e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:new Date(t[s.accessor]*1e3).toLocaleDateString("en-US",{dateStyle:"medium"})},r);if(t.planType==="lifetime"&&s.type==="timestamp"){if(s.accessor==="currentPeriodStart")return e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:new Date(t.createdAt*1e3).toLocaleDateString("en-US",{dateStyle:"medium"})},r);if(s.accessor==="currentPeriodEnd")return e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:"Infinity"},r)}else if(s.type=="currency")return e.jsxs("td",{className:"whitespace-nowrap px-6 py-4",children:["$",+(t[s.accessor]??0)]},r);return e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[s.accessor]},r)})},a))})]})}),e.jsx(ue,{currentPage:d,pageCount:P,pageSize:o,canPreviousPage:M,canNextPage:G,updatePageSize:Y,previousPage:Z,nextPage:ee})]})};export{He as default};
