import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as a,b as oe}from"./vendor-4f06b3f4.js";import{u as le}from"./react-hook-form-f3d72793.js";import{o as de}from"./yup-2324a46a.js";import{c as ce,a as c}from"./yup-17027d7a.js";import{M as pe,G as me,A as ue,t as xe,g as he}from"./index-06b5b6dd.js";import{M as R}from"./index-d97c616d.js";import{B as ge,R as fe,A as je}from"./index.esm-1a4cea12.js";import ye from"./EditAdminCmsPage-41857e5d.js";import be from"./AddAdminCmsPage-fc51bbc2.js";import{A as ve}from"./AddButton-39426f55.js";import we from"./Skeleton-4c05cda0.js";import{P as Ne}from"./index-19801678.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./moment-a9aaa855.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./react-icons-e5379072.js";import"./index-3e43e6ef.js";import"./AddButton.module-98aac587.js";import"./react-loading-skeleton-e20104c1.js";let T=new pe;const p=[{header:"Page",accessor:"page",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Identifier",accessor:"content_key",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Content Type",accessor:"content_type",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],it=()=>{const{dispatch:$}=a.useContext(me),{dispatch:M}=a.useContext(ue),[N,I]=a.useState([]),[o,S]=a.useState(10),[L,O]=a.useState(0),[Se,q]=a.useState(0),[m,z]=a.useState(0),[B,_]=a.useState(!1),[G,H]=a.useState(!1),[C,A]=a.useState(!1),[F,E]=a.useState(!1),[l,u]=a.useState([]),[K,x]=a.useState([]),[U,Y]=a.useState("eq"),[h,g]=a.useState(!0),[J,f]=a.useState(!1),[Q,j]=a.useState(!1),[V,W]=a.useState();oe();const y=a.useRef(null),X=ce({id:c(),email:c(),role:c(),status:c()}),{register:Ce,handleSubmit:Z,formState:{errors:Ae}}=le({resolver:de(X)});function ee(){d(m-1,o)}function te(){d(m+1,o)}const se=(t,i,s)=>{const r=i==="eq"&&isNaN(s)?`${s}`:s,n=`${t},${i},${r}`;x(b=>[...b.filter(v=>!v.includes(t)),n])},ae=()=>{d(0,o,{},K)},ie=t=>{d(0,o,{},t)};async function d(t,i,s={},r=[]){g(!0);try{T.setTable("cms");const n=await T.callRestAPI({payload:{...s},page:t,limit:i,filter:r},"PAGINATE");n&&g(!1);const{list:b,total:k,limit:v,num_pages:D,page:w}=n;I(b),S(v),O(D),z(w),q(k),_(w>1),H(w+1<=D)}catch(n){g(!1),console.log("ERROR",n),xe(M,n.message)}}const re=t=>{const i=p.filter(s=>s.accessor).map(s=>{const r=he(t[s.accessor]);return r?`${s.accessor},cs,${r}`:null}).filter(Boolean);d(0,o,{},i)};a.useEffect(()=>{$({type:"SETPATH",payload:{path:"cms"}});const i=setTimeout(async()=>{await d(1,o)},700);return()=>{clearTimeout(i)}},[]);const P=t=>{y.current&&!y.current.contains(t.target)&&A(!1)};a.useEffect(()=>(document.addEventListener("mousedown",P),()=>{document.removeEventListener("mousedown",P)}),[]);const ne=()=>{u([]),x([]),d(1,o)};return e.jsxs("div",{className:"px-8",children:[e.jsxs("div",{className:"flex items-center justify-between py-3",children:[e.jsx("form",{className:"relative rounded bg-white",onSubmit:Z(re),children:e.jsx("div",{className:"flex items-center gap-4 text-gray-700 text-nowrap",children:e.jsxs("div",{className:"relative",ref:y,children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-1",onClick:()=>A(!C),children:[e.jsx(ge,{}),e.jsx("span",{children:"Filters"}),l.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:l.length})]}),C&&e.jsx("div",{className:"absolute z-10 mt-4 w-[500px] min-w-[90%] top-fill left-0 filter-form-holder bg-white border border-gray-200 rounded-md shadow-lg",children:e.jsxs("div",{className:"p-4",children:[l==null?void 0:l.map((t,i)=>e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:" mb-3 w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",children:t}),e.jsxs("select",{className:"mb-3 w-[40%] rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:s=>{Y(s.target.value)},children:[e.jsx("option",{value:"eq",selected:!0,children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),e.jsx("input",{type:"text",placeholder:"Enter value...",className:" mb-3 w-1/3 rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:s=>se(t,U,s.target.value)}),e.jsx("div",{className:"w-1/12 mt-[-10px]",children:e.jsx(fe,{className:" cursor-pointer text-xl",onClick:()=>{u(s=>s.filter(r=>r!==t)),x(s=>{const r=s.filter(n=>!n.includes(t));return ie(r),r})}})})]},i)),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out ",onClick:()=>{E(!F)},children:[e.jsx(je,{}),"Add filter"]}),F&&e.jsx("div",{className:"absolute top-11 z-10 bg-white px-5 py-3 text-gray-600 shadow-md",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:p.slice(0,-1).map(t=>e.jsx("li",{className:`${l.includes(t.accessor)?"cursor-not-allowed text-gray-400":"cursor-pointer"}`,onClick:()=>{l.includes(t.header)||u(i=>[...i,t.accessor]),E(!1)},children:t.header},t.header))})}),l.length>0&&e.jsx("div",{onClick:ne,className:"inline-block cursor-pointer  rounded px-6  py-2.5 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out",children:"Clear all filter"})]}),e.jsx("button",{type:"button",onClick:ae,className:"mt-4 inline-block cursor-pointer rounded bg-blue-500 px-6 py-2.5 font-medium leading-tight text-white transition duration-150 ease-in-out",children:"Apply Filters"})]})})]})})}),e.jsx(ve,{onClick:()=>j(!0)})]}),h?e.jsx(we,{}):e.jsxs("div",{className:"overflow-x-auto border-b border-gray-200 shadow ",children:[e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:p.map((t,i)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},i))})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:N.map((t,i)=>e.jsx("tr",{children:p.map((s,r)=>s.accessor==""?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsxs("button",{className:"text-[#4F46E5]",onClick:()=>{W(t.id),f(!0)},children:[" ","Edit"]})},r):s.mapping&&s.accessor==="status"?e.jsx("td",{className:"px-6 py-5 whitespace-nowrap inline-block text-sm",children:t[s.accessor]===1?e.jsx("span",{className:"bg-[#D1FAE5] rounded-md py-1 px-3 text-[#065F46]",children:s.mapping[t[s.accessor]]}):e.jsx("span",{className:"bg-[#F4F4F4] rounded-md py-1 px-3 text-[#393939]",children:s.mapping[t[s.accessor]]})},r):s.mapping?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s.mapping[t[s.accessor]]},r):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[s.accessor]},r))},i))})]}),h&&e.jsx(e.Fragment,{children:e.jsx("p",{className:" px-10 py-3 text-xl capitalize ",children:"Loading..."})}),!h&&N.length===0&&e.jsx(e.Fragment,{children:e.jsx("p",{className:" px-10 py-3 text-xl capitalize ",children:"You Don't have any Data"})})]}),e.jsx(Ne,{currentPage:m,pageCount:L,pageSize:o,canPreviousPage:B,canNextPage:G,updatePageSize:t=>{S(t),d(1,t)},previousPage:ee,nextPage:te}),e.jsx(R,{isModalActive:Q,closeModalFn:()=>j(!1),children:e.jsx(be,{setSidebar:j})}),e.jsx(R,{isModalActive:J,closeModalFn:()=>f(!1),children:e.jsx(ye,{activeId:V,setSidebar:f})})]})};export{it as default};
