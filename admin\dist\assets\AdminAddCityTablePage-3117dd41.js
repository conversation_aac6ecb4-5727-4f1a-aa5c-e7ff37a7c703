import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as i,b as N}from"./vendor-4f06b3f4.js";import{u as j}from"./react-hook-form-f3d72793.js";import{o as A}from"./yup-2324a46a.js";import{c as q,a as r}from"./yup-17027d7a.js";import{G as w,A as E,M as L,s as C,t as k}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{M as m}from"./MkdInput-ff3aa862.js";import{I}from"./InteractiveButton-8f7d74ee.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./MoonLoader-16bed42a.js";const te=({setSidebar:x})=>{const{dispatch:l}=i.useContext(w),h=q({name:r().required("Name is required"),latitude:r().required("Latitude is required"),longitude:r().required("Longitude is required"),status:r().required("Status is required")}).required(),{dispatch:y}=i.useContext(E),[u,d]=i.useState(!1),b=N(),{register:a,handleSubmit:S,setError:p,formState:{errors:s}}=j({resolver:A(h)}),v=async o=>{let c=new L;d(!0);try{c.setTable("city");const e=await c.callRestAPI({name:o.name,lattitude:o.latitude,longitude:o.longitude,status:o.status},"POST");if(!e.error)C(l,"City Added"),b("/admin/city"),x(!1),l({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(e.validation){const f=Object.keys(e.validation);for(let n=0;n<f.length;n++){const g=f[n];p(g,{type:"manual",message:e.validation[g]})}}d(!1)}catch(e){d(!1),console.log("Error",e),p("name",{type:"manual",message:e.message}),k(y,e.message)}};return i.useEffect(()=>{l({type:"SETPATH",payload:{path:"city"}})},[]),t.jsxs("div",{className:"mx-auto rounded p-5 shadow-md",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Add City"}),t.jsxs("form",{className:"w-full max-w-lg",onSubmit:S(v),children:[t.jsx(m,{type:"text",page:"add",name:"name",errors:s,label:"Name",placeholder:"Name",register:a,className:""}),t.jsx(m,{type:"text",page:"add",name:"latitude",errors:s,label:"Latitude",placeholder:"Latitude",register:a,className:""}),t.jsx(m,{type:"text",page:"add",name:"longitude",errors:s,label:"Longitude",placeholder:"Longitude",register:a,className:""}),t.jsx(m,{type:"dropdown",page:"add",name:"status",errors:s,label:"Status",placeholder:"Status",register:a,className:"",options:[{name:"Active",value:"active"},{name:"Inactive",value:"inactive"}]}),t.jsx(I,{type:"submit",loading:u,disabled:u,className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",children:"Submit"})]})]})};export{te as default};
