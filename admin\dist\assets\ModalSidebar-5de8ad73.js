import{j as r}from"./@react-google-maps/api-ac2f9d6f.js";import{r as n}from"./vendor-4f06b3f4.js";const f=({customMinWidthInTw:a="min-w-[30%]",isModalActive:t=!1,closeModalFn:o=()=>{},children:d})=>{const e=n.useRef();return n.useEffect(()=>{const s=l=>{e.current&&!e.current.contains(l.target)&&o()};return document.addEventListener("mousedown",s),()=>{document.removeEventListener("mousedown",s)}},[]),n.useEffect(()=>{t&&e&&e.current.focus()},[t]),r.jsx("div",{id:"modal","aria-hidden":"false",autoFocus:!0,className:`transition-all ${t?"-translate-x-0 flex":"translate-x-full hidden"} z-50 bg-[#292828d2] overflow-x-hidden overflow-y-auto fixed h-screen w-screen md:h-full left-0 right-0 md:inset-0 justify-center items-center`,children:r.jsx("div",{className:"relative overflow-hidden !max-w-[250px] min-h-[100vh] px-4 h-full",children:r.jsx("div",{ref:e,autoFocus:!0,className:`${a} fixed right-0 z-[9999] bg-white py-1 rounded-md border min-h-[100vh]  overflow-y-auto block items-center text-center shadow-xl transition-all ${t?"-translate-x-0":"translate-x-full hidden"}`,children:d})})})};export{f as default};
