import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as r,b as T,r as f,h as L}from"./vendor-4f06b3f4.js";import{u as D}from"./react-hook-form-f3d72793.js";import{o as F}from"./yup-2324a46a.js";import{c as _,a as j}from"./yup-17027d7a.js";import{M as C,A as M,G,t as O,s as $}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as B}from"./MkdInput-ff3aa862.js";import{I as H}from"./InteractiveButton-8f7d74ee.js";import{S as U}from"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";let m=new C;const ye=i=>{var y,S;const{dispatch:w}=r.useContext(M),N=_({name:j(),logo:j()}).required(),{dispatch:d}=r.useContext(G),[g,q]=r.useState({}),[x,c]=r.useState(!1),[E,u]=r.useState(!1),I=T(),[K,k]=f.useState(""),[V,A]=f.useState(""),{register:h,handleSubmit:P,setError:b,setValue:v,formState:{errors:p}}=D({resolver:F(N)}),a=L();f.useEffect(function(){(async function(){try{u(!0),m.setTable("provider_service");const e=await m.callRestAPI({id:i.activeId?i.activeId:Number(a==null?void 0:a.id)},"GET");e.error||(v("name",e.model.name),v("logo",e.model.logo),k(e.model.name),A(e.model.logo),u(!1))}catch(e){u(!1),console.log("error",e),O(w,e.message)}})()},[]);const R=async e=>{c(!0);try{m.setTable("provider_service");for(let l in g){let s=new FormData;s.append("file",g[l].file);let n=await m.uploadImage(s);e[l]=n.url}const o=await m.callRestAPI({id:i.activeId?i.activeId:Number(a==null?void 0:a.id),name:e.name,logo:e.logo},"PUT");if(!o.error)$(d,"Updated"),I("/admin/provider_service"),d({type:"REFRESH_DATA",payload:{refreshData:!0}}),i.setSidebar(!1);else if(o.validation){const l=Object.keys(o.validation);for(let s=0;s<l.length;s++){const n=l[s];b(n,{type:"manual",message:o.validation[n]})}}c(!1)}catch(o){c(!1),console.log("Error",o),b("name",{type:"manual",message:o.message})}};return r.useEffect(()=>{d({type:"SETPATH",payload:{path:"provider_service"}})},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Provider Service"}),E?t.jsx(U,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:P(R),children:[t.jsx(B,{type:"text",page:"edit",name:"name",errors:p,label:"Name",placeholder:"Name",register:h,className:""}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"logo",children:"Logo"}),t.jsx("textarea",{placeholder:"Logo",...h("logo"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(y=p.logo)!=null&&y.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(S=p.logo)==null?void 0:S.message})]}),t.jsx(H,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:x,disable:x,children:"Submit"})]})]})};export{ye as default};
