import{j as m}from"./@react-google-maps/api-ee55a349.js";import{r as o,R as f}from"./vendor-b16525a8.js";import{_}from"./qr-scanner-cf010ec4.js";const d=o.lazy(()=>_(()=>import("./Skeleton-94e6f3dc.js"),["assets/Skeleton-94e6f3dc.js","assets/@react-google-maps/api-ee55a349.js","assets/vendor-b16525a8.js","assets/react-loading-skeleton-5d7759a2.js"])),x=({children:a,counts:n=[1],count:p=1,circle:c=!1})=>{var s,e,t,l;const r=f.Children.toArray(a).filter(Boolean),i=(e=(s=r.filter(Boolean)[0])==null?void 0:s.props)!=null&&e.className?(l=(t=r[0])==null?void 0:t.props)==null?void 0:l.className:"";return m.jsx(o.Suspense,{fallback:m.jsx(d,{counts:n,count:p,className:i,circle:c}),children:a})},j=o.memo(x);export{j as default};
