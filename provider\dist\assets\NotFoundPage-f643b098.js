import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{R as o,d as l}from"./vendor-f36d475e.js";import{L as i}from"./index-65bc3378.js";import{B as r,a as c}from"./index-895fa99b.js";import{t}from"./i18next-7389dd8c.js";import"./qr-scanner-cf010ec4.js";const p=()=>{const[s,a]=o.useState(!0),n=l();return console.log(s),o.useEffect(()=>{setTimeout(()=>{a(!1)},5e3)},[]),e.jsx(e.Fragment,{children:s?e.jsx(i,{}):e.jsxs("div",{className:"p-5",children:[e.jsxs("div",{className:"relative flex w-full items-center justify-between ",children:[e.jsx("div",{className:" ",children:e.jsx(r,{})}),e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:t("not_found.title")}),e.jsx("div",{className:" "})]}),e.jsxs("div",{className:"flex h-screen w-full flex-col items-center justify-center bg-gradient-to-br from-[#FCF3F9] to-[#EAF8FB] text-gray-700",children:[e.jsx("h1",{className:"mb-4 text-2xl",children:t("not_found.sub_title")}),e.jsx("p",{className:"mb-8 text-xl",children:t("not_found.decs")}),e.jsx(c,{onClick:()=>n("/provider/login"),children:t("not_found.btn")})]})]})})};export{p as default};
