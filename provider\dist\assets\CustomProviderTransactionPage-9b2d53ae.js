import{j as t}from"./@react-google-maps/api-afbf18d5.js";import{h as S,r,R as Y}from"./vendor-f36d475e.js";import"./index-5deedf4a.js";import{M as E,A as _,G as M}from"./index-cf5e6bc7.js";import{P as C}from"./index-55e4d382.js";import{T as u}from"./index-010bc024.js";import{B as R}from"./index-895fa99b.js";import{S as D}from"./index-65bc3378.js";import{h as f}from"./moment-a9aaa855.js";import{t as n}from"./i18next-7389dd8c.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-2487dba8.js";import"./@react-pdf-viewer/core-9d395990.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-1e3e6bc5.js";const h=new E,ot=()=>{var x,p;S(),r.useContext(_);const{state:G,dispatch:v}=r.useContext(M),[g,l]=r.useState(!1),[o,d]=r.useState("active"),[a,j]=r.useState([]),[y,b]=r.useState(0),[m,w]=r.useState([]),[k,N]=r.useState(0),A=async()=>{try{const s=await h.callRawAPI("/v3/api/custom/chumpchange/provider/transactions/monthly",{},"GET");if(!s.error){j(s.data);const c=s.data.reduce((i,e)=>i+(e!=null&&e.amount?e==null?void 0:e.amount:0),0);b(c)}}catch(s){console.error("Error fetching active tasks:",s)}},P=async()=>{try{const s=await h.callRawAPI("/v3/api/custom/chumpchange/provider/transactions/ytd",{},"GET");if(!s.error){w(s.data);const c=s.data.reduce((i,e)=>i+(e!=null&&e.amount?e==null?void 0:e.amount:0),0);N(c)}}catch(s){return console.error("Error fetching expired tasks:",s),[]}};return Y.useEffect(()=>{v({type:"SETPATH",payload:{path:"earnings"}}),(async()=>(l(!0),await A(),await P(),l(!1)))()},[]),t.jsxs(C,{className:"",children:[t.jsxs("div",{className:"relative mt-4 flex w-full items-center justify-center px-5 pt-1 ",children:[t.jsx("div",{className:" absolute left-5 top-1 ",children:t.jsx(R,{})}),t.jsx("div",{className:"",children:t.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:n("provider.earnings.t_title")})})]}),t.jsxs("div",{className:" mt-14 px-5 ",children:[t.jsxs("div",{className:" grid h-12 w-full grid-cols-2 items-center rounded-2xl bg-[#8181a4]/20 px-1",children:[t.jsx("button",{className:`flex h-10 items-center justify-center rounded-2xl font-['Poppins'] text-sm font-medium ${o==="active"?"bg-black text-white":"text-black"}`,onClick:()=>d("active"),children:n("provider.earnings.this_month")}),t.jsx("button",{className:`flex h-10 items-center justify-center rounded-2xl font-['Poppins'] text-sm font-medium ${o==="expired"?"bg-black text-white":"text-black"}`,onClick:()=>d("expired"),children:n("provider.earnings.year_to_date")})]}),t.jsxs("div",{className:" mt-10 flex flex-col items-center justify-center",children:[t.jsxs("div",{className:"text-center font-['Poppins'] text-[42px] font-medium text-[#56ccf2]",children:["$",o==="active"?y:k]}),t.jsxs("div",{className:"text-center font-['Poppins'] text-base font-normal text-black",children:[console.log((a==null?void 0:a.length)>0&&o==="active"),(a==null?void 0:a.length)>0&&(o==="active"?f((x=a[0])==null?void 0:x.create_at).format("MMMM YYYY"):n("provider.earnings.total_amount")+" "+f((p=m[0])==null?void 0:p.create_at).format("YYYY"))]})]})]}),t.jsxs("div",{className:" relative left-0 mt-6 flex w-full flex-1 flex-col items-center justify-start overflow-hidden rounded-tl-[32px] rounded-tr-[32px] bg-white",children:[t.jsx("div",{className:" flex h-7 w-full max-w-[335px] items-center justify-center border-b bg-white ",children:t.jsx("div",{className:" h-1 w-[50px] rounded bg-[#f2f2f7]"})}),g?t.jsx("div",{className:"flex h-full w-full items-center justify-center pt-10 ",children:t.jsx(D,{})}):o==="active"?t.jsx(u,{taskData:a}):t.jsx(u,{taskData:m})]})," "]})};export{ot as default};
