import{j as t}from"./@react-google-maps/api-afbf18d5.js";import{R as o,L as n}from"./vendor-f36d475e.js";import{G as r}from"./index-cf5e6bc7.js";import{u as d}from"./react-i18next-1e3e6bc5.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";const l=[{icon:t.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M9.02 2.84016L3.63 7.04016C2.73 7.74016 2 9.23016 2 10.3602V17.7702C2 20.0902 3.89 21.9902 6.21 21.9902H17.79C20.11 21.9902 22 20.0902 22 17.7802V10.5002C22 9.29016 21.19 7.74016 20.2 7.05016L14.02 2.72016C12.62 1.74016 10.37 1.79016 9.02 2.84016Z",stroke:"#A3A9AE",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),iconActive:t.jsx("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M12.027 3.78672L4.84033 9.38672C3.64033 10.3201 2.66699 12.3067 2.66699 13.8134V23.6934C2.66699 26.7867 5.18699 29.3201 8.28033 29.3201H23.7203C26.8137 29.3201 29.3337 26.7867 29.3337 23.7067V14.0001C29.3337 12.3867 28.2537 10.3201 26.9337 9.40005L18.6937 3.62672C16.827 2.32005 13.827 2.38672 12.027 3.78672Z",stroke:"#56CCF2",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),title:"Home",link:"/provider/home",value:"providers",tran:"home"},{icon:t.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M7.01408 16.003H11.0141M7.01408 11.005H15.0141M10.0141 22H11.0141M7.51008 22C6.34008 21.975 5.54008 21.9 4.76408 21.675C3.71408 21.4 2.94908 20.475 2.72408 19.176C2.43908 17.952 2.53908 14.929 2.51408 11.33C2.51608 8.53904 2.38908 5.85804 2.83908 4.33404C3.11408 3.83404 3.33908 2.01004 7.06408 2.03404C7.68908 2.01004 14.3141 1.96004 15.3641 2.06004C18.8641 2.13504 19.2141 3.93404 19.4141 5.63304C19.5411 6.88304 19.5141 8.88104 19.5141 11.005M7.01408 2.01004C7.31408 3.63404 7.28908 4.68404 8.38908 4.93304C8.86408 5.00804 9.94908 5.00604 11.1141 5.00804C12.1541 5.01004 13.2141 5.02004 13.6891 4.90804C14.8641 4.63404 14.7391 3.18404 15.0391 2.01004M18.2791 14.379C16.9041 15.779 14.2561 18.277 14.2561 18.452C14.0431 18.749 13.8561 19.352 13.7321 20.202C13.5751 20.989 13.3871 21.675 13.6071 21.875C13.8271 22.075 14.6541 21.907 15.5301 21.725C16.2301 21.65 16.8801 21.4 17.2041 21.151C17.6791 20.731 20.9021 17.477 21.2761 17.053C21.5511 16.678 21.5761 15.978 21.3361 15.553C21.2021 15.253 20.3521 14.453 20.0771 14.229C19.8006 14.0585 19.4767 13.981 19.1529 14.008C18.8291 14.035 18.5235 14.1651 18.2791 14.379Z",stroke:"#A3A9AE",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),iconActive:t.jsx("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M9.35211 21.3373H14.6854M9.35211 14.6733H20.0188M13.3521 29.3333H14.6854M10.0134 29.3333C8.45344 29.3 7.38678 29.2 6.35211 28.9C4.95211 28.5333 3.93211 27.3 3.63211 25.568C3.25211 23.936 3.38544 19.9053 3.35211 15.1066C3.35478 11.3853 3.18544 7.81064 3.78544 5.77864C4.15211 5.11197 4.45211 2.67997 9.41878 2.71197C10.2521 2.67997 19.0854 2.6133 20.4854 2.74664C25.1521 2.84664 25.6188 5.2453 25.8854 7.51064C26.0548 9.1773 26.0188 11.8413 26.0188 14.6733M9.35211 2.67997C9.75211 4.8453 9.71878 6.2453 11.1854 6.5773C11.8188 6.6773 13.2654 6.67464 14.8188 6.6773C16.2054 6.67997 17.6188 6.6933 18.2521 6.54397C19.8188 6.17864 19.6521 4.2453 20.0521 2.67997M24.3721 19.172C22.5388 21.0386 19.0081 24.3693 19.0081 24.6026C18.7241 24.9986 18.4748 25.8026 18.3094 26.936C18.1001 27.9853 17.8494 28.9 18.1428 29.1666C18.4361 29.4333 19.5388 29.2093 20.7068 28.9666C21.6401 28.8666 22.5068 28.5333 22.9388 28.2013C23.5721 27.6413 27.8694 23.3026 28.3681 22.7373C28.7348 22.2373 28.7681 21.304 28.4481 20.7373C28.2694 20.3373 27.1361 19.2706 26.7694 18.972C26.4007 18.7446 25.9689 18.6413 25.5372 18.6773C25.1055 18.7133 24.6981 18.8867 24.3721 19.172Z",stroke:"#56CCF2",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),title:"My tasks",link:"/provider/my-tasks",value:"my-task",tran:"task"},{icon:t.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[t.jsx("path",{d:"M4.16989 15.3L8.69989 19.83C10.5599 21.69 13.5799 21.69 15.4499 19.83L19.8399 15.44C21.6999 13.58 21.6999 10.56 19.8399 8.69005L15.2999 4.17005C14.3499 3.22005 13.0399 2.71005 11.6999 2.78005L6.69989 3.02005C4.69989 3.11005 3.10989 4.70005 3.00989 6.69005L2.76989 11.69C2.70989 13.04 3.21989 14.35 4.16989 15.3Z",stroke:"#A3A9AE",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),t.jsx("path",{d:"M9.5 12C10.8807 12 12 10.8807 12 9.5C12 8.11929 10.8807 7 9.5 7C8.11929 7 7 8.11929 7 9.5C7 10.8807 8.11929 12 9.5 12Z",stroke:"#A3A9AE",strokeWidth:"2",strokeLinecap:"round"})]}),iconActive:t.jsxs("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[t.jsx("path",{d:"M5.55953 20.4001L11.5995 26.4401C14.0795 28.9201 18.1062 28.9201 20.5995 26.4401L26.4529 20.5867C28.9329 18.1067 28.9329 14.0801 26.4529 11.5867L20.3995 5.56007C19.1329 4.2934 17.3862 3.6134 15.5995 3.70673L8.93286 4.02673C6.2662 4.14673 4.1462 6.26673 4.01286 8.92007L3.69286 15.5867C3.61286 17.3867 4.29286 19.1334 5.55953 20.4001Z",stroke:"#56CCF2",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),t.jsx("path",{d:"M12.6663 16.0002C14.5073 16.0002 15.9997 14.5078 15.9997 12.6668C15.9997 10.8259 14.5073 9.3335 12.6663 9.3335C10.8254 9.3335 9.33301 10.8259 9.33301 12.6668C9.33301 14.5078 10.8254 16.0002 12.6663 16.0002Z",stroke:"#56CCF2",strokeWidth:"2",strokeLinecap:"round"})]}),title:"Listings",link:"/provider/my-listings",value:"listings",tran:"listing"},{icon:t.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[t.jsxs("mask",{id:"mask0_7217_7718",maskUnits:"userSpaceOnUse",x:"2",y:"0",width:"20",height:"24",children:[t.jsx("path",{d:"M13.8699 3.19994C13.5599 3.10994 13.2399 3.03994 12.9099 2.99994C11.9499 2.87994 11.0299 2.94994 10.1699 3.19994C10.4599 2.45994 11.1799 1.93994 12.0199 1.93994C12.8599 1.93994 13.5799 2.45994 13.8699 3.19994Z",stroke:"#9D87D5",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),t.jsx("path",{d:"M15 20C15 21.65 13.67 22.06 12.02 22.06C11.0482 22.06 9 21.1554 9 20",stroke:"#9D87D5",strokeWidth:"3",strokeLinecap:"round",strokeLinejoin:"round"}),t.jsx("path",{d:"M12.0196 2.91016C8.7096 2.91016 6.0196 5.60016 6.0196 8.91016V11.8002C6.0196 12.4102 5.7596 13.3402 5.4496 13.8602L4.2996 15.7702C3.5896 16.9502 4.0796 18.2602 5.3796 18.7002C9.6896 20.1402 14.3396 20.1402 18.6496 18.7002C19.8596 18.3002 20.3896 16.8702 19.7296 15.7702L18.5796 13.8602C18.2796 13.3402 18.0196 12.4102 18.0196 11.8002V8.91016C18.0196 5.61016 15.3196 2.91016 12.0196 2.91016Z",stroke:"#9D87D5",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),t.jsx("g",{mask:"url(#mask0_7217_7718)",children:t.jsx("rect",{width:"24",height:"24",fill:"#A3A9AE"})})]}),iconActive:t.jsxs("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[t.jsxs("mask",{id:"mask0_7352_8003",maskUnits:"userSpaceOnUse",x:"4",y:"1",width:"24",height:"30",children:[t.jsx("path",{d:"M18.4934 4.26667C18.0801 4.14667 17.6534 4.05334 17.2134 4C15.9334 3.84 14.7067 3.93334 13.5601 4.26667C13.9467 3.28 14.9067 2.58667 16.0267 2.58667C17.1467 2.58667 18.1067 3.28 18.4934 4.26667Z",stroke:"#9D87D5",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),t.jsx("path",{d:"M20 26.6667C20 28.8667 18.2267 29.4134 16.0267 29.4134C14.7309 29.4134 12 28.2073 12 26.6667",stroke:"#9D87D5",strokeWidth:"3",strokeLinecap:"round",strokeLinejoin:"round"}),t.jsx("path",{d:"M16.0263 3.88013C11.613 3.88013 8.0263 7.46679 8.0263 11.8801V15.7335C8.0263 16.5468 7.67963 17.7868 7.2663 18.4801L5.73296 21.0268C4.7863 22.6001 5.43963 24.3468 7.17296 24.9335C12.9196 26.8535 19.1196 26.8535 24.8663 24.9335C26.4796 24.4001 27.1863 22.4935 26.3063 21.0268L24.773 18.4801C24.373 17.7868 24.0263 16.5468 24.0263 15.7335V11.8801C24.0263 7.48013 20.4263 3.88013 16.0263 3.88013Z",stroke:"#9D87D5",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),t.jsx("g",{mask:"url(#mask0_7352_8003)",children:t.jsx("rect",{width:"32",height:"32",fill:"#56CCF2"})})]}),title:"Notifications",link:"/provider/notifications",value:"notifications",tran:"notification"},{icon:t.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M4 3C3.20435 3 2.44129 3.31607 1.87868 3.87868C1.31607 4.44129 1 5.20435 1 6V19C1 19.7956 1.31607 20.5587 1.87868 21.1213C2.44129 21.6839 3.20435 22 4 22H17C17.7956 22 18.5587 21.6839 19.1213 21.1213C19.6839 20.5587 20 19.7956 20 19V17.23C20.63 16.66 21 15.85 21 15V10C21 9.15 20.63 8.34 20 7.77V6C20 5.20435 19.6839 4.44129 19.1213 3.87868C18.5587 3.31607 17.7956 3 17 3H4ZM4 4H17C17.5304 4 18.0391 4.21071 18.4142 4.58579C18.7893 4.96086 19 5.46957 19 6V7.17C18.68 7.06 18.34 7 18 7H12C11.2044 7 10.4413 7.31607 9.87868 7.87868C9.31607 8.44129 9 9.20435 9 10V15C9 15.7956 9.31607 16.5587 9.87868 17.1213C10.4413 17.6839 11.2044 18 12 18H18C18.34 18 18.68 17.94 19 17.83V19C19 19.5304 18.7893 20.0391 18.4142 20.4142C18.0391 20.7893 17.5304 21 17 21H4C3.46957 21 2.96086 20.7893 2.58579 20.4142C2.21071 20.0391 2 19.5304 2 19V6C2 5.46957 2.21071 4.96086 2.58579 4.58579C2.96086 4.21071 3.46957 4 4 4ZM12 8H18C18.5304 8 19.0391 8.21071 19.4142 8.58579C19.7893 8.96086 20 9.46957 20 10V15C20 15.5304 19.7893 16.0391 19.4142 16.4142C19.0391 16.7893 18.5304 17 18 17H12C11.4696 17 10.9609 16.7893 10.5858 16.4142C10.2107 16.0391 10 15.5304 10 15V10C10 9.46957 10.2107 8.96086 10.5858 8.58579C10.9609 8.21071 11.4696 8 12 8ZM14.5 10C13.837 10 13.2011 10.2634 12.7322 10.7322C12.2634 11.2011 12 11.837 12 12.5C12 13.163 12.2634 13.7989 12.7322 14.2678C13.2011 14.7366 13.837 15 14.5 15C15.163 15 15.7989 14.7366 16.2678 14.2678C16.7366 13.7989 17 13.163 17 12.5C17 11.837 16.7366 11.2011 16.2678 10.7322C15.7989 10.2634 15.163 10 14.5 10ZM14.5 11C14.8978 11 15.2794 11.158 15.5607 11.4393C15.842 11.7206 16 12.1022 16 12.5C16 12.8978 15.842 13.2794 15.5607 13.5607C15.2794 13.842 14.8978 14 14.5 14C14.1022 14 13.7206 13.842 13.4393 13.5607C13.158 13.2794 13 12.8978 13 12.5C13 12.1022 13.158 11.7206 13.4393 11.4393C13.7206 11.158 14.1022 11 14.5 11Z",fill:"#A3A9AE",stroke:"#A3A9AE"})}),iconActive:t.jsx("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M5.3335 4C4.27263 4 3.25521 4.42143 2.50507 5.17157C1.75492 5.92172 1.3335 6.93913 1.3335 8V25.3333C1.3335 26.3942 1.75492 27.4116 2.50507 28.1618C3.25521 28.9119 4.27263 29.3333 5.3335 29.3333H22.6668C23.7277 29.3333 24.7451 28.9119 25.4953 28.1618C26.2454 27.4116 26.6668 26.3942 26.6668 25.3333V22.9733C27.5068 22.2133 28.0002 21.1333 28.0002 20V13.3333C28.0002 12.2 27.5068 11.12 26.6668 10.36V8C26.6668 6.93913 26.2454 5.92172 25.4953 5.17157C24.7451 4.42143 23.7277 4 22.6668 4H5.3335ZM5.3335 5.33333H22.6668C23.3741 5.33333 24.0523 5.61428 24.5524 6.11438C25.0525 6.61448 25.3335 7.29276 25.3335 8V9.56C24.9068 9.41333 24.4535 9.33333 24.0002 9.33333H16.0002C14.9393 9.33333 13.9219 9.75476 13.1717 10.5049C12.4216 11.2551 12.0002 12.2725 12.0002 13.3333V20C12.0002 21.0609 12.4216 22.0783 13.1717 22.8284C13.9219 23.5786 14.9393 24 16.0002 24H24.0002C24.4535 24 24.9068 23.92 25.3335 23.7733V25.3333C25.3335 26.0406 25.0525 26.7189 24.5524 27.219C24.0523 27.719 23.3741 28 22.6668 28H5.3335C4.62625 28 3.94797 27.719 3.44788 27.219C2.94778 26.7189 2.66683 26.0406 2.66683 25.3333V8C2.66683 7.29276 2.94778 6.61448 3.44788 6.11438C3.94797 5.61428 4.62625 5.33333 5.3335 5.33333ZM16.0002 10.6667H24.0002C24.7074 10.6667 25.3857 10.9476 25.8858 11.4477C26.3859 11.9478 26.6668 12.6261 26.6668 13.3333V20C26.6668 20.7072 26.3859 21.3855 25.8858 21.8856C25.3857 22.3857 24.7074 22.6667 24.0002 22.6667H16.0002C15.2929 22.6667 14.6146 22.3857 14.1145 21.8856C13.6144 21.3855 13.3335 20.7072 13.3335 20V13.3333C13.3335 12.6261 13.6144 11.9478 14.1145 11.4477C14.6146 10.9476 15.2929 10.6667 16.0002 10.6667ZM19.3335 13.3333C18.4494 13.3333 17.6016 13.6845 16.9765 14.3096C16.3514 14.9348 16.0002 15.7826 16.0002 16.6667C16.0002 17.5507 16.3514 18.3986 16.9765 19.0237C17.6016 19.6488 18.4494 20 19.3335 20C20.2176 20 21.0654 19.6488 21.6905 19.0237C22.3156 18.3986 22.6668 17.5507 22.6668 16.6667C22.6668 15.7826 22.3156 14.9348 21.6905 14.3096C21.0654 13.6845 20.2176 13.3333 19.3335 13.3333ZM19.3335 14.6667C19.8639 14.6667 20.3726 14.8774 20.7477 15.2525C21.1228 15.6275 21.3335 16.1362 21.3335 16.6667C21.3335 17.1971 21.1228 17.7058 20.7477 18.0809C20.3726 18.456 19.8639 18.6667 19.3335 18.6667C18.8031 18.6667 18.2944 18.456 17.9193 18.0809C17.5442 17.7058 17.3335 17.1971 17.3335 16.6667C17.3335 16.1362 17.5442 15.6275 17.9193 15.2525C18.2944 14.8774 18.8031 14.6667 19.3335 14.6667Z",fill:"#56CCF2",stroke:"#56CCF2"})}),title:"Earnings",link:"/provider/earning-history",value:"earnings",tran:"earnings"}],h=()=>{const{state:{isOpen:s,path:e},dispatch:a}=o.useContext(r),{t:C}=d();return t.jsxs("div",{className:" fixed bottom-0 left-0 flex h-[84px] w-full flex-col items-center justify-center bg-white",children:[t.jsx("div",{className:"w-full px-5 ",children:t.jsx("div",{className:"h-px w-[full] bg-[#f2f2f7]"})}),t.jsx("div",{className:" grid w-full grid-cols-5 pt-1 ",children:l.map(i=>t.jsxs(n,{to:i.link,className:"flex flex-col items-center justify-center gap-1 duration-200 ",children:[t.jsx("div",{className:"",children:e==i.value?i.iconActive:i.icon}),t.jsx("div",{className:`text-center font-['Poppins'] text-[10px] ${e==i.value?"font-semibold text-black":"font-medium text-[#8080a3]"}`,children:C(`provider.navigation.${i.tran}`)})]}))})]})},W=({children:s,className:e=""})=>t.jsxs("div",{className:` mx-auto flex min-h-screen max-w-full flex-col justify-between pb-[84px] ${e}`,children:[s,t.jsx(h,{})]});export{W as default};
