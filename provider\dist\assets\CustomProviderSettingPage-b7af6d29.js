import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{r as s,h as b,d as _,R as S,L as W}from"./vendor-f36d475e.js";import"./index-5deedf4a.js";import{M as V,A as H,G as y,s as h}from"./index-cf5e6bc7.js";import{B as A,a as P}from"./index-895fa99b.js";import{M as d}from"./index-bf8d79cc.js";import{S as F}from"./index-65bc3378.js";import{t as o}from"./i18next-7389dd8c.js";import{_ as m}from"./qr-scanner-cf010ec4.js";import{u as B}from"./react-i18next-1e3e6bc5.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";s.lazy(()=>m(()=>import("./DropDownSelection-d1fecb01.js"),["assets/DropDownSelection-d1fecb01.js","assets/@react-google-maps/api-afbf18d5.js","assets/vendor-f36d475e.js"]));const I=s.lazy(()=>m(()=>import("./Translation-d36af343.js"),["assets/Translation-d36af343.js","assets/@react-google-maps/api-afbf18d5.js","assets/vendor-f36d475e.js","assets/i18next-7389dd8c.js"])),E=[{to:"/provider/edit-profile",icon:e.jsxs("svg",{width:"20",height:"21",viewBox:"0 0 20 21",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M11.0524 5.03249L4.21073 12.2742C3.9524 12.5492 3.7024 13.0908 3.6524 13.4658L3.34407 16.1658C3.23573 17.1408 3.93573 17.8075 4.9024 17.6408L7.58573 17.1825C7.96073 17.1158 8.48573 16.8408 8.74407 16.5575L15.5857 9.31582C16.7691 8.06582 17.3024 6.64082 15.4607 4.89916C13.6274 3.17416 12.2357 3.78249 11.0524 5.03249Z",stroke:"#56CCF2",strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M9.91113 6.24072C10.2695 8.54072 12.1361 10.2991 14.4528 10.5324",stroke:"#56CCF2",strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"})]}),text:"edit_profile"},{to:"/provider/account-activation",icon:e.jsxs("svg",{width:"20",height:"21",viewBox:"0 0 20 21",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M9.99984 18.8334C14.5832 18.8334 18.3332 15.0834 18.3332 10.5001C18.3332 5.91675 14.5832 2.16675 9.99984 2.16675C5.4165 2.16675 1.6665 5.91675 1.6665 10.5001C1.6665 15.0834 5.4165 18.8334 9.99984 18.8334Z",stroke:"#56CCF2",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M10 7.16675V11.3334",stroke:"#56CCF2",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M9.99561 13.8333H10.0031",stroke:"#56CCF2",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),text:"activate_account"},{to:"/provider/recharge-balance",icon:e.jsxs("svg",{width:"20",height:"21",viewBox:"0 0 20 21",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M9.99984 18.8334C14.5832 18.8334 18.3332 15.0834 18.3332 10.5001C18.3332 5.91675 14.5832 2.16675 9.99984 2.16675C5.4165 2.16675 1.6665 5.91675 1.6665 10.5001C1.6665 15.0834 5.4165 18.8334 9.99984 18.8334Z",stroke:"#56CCF2",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M10 7.16675V11.3334",stroke:"#56CCF2",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M9.99561 13.8333H10.0031",stroke:"#56CCF2",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),text:"recharge_listing_balance"},{to:"/provider/update-id",icon:e.jsxs("svg",{width:"20",height:"21",viewBox:"0 0 20 21",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M1.6665 8.00008V5.91675C1.6665 3.84175 3.3415 2.16675 5.4165 2.16675H7.49984",stroke:"#56CCF2",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M12.5 2.16675H14.5833C16.6583 2.16675 18.3333 3.84175 18.3333 5.91675V8.00008",stroke:"#56CCF2",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M18.3335 13.8333V15.0833C18.3335 17.1583 16.6585 18.8333 14.5835 18.8333H13.3335",stroke:"#56CCF2",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.49984 18.8333H5.4165C3.3415 18.8333 1.6665 17.1583 1.6665 15.0833V13",stroke:"#56CCF2",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M14.1668 8.41675V12.5834C14.1668 14.2501 13.3335 15.0834 11.6668 15.0834H8.3335C6.66683 15.0834 5.8335 14.2501 5.8335 12.5834V8.41675C5.8335 6.75008 6.66683 5.91675 8.3335 5.91675H11.6668C13.3335 5.91675 14.1668 6.75008 14.1668 8.41675Z",stroke:"#56CCF2",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M15.8332 10.5H4.1665",stroke:"white",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),text:"update_id"},{to:"/provider/forgot",icon:e.jsx("svg",{width:"20",height:"21",viewBox:"0 0 20 21",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M14.6875 7.74992V5.91658C14.6875 4.70101 14.1936 3.53522 13.3146 2.67568C12.4355 1.81614 11.2432 1.33325 10 1.33325C8.7568 1.33325 7.56451 1.81614 6.68544 2.67568C5.80636 3.53522 5.3125 4.70101 5.3125 5.91658V7.74992C4.56658 7.74992 3.85121 8.03965 3.32376 8.55537C2.79632 9.0711 2.5 9.77057 2.5 10.4999V16.9166C2.5 17.6459 2.79632 18.3454 3.32376 18.8611C3.85121 19.3769 4.56658 19.6666 5.3125 19.6666H14.6875C15.4334 19.6666 16.1488 19.3769 16.6762 18.8611C17.2037 18.3454 17.5 17.6459 17.5 16.9166V10.4999C17.5 9.77057 17.2037 9.0711 16.6762 8.55537C16.1488 8.03965 15.4334 7.74992 14.6875 7.74992ZM7.1875 5.91658C7.1875 5.18724 7.48382 4.48777 8.01126 3.97204C8.53871 3.45632 9.25408 3.16659 10 3.16659C10.7459 3.16659 11.4613 3.45632 11.9887 3.97204C12.5162 4.48777 12.8125 5.18724 12.8125 5.91658V7.74992H7.1875V5.91658ZM15.625 16.9166C15.625 17.1597 15.5262 17.3929 15.3504 17.5648C15.1746 17.7367 14.9361 17.8333 14.6875 17.8333H5.3125C5.06386 17.8333 4.8254 17.7367 4.64959 17.5648C4.47377 17.3929 4.375 17.1597 4.375 16.9166V10.4999C4.375 10.2568 4.47377 10.0236 4.64959 9.85174C4.8254 9.67983 5.06386 9.58325 5.3125 9.58325H14.6875C14.9361 9.58325 15.1746 9.67983 15.3504 9.85174C15.5262 10.0236 15.625 10.2568 15.625 10.4999V16.9166Z",fill:"#56CCF2"})}),text:"reset_password"}],r=[{id:1,lang:"en",name:"English",icon:e.jsxs("svg",{width:"20",height:"21",viewBox:"0 0 20 21",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M9.99984 18.8334C14.6022 18.8334 18.3332 15.1025 18.3332 10.5001C18.3332 5.89771 14.6022 2.16675 9.99984 2.16675C5.39746 2.16675 1.6665 5.89771 1.6665 10.5001C1.6665 15.1025 5.39746 18.8334 9.99984 18.8334Z",stroke:"#8181A4",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M6.66667 3H7.5C5.875 7.86667 5.875 13.1333 7.5 18H6.66667",stroke:"#8181A4",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M12.5 3C14.125 7.86667 14.125 13.1333 12.5 18",stroke:"#8181A4",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M2.5 13.8333V13C7.36667 14.625 12.6333 14.625 17.5 13V13.8333",stroke:"#8181A4",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M2.5 8C7.36667 6.375 12.6333 6.375 17.5 8",stroke:"#8181A4",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),tran:"en"},{id:2,lang:"es",tran:"es",name:"Spanish",icon:e.jsxs("svg",{width:"20",height:"21",viewBox:"0 0 20 21",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M9.99984 18.8334C14.6022 18.8334 18.3332 15.1025 18.3332 10.5001C18.3332 5.89771 14.6022 2.16675 9.99984 2.16675C5.39746 2.16675 1.6665 5.89771 1.6665 10.5001C1.6665 15.1025 5.39746 18.8334 9.99984 18.8334Z",stroke:"#8181A4",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M6.66667 3H7.5C5.875 7.86667 5.875 13.1333 7.5 18H6.66667",stroke:"#8181A4",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M12.5 3C14.125 7.86667 14.125 13.1333 12.5 18",stroke:"#8181A4",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M2.5 13.8333V13C7.36667 14.625 12.6333 14.625 17.5 13V13.8333",stroke:"#8181A4",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M2.5 8C7.36667 6.375 12.6333 6.375 17.5 8",stroke:"#8181A4",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})}],T=new V,le=()=>{b();const{state:D,dispatch:l}=s.useContext(H),{state:Z,dispatch:c}=s.useContext(y),[u,i]=s.useState(!1),[k,n]=s.useState(!1),j=localStorage.getItem("lang")||r[0].lang,g=r.find(t=>t.lang===j)||r[0],[C,x]=s.useState(g),[f,a]=s.useState(!1),{i18n:v}=B(),p=_(),L=t=>{x(t),console.log(t),localStorage.setItem("lang",t.lang),v.changeLanguage(t.lang)},w=async()=>{try{n(!1),a(!0);const t=await T.callRawAPI("/v3/api/custom/chumpchange/provider/delete-account",{},"DELETE");console.log(t),l({type:"LOGOUT"}),p("/provider/login"),h(c,"Deleted successfully"),a(!1)}catch(t){a(!1),console.log(t),h(c,t.message,5e3,"error")}},M=()=>{l({type:"LOGOUT"}),localStorage.removeItem("user"),localStorage.removeItem("token"),localStorage.removeItem("role"),localStorage.removeItem("plan_id"),localStorage.removeItem("lang"),localStorage.removeItem("cart-session"),p("/provider/login")};return S.useEffect(()=>{},[]),console.log(o("provier_setting.title")),e.jsxs("div",{className:"p-5",children:[f&&e.jsx(d,{showCloseButton:!1,children:e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsxs("div",{className:"text-lg font-semibold",children:[o("loading.deleting"),"..."]}),e.jsx("div",{className:"mt-12",children:e.jsx(F,{})})]})}),e.jsxs("div",{className:"relative flex w-full items-center justify-between ",children:[e.jsx("div",{className:" ",children:e.jsx(A,{})}),e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:o("provider.setting.title")}),e.jsx("div",{className:" ",children:e.jsx("button",{className:"relative flex h-10 w-[67px] items-center justify-center rounded-[32px] bg-white/60 font-['Poppins'] text-sm font-semibold text-[#828282] backdrop-blur-[20px]",children:o("buttons.save")})})]}),e.jsx("div",{className:"mt-[74px]",children:e.jsx(I,{services:r,onServiceSelect:L,selectedService:C,setSelectedService:x,className:"!bg-[#8181a4]/10"})}),e.jsx("div",{className:"mt-10 flex flex-col gap-2 ",children:E.map((t,N)=>e.jsxs(W,{to:t.to,className:" flex h-12 w-full items-center gap-3 rounded-2xl border-2 border-transparent bg-[#50a8f9]/10 px-4 py-[14px] duration-200 active:bg-[#50a8f9] ",children:[e.jsx("div",{className:" h-5 w-5",children:t.icon}),e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:o(`provider.setting.${t.text}`)})]},N))}),e.jsxs("div",{className:" mt-10 ",children:[e.jsx(P,{onClick:()=>i(!0),children:o("provider.setting.log_out")}),e.jsx("div",{className:" flex items-center justify-center ",children:e.jsx("button",{onClick:()=>n(!0),className:" mt-10 text-center font-['Poppins'] text-sm font-medium text-[#f95050] ",children:o("provider.setting.delete_account")})})]}),u&&e.jsx(d,{closeModal:()=>i(!1),children:e.jsxs("div",{className:" flex flex-col items-center justify-center pt-8 ",children:[e.jsx("div",{className:" text-center font-['Poppins'] text-2xl font-medium leading-[31.20px] text-black",dangerouslySetInnerHTML:{__html:o("provider.setting.sure")}}),e.jsxs("div",{className:"px5 mt-[90px] grid grid-cols-2 ",children:[e.jsx("div",{className:" flex items-center justify-center ",children:e.jsx("button",{onClick:M,className:"text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:o("provider.setting.log_out")})}),e.jsx("div",{className:"",onClick:()=>i(!1),children:e.jsx("button",{className:"relative h-12 w-[163px] rounded-2xl bg-[#8181a4]/20 text-center font-['Poppins'] text-sm font-medium text-[#8080a3]",children:o("buttons.cancel")})})]})]})}),k&&e.jsx(d,{closeModal:()=>n(!1),children:e.jsxs("div",{className:" flex flex-col items-center justify-center pt-8 ",children:[e.jsx("div",{className:" text-center font-['Poppins'] text-2xl font-medium leading-[31.20px] text-black",dangerouslySetInnerHTML:{__html:o("provider.setting.d_sure")},z:!0}),e.jsxs("div",{className:"px5 mt-[90px] grid grid-cols-2 ",children:[e.jsx("div",{className:" flex items-center justify-center ",children:e.jsx("button",{onClick:w,className:"text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:o("provider.setting.del_for")})}),e.jsx("div",{className:"",onClick:()=>n(!1),children:e.jsx("button",{className:"relative h-12 w-[163px] rounded-2xl bg-[#8181a4]/20 text-center font-['Poppins'] text-sm font-medium text-[#8080a3]",children:o("buttons.cancel")})})]})]})})]})};export{le as default};
