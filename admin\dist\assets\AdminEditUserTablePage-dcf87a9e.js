import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as d,b as te,r as o,h as ae}from"./vendor-4f06b3f4.js";import{u as oe}from"./react-hook-form-f3d72793.js";import{o as se}from"./yup-2324a46a.js";import{c as le,a as i}from"./yup-17027d7a.js";import{M as ie,A as re,G as ne,t as me,s as ce}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as r}from"./MkdInput-ff3aa862.js";import{I as de}from"./InteractiveButton-8f7d74ee.js";import{S as pe}from"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";let h=new ie;const nt=p=>{var w,S,j,k,v,C;const{dispatch:P}=d.useContext(re),E=le({oauth:i(),role:i(),first_name:i(),last_name:i(),email:i(),password:i(),type:i(),verify:i(),phone:i(),photo:i(),operating_city:i(),refer:i(),stripe_uid:i(),paypal_uid:i(),listing_balance:i(),confirmation_code:i(),two_factor_authentication:i(),cedula_number:i(),cedula_image_link:i(),qr_link:i(),status:i()}).required(),{dispatch:_}=d.useContext(ne),[y,ue]=d.useState({}),[b,g]=d.useState(!1),[L,x]=d.useState(!1),I=te(),[he,R]=o.useState(""),[fe,T]=o.useState(""),[_e,F]=o.useState(""),[ge,q]=o.useState(""),[xe,A]=o.useState(""),[ye,U]=o.useState(""),[be,O]=o.useState(0),[Ne,D]=o.useState(0),[we,B]=o.useState(""),[Se,$]=o.useState(""),[je,M]=o.useState(""),[ke,V]=o.useState(""),[ve,G]=o.useState(""),[Ce,Q]=o.useState(""),[Pe,H]=o.useState(0),[Ee,K]=o.useState(""),[Le,z]=o.useState(0),[Ie,J]=o.useState(""),[Re,W]=o.useState(""),[Te,X]=o.useState(""),[Fe,Y]=o.useState(0),{register:s,handleSubmit:Z,setError:N,setValue:l,formState:{errors:a}}=oe({resolver:se(E)}),m=ae();o.useEffect(function(){(async function(){try{x(!0),h.setTable("user");const e=await h.callRestAPI({id:p.activeId?p.activeId:Number(m==null?void 0:m.id)},"GET");e.error||(l("oauth",e.model.oauth),l("role",e.model.role),l("first_name",e.model.first_name),l("last_name",e.model.last_name),l("email",e.model.email),l("password",e.model.password),l("type",e.model.type),l("verify",e.model.verify),l("phone",e.model.phone),l("photo",e.model.photo),l("operating_city",e.model.operating_city),l("refer",e.model.refer),l("stripe_uid",e.model.stripe_uid),l("paypal_uid",e.model.paypal_uid),l("listing_balance",e.model.listing_balance),l("confirmation_code",e.model.confirmation_code),l("two_factor_authentication",e.model.two_factor_authentication),l("cedula_number",e.model.cedula_number),l("cedula_image_link",e.model.cedula_image_link),l("qr_link",e.model.qr_link),l("status",e.model.status),R(e.model.oauth),T(e.model.role),F(e.model.first_name),q(e.model.last_name),A(e.model.email),U(e.model.password),O(e.model.type),D(e.model.verify),B(e.model.phone),$(e.model.photo),M(e.model.operating_city),V(e.model.refer),G(e.model.stripe_uid),Q(e.model.paypal_uid),H(e.model.listing_balance),K(e.model.confirmation_code),z(e.model.two_factor_authentication),J(e.model.cedula_number),W(e.model.cedula_image_link),X(e.model.qr_link),Y(e.model.status),x(!1))}catch(e){x(!1),console.log("error",e),me(P,e.message)}})()},[]);const ee=async e=>{g(!0);try{h.setTable("user");for(let u in y){let c=new FormData;c.append("file",y[u].file);let f=await h.uploadImage(c);e[u]=f.url}const n=await h.callRestAPI({id:p.activeId?p.activeId:Number(m==null?void 0:m.id),oauth:e.oauth,role:e.role,first_name:e.first_name,last_name:e.last_name,email:e.email,password:e.password,type:e.type,verify:e.verify,phone:e.phone,photo:e.photo,operating_city:e.operating_city,refer:e.refer,stripe_uid:e.stripe_uid,paypal_uid:e.paypal_uid,listing_balance:e.listing_balance,confirmation_code:e.confirmation_code,two_factor_authentication:e.two_factor_authentication,cedula_number:e.cedula_number,cedula_image_link:e.cedula_image_link,qr_link:e.qr_link,status:e.status},"PUT");if(!n.error)ce(_,"Updated"),I("/admin/user"),_({type:"REFRESH_DATA",payload:{refreshData:!0}}),p.setSidebar(!1);else if(n.validation){const u=Object.keys(n.validation);for(let c=0;c<u.length;c++){const f=u[c];N(f,{type:"manual",message:n.validation[f]})}}g(!1)}catch(n){g(!1),console.log("Error",n),N("oauth",{type:"manual",message:n.message})}};return d.useEffect(()=>{_({type:"SETPATH",payload:{path:"user"}})},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit User"}),L?t.jsx(pe,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:Z(ee),children:[t.jsx(r,{type:"text",page:"edit",name:"oauth",errors:a,label:"Oauth",placeholder:"Oauth",register:s,className:""}),t.jsx(r,{type:"text",page:"edit",name:"role",errors:a,label:"Role",placeholder:"Role",register:s,className:""}),t.jsx(r,{type:"text",page:"edit",name:"first_name",errors:a,label:"First Name",placeholder:"First Name",register:s,className:""}),t.jsx(r,{type:"text",page:"edit",name:"last_name",errors:a,label:"Last Name",placeholder:"Last Name",register:s,className:""}),t.jsx(r,{type:"text",page:"edit",name:"email",errors:a,label:"Email",placeholder:"Email",register:s,className:""}),t.jsx(r,{type:"text",page:"edit",name:"password",errors:a,label:"Password",placeholder:"Password",register:s,className:""}),t.jsx(r,{type:"number",page:"edit",name:"type",errors:a,label:"Type",placeholder:"Type",register:s,className:""}),t.jsx(r,{type:"number",page:"edit",name:"verify",errors:a,label:"Verify",placeholder:"Verify",register:s,className:""}),t.jsx(r,{type:"text",page:"edit",name:"phone",errors:a,label:"Phone",placeholder:"Phone",register:s,className:""}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"photo",children:"Photo"}),t.jsx("textarea",{placeholder:"Photo",...s("photo"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(w=a.photo)!=null&&w.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(S=a.photo)==null?void 0:S.message})]}),t.jsx(r,{type:"text",page:"edit",name:"operating_city",errors:a,label:"Operating City",placeholder:"Operating City",register:s,className:""}),t.jsx(r,{type:"text",page:"edit",name:"refer",errors:a,label:"Refer",placeholder:"Refer",register:s,className:""}),t.jsx(r,{type:"text",page:"edit",name:"stripe_uid",errors:a,label:"Stripe Uid",placeholder:"Stripe Uid",register:s,className:""}),t.jsx(r,{type:"text",page:"edit",name:"paypal_uid",errors:a,label:"Paypal Uid",placeholder:"Paypal Uid",register:s,className:""}),t.jsx(r,{type:"number",page:"edit",name:"listing_balance",errors:a,label:"Listing Balance",placeholder:"Listing Balance",register:s,className:""}),t.jsx(r,{type:"text",page:"edit",name:"confirmation_code",errors:a,label:"Confirmation Code",placeholder:"Confirmation Code",register:s,className:""}),t.jsx(r,{type:"number",page:"edit",name:"two_factor_authentication",errors:a,label:"Two Factor Authentication",placeholder:"Two Factor Authentication",register:s,className:""}),t.jsx(r,{type:"text",page:"edit",name:"cedula_number",errors:a,label:"Cedula Number",placeholder:"Cedula Number",register:s,className:""}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"cedula_image_link",children:"Cedula Image Link"}),t.jsx("textarea",{placeholder:"Cedula Image Link",...s("cedula_image_link"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(j=a.cedula_image_link)!=null&&j.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(k=a.cedula_image_link)==null?void 0:k.message})]}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"qr_link",children:"Qr Link"}),t.jsx("textarea",{placeholder:"Qr Link",...s("qr_link"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(v=a.qr_link)!=null&&v.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(C=a.qr_link)==null?void 0:C.message})]}),t.jsx(r,{type:"number",page:"edit",name:"status",errors:a,label:"Status",placeholder:"Status",register:s,className:""}),t.jsx(de,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:b,disable:b,children:"Submit"})]})]})};export{nt as default};
