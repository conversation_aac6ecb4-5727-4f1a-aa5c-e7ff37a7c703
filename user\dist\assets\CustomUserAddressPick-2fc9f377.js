import{j as s}from"./@react-google-maps/api-ee55a349.js";import{B as m}from"./index-d54cffea.js";import{P as x}from"./index-d152a0de.js";import{d as u}from"./vendor-b16525a8.js";import"./qr-scanner-cf010ec4.js";const $=()=>{const e=new URLSearchParams(location.search),t=u(),r=e.get("redirect_url"),a=(c,i,d,n,o,l)=>{t(`${r||"/user/add-address"}?lat=${c}&lng=${i}&address=${d}&city=${n}&state=${o}$country=${l}`)};return s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"p-5",children:s.jsxs("div",{className:"relative flex w-full items-center justify-between ",children:[s.jsx("div",{className:" ",children:s.jsx(m,{link:"/user/add-address"})}),s.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:"Pick Location"}),s.jsx("div",{className:""})]})}),s.jsx("div",{className:" h-[calc(100vh-88px)] ",children:s.jsx(x,{handleChoose:a})})]})};export{$ as default};
