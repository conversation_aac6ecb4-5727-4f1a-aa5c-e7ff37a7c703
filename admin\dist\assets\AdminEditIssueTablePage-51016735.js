import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as i,b as C,r as l,h as L}from"./vendor-4f06b3f4.js";import{u as M}from"./react-hook-form-f3d72793.js";import{o as G}from"./yup-2324a46a.js";import{c as O,a as p}from"./yup-17027d7a.js";import{M as $,A as B,G as H,t as U,s as q}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as x}from"./MkdInput-ff3aa862.js";import{I as K}from"./InteractiveButton-8f7d74ee.js";import{S as V}from"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";let u=new $;const Ee=d=>{var I,j;const{dispatch:w}=i.useContext(B),N=O({title:p(),reporter_id:p(),task_id:p(),description:p(),status:p()}).required(),{dispatch:h}=i.useContext(H),[y,z]=i.useState({}),[S,b]=i.useState(!1),[E,g]=i.useState(!1),T=C(),[J,_]=l.useState(""),[Q,v]=l.useState(0),[W,R]=l.useState(0),[X,D]=l.useState(""),[Y,A]=l.useState(""),{register:n,handleSubmit:P,setError:k,setValue:m,formState:{errors:a}}=M({resolver:G(N)}),o=L();l.useEffect(function(){(async function(){try{g(!0),u.setTable("issue");const e=await u.callRestAPI({id:d.activeId?d.activeId:Number(o==null?void 0:o.id)},"GET");e.error||(m("title",e.model.title),m("reporter_id",e.model.reporter_id),m("task_id",e.model.task_id),m("description",e.model.description),m("status",e.model.status),_(e.model.title),v(e.model.reporter_id),R(e.model.task_id),D(e.model.description),A(e.model.status),g(!1))}catch(e){g(!1),console.log("error",e),U(w,e.message)}})()},[]);const F=async e=>{b(!0);try{u.setTable("issue");for(let c in y){let r=new FormData;r.append("file",y[c].file);let f=await u.uploadImage(r);e[c]=f.url}const s=await u.callRestAPI({id:d.activeId?d.activeId:Number(o==null?void 0:o.id),title:e.title,reporter_id:e.reporter_id,task_id:e.task_id,description:e.description,status:e.status},"PUT");if(!s.error)q(h,"Updated"),T("/admin/issue"),h({type:"REFRESH_DATA",payload:{refreshData:!0}}),d.setSidebar(!1);else if(s.validation){const c=Object.keys(s.validation);for(let r=0;r<c.length;r++){const f=c[r];k(f,{type:"manual",message:s.validation[f]})}}b(!1)}catch(s){b(!1),console.log("Error",s),k("title",{type:"manual",message:s.message})}};return i.useEffect(()=>{h({type:"SETPATH",payload:{path:"issue"}})},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Issue"}),E?t.jsx(V,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:P(F),children:[t.jsx(x,{type:"text",page:"edit",name:"title",errors:a,label:"Title",placeholder:"Title",register:n,className:""}),t.jsx(x,{type:"number",page:"edit",name:"reporter_id",errors:a,label:"Reporter Id",placeholder:"Reporter Id",register:n,className:""}),t.jsx(x,{type:"number",page:"edit",name:"task_id",errors:a,label:"Task Id",placeholder:"Task Id",register:n,className:""}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"description",children:"Description"}),t.jsx("textarea",{placeholder:"Description",...n("description"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(I=a.description)!=null&&I.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(j=a.description)==null?void 0:j.message})]}),t.jsx(x,{type:"text",page:"edit",name:"status",errors:a,label:"Status",placeholder:"Status",register:n,className:""}),t.jsx(K,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:S,disable:S,children:"Submit"})]})]})};export{Ee as default};
