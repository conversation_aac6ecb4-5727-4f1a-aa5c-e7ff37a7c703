import{j as e}from"./@react-google-maps/api-ee55a349.js";import{i as g,r as s,R as n,L as w}from"./vendor-b16525a8.js";import"./index-9ef1c042.js";import{M as k,A as y,G as N}from"./index-09a1718e.js";import{U as T}from"./index-dd254604.js";import{J as m}from"./index-e25e12e5.js";import{u as C}from"./user-a875fff3.js";import{S as E}from"./index-5a645c18.js";import{t as o}from"./i18next-7389dd8c.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";const p=new k,X=()=>{var l;g();const{state:r,dispatch:S}=s.useContext(y),{state:D,dispatch:u}=s.useContext(N),[a,i]=s.useState("active"),[d,x]=s.useState([]),[f,c]=s.useState(!1),[h,j]=s.useState([]),b=async()=>{try{const t=await p.callRawAPI("/v3/api/custom/chumpchange/user/request/current",{},"GET");t.error||x(t.data)}catch(t){console.error("Error fetching active tasks:",t)}},v=async()=>{try{const t=await p.callRawAPI("/v3/api/custom/chumpchange/user/request/completed",{},"GET");t.error||j(t.data)}catch(t){return console.error("Error fetching completed tasks:",t),[]}};return n.useEffect(()=>{(async()=>(c(!0),await b(),await v(),c(!1)))()},[]),n.useEffect(()=>{u({type:"SETPATH",payload:{path:"requests"}})},[]),e.jsxs(T,{className:" relative ",children:[" ",e.jsxs("div",{className:" mt-2 flex items-center justify-between px-10 ",children:[e.jsx("div",{className:"font-['Poppins'] text-lg font-bold text-black",children:o("user.my_reuqest.title")}),e.jsx(w,{to:"/user/profile",className:" relative z-10 ",children:e.jsx("img",{className:"h-[45px] w-[45px] rounded-full",src:((l=r==null?void 0:r.userDetails)==null?void 0:l.photo)||C})})]}),e.jsx("div",{className:" mt-14 px-5 ",children:e.jsxs("div",{className:" grid h-12 w-full grid-cols-2 items-center rounded-2xl bg-[#8181a4]/20 px-1",children:[e.jsx("button",{className:`flex h-10 items-center justify-center rounded-2xl font-['Poppins'] text-sm font-medium ${a==="active"?"bg-black text-white":"text-black"}`,onClick:()=>i("active"),children:o("user.my_reuqest.current")}),e.jsx("button",{className:`flex h-10 items-center justify-center rounded-2xl font-['Poppins'] text-sm font-medium ${a==="completed"?"bg-black text-white":"text-black"}`,onClick:()=>i("completed"),children:o("user.my_reuqest.completed")})]})}),e.jsxs("div",{className:" relative left-0 mt-6 flex w-full flex-1 flex-col items-center justify-start overflow-hidden rounded-tl-[32px] rounded-tr-[32px] bg-white",children:[e.jsx("div",{className:" flex h-7 w-full max-w-[335px] items-center justify-center border-b bg-white ",children:e.jsx("div",{className:" h-1 w-[50px] rounded bg-[#f2f2f7]"})}),f?e.jsx("div",{className:"flex h-full w-full items-center justify-center pt-10 ",children:e.jsx(E,{})}):a==="active"?e.jsx(m,{taskData:d}):e.jsx(m,{taskData:h})]})]})};export{X as default};
