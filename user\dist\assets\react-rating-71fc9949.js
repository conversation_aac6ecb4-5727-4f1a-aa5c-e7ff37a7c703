import{R as i}from"./vendor-b16525a8.js";function E(a){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?E=function(t){return typeof t}:E=function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},E(a)}function j(a,t){if(!(a instanceof t))throw new TypeError("Cannot call a class as a function")}function W(a,t){for(var n=0;n<t.length;n++){var e=t[n];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(a,e.key,e)}}function L(a,t,n){return t&&W(a.prototype,t),n&&W(a,n),a}function x(a,t,n){return t in a?Object.defineProperty(a,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[t]=n,a}function D(){return D=Object.assign||function(a){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&(a[e]=n[e])}return a},D.apply(this,arguments)}function R(a){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{},e=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(e=e.concat(Object.getOwnPropertySymbols(n).filter(function(o){return Object.getOwnPropertyDescriptor(n,o).enumerable}))),e.forEach(function(o){x(a,o,n[o])})}return a}function q(a,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");a.prototype=Object.create(t&&t.prototype,{constructor:{value:a,writable:!0,configurable:!0}}),t&&H(a,t)}function C(a){return C=Object.setPrototypeOf?Object.getPrototypeOf:function(n){return n.__proto__||Object.getPrototypeOf(n)},C(a)}function H(a,t){return H=Object.setPrototypeOf||function(e,o){return e.__proto__=o,e},H(a,t)}function u(a){if(a===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a}function A(a,t){return t&&(typeof t=="object"||typeof t=="function")?t:u(a)}var T={display:"inline-block",borderRadius:"50%",border:"5px double white",width:30,height:30},N={empty:R({},T,{backgroundColor:"#ccc"}),full:R({},T,{backgroundColor:"black"}),placeholder:R({},T,{backgroundColor:"red"})},X=function(t){if(i.isValidElement(t))return t;if(E(t)==="object"&&t!==null)return i.createElement("span",{style:t});if(Object.prototype.toString.call(t)==="[object String]")return i.createElement("span",{className:t})},z=function(a){q(t,a);function t(){return j(this,t),A(this,C(t).apply(this,arguments))}return L(t,[{key:"render",value:function(){var e,o=this.props,l=o.index,r=o.inactiveIcon,p=o.activeIcon,s=o.percent,h=o.direction,g=o.readonly,d=o.onClick,v=o.onMouseMove,S=X(r),k=s<100,_=k?{}:{visibility:"hidden"},M=X(p),b=(e={display:"inline-block",position:"absolute",overflow:"hidden",top:0},x(e,h==="rtl"?"right":"left",0),x(e,"width","".concat(s,"%")),e),V={cursor:g?"inherit":"pointer",display:"inline-block",position:"relative"};function m(y){v&&v(l,y)}function f(y){d&&(y.preventDefault(),d(l,y))}return i.createElement("span",{style:V,onClick:f,onMouseMove:m,onTouchMove:m,onTouchEnd:f},i.createElement("span",{style:_},S),i.createElement("span",{style:b},M))}}]),t}(i.PureComponent),B=function(a){q(t,a);function t(n){var e;return j(this,t),e=A(this,C(t).call(this,n)),e.state={displayValue:e.props.value,interacting:!1},e.onMouseLeave=e.onMouseLeave.bind(u(u(e))),e.symbolMouseMove=e.symbolMouseMove.bind(u(u(e))),e.symbolClick=e.symbolClick.bind(u(u(e))),e}return L(t,[{key:"UNSAFE_componentWillReceiveProps",value:function(e){var o=this.props.value!==e.value;this.setState(function(l){return{displayValue:o?e.value:l.displayValue}})}},{key:"componentDidUpdate",value:function(e,o){if(e.value===this.props.value){if(o.interacting&&!this.state.interacting)return this.props.onHover();this.state.interacting&&this.props.onHover(this.state.displayValue)}}},{key:"symbolClick",value:function(e,o){var l=this.calculateDisplayValue(e,o);this.props.onClick(l,o)}},{key:"symbolMouseMove",value:function(e,o){var l=this.calculateDisplayValue(e,o);this.setState({interacting:!this.props.readonly,displayValue:l})}},{key:"onMouseLeave",value:function(){this.setState({displayValue:this.props.value,interacting:!1})}},{key:"calculateDisplayValue",value:function(e,o){var l=this.calculateHoverPercentage(o),r=Math.ceil(l%1*this.props.fractions)/this.props.fractions,p=Math.pow(10,3),s=e+(Math.floor(l)+Math.floor(r*p)/p);return s>0?s>this.props.totalSymbols?this.props.totalSymbols:s:1/this.props.fractions}},{key:"calculateHoverPercentage",value:function(e){var o=e.nativeEvent.type.indexOf("touch")>-1?e.nativeEvent.type.indexOf("touchend")>-1?e.changedTouches[0].clientX:e.touches[0].clientX:e.clientX,l=e.target.getBoundingClientRect(),r=this.props.direction==="rtl"?l.right-o:o-l.left;return r<0?0:r/l.width}},{key:"render",value:function(){var e=this.props,o=e.readonly,l=e.quiet,r=e.totalSymbols,p=e.value,s=e.placeholderValue,h=e.direction,g=e.emptySymbol,d=e.fullSymbol,v=e.placeholderSymbol,S=e.className,k=e.id,_=e.style,M=e.tabIndex,b=this.state,V=b.displayValue,m=b.interacting,f=[],y=[].concat(g),I=[].concat(d),$=[].concat(v),U=s!==0&&p===0&&!m,O;U?O=s:O=l?p:V;for(var F=Math.floor(O),c=0;c<r;c++){var P=void 0;c-F<0?P=100:c-F===0?P=(O-c)*100:P=0,f.push(i.createElement(z,D({key:c,index:c,readonly:o,inactiveIcon:y[c%y.length],activeIcon:U?$[c%I.length]:I[c%I.length],percent:P,direction:h},!o&&{onClick:this.symbolClick,onMouseMove:this.symbolMouseMove,onTouchMove:this.symbolMouseMove,onTouchEnd:this.symbolClick})))}return i.createElement("span",D({id:k,style:R({},_,{display:"inline-block",direction:h}),className:S,tabIndex:M,"aria-label":this.props["aria-label"]},!o&&{onMouseLeave:this.onMouseLeave}),f)}}]),t}(i.PureComponent);function w(){}w._name="react_rating_noop";var G=function(a){q(t,a);function t(n){var e;return j(this,t),e=A(this,C(t).call(this,n)),e.state={value:n.initialRating},e.handleClick=e.handleClick.bind(u(u(e))),e.handleHover=e.handleHover.bind(u(u(e))),e}return L(t,[{key:"UNSAFE_componentWillReceiveProps",value:function(e){this.setState({value:e.initialRating})}},{key:"handleClick",value:function(e,o){var l=this,r=this.translateDisplayValueToValue(e);this.props.onClick(r),this.state.value!==r&&this.setState({value:r},function(){return l.props.onChange(l.state.value)})}},{key:"handleHover",value:function(e){var o=e===void 0?e:this.translateDisplayValueToValue(e);this.props.onHover(o)}},{key:"translateDisplayValueToValue",value:function(e){var o=e*this.props.step+this.props.start;return o===this.props.start?o+1/this.props.fractions:o}},{key:"tranlateValueToDisplayValue",value:function(e){return e===void 0?0:(e-this.props.start)/this.props.step}},{key:"render",value:function(){var e=this.props,o=e.step,l=e.emptySymbol,r=e.fullSymbol,p=e.placeholderSymbol,s=e.readonly,h=e.quiet,g=e.fractions,d=e.direction,v=e.start,S=e.stop,k=e.id,_=e.className,M=e.style,b=e.tabIndex;function V(m,f,y){return Math.floor((f-m)/y)}return i.createElement(B,{id:k,style:M,className:_,tabIndex:b,"aria-label":this.props["aria-label"],totalSymbols:V(v,S,o),value:this.tranlateValueToDisplayValue(this.state.value),placeholderValue:this.tranlateValueToDisplayValue(this.props.placeholderRating),readonly:s,quiet:h,fractions:g,direction:d,emptySymbol:l,fullSymbol:r,placeholderSymbol:p,onClick:this.handleClick,onHover:this.handleHover})}}]),t}(i.PureComponent);G.defaultProps={start:0,stop:5,step:1,readonly:!1,quiet:!1,fractions:1,direction:"ltr",onHover:w,onClick:w,onChange:w,emptySymbol:N.empty,fullSymbol:N.full,placeholderSymbol:N.placeholder};export{G as R};
