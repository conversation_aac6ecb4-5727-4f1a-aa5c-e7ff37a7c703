import{j as s}from"./@react-google-maps/api-ac2f9d6f.js";import{R as t,h as x}from"./vendor-4f06b3f4.js";import"./yup-17027d7a.js";import{M as p,G as o,t as f}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{S as u}from"./index-2d8231e7.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";let m=new p;const _=()=>{const{dispatch:l}=t.useContext(o),{dispatch:c}=t.useContext(o),[e,n]=t.useState({}),[d,r]=t.useState(!0),i=x();return t.useEffect(function(){(async function(){try{r(!0),m.setTable("plan");const a=await m.callRestAPI({id:Number(i==null?void 0:i.id),join:""},"GET");a.error||(n(a.model),r(!1))}catch(a){r(!1),console.log("error",a),f(c,a.message)}})()},[]),t.useEffect(()=>{l({type:"SHOW_BACKBUTTON",payload:{showBackButton:!0}})},[]),s.jsx("div",{className:" mx-auto rounded  p-5 shadow-md",children:d?s.jsx(u,{}):s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Name"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.name})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Amount"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.amount})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Description"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.description})]})})]})})};export{_ as default};
