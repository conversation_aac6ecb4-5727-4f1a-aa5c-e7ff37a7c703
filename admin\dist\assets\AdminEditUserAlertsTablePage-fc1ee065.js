import{j as o}from"./@react-google-maps/api-ac2f9d6f.js";import{R as i,b as z,r as d,h as J}from"./vendor-4f06b3f4.js";import{u as Q}from"./react-hook-form-f3d72793.js";import{o as W}from"./yup-2324a46a.js";import{c as X,a as p}from"./yup-17027d7a.js";import{M as Y,A as Z,G as ee,t as T,s as se}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as _}from"./MkdInput-ff3aa862.js";import{I as te}from"./InteractiveButton-8f7d74ee.js";import{S as ae}from"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";let c=new Y;const Ce=v=>{const{dispatch:b}=i.useContext(Z),N=X({message:p(),service_alert_id:p(),service_id:p(),location_id:p(),user_id:p(),status:p()}).required(),{dispatch:S}=i.useContext(ee),[I,oe]=i.useState({}),[y,x]=i.useState(!1),[L,n]=i.useState(!1),R=z(),[P,k]=i.useState([]),[D,M]=i.useState([]),[G,U]=i.useState([]),[re,C]=d.useState(""),[ie,F]=d.useState(0),[le,O]=d.useState(0),[ce,B]=d.useState(0),[de,H]=d.useState(0),[ne,$]=d.useState(""),{register:g,handleSubmit:q,setError:w,setValue:m,formState:{errors:f}}=Q({resolver:W(N)}),u=J(),K=async()=>{var e,a,l;try{n(!0);const s=await c.callRawAPI("/v3/api/custom/chumpchange/users/services",{},"GET"),r=await c.callRawAPI("/v3/api/custom/chumpchange/users/locations",{},"GET"),h=await c.callRawAPI("/v3/api/custom/chumpchange/users/service_alert",{},"GET");if(console.log("services",s),console.log("locations",r),console.log("service_alert",h),!s.error){let A=[{name:"Select"}];(e=s==null?void 0:s.data)==null||e.map(t=>{A.push({name:t==null?void 0:t.name,value:t.id})}),k(A);let E=[{name:"Select"}];(a=r==null?void 0:r.data)==null||a.map(t=>{E.push({name:t==null?void 0:t.address,value:t.id})}),M(E);let j=[{name:"Select"}];(l=h==null?void 0:h.data)==null||l.map(t=>{j.push({name:t==null?void 0:t.name,value:t.id})}),U(j),n(!1)}}catch(s){n(!1),console.log("error",s),T(b,s.message)}};d.useEffect(function(){(async function(){try{n(!0),await K(),c.setTable("user_alerts");const e=await c.callRestAPI({id:v.activeId?v.activeId:Number(u==null?void 0:u.id)},"GET");e.error||(m("message",e.model.message),m("service_alert_id",e.model.service_alert_id),m("service_id",e.model.service_id),m("location_id",e.model.location_id),m("user_id",e.model.user_id),m("status",e.model.status),C(e.model.message),F(e.model.service_alert_id),O(e.model.service_id),B(e.model.location_id),H(e.model.user_id),$(e.model.status),n(!1))}catch(e){n(!1),console.log("error",e),T(b,e.message)}})()},[]);const V=async e=>{x(!0);try{c.setTable("user_alerts");for(let l in I){let s=new FormData;s.append("file",I[l].file);let r=await c.uploadImage(s);e[l]=r.url}const a=await c.callRestAPI({id:v.activeId?v.activeId:Number(u==null?void 0:u.id),message:e.message,service_alert_id:e.service_alert_id,service_id:e.service_id,location_id:e.location_id,user_id:e.user_id,status:e.status},"PUT");if(!a.error)se(S,"Updated"),R("/admin/user_alerts"),S({type:"REFRESH_DATA",payload:{refreshData:!0}}),v.setSidebar(!1);else if(a.validation){const l=Object.keys(a.validation);for(let s=0;s<l.length;s++){const r=l[s];w(r,{type:"manual",message:a.validation[r]})}}x(!1)}catch(a){x(!1),console.log("Error",a),w("message",{type:"manual",message:a.message})}};return i.useEffect(()=>{S({type:"SETPATH",payload:{path:"user_alerts"}})},[]),o.jsxs("div",{className:" mx-auto rounded   p-5 shadow-md",children:[o.jsx("h4",{className:"text-2xl font-medium",children:"Edit User Alerts"}),L?o.jsx(ae,{}):o.jsxs("form",{className:" w-full max-w-lg",onSubmit:q(V),children:[o.jsx(_,{type:"text",page:"edit",name:"message",errors:f,label:"Message",placeholder:"Message",register:g,className:""}),o.jsx(_,{type:"dropdown",page:"edit",name:"service_alert_id",errors:f,label:"Service Alert Id",placeholder:"Service Alert Id",register:g,className:"",options:G}),o.jsx(_,{type:"dropdown",page:"edit",name:"service_id",errors:f,label:"Service Id",placeholder:"Service Id",register:g,className:"",options:P}),o.jsx(_,{type:"dropdown",page:"edit",name:"location_id",errors:f,label:"Location Id",placeholder:"Location Id",register:g,className:"",options:D}),o.jsx(_,{type:"select",page:"edit",name:"status",errors:f,label:"Status",placeholder:"Status",register:g,className:"",options:["active","expired"]}),o.jsx(te,{type:"submit",className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",loading:y,disable:y,children:"Submit"})]})]})};export{Ce as default};
