import{j as a}from"./@react-google-maps/api-ee55a349.js";import{R as i,h as m,d as p,r as c}from"./vendor-b16525a8.js";import{S as n}from"./index-5a645c18.js";import{A as d}from"./index-09a1718e.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./react-i18next-4a61273e.js";const O=()=>{const{dispatch:r}=i.useContext(d),[e,l]=m();let o=e.get("data");const s=p();return c.useEffect(()=>{const t=JSON.parse(o);console.log("parsedData >> ",t),r({type:"LOGIN",payload:t}),s("/user/dashboard")},[]),a.jsx("div",{className:" flex h-screen w-full items-center justify-center ",children:a.jsx(n,{})})};export{O as default};
