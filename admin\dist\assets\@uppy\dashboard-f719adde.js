import{_ as l,k as he,m as Fi,H as Y,E as Lt,b as Se,T as Pi,h as Te,q as Ci,y as Xe,A as Be}from"./audio-a0565fb1.js";import{U as Ee,g as Si,i as Ti}from"./core-10860ef6.js";import{g as xt}from"../vendor-4f06b3f4.js";import{p as Ye}from"./compressor-af9f85a3.js";import{d as Ai,n as _t}from"./aws-s3-e4097d1c.js";function dt(i,e,t,s){return t===0||i===e?i:s===0?e:i+(e-i)*2**(-s/t)}const $={STATE_ERROR:"error",STATE_WAITING:"waiting",STATE_PREPROCESSING:"preprocessing",STATE_UPLOADING:"uploading",STATE_POSTPROCESSING:"postprocessing",STATE_COMPLETE:"complete"};var zt={exports:{}};/*!
	Copyright (c) 2018 <PERSON>.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/(function(i){(function(){var e={}.hasOwnProperty;function t(){for(var r="",a=0;a<arguments.length;a++){var o=arguments[a];o&&(r=n(r,s(o)))}return r}function s(r){if(typeof r=="string"||typeof r=="number")return r;if(typeof r!="object")return"";if(Array.isArray(r))return t.apply(null,r);if(r.toString!==Object.prototype.toString&&!r.toString.toString().includes("[native code]"))return r.toString();var a="";for(var o in r)e.call(r,o)&&r[o]&&(a=n(a,o));return a}function n(r,a){return a?r?r+" "+a:r+a:r}i.exports?(t.default=t,i.exports=t):window.classNames=t})()})(zt);var Ei=zt.exports;const x=xt(Ei);function Ie(i){const e=[];let t="indeterminate",s;for(const{progress:r}of Object.values(i)){const{preprocess:a,postprocess:o}=r;s==null&&(a||o)&&({mode:t,message:s}=a||o),(a==null?void 0:a.mode)==="determinate"&&e.push(a.value),(o==null?void 0:o.mode)==="determinate"&&e.push(o.value)}const n=e.reduce((r,a)=>r+a/e.length,0);return{mode:t,message:s,value:n}}function Di(i){const e=Math.floor(i/3600)%24,t=Math.floor(i/60)%60,s=Math.floor(i%60);return{hours:e,minutes:t,seconds:s}}function ki(i){const e=Di(i),t=e.hours===0?"":`${e.hours}h`,s=e.minutes===0?"":`${e.hours===0?e.minutes:` ${e.minutes.toString(10).padStart(2,"0")}`}m`,n=e.hours!==0?"":`${e.minutes===0?e.seconds:` ${e.seconds.toString(10).padStart(2,"0")}`}s`;return`${t}${s}${n}`}const Oi="·",ut=()=>` ${Oi} `;function Bi(i){const{newFiles:e,isUploadStarted:t,recoveredState:s,i18n:n,uploadState:r,isSomeGhost:a,startUpload:o}=i,d=x("uppy-u-reset","uppy-c-btn","uppy-StatusBar-actionBtn","uppy-StatusBar-actionBtn--upload",{"uppy-c-btn-primary":r===$.STATE_WAITING},{"uppy-StatusBar-actionBtn--disabled":a}),u=e&&t&&!s?n("uploadXNewFiles",{smart_count:e}):n("uploadXFiles",{smart_count:e});return l("button",{type:"button",className:d,"aria-label":n("uploadXFiles",{smart_count:e}),onClick:o,disabled:a,"data-uppy-super-focusable":!0},u)}function Ii(i){const{i18n:e,uppy:t}=i;return l("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-StatusBar-actionBtn uppy-StatusBar-actionBtn--retry","aria-label":e("retryUpload"),onClick:()=>t.retryAll().catch(()=>{}),"data-uppy-super-focusable":!0,"data-cy":"retry"},l("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"8",height:"10",viewBox:"0 0 8 10"},l("path",{d:"M4 2.408a2.75 2.75 0 1 0 2.75 2.75.626.626 0 0 1 1.25.018v.023a4 4 0 1 1-4-4.041V.25a.25.25 0 0 1 .389-.208l2.299 1.533a.25.25 0 0 1 0 .416l-2.3 1.533A.25.25 0 0 1 4 3.316v-.908z"})),e("retry"))}function Ui(i){const{i18n:e,uppy:t}=i;return l("button",{type:"button",className:"uppy-u-reset uppy-StatusBar-actionCircleBtn",title:e("cancel"),"aria-label":e("cancel"),onClick:()=>t.cancelAll(),"data-cy":"cancel","data-uppy-super-focusable":!0},l("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"16",height:"16",viewBox:"0 0 16 16"},l("g",{fill:"none",fillRule:"evenodd"},l("circle",{fill:"#888",cx:"8",cy:"8",r:"8"}),l("path",{fill:"#FFF",d:"M9.283 8l2.567 2.567-1.283 1.283L8 9.283 5.433 11.85 4.15 10.567 6.717 8 4.15 5.433 5.433 4.15 8 6.717l2.567-2.567 1.283 1.283z"}))))}function Ni(i){const{isAllPaused:e,i18n:t,isAllComplete:s,resumableUploads:n,uppy:r}=i,a=t(e?"resume":"pause");function o(){if(!s){if(!n){r.cancelAll();return}if(e){r.resumeAll();return}r.pauseAll()}}return l("button",{title:a,"aria-label":a,className:"uppy-u-reset uppy-StatusBar-actionCircleBtn",type:"button",onClick:o,"data-cy":"togglePauseResume","data-uppy-super-focusable":!0},l("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"16",height:"16",viewBox:"0 0 16 16"},l("g",{fill:"none",fillRule:"evenodd"},l("circle",{fill:"#888",cx:"8",cy:"8",r:"8"}),l("path",{fill:"#FFF",d:e?"M6 4.25L11.5 8 6 11.75z":"M5 4.5h2v7H5v-7zm4 0h2v7H9v-7z"}))))}function Ri(i){const{i18n:e,doneButtonHandler:t}=i;return l("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-StatusBar-actionBtn uppy-StatusBar-actionBtn--done",onClick:t,"data-uppy-super-focusable":!0},e("done"))}function $t(){return l("svg",{className:"uppy-StatusBar-spinner","aria-hidden":"true",focusable:"false",width:"14",height:"14"},l("path",{d:"M13.983 6.547c-.12-2.509-1.64-4.893-3.939-5.936-2.48-1.127-5.488-.656-7.556 1.094C.524 3.367-.398 6.048.162 8.562c.556 2.495 2.46 4.52 4.94 5.183 2.932.784 5.61-.602 7.256-3.015-1.493 1.993-3.745 3.309-6.298 2.868-2.514-.434-4.578-2.349-5.153-4.84a6.226 6.226 0 0 1 2.98-6.778C6.34.586 9.74 1.1 11.373 3.493c.407.596.693 1.282.842 1.988.127.598.073 1.197.161 1.794.078.525.543 1.257 1.15.864.525-.341.49-1.05.456-1.592-.007-.15.02.3 0 0",fillRule:"evenodd"}))}function Mi(i){const{progress:e}=i,{value:t,mode:s,message:n}=e,r="·";return l("div",{className:"uppy-StatusBar-content"},l($t,null),s==="determinate"?`${Math.round(t*100)}% ${r} `:"",n)}function Li(i){const{numUploads:e,complete:t,totalUploadedSize:s,totalSize:n,totalETA:r,i18n:a}=i,o=e>1;return l("div",{className:"uppy-StatusBar-statusSecondary"},o&&a("filesUploadedOfTotal",{complete:t,smart_count:e}),l("span",{className:"uppy-StatusBar-additionalInfo"},o&&ut(),a("dataUploadedOfTotal",{complete:Ye(s),total:Ye(n)}),ut(),a("xTimeLeft",{time:ki(r)})))}function Ht(i){const{i18n:e,complete:t,numUploads:s}=i;return l("div",{className:"uppy-StatusBar-statusSecondary"},e("filesUploadedOfTotal",{complete:t,smart_count:s}))}function xi(i){const{i18n:e,newFiles:t,startUpload:s}=i,n=x("uppy-u-reset","uppy-c-btn","uppy-StatusBar-actionBtn","uppy-StatusBar-actionBtn--uploadNewlyAdded");return l("div",{className:"uppy-StatusBar-statusSecondary"},l("div",{className:"uppy-StatusBar-statusSecondaryHint"},e("xMoreFilesAdded",{smart_count:t})),l("button",{type:"button",className:n,"aria-label":e("uploadXFiles",{smart_count:t}),onClick:s},e("upload")))}function _i(i){const{i18n:e,supportsUploadProgress:t,totalProgress:s,showProgressDetails:n,isUploadStarted:r,isAllComplete:a,isAllPaused:o,newFiles:d,numUploads:u,complete:h,totalUploadedSize:c,totalSize:p,totalETA:g,startUpload:f}=i,v=d&&r;if(!r||a)return null;const m=e(o?"paused":"uploading");function w(){return!o&&!v&&n?t?l(Li,{numUploads:u,complete:h,totalUploadedSize:c,totalSize:p,totalETA:g,i18n:e}):l(Ht,{i18n:e,complete:h,numUploads:u}):null}return l("div",{className:"uppy-StatusBar-content","aria-label":m,title:m},o?null:l($t,null),l("div",{className:"uppy-StatusBar-status"},l("div",{className:"uppy-StatusBar-statusPrimary"},t?`${m}: ${s}%`:m),w(),v?l(xi,{i18n:e,newFiles:d,startUpload:f}):null))}function zi(i){const{i18n:e}=i;return l("div",{className:"uppy-StatusBar-content",role:"status",title:e("complete")},l("div",{className:"uppy-StatusBar-status"},l("div",{className:"uppy-StatusBar-statusPrimary"},l("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-StatusBar-statusIndicator uppy-c-icon",width:"15",height:"11",viewBox:"0 0 15 11"},l("path",{d:"M.414 5.843L1.627 4.63l3.472 3.472L13.202 0l1.212 1.213L5.1 10.528z"})),e("complete"))))}function $i(i){const{error:e,i18n:t,complete:s,numUploads:n}=i;function r(){const a=`${t("uploadFailed")} 

 ${e}`;alert(a)}return l("div",{className:"uppy-StatusBar-content",title:t("uploadFailed")},l("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-StatusBar-statusIndicator uppy-c-icon",width:"11",height:"11",viewBox:"0 0 11 11"},l("path",{d:"M4.278 5.5L0 1.222 1.222 0 5.5 4.278 9.778 0 11 1.222 6.722 5.5 11 9.778 9.778 11 5.5 6.722 1.222 11 0 9.778z"})),l("div",{className:"uppy-StatusBar-status"},l("div",{className:"uppy-StatusBar-statusPrimary"},t("uploadFailed"),l("button",{className:"uppy-u-reset uppy-StatusBar-details","aria-label":t("showErrorDetails"),"data-microtip-position":"top-right","data-microtip-size":"medium",onClick:r,type:"button"},"?")),l(Ht,{i18n:t,complete:s,numUploads:n})))}const{STATE_ERROR:ht,STATE_WAITING:ct,STATE_PREPROCESSING:Ue,STATE_UPLOADING:ge,STATE_POSTPROCESSING:Ne,STATE_COMPLETE:ye}=$;function Vt(i){const{newFiles:e,allowNewUpload:t,isUploadInProgress:s,isAllPaused:n,resumableUploads:r,error:a,hideUploadButton:o,hidePauseResumeButton:d,hideCancelButton:u,hideRetryButton:h,recoveredState:c,uploadState:p,totalProgress:g,files:f,supportsUploadProgress:v,hideAfterFinish:m,isSomeGhost:w,doneButtonHandler:C,isUploadStarted:y,i18n:F,startUpload:T,uppy:U,isAllComplete:A,showProgressDetails:E,numUploads:B,complete:ce,totalSize:De,totalETA:pe,totalUploadedSize:fe}=i;function J(){switch(p){case Ne:case Ue:{const me=Ie(f);return me.mode==="determinate"?me.value*100:g}case ht:return null;case ge:return v?g:null;default:return g}}function W(){switch(p){case Ne:case Ue:{const{mode:me}=Ie(f);return me==="indeterminate"}case ge:return!v;default:return!1}}function ke(){if(c)return!1;switch(p){case ct:return o||e===0;case ye:return m;default:return!1}}const ie=J(),pi=ke(),Oe=ie??100,fi=!a&&e&&!s&&!n&&t&&!o,mi=!u&&p!==ct&&p!==ye,gi=r&&!d&&p===ge,yi=a&&!A&&!h,bi=C&&p===ye,vi=x("uppy-StatusBar-progress",{"is-indeterminate":W()}),wi=x("uppy-StatusBar",`is-${p}`,{"has-ghosts":w});return l("div",{className:wi,"aria-hidden":pi},l("div",{className:vi,style:{width:`${Oe}%`},role:"progressbar","aria-label":`${Oe}%`,"aria-valuetext":`${Oe}%`,"aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":ie}),(()=>{switch(p){case Ue:case Ne:return l(Mi,{progress:Ie(f)});case ye:return l(zi,{i18n:F});case ht:return l($i,{error:a,i18n:F,numUploads:B,complete:ce});case ge:return l(_i,{i18n:F,supportsUploadProgress:v,totalProgress:g,showProgressDetails:E,isUploadStarted:y,isAllComplete:A,isAllPaused:n,newFiles:e,numUploads:B,complete:ce,totalUploadedSize:fe,totalSize:De,totalETA:pe,startUpload:T});default:return null}})(),l("div",{className:"uppy-StatusBar-actions"},c||fi?l(Bi,{newFiles:e,isUploadStarted:y,recoveredState:c,i18n:F,isSomeGhost:w,startUpload:T,uploadState:p}):null,yi?l(Ii,{i18n:F,uppy:U}):null,gi?l(Ni,{isAllPaused:n,i18n:F,isAllComplete:A,resumableUploads:r,uppy:U}):null,mi?l(Ui,{i18n:F,uppy:U}):null,bi?l(Ri,{i18n:F,doneButtonHandler:C}):null))}Vt.defaultProps={doneButtonHandler:void 0,hideAfterFinish:!1,hideCancelButton:!1,hidePauseResumeButton:!1,hideRetryButton:!1,hideUploadButton:void 0,showProgressDetails:void 0};const Hi={strings:{uploading:"Uploading",complete:"Complete",uploadFailed:"Upload failed",paused:"Paused",retry:"Retry",cancel:"Cancel",pause:"Pause",resume:"Resume",done:"Done",filesUploadedOfTotal:{0:"%{complete} of %{smart_count} file uploaded",1:"%{complete} of %{smart_count} files uploaded"},dataUploadedOfTotal:"%{complete} of %{total}",xTimeLeft:"%{time} left",uploadXFiles:{0:"Upload %{smart_count} file",1:"Upload %{smart_count} files"},uploadXNewFiles:{0:"Upload +%{smart_count} file",1:"Upload +%{smart_count} files"},upload:"Upload",retryUpload:"Retry upload",xMoreFilesAdded:{0:"%{smart_count} more file added",1:"%{smart_count} more files added"},showErrorDetails:"Show error details"}};function S(i,e){if(!Object.prototype.hasOwnProperty.call(i,e))throw new TypeError("attempted to use private field on non-instance");return i}var Vi=0;function te(i){return"__private_"+Vi+++"_"+i}const qi={version:"3.3.3"},ji=2e3,Wi=2e3;function Gi(i,e,t,s){if(i)return $.STATE_ERROR;if(e)return $.STATE_COMPLETE;if(t)return $.STATE_WAITING;let n=$.STATE_WAITING;const r=Object.keys(s);for(let a=0;a<r.length;a++){const{progress:o}=s[r[a]];if(o.uploadStarted&&!o.uploadComplete)return $.STATE_UPLOADING;o.preprocess&&(n=$.STATE_PREPROCESSING),o.postprocess&&n!==$.STATE_PREPROCESSING&&(n=$.STATE_POSTPROCESSING)}return n}const Ki={target:"body",hideUploadButton:!1,hideRetryButton:!1,hidePauseResumeButton:!1,hideCancelButton:!1,showProgressDetails:!1,hideAfterFinish:!0,doneButtonHandler:null};var N=te("lastUpdateTime"),R=te("previousUploadedBytes"),q=te("previousSpeed"),I=te("previousETA"),Re=te("computeSmoothETA"),se=te("onUploadStart");let qt=class extends Ee{constructor(e,t){super(e,{...Ki,...t}),Object.defineProperty(this,Re,{value:Xi}),Object.defineProperty(this,N,{writable:!0,value:void 0}),Object.defineProperty(this,R,{writable:!0,value:void 0}),Object.defineProperty(this,q,{writable:!0,value:void 0}),Object.defineProperty(this,I,{writable:!0,value:void 0}),this.startUpload=()=>this.uppy.upload().catch(()=>{}),Object.defineProperty(this,se,{writable:!0,value:()=>{const{recoveredState:s}=this.uppy.getState();if(S(this,q)[q]=null,S(this,I)[I]=null,s){S(this,R)[R]=Object.values(s.files).reduce((n,r)=>{let{progress:a}=r;return n+a.bytesUploaded},0),this.uppy.emit("restore-confirmed");return}S(this,N)[N]=performance.now(),S(this,R)[R]=0}}),this.id=this.opts.id||"StatusBar",this.title="StatusBar",this.type="progressindicator",this.defaultLocale=Hi,this.i18nInit(),this.render=this.render.bind(this),this.install=this.install.bind(this)}render(e){const{capabilities:t,files:s,allowNewUpload:n,totalProgress:r,error:a,recoveredState:o}=e,{newFiles:d,startedFiles:u,completeFiles:h,isUploadStarted:c,isAllComplete:p,isAllErrored:g,isAllPaused:f,isUploadInProgress:v,isSomeGhost:m}=this.uppy.getObjectOfFilesPerState(),w=o?Object.values(s):d,C=!!t.resumableUploads,y=t.uploadProgress!==!1;let F=0,T=0;u.forEach(A=>{F+=A.progress.bytesTotal||0,T+=A.progress.bytesUploaded||0});const U=S(this,Re)[Re]({uploaded:T,total:F,remaining:F-T});return Vt({error:a,uploadState:Gi(a,p,o,e.files||{}),allowNewUpload:n,totalProgress:r,totalSize:F,totalUploadedSize:T,isAllComplete:!1,isAllPaused:f,isAllErrored:g,isUploadStarted:c,isUploadInProgress:v,isSomeGhost:m,recoveredState:o,complete:h.length,newFiles:w.length,numUploads:u.length,totalETA:U,files:s,i18n:this.i18n,uppy:this.uppy,startUpload:this.startUpload,doneButtonHandler:this.opts.doneButtonHandler,resumableUploads:C,supportsUploadProgress:y,showProgressDetails:this.opts.showProgressDetails,hideUploadButton:this.opts.hideUploadButton,hideRetryButton:this.opts.hideRetryButton,hidePauseResumeButton:this.opts.hidePauseResumeButton,hideCancelButton:this.opts.hideCancelButton,hideAfterFinish:this.opts.hideAfterFinish,isTargetDOMEl:this.isTargetDOMEl})}onMount(){const e=this.el;Si(e)||(e.dir="ltr")}install(){const{target:e}=this.opts;e&&this.mount(e,this),this.uppy.on("upload",S(this,se)[se]),S(this,N)[N]=performance.now(),S(this,R)[R]=this.uppy.getFiles().reduce((t,s)=>t+s.progress.bytesUploaded,0)}uninstall(){this.unmount(),this.uppy.off("upload",S(this,se)[se])}};function Xi(i){var e,t;if(i.total===0||i.remaining===0)return 0;(t=(e=S(this,N))[N])!=null||(e[N]=performance.now());const s=performance.now()-S(this,N)[N];if(s===0){var n;return Math.round(((n=S(this,I)[I])!=null?n:0)/100)/10}const r=i.uploaded-S(this,R)[R];if(S(this,R)[R]=i.uploaded,r<=0){var a;return Math.round(((a=S(this,I)[I])!=null?a:0)/100)/10}const o=r/s,d=S(this,q)[q]==null?o:dt(o,S(this,q)[q],ji,s);S(this,q)[q]=d;const u=i.remaining/d,h=Math.max(S(this,I)[I]-s,0),c=S(this,I)[I]==null?u:dt(u,h,Wi,s);return S(this,I)[I]=c,S(this,N)[N]=performance.now(),Math.round(c/100)/10}qt.VERSION=qi.version;const pt=300;class Yi extends he{constructor(){super(...arguments),this.ref=Fi()}componentWillEnter(e){this.ref.current.style.opacity="1",this.ref.current.style.transform="none",setTimeout(e,pt)}componentWillLeave(e){this.ref.current.style.opacity="0",this.ref.current.style.transform="translateY(350%)",setTimeout(e,pt)}render(){const{children:e}=this.props;return l("div",{className:"uppy-Informer-animated",ref:this.ref},e)}}function Qi(i,e){return Object.assign(i,e)}function Ji(i,e){var t;return(t=i==null?void 0:i.key)!=null?t:e}function Zi(i,e){const t=i._ptgLinkedRefs||(i._ptgLinkedRefs={});return t[e]||(t[e]=s=>{i.refs[e]=s})}function ne(i){const e={};for(let t=0;t<i.length;t++)if(i[t]!=null){const s=Ji(i[t],t.toString(36));e[s]=i[t]}return e}function es(i,e){i=i||{},e=e||{};const t=a=>e.hasOwnProperty(a)?e[a]:i[a],s={};let n=[];for(const a in i)e.hasOwnProperty(a)?n.length&&(s[a]=n,n=[]):n.push(a);const r={};for(const a in e){if(s.hasOwnProperty(a))for(let o=0;o<s[a].length;o++){const d=s[a][o];r[s[a][o]]=t(d)}r[a]=t(a)}for(let a=0;a<n.length;a++)r[n[a]]=t(n[a]);return r}const ts=i=>i;class jt extends he{constructor(e,t){super(e,t),this.refs={},this.state={children:ne(Y(Y(this.props.children))||[])},this.performAppear=this.performAppear.bind(this),this.performEnter=this.performEnter.bind(this),this.performLeave=this.performLeave.bind(this)}componentWillMount(){this.currentlyTransitioningKeys={},this.keysToAbortLeave=[],this.keysToEnter=[],this.keysToLeave=[]}componentDidMount(){const e=this.state.children;for(const t in e)e[t]&&this.performAppear(t)}componentWillReceiveProps(e){const t=ne(Y(e.children)||[]),s=this.state.children;this.setState(r=>({children:es(r.children,t)}));let n;for(n in t)if(t.hasOwnProperty(n)){const r=s&&s.hasOwnProperty(n);t[n]&&r&&this.currentlyTransitioningKeys[n]?(this.keysToEnter.push(n),this.keysToAbortLeave.push(n)):t[n]&&!r&&!this.currentlyTransitioningKeys[n]&&this.keysToEnter.push(n)}for(n in s)if(s.hasOwnProperty(n)){const r=t&&t.hasOwnProperty(n);s[n]&&!r&&!this.currentlyTransitioningKeys[n]&&this.keysToLeave.push(n)}}componentDidUpdate(){const{keysToEnter:e}=this;this.keysToEnter=[],e.forEach(this.performEnter);const{keysToLeave:t}=this;this.keysToLeave=[],t.forEach(this.performLeave)}_finishAbort(e){const t=this.keysToAbortLeave.indexOf(e);t!==-1&&this.keysToAbortLeave.splice(t,1)}performAppear(e){this.currentlyTransitioningKeys[e]=!0;const t=this.refs[e];t!=null&&t.componentWillAppear?t.componentWillAppear(this._handleDoneAppearing.bind(this,e)):this._handleDoneAppearing(e)}_handleDoneAppearing(e){const t=this.refs[e];t!=null&&t.componentDidAppear&&t.componentDidAppear(),delete this.currentlyTransitioningKeys[e],this._finishAbort(e);const s=ne(Y(this.props.children)||[]);(!s||!s.hasOwnProperty(e))&&this.performLeave(e)}performEnter(e){this.currentlyTransitioningKeys[e]=!0;const t=this.refs[e];t!=null&&t.componentWillEnter?t.componentWillEnter(this._handleDoneEntering.bind(this,e)):this._handleDoneEntering(e)}_handleDoneEntering(e){const t=this.refs[e];t!=null&&t.componentDidEnter&&t.componentDidEnter(),delete this.currentlyTransitioningKeys[e],this._finishAbort(e);const s=ne(Y(this.props.children)||[]);(!s||!s.hasOwnProperty(e))&&this.performLeave(e)}performLeave(e){if(this.keysToAbortLeave.indexOf(e)!==-1)return;this.currentlyTransitioningKeys[e]=!0;const s=this.refs[e];s!=null&&s.componentWillLeave?s.componentWillLeave(this._handleDoneLeaving.bind(this,e)):this._handleDoneLeaving(e)}_handleDoneLeaving(e){if(this.keysToAbortLeave.indexOf(e)!==-1)return;const s=this.refs[e];s!=null&&s.componentDidLeave&&s.componentDidLeave(),delete this.currentlyTransitioningKeys[e];const n=ne(Y(this.props.children)||[]);if(n&&n.hasOwnProperty(e))this.performEnter(e);else{const r=Qi({},this.state.children);delete r[e],this.setState({children:r})}}render(e,t){let{childFactory:s,transitionLeave:n,transitionName:r,transitionAppear:a,transitionEnter:o,transitionLeaveTimeout:d,transitionEnterTimeout:u,transitionAppearTimeout:h,component:c,...p}=e,{children:g}=t;const f=Object.entries(g).map(v=>{let[m,w]=v;if(!w)return;const C=Zi(this,m);return Lt(s(w),{ref:C,key:m})}).filter(Boolean);return l(c,p,f)}}jt.defaultProps={component:"span",childFactory:ts};const is={version:"3.1.0"};class Wt extends Ee{constructor(e,t){super(e,t),this.render=s=>l("div",{className:"uppy uppy-Informer"},l(jt,null,s.info.map(n=>l(Yi,{key:n.message},l("p",{role:"alert"},n.message," ",n.details&&l("span",{"aria-label":n.details,"data-microtip-position":"top-left","data-microtip-size":"medium",role:"tooltip",onClick:()=>alert(`${n.message} 

 ${n.details}`)},"?")))))),this.type="progressindicator",this.id=this.opts.id||"Informer",this.title="Informer"}install(){const{target:e}=this.opts;e&&this.mount(e,this)}}Wt.VERSION=is.version;const ss=/^data:([^/]+\/[^,;]+(?:[^,]*?))(;base64)?,([\s\S]*)$/;function ns(i,e,t){var s,n;const r=ss.exec(i),a=(s=(n=e.mimeType)!=null?n:r==null?void 0:r[1])!=null?s:"plain/text";let o;if((r==null?void 0:r[2])!=null){const d=atob(decodeURIComponent(r[3])),u=new Uint8Array(d.length);for(let h=0;h<d.length;h++)u[h]=d.charCodeAt(h);o=[u]}else(r==null?void 0:r[3])!=null&&(o=[decodeURIComponent(r[3])]);return t?new File(o,e.name||"",{type:a}):new Blob(o,{type:a})}function ft(i){return i.startsWith("blob:")}function mt(i){return i?/^[^/]+\/(jpe?g|gif|png|svg|svg\+xml|bmp|webp|avif)$/.test(i):!1}function b(i,e,t){return e in i?Object.defineProperty(i,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):i[e]=t,i}var Gt=typeof self<"u"?self:global;const ue=typeof navigator<"u",as=ue&&typeof HTMLImageElement>"u",gt=!(typeof global>"u"||typeof process>"u"||!process.versions||!process.versions.node),Kt=Gt.Buffer,Xt=!!Kt,rs=i=>i!==void 0;function Yt(i){return i===void 0||(i instanceof Map?i.size===0:Object.values(i).filter(rs).length===0)}function k(i){let e=new Error(i);throw delete e.stack,e}function yt(i){let e=function(t){let s=0;return t.ifd0.enabled&&(s+=1024),t.exif.enabled&&(s+=2048),t.makerNote&&(s+=2048),t.userComment&&(s+=1024),t.gps.enabled&&(s+=512),t.interop.enabled&&(s+=100),t.ifd1.enabled&&(s+=1024),s+2048}(i);return i.jfif.enabled&&(e+=50),i.xmp.enabled&&(e+=2e4),i.iptc.enabled&&(e+=14e3),i.icc.enabled&&(e+=6e3),e}const Me=i=>String.fromCharCode.apply(null,i),bt=typeof TextDecoder<"u"?new TextDecoder("utf-8"):void 0;class L{static from(e,t){return e instanceof this&&e.le===t?e:new L(e,void 0,void 0,t)}constructor(e,t=0,s,n){if(typeof n=="boolean"&&(this.le=n),Array.isArray(e)&&(e=new Uint8Array(e)),e===0)this.byteOffset=0,this.byteLength=0;else if(e instanceof ArrayBuffer){s===void 0&&(s=e.byteLength-t);let r=new DataView(e,t,s);this._swapDataView(r)}else if(e instanceof Uint8Array||e instanceof DataView||e instanceof L){s===void 0&&(s=e.byteLength-t),(t+=e.byteOffset)+s>e.byteOffset+e.byteLength&&k("Creating view outside of available memory in ArrayBuffer");let r=new DataView(e.buffer,t,s);this._swapDataView(r)}else if(typeof e=="number"){let r=new DataView(new ArrayBuffer(e));this._swapDataView(r)}else k("Invalid input argument for BufferView: "+e)}_swapArrayBuffer(e){this._swapDataView(new DataView(e))}_swapBuffer(e){this._swapDataView(new DataView(e.buffer,e.byteOffset,e.byteLength))}_swapDataView(e){this.dataView=e,this.buffer=e.buffer,this.byteOffset=e.byteOffset,this.byteLength=e.byteLength}_lengthToEnd(e){return this.byteLength-e}set(e,t,s=L){return e instanceof DataView||e instanceof L?e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength):e instanceof ArrayBuffer&&(e=new Uint8Array(e)),e instanceof Uint8Array||k("BufferView.set(): Invalid data argument."),this.toUint8().set(e,t),new s(this,t,e.byteLength)}subarray(e,t){return t=t||this._lengthToEnd(e),new L(this,e,t)}toUint8(){return new Uint8Array(this.buffer,this.byteOffset,this.byteLength)}getUint8Array(e,t){return new Uint8Array(this.buffer,this.byteOffset+e,t)}getString(e=0,t=this.byteLength){return n=this.getUint8Array(e,t),bt?bt.decode(n):Xt?Buffer.from(n).toString("utf8"):decodeURIComponent(escape(Me(n)));var n}getLatin1String(e=0,t=this.byteLength){let s=this.getUint8Array(e,t);return Me(s)}getUnicodeString(e=0,t=this.byteLength){const s=[];for(let n=0;n<t&&e+n<this.byteLength;n+=2)s.push(this.getUint16(e+n));return Me(s)}getInt8(e){return this.dataView.getInt8(e)}getUint8(e){return this.dataView.getUint8(e)}getInt16(e,t=this.le){return this.dataView.getInt16(e,t)}getInt32(e,t=this.le){return this.dataView.getInt32(e,t)}getUint16(e,t=this.le){return this.dataView.getUint16(e,t)}getUint32(e,t=this.le){return this.dataView.getUint32(e,t)}getFloat32(e,t=this.le){return this.dataView.getFloat32(e,t)}getFloat64(e,t=this.le){return this.dataView.getFloat64(e,t)}getFloat(e,t=this.le){return this.dataView.getFloat32(e,t)}getDouble(e,t=this.le){return this.dataView.getFloat64(e,t)}getUintBytes(e,t,s){switch(t){case 1:return this.getUint8(e,s);case 2:return this.getUint16(e,s);case 4:return this.getUint32(e,s);case 8:return this.getUint64&&this.getUint64(e,s)}}getUint(e,t,s){switch(t){case 8:return this.getUint8(e,s);case 16:return this.getUint16(e,s);case 32:return this.getUint32(e,s);case 64:return this.getUint64&&this.getUint64(e,s)}}toString(e){return this.dataView.toString(e,this.constructor.name)}ensureChunk(){}}function Qe(i,e){k(`${i} '${e}' was not loaded, try using full build of exifr.`)}class st extends Map{constructor(e){super(),this.kind=e}get(e,t){return this.has(e)||Qe(this.kind,e),t&&(e in t||function(s,n){k(`Unknown ${s} '${n}'.`)}(this.kind,e),t[e].enabled||Qe(this.kind,e)),super.get(e)}keyList(){return Array.from(this.keys())}}var Qt=new st("file parser"),M=new st("segment parser"),nt=new st("file reader");let os=Gt.fetch;function vt(i,e){return(t=i).startsWith("data:")||t.length>1e4?Ze(i,e,"base64"):gt&&i.includes("://")?Je(i,e,"url",wt):gt?Ze(i,e,"fs"):ue?Je(i,e,"url",wt):void k("Invalid input argument");var t}async function Je(i,e,t,s){return nt.has(t)?Ze(i,e,t):s?async function(n,r){let a=await r(n);return new L(a)}(i,s):void k(`Parser ${t} is not loaded`)}async function Ze(i,e,t){let s=new(nt.get(t))(i,e);return await s.read(),s}const wt=i=>os(i).then(e=>e.arrayBuffer()),et=i=>new Promise((e,t)=>{let s=new FileReader;s.onloadend=()=>e(s.result||new ArrayBuffer),s.onerror=t,s.readAsArrayBuffer(i)}),at=new Map,ls=new Map,ds=new Map,be=["chunked","firstChunkSize","firstChunkSizeNode","firstChunkSizeBrowser","chunkSize","chunkLimit"],Jt=["jfif","xmp","icc","iptc","ihdr"],tt=["tiff",...Jt],D=["ifd0","ifd1","exif","gps","interop"],ve=[...tt,...D],we=["makerNote","userComment"],Zt=["translateKeys","translateValues","reviveValues","multiSegment"],Fe=[...Zt,"sanitize","mergeOutput","silentErrors"];class ei{get translate(){return this.translateKeys||this.translateValues||this.reviveValues}}class ae extends ei{get needed(){return this.enabled||this.deps.size>0}constructor(e,t,s,n){if(super(),b(this,"enabled",!1),b(this,"skip",new Set),b(this,"pick",new Set),b(this,"deps",new Set),b(this,"translateKeys",!1),b(this,"translateValues",!1),b(this,"reviveValues",!1),this.key=e,this.enabled=t,this.parse=this.enabled,this.applyInheritables(n),this.canBeFiltered=D.includes(e),this.canBeFiltered&&(this.dict=at.get(e)),s!==void 0)if(Array.isArray(s))this.parse=this.enabled=!0,this.canBeFiltered&&s.length>0&&this.translateTagSet(s,this.pick);else if(typeof s=="object"){if(this.enabled=!0,this.parse=s.parse!==!1,this.canBeFiltered){let{pick:r,skip:a}=s;r&&r.length>0&&this.translateTagSet(r,this.pick),a&&a.length>0&&this.translateTagSet(a,this.skip)}this.applyInheritables(s)}else s===!0||s===!1?this.parse=this.enabled=s:k(`Invalid options argument: ${s}`)}applyInheritables(e){let t,s;for(t of Zt)s=e[t],s!==void 0&&(this[t]=s)}translateTagSet(e,t){if(this.dict){let s,n,{tagKeys:r,tagValues:a}=this.dict;for(s of e)typeof s=="string"?(n=a.indexOf(s),n===-1&&(n=r.indexOf(Number(s))),n!==-1&&t.add(Number(r[n]))):t.add(s)}else for(let s of e)t.add(s)}finalizeFilters(){!this.enabled&&this.deps.size>0?(this.enabled=!0,Ae(this.pick,this.deps)):this.enabled&&this.pick.size>0&&Ae(this.pick,this.deps)}}var O={jfif:!1,tiff:!0,xmp:!1,icc:!1,iptc:!1,ifd0:!0,ifd1:!1,exif:!0,gps:!0,interop:!1,ihdr:void 0,makerNote:!1,userComment:!1,multiSegment:!1,skip:[],pick:[],translateKeys:!0,translateValues:!0,reviveValues:!0,sanitize:!0,mergeOutput:!0,silentErrors:!0,chunked:!0,firstChunkSize:void 0,firstChunkSizeNode:512,firstChunkSizeBrowser:65536,chunkSize:65536,chunkLimit:5},Ft=new Map;class rt extends ei{static useCached(e){let t=Ft.get(e);return t!==void 0||(t=new this(e),Ft.set(e,t)),t}constructor(e){super(),e===!0?this.setupFromTrue():e===void 0?this.setupFromUndefined():Array.isArray(e)?this.setupFromArray(e):typeof e=="object"?this.setupFromObject(e):k(`Invalid options argument ${e}`),this.firstChunkSize===void 0&&(this.firstChunkSize=ue?this.firstChunkSizeBrowser:this.firstChunkSizeNode),this.mergeOutput&&(this.ifd1.enabled=!1),this.filterNestedSegmentTags(),this.traverseTiffDependencyTree(),this.checkLoadedPlugins()}setupFromUndefined(){let e;for(e of be)this[e]=O[e];for(e of Fe)this[e]=O[e];for(e of we)this[e]=O[e];for(e of ve)this[e]=new ae(e,O[e],void 0,this)}setupFromTrue(){let e;for(e of be)this[e]=O[e];for(e of Fe)this[e]=O[e];for(e of we)this[e]=!0;for(e of ve)this[e]=new ae(e,!0,void 0,this)}setupFromArray(e){let t;for(t of be)this[t]=O[t];for(t of Fe)this[t]=O[t];for(t of we)this[t]=O[t];for(t of ve)this[t]=new ae(t,!1,void 0,this);this.setupGlobalFilters(e,void 0,D)}setupFromObject(e){let t;for(t of(D.ifd0=D.ifd0||D.image,D.ifd1=D.ifd1||D.thumbnail,Object.assign(this,e),be))this[t]=Le(e[t],O[t]);for(t of Fe)this[t]=Le(e[t],O[t]);for(t of we)this[t]=Le(e[t],O[t]);for(t of tt)this[t]=new ae(t,O[t],e[t],this);for(t of D)this[t]=new ae(t,O[t],e[t],this.tiff);this.setupGlobalFilters(e.pick,e.skip,D,ve),e.tiff===!0?this.batchEnableWithBool(D,!0):e.tiff===!1?this.batchEnableWithUserValue(D,e):Array.isArray(e.tiff)?this.setupGlobalFilters(e.tiff,void 0,D):typeof e.tiff=="object"&&this.setupGlobalFilters(e.tiff.pick,e.tiff.skip,D)}batchEnableWithBool(e,t){for(let s of e)this[s].enabled=t}batchEnableWithUserValue(e,t){for(let s of e){let n=t[s];this[s].enabled=n!==!1&&n!==void 0}}setupGlobalFilters(e,t,s,n=s){if(e&&e.length){for(let a of n)this[a].enabled=!1;let r=Pt(e,s);for(let[a,o]of r)Ae(this[a].pick,o),this[a].enabled=!0}else if(t&&t.length){let r=Pt(t,s);for(let[a,o]of r)Ae(this[a].skip,o)}}filterNestedSegmentTags(){let{ifd0:e,exif:t,xmp:s,iptc:n,icc:r}=this;this.makerNote?t.deps.add(37500):t.skip.add(37500),this.userComment?t.deps.add(37510):t.skip.add(37510),s.enabled||e.skip.add(700),n.enabled||e.skip.add(33723),r.enabled||e.skip.add(34675)}traverseTiffDependencyTree(){let{ifd0:e,exif:t,gps:s,interop:n}=this;n.needed&&(t.deps.add(40965),e.deps.add(40965)),t.needed&&e.deps.add(34665),s.needed&&e.deps.add(34853),this.tiff.enabled=D.some(r=>this[r].enabled===!0)||this.makerNote||this.userComment;for(let r of D)this[r].finalizeFilters()}get onlyTiff(){return!Jt.map(e=>this[e].enabled).some(e=>e===!0)&&this.tiff.enabled}checkLoadedPlugins(){for(let e of tt)this[e].enabled&&!M.has(e)&&Qe("segment parser",e)}}function Pt(i,e){let t,s,n,r,a=[];for(n of e){for(r of(t=at.get(n),s=[],t))(i.includes(r[0])||i.includes(r[1]))&&s.push(r[0]);s.length&&a.push([n,s])}return a}function Le(i,e){return i!==void 0?i:e!==void 0?e:void 0}function Ae(i,e){for(let t of e)i.add(t)}b(rt,"default",O);class us{constructor(e){b(this,"parsers",{}),b(this,"output",{}),b(this,"errors",[]),b(this,"pushToErrors",t=>this.errors.push(t)),this.options=rt.useCached(e)}async read(e){this.file=await function(t,s){return typeof t=="string"?vt(t,s):ue&&!as&&t instanceof HTMLImageElement?vt(t.src,s):t instanceof Uint8Array||t instanceof ArrayBuffer||t instanceof DataView?new L(t):ue&&t instanceof Blob?Je(t,s,"blob",et):void k("Invalid input argument")}(e,this.options)}setup(){if(this.fileParser)return;let{file:e}=this,t=e.getUint16(0);for(let[s,n]of Qt)if(n.canHandle(e,t))return this.fileParser=new n(this.options,this.file,this.parsers),e[s]=!0;this.file.close&&this.file.close(),k("Unknown file format")}async parse(){let{output:e,errors:t}=this;return this.setup(),this.options.silentErrors?(await this.executeParsers().catch(this.pushToErrors),t.push(...this.fileParser.errors)):await this.executeParsers(),this.file.close&&this.file.close(),this.options.silentErrors&&t.length>0&&(e.errors=t),Yt(s=e)?void 0:s;var s}async executeParsers(){let{output:e}=this;await this.fileParser.parse();let t=Object.values(this.parsers).map(async s=>{let n=await s.parse();s.assignToOutput(e,n)});this.options.silentErrors&&(t=t.map(s=>s.catch(this.pushToErrors))),await Promise.all(t)}async extractThumbnail(){this.setup();let{options:e,file:t}=this,s=M.get("tiff",e);var n;if(t.tiff?n={start:0,type:"tiff"}:t.jpeg&&(n=await this.fileParser.getOrFindSegment("tiff")),n===void 0)return;let r=await this.fileParser.ensureSegmentChunk(n),a=this.parsers.tiff=new s(r,e,t),o=await a.extractThumbnail();return t.close&&t.close(),o}}class ee{static findPosition(e,t){let s=e.getUint16(t+2)+2,n=typeof this.headerLength=="function"?this.headerLength(e,t,s):this.headerLength,r=t+n,a=s-n;return{offset:t,length:s,headerLength:n,start:r,size:a,end:r+a}}static parse(e,t={}){return new this(e,new rt({[this.type]:t}),e).parse()}normalizeInput(e){return e instanceof L?e:new L(e)}constructor(e,t={},s){b(this,"errors",[]),b(this,"raw",new Map),b(this,"handleError",n=>{if(!this.options.silentErrors)throw n;this.errors.push(n.message)}),this.chunk=this.normalizeInput(e),this.file=s,this.type=this.constructor.type,this.globalOptions=this.options=t,this.localOptions=t[this.type],this.canTranslate=this.localOptions&&this.localOptions.translate}translate(){this.canTranslate&&(this.translated=this.translateBlock(this.raw,this.type))}get output(){return this.translated?this.translated:this.raw?Object.fromEntries(this.raw):void 0}translateBlock(e,t){let s=ds.get(t),n=ls.get(t),r=at.get(t),a=this.options[t],o=a.reviveValues&&!!s,d=a.translateValues&&!!n,u=a.translateKeys&&!!r,h={};for(let[c,p]of e)o&&s.has(c)?p=s.get(c)(p):d&&n.has(c)&&(p=this.translateValue(p,n.get(c))),u&&r.has(c)&&(c=r.get(c)||c),h[c]=p;return h}translateValue(e,t){return t[e]||t.DEFAULT||e}assignToOutput(e,t){this.assignObjectToOutput(e,this.constructor.type,t)}assignObjectToOutput(e,t,s){if(this.globalOptions.mergeOutput)return Object.assign(e,s);e[t]?Object.assign(e[t],s):e[t]=s}}b(ee,"headerLength",4),b(ee,"type",void 0),b(ee,"multiSegment",!1),b(ee,"canHandle",()=>!1);function hs(i){return i===192||i===194||i===196||i===219||i===221||i===218||i===254}function cs(i){return i>=224&&i<=239}function ps(i,e,t){for(let[s,n]of M)if(n.canHandle(i,e,t))return s}class Ct extends class{constructor(e,t,s){b(this,"errors",[]),b(this,"ensureSegmentChunk",async n=>{let r=n.start,a=n.size||65536;if(this.file.chunked)if(this.file.available(r,a))n.chunk=this.file.subarray(r,a);else try{n.chunk=await this.file.readChunk(r,a)}catch(o){k(`Couldn't read segment: ${JSON.stringify(n)}. ${o.message}`)}else this.file.byteLength>r+a?n.chunk=this.file.subarray(r,a):n.size===void 0?n.chunk=this.file.subarray(r):k("Segment unreachable: "+JSON.stringify(n));return n.chunk}),this.extendOptions&&this.extendOptions(e),this.options=e,this.file=t,this.parsers=s}injectSegment(e,t){this.options[e].enabled&&this.createParser(e,t)}createParser(e,t){let s=new(M.get(e))(t,this.options,this.file);return this.parsers[e]=s}createParsers(e){for(let t of e){let{type:s,chunk:n}=t,r=this.options[s];if(r&&r.enabled){let a=this.parsers[s];a&&a.append||a||this.createParser(s,n)}}}async readSegments(e){let t=e.map(this.ensureSegmentChunk);await Promise.all(t)}}{constructor(...e){super(...e),b(this,"appSegments",[]),b(this,"jpegSegments",[]),b(this,"unknownSegments",[])}static canHandle(e,t){return t===65496}async parse(){await this.findAppSegments(),await this.readSegments(this.appSegments),this.mergeMultiSegments(),this.createParsers(this.mergedAppSegments||this.appSegments)}setupSegmentFinderArgs(e){e===!0?(this.findAll=!0,this.wanted=new Set(M.keyList())):(e=e===void 0?M.keyList().filter(t=>this.options[t].enabled):e.filter(t=>this.options[t].enabled&&M.has(t)),this.findAll=!1,this.remaining=new Set(e),this.wanted=new Set(e)),this.unfinishedMultiSegment=!1}async findAppSegments(e=0,t){this.setupSegmentFinderArgs(t);let{file:s,findAll:n,wanted:r,remaining:a}=this;if(!n&&this.file.chunked&&(n=Array.from(r).some(o=>{let d=M.get(o),u=this.options[o];return d.multiSegment&&u.multiSegment}),n&&await this.file.readWhole()),e=this.findAppSegmentsInRange(e,s.byteLength),!this.options.onlyTiff&&s.chunked){let o=!1;for(;a.size>0&&!o&&(s.canReadNextChunk||this.unfinishedMultiSegment);){let{nextChunkOffset:d}=s,u=this.appSegments.some(h=>!this.file.available(h.offset||h.start,h.length||h.size));if(o=e>d&&!u?!await s.readNextChunk(e):!await s.readNextChunk(d),(e=this.findAppSegmentsInRange(e,s.byteLength))===void 0)return}}}findAppSegmentsInRange(e,t){t-=2;let s,n,r,a,o,d,{file:u,findAll:h,wanted:c,remaining:p,options:g}=this;for(;e<t;e++)if(u.getUint8(e)===255){if(s=u.getUint8(e+1),cs(s)){if(n=u.getUint16(e+2),r=ps(u,e,n),r&&c.has(r)&&(a=M.get(r),o=a.findPosition(u,e),d=g[r],o.type=r,this.appSegments.push(o),!h&&(a.multiSegment&&d.multiSegment?(this.unfinishedMultiSegment=o.chunkNumber<o.chunkCount,this.unfinishedMultiSegment||p.delete(r)):p.delete(r),p.size===0)))break;g.recordUnknownSegments&&(o=ee.findPosition(u,e),o.marker=s,this.unknownSegments.push(o)),e+=n+1}else if(hs(s)){if(n=u.getUint16(e+2),s===218&&g.stopAfterSos!==!1)return;g.recordJpegSegments&&this.jpegSegments.push({offset:e,length:n,marker:s}),e+=n+1}}return e}mergeMultiSegments(){if(!this.appSegments.some(t=>t.multiSegment))return;let e=function(t,s){let n,r,a,o=new Map;for(let d=0;d<t.length;d++)n=t[d],r=n[s],o.has(r)?a=o.get(r):o.set(r,a=[]),a.push(n);return Array.from(o)}(this.appSegments,"type");this.mergedAppSegments=e.map(([t,s])=>{let n=M.get(t,this.options);return n.handleMultiSegments?{type:t,chunk:n.handleMultiSegments(s)}:s[0]})}getSegment(e){return this.appSegments.find(t=>t.type===e)}async getOrFindSegment(e){let t=this.getSegment(e);return t===void 0&&(await this.findAppSegments(0,[e]),t=this.getSegment(e)),t}}b(Ct,"type","jpeg"),Qt.set("jpeg",Ct);const fs=[void 0,1,1,2,4,8,1,1,2,4,8,4,8,4];class ms extends ee{parseHeader(){var e=this.chunk.getUint16();e===18761?this.le=!0:e===19789&&(this.le=!1),this.chunk.le=this.le,this.headerParsed=!0}parseTags(e,t,s=new Map){let{pick:n,skip:r}=this.options[t];n=new Set(n);let a=n.size>0,o=r.size===0,d=this.chunk.getUint16(e);e+=2;for(let u=0;u<d;u++){let h=this.chunk.getUint16(e);if(a){if(n.has(h)&&(s.set(h,this.parseTag(e,h,t)),n.delete(h),n.size===0))break}else!o&&r.has(h)||s.set(h,this.parseTag(e,h,t));e+=12}return s}parseTag(e,t,s){let{chunk:n}=this,r=n.getUint16(e+2),a=n.getUint32(e+4),o=fs[r];if(o*a<=4?e+=8:e=n.getUint32(e+8),(r<1||r>13)&&k(`Invalid TIFF value type. block: ${s.toUpperCase()}, tag: ${t.toString(16)}, type: ${r}, offset ${e}`),e>n.byteLength&&k(`Invalid TIFF value offset. block: ${s.toUpperCase()}, tag: ${t.toString(16)}, type: ${r}, offset ${e} is outside of chunk size ${n.byteLength}`),r===1)return n.getUint8Array(e,a);if(r===2)return(d=function(u){for(;u.endsWith("\0");)u=u.slice(0,-1);return u}(d=n.getString(e,a)).trim())===""?void 0:d;var d;if(r===7)return n.getUint8Array(e,a);if(a===1)return this.parseTagValue(r,e);{let u=new(function(c){switch(c){case 1:return Uint8Array;case 3:return Uint16Array;case 4:return Uint32Array;case 5:return Array;case 6:return Int8Array;case 8:return Int16Array;case 9:return Int32Array;case 10:return Array;case 11:return Float32Array;case 12:return Float64Array;default:return Array}}(r))(a),h=o;for(let c=0;c<a;c++)u[c]=this.parseTagValue(r,e),e+=h;return u}}parseTagValue(e,t){let{chunk:s}=this;switch(e){case 1:return s.getUint8(t);case 3:return s.getUint16(t);case 4:return s.getUint32(t);case 5:return s.getUint32(t)/s.getUint32(t+4);case 6:return s.getInt8(t);case 8:return s.getInt16(t);case 9:return s.getInt32(t);case 10:return s.getInt32(t)/s.getInt32(t+4);case 11:return s.getFloat(t);case 12:return s.getDouble(t);case 13:return s.getUint32(t);default:k(`Invalid tiff type ${e}`)}}}class xe extends ms{static canHandle(e,t){return e.getUint8(t+1)===225&&e.getUint32(t+4)===1165519206&&e.getUint16(t+8)===0}async parse(){this.parseHeader();let{options:e}=this;return e.ifd0.enabled&&await this.parseIfd0Block(),e.exif.enabled&&await this.safeParse("parseExifBlock"),e.gps.enabled&&await this.safeParse("parseGpsBlock"),e.interop.enabled&&await this.safeParse("parseInteropBlock"),e.ifd1.enabled&&await this.safeParse("parseThumbnailBlock"),this.createOutput()}safeParse(e){let t=this[e]();return t.catch!==void 0&&(t=t.catch(this.handleError)),t}findIfd0Offset(){this.ifd0Offset===void 0&&(this.ifd0Offset=this.chunk.getUint32(4))}findIfd1Offset(){if(this.ifd1Offset===void 0){this.findIfd0Offset();let e=this.chunk.getUint16(this.ifd0Offset),t=this.ifd0Offset+2+12*e;this.ifd1Offset=this.chunk.getUint32(t)}}parseBlock(e,t){let s=new Map;return this[t]=s,this.parseTags(e,t,s),s}async parseIfd0Block(){if(this.ifd0)return;let{file:e}=this;this.findIfd0Offset(),this.ifd0Offset<8&&k("Malformed EXIF data"),!e.chunked&&this.ifd0Offset>e.byteLength&&k(`IFD0 offset points to outside of file.
this.ifd0Offset: ${this.ifd0Offset}, file.byteLength: ${e.byteLength}`),e.tiff&&await e.ensureChunk(this.ifd0Offset,yt(this.options));let t=this.parseBlock(this.ifd0Offset,"ifd0");return t.size!==0?(this.exifOffset=t.get(34665),this.interopOffset=t.get(40965),this.gpsOffset=t.get(34853),this.xmp=t.get(700),this.iptc=t.get(33723),this.icc=t.get(34675),this.options.sanitize&&(t.delete(34665),t.delete(40965),t.delete(34853),t.delete(700),t.delete(33723),t.delete(34675)),t):void 0}async parseExifBlock(){if(this.exif||(this.ifd0||await this.parseIfd0Block(),this.exifOffset===void 0))return;this.file.tiff&&await this.file.ensureChunk(this.exifOffset,yt(this.options));let e=this.parseBlock(this.exifOffset,"exif");return this.interopOffset||(this.interopOffset=e.get(40965)),this.makerNote=e.get(37500),this.userComment=e.get(37510),this.options.sanitize&&(e.delete(40965),e.delete(37500),e.delete(37510)),this.unpack(e,41728),this.unpack(e,41729),e}unpack(e,t){let s=e.get(t);s&&s.length===1&&e.set(t,s[0])}async parseGpsBlock(){if(this.gps||(this.ifd0||await this.parseIfd0Block(),this.gpsOffset===void 0))return;let e=this.parseBlock(this.gpsOffset,"gps");return e&&e.has(2)&&e.has(4)&&(e.set("latitude",St(...e.get(2),e.get(1))),e.set("longitude",St(...e.get(4),e.get(3)))),e}async parseInteropBlock(){if(!this.interop&&(this.ifd0||await this.parseIfd0Block(),this.interopOffset!==void 0||this.exif||await this.parseExifBlock(),this.interopOffset!==void 0))return this.parseBlock(this.interopOffset,"interop")}async parseThumbnailBlock(e=!1){if(!this.ifd1&&!this.ifd1Parsed&&(!this.options.mergeOutput||e))return this.findIfd1Offset(),this.ifd1Offset>0&&(this.parseBlock(this.ifd1Offset,"ifd1"),this.ifd1Parsed=!0),this.ifd1}async extractThumbnail(){if(this.headerParsed||this.parseHeader(),this.ifd1Parsed||await this.parseThumbnailBlock(!0),this.ifd1===void 0)return;let e=this.ifd1.get(513),t=this.ifd1.get(514);return this.chunk.getUint8Array(e,t)}get image(){return this.ifd0}get thumbnail(){return this.ifd1}createOutput(){let e,t,s,n={};for(t of D)if(e=this[t],!Yt(e))if(s=this.canTranslate?this.translateBlock(e,t):Object.fromEntries(e),this.options.mergeOutput){if(t==="ifd1")continue;Object.assign(n,s)}else n[t]=s;return this.makerNote&&(n.makerNote=this.makerNote),this.userComment&&(n.userComment=this.userComment),n}assignToOutput(e,t){if(this.globalOptions.mergeOutput)Object.assign(e,t);else for(let[s,n]of Object.entries(t))this.assignObjectToOutput(e,s,n)}}function St(i,e,t,s){var n=i+e/60+t/3600;return s!=="S"&&s!=="W"||(n*=-1),n}b(xe,"type","tiff"),b(xe,"headerLength",10),M.set("tiff",xe);const ot={ifd0:!1,ifd1:!1,exif:!1,gps:!1,interop:!1,sanitize:!1,reviveValues:!0,translateKeys:!1,translateValues:!1,mergeOutput:!1};Object.assign({},ot,{firstChunkSize:4e4,gps:[1,2,3,4]});Object.assign({},ot,{tiff:!1,ifd1:!0,mergeOutput:!1});const gs=Object.assign({},ot,{firstChunkSize:4e4,ifd0:[274]});async function ys(i){let e=new us(gs);await e.read(i);let t=await e.parse();if(t&&t.ifd0)return t.ifd0[274]}const bs=Object.freeze({1:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:0,rad:0},2:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:0,rad:0},3:{dimensionSwapped:!1,scaleX:1,scaleY:1,deg:180,rad:180*Math.PI/180},4:{dimensionSwapped:!1,scaleX:-1,scaleY:1,deg:180,rad:180*Math.PI/180},5:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:90,rad:90*Math.PI/180},6:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:90,rad:90*Math.PI/180},7:{dimensionSwapped:!0,scaleX:1,scaleY:-1,deg:270,rad:270*Math.PI/180},8:{dimensionSwapped:!0,scaleX:1,scaleY:1,deg:270,rad:270*Math.PI/180}});let oe=!0,le=!0;if(typeof navigator=="object"){let i=navigator.userAgent;if(i.includes("iPad")||i.includes("iPhone")){let e=i.match(/OS (\d+)_(\d+)/);if(e){let[,t,s]=e;oe=Number(t)+.1*Number(s)<13.4,le=!1}}else if(i.includes("OS X 10")){let[,e]=i.match(/OS X 10[_.](\d+)/);oe=le=Number(e)<15}if(i.includes("Chrome/")){let[,e]=i.match(/Chrome\/(\d+)/);oe=le=Number(e)<81}else if(i.includes("Firefox/")){let[,e]=i.match(/Firefox\/(\d+)/);oe=le=Number(e)<77}}async function vs(i){let e=await ys(i);return Object.assign({canvas:oe,css:le},bs[e])}class ws extends L{constructor(...e){super(...e),b(this,"ranges",new Fs),this.byteLength!==0&&this.ranges.add(0,this.byteLength)}_tryExtend(e,t,s){if(e===0&&this.byteLength===0&&s){let n=new DataView(s.buffer||s,s.byteOffset,s.byteLength);this._swapDataView(n)}else{let n=e+t;if(n>this.byteLength){let{dataView:r}=this._extend(n);this._swapDataView(r)}}}_extend(e){let t;t=Xt?Kt.allocUnsafe(e):new Uint8Array(e);let s=new DataView(t.buffer,t.byteOffset,t.byteLength);return t.set(new Uint8Array(this.buffer,this.byteOffset,this.byteLength),0),{uintView:t,dataView:s}}subarray(e,t,s=!1){return t=t||this._lengthToEnd(e),s&&this._tryExtend(e,t),this.ranges.add(e,t),super.subarray(e,t)}set(e,t,s=!1){s&&this._tryExtend(t,e.byteLength,e);let n=super.set(e,t);return this.ranges.add(t,n.byteLength),n}async ensureChunk(e,t){this.chunked&&(this.ranges.available(e,t)||await this.readChunk(e,t))}available(e,t){return this.ranges.available(e,t)}}class Fs{constructor(){b(this,"list",[])}get length(){return this.list.length}add(e,t,s=0){let n=e+t,r=this.list.filter(a=>Tt(e,a.offset,n)||Tt(e,a.end,n));if(r.length>0){e=Math.min(e,...r.map(o=>o.offset)),n=Math.max(n,...r.map(o=>o.end)),t=n-e;let a=r.shift();a.offset=e,a.length=t,a.end=n,this.list=this.list.filter(o=>!r.includes(o))}else this.list.push({offset:e,length:t,end:n})}available(e,t){let s=e+t;return this.list.some(n=>n.offset<=e&&s<=n.end)}}function Tt(i,e,t){return i<=e&&e<=t}class Ps extends ws{constructor(e,t){super(0),b(this,"chunksRead",0),this.input=e,this.options=t}async readWhole(){this.chunked=!1,await this.readChunk(this.nextChunkOffset)}async readChunked(){this.chunked=!0,await this.readChunk(0,this.options.firstChunkSize)}async readNextChunk(e=this.nextChunkOffset){if(this.fullyRead)return this.chunksRead++,!1;let t=this.options.chunkSize,s=await this.readChunk(e,t);return!!s&&s.byteLength===t}async readChunk(e,t){if(this.chunksRead++,(t=this.safeWrapAddress(e,t))!==0)return this._readChunk(e,t)}safeWrapAddress(e,t){return this.size!==void 0&&e+t>this.size?Math.max(0,this.size-e):t}get nextChunkOffset(){if(this.ranges.list.length!==0)return this.ranges.list[0].length}get canReadNextChunk(){return this.chunksRead<this.options.chunkLimit}get fullyRead(){return this.size!==void 0&&this.nextChunkOffset===this.size}read(){return this.options.chunked?this.readChunked():this.readWhole()}close(){}}nt.set("blob",class extends Ps{async readWhole(){this.chunked=!1;let i=await et(this.input);this._swapArrayBuffer(i)}readChunked(){return this.chunked=!0,this.size=this.input.size,super.readChunked()}async _readChunk(i,e){let t=e?i+e:void 0,s=this.input.slice(i,t),n=await et(s);return this.set(n,i,!0)}});const Cs={strings:{generatingThumbnails:"Generating thumbnails..."}},Ss={version:"3.1.0"};function Ts(i,e,t){try{i.getContext("2d").getImageData(0,0,1,1)}catch(s){if(s.code===18)return Promise.reject(new Error("cannot read image, probably an svg with external resources"))}return i.toBlob?new Promise(s=>{i.toBlob(s,e,t)}).then(s=>{if(s===null)throw new Error("cannot read image, probably an svg with external resources");return s}):Promise.resolve().then(()=>ns(i.toDataURL(e,t),{})).then(s=>{if(s===null)throw new Error("could not extract blob, probably an old browser");return s})}function As(i,e){let t=i.width,s=i.height;(e.deg===90||e.deg===270)&&(t=i.height,s=i.width);const n=document.createElement("canvas");n.width=t,n.height=s;const r=n.getContext("2d");return r.translate(t/2,s/2),e.canvas&&(r.rotate(e.rad),r.scale(e.scaleX,e.scaleY)),r.drawImage(i,-i.width/2,-i.height/2,i.width,i.height),n}function Es(i){const e=i.width/i.height,t=5e6,s=4096;let n=Math.floor(Math.sqrt(t*e)),r=Math.floor(t/Math.sqrt(t*e));if(n>s&&(n=s,r=Math.round(n/e)),r>s&&(r=s,n=Math.round(e*r)),i.width>n){const a=document.createElement("canvas");return a.width=n,a.height=r,a.getContext("2d").drawImage(i,0,0,n,r),a}return i}const Ds={thumbnailWidth:null,thumbnailHeight:null,thumbnailType:"image/jpeg",waitForThumbnailsBeforeUpload:!1,lazy:!1};class ti extends Ee{constructor(e,t){if(super(e,{...Ds,...t}),this.onFileAdded=s=>{!s.preview&&s.data&&mt(s.type)&&!s.isRemote&&this.addToQueue(s.id)},this.onCancelRequest=s=>{const n=this.queue.indexOf(s.id);n!==-1&&this.queue.splice(n,1)},this.onFileRemoved=s=>{const n=this.queue.indexOf(s.id);n!==-1&&this.queue.splice(n,1),s.preview&&ft(s.preview)&&URL.revokeObjectURL(s.preview)},this.onRestored=()=>{this.uppy.getFiles().filter(n=>n.isRestored).forEach(n=>{(!n.preview||ft(n.preview))&&this.addToQueue(n.id)})},this.onAllFilesRemoved=()=>{this.queue=[]},this.waitUntilAllProcessed=s=>{s.forEach(r=>{const a=this.uppy.getFile(r);this.uppy.emit("preprocess-progress",a,{mode:"indeterminate",message:this.i18n("generatingThumbnails")})});const n=()=>{s.forEach(r=>{const a=this.uppy.getFile(r);this.uppy.emit("preprocess-complete",a)})};return new Promise(r=>{this.queueProcessing?this.uppy.once("thumbnail:all-generated",()=>{n(),r()}):(n(),r())})},this.type="modifier",this.id=this.opts.id||"ThumbnailGenerator",this.title="Thumbnail Generator",this.queue=[],this.queueProcessing=!1,this.defaultThumbnailDimension=200,this.thumbnailType=this.opts.thumbnailType,this.defaultLocale=Cs,this.i18nInit(),this.opts.lazy&&this.opts.waitForThumbnailsBeforeUpload)throw new Error("ThumbnailGenerator: The `lazy` and `waitForThumbnailsBeforeUpload` options are mutually exclusive. Please ensure at most one of them is set to `true`.")}createThumbnail(e,t,s){const n=URL.createObjectURL(e.data),r=new Promise((o,d)=>{const u=new Image;u.src=n,u.addEventListener("load",()=>{URL.revokeObjectURL(n),o(u)}),u.addEventListener("error",h=>{URL.revokeObjectURL(n),d(h.error||new Error("Could not create thumbnail"))})}),a=vs(e.data).catch(()=>1);return Promise.all([r,a]).then(o=>{let[d,u]=o;const h=this.getProportionalDimensions(d,t,s,u.deg),c=As(d,u),p=this.resizeImage(c,h.width,h.height);return Ts(p,this.thumbnailType,80)}).then(o=>URL.createObjectURL(o))}getProportionalDimensions(e,t,s,n){let r=e.width/e.height;return(n===90||n===270)&&(r=e.height/e.width),t!=null?{width:t,height:Math.round(t/r)}:s!=null?{width:Math.round(s*r),height:s}:{width:this.defaultThumbnailDimension,height:Math.round(this.defaultThumbnailDimension/r)}}resizeImage(e,t,s){let n=Es(e),r=Math.ceil(Math.log2(n.width/t));r<1&&(r=1);let a=t*2**(r-1),o=s*2**(r-1);const d=2;for(;r--;){const u=document.createElement("canvas");u.width=a,u.height=o,u.getContext("2d").drawImage(n,0,0,a,o),n=u,a=Math.round(a/d),o=Math.round(o/d)}return n}setPreviewURL(e,t){this.uppy.setFileState(e,{preview:t})}addToQueue(e){this.queue.push(e),this.queueProcessing===!1&&this.processQueue()}processQueue(){if(this.queueProcessing=!0,this.queue.length>0){const e=this.uppy.getFile(this.queue.shift());return e?this.requestThumbnail(e).catch(()=>{}).then(()=>this.processQueue()):(this.uppy.log("[ThumbnailGenerator] file was removed before a thumbnail could be generated, but not removed from the queue. This is probably a bug","error"),Promise.resolve())}return this.queueProcessing=!1,this.uppy.log("[ThumbnailGenerator] Emptied thumbnail queue"),this.uppy.emit("thumbnail:all-generated"),Promise.resolve()}requestThumbnail(e){return mt(e.type)&&!e.isRemote?this.createThumbnail(e,this.opts.thumbnailWidth,this.opts.thumbnailHeight).then(t=>{this.setPreviewURL(e.id,t),this.uppy.log(`[ThumbnailGenerator] Generated thumbnail for ${e.id}`),this.uppy.emit("thumbnail:generated",this.uppy.getFile(e.id),t)}).catch(t=>{this.uppy.log(`[ThumbnailGenerator] Failed thumbnail for ${e.id}:`,"warning"),this.uppy.log(t,"warning"),this.uppy.emit("thumbnail:error",this.uppy.getFile(e.id),t)}):Promise.resolve()}install(){this.uppy.on("file-removed",this.onFileRemoved),this.uppy.on("cancel-all",this.onAllFilesRemoved),this.opts.lazy?(this.uppy.on("thumbnail:request",this.onFileAdded),this.uppy.on("thumbnail:cancel",this.onCancelRequest)):(this.uppy.on("thumbnail:request",this.onFileAdded),this.uppy.on("file-added",this.onFileAdded),this.uppy.on("restored",this.onRestored)),this.opts.waitForThumbnailsBeforeUpload&&this.uppy.addPreProcessor(this.waitUntilAllProcessed)}uninstall(){this.uppy.off("file-removed",this.onFileRemoved),this.uppy.off("cancel-all",this.onAllFilesRemoved),this.opts.lazy?(this.uppy.off("thumbnail:request",this.onFileAdded),this.uppy.off("thumbnail:cancel",this.onCancelRequest)):(this.uppy.off("thumbnail:request",this.onFileAdded),this.uppy.off("file-added",this.onFileAdded),this.uppy.off("restored",this.onRestored)),this.opts.waitForThumbnailsBeforeUpload&&this.uppy.removePreProcessor(this.waitUntilAllProcessed)}}ti.VERSION=Ss.version;function At(i){if(typeof i=="string"){const e=document.querySelectorAll(i);return e.length===0?null:Array.from(e)}return typeof i=="object"&&Ti(i)?[i]:null}const de=Array.from;function ii(i,e,t,s){let{onSuccess:n}=s;i.readEntries(r=>{const a=[...e,...r];r.length?queueMicrotask(()=>{ii(i,a,t,{onSuccess:n})}):n(a)},r=>{t(r),n(e)})}function si(i,e){return i==null?i:{kind:i.isFile?"file":i.isDirectory?"directory":void 0,name:i.name,getFile(){return new Promise((t,s)=>i.file(t,s))},async*values(){const t=i.createReader();yield*await new Promise(n=>{ii(t,[],e,{onSuccess:r=>n(r.map(a=>si(a,e)))})})},isSameEntry:void 0}}function ni(i,e,t){try{return t===void 0&&(t=void 0),async function*(){const s=()=>`${e}/${i.name}`;if(i.kind==="file"){const n=await i.getFile();n!=null?(n.relativePath=e?s():null,yield n):t!=null&&(yield t)}else if(i.kind==="directory")for await(const n of i.values())yield*ni(n,e?s():i.name);else t!=null&&(yield t)}()}catch(s){return Promise.reject(s)}}async function*ks(i,e){const t=await Promise.all(Array.from(i.items,async s=>{var n;let r;const a=()=>typeof s.getAsEntry=="function"?s.getAsEntry():s.webkitGetAsEntry();return(n=r)!=null||(r=si(a(),e)),{fileSystemHandle:r,lastResortFile:s.getAsFile()}}));for(const{lastResortFile:s,fileSystemHandle:n}of t)if(n!=null)try{yield*ni(n,"",s)}catch(r){s!=null?yield s:e(r)}else s!=null&&(yield s)}function Os(i){const e=de(i.files);return Promise.resolve(e)}async function Bs(i,e){var t;const s=(t=e==null?void 0:e.logDropError)!=null?t:Function.prototype;try{const n=[];for await(const r of ks(i,s))n.push(r);return n}catch{return Os(i)}}var Is={exports:{}};(function(i){var e=Object.prototype.hasOwnProperty,t="~";function s(){}Object.create&&(s.prototype=Object.create(null),new s().__proto__||(t=!1));function n(d,u,h){this.fn=d,this.context=u,this.once=h||!1}function r(d,u,h,c,p){if(typeof h!="function")throw new TypeError("The listener must be a function");var g=new n(h,c||d,p),f=t?t+u:u;return d._events[f]?d._events[f].fn?d._events[f]=[d._events[f],g]:d._events[f].push(g):(d._events[f]=g,d._eventsCount++),d}function a(d,u){--d._eventsCount===0?d._events=new s:delete d._events[u]}function o(){this._events=new s,this._eventsCount=0}o.prototype.eventNames=function(){var u=[],h,c;if(this._eventsCount===0)return u;for(c in h=this._events)e.call(h,c)&&u.push(t?c.slice(1):c);return Object.getOwnPropertySymbols?u.concat(Object.getOwnPropertySymbols(h)):u},o.prototype.listeners=function(u){var h=t?t+u:u,c=this._events[h];if(!c)return[];if(c.fn)return[c.fn];for(var p=0,g=c.length,f=new Array(g);p<g;p++)f[p]=c[p].fn;return f},o.prototype.listenerCount=function(u){var h=t?t+u:u,c=this._events[h];return c?c.fn?1:c.length:0},o.prototype.emit=function(u,h,c,p,g,f){var v=t?t+u:u;if(!this._events[v])return!1;var m=this._events[v],w=arguments.length,C,y;if(m.fn){switch(m.once&&this.removeListener(u,m.fn,void 0,!0),w){case 1:return m.fn.call(m.context),!0;case 2:return m.fn.call(m.context,h),!0;case 3:return m.fn.call(m.context,h,c),!0;case 4:return m.fn.call(m.context,h,c,p),!0;case 5:return m.fn.call(m.context,h,c,p,g),!0;case 6:return m.fn.call(m.context,h,c,p,g,f),!0}for(y=1,C=new Array(w-1);y<w;y++)C[y-1]=arguments[y];m.fn.apply(m.context,C)}else{var F=m.length,T;for(y=0;y<F;y++)switch(m[y].once&&this.removeListener(u,m[y].fn,void 0,!0),w){case 1:m[y].fn.call(m[y].context);break;case 2:m[y].fn.call(m[y].context,h);break;case 3:m[y].fn.call(m[y].context,h,c);break;case 4:m[y].fn.call(m[y].context,h,c,p);break;default:if(!C)for(T=1,C=new Array(w-1);T<w;T++)C[T-1]=arguments[T];m[y].fn.apply(m[y].context,C)}}return!0},o.prototype.on=function(u,h,c){return r(this,u,h,c,!1)},o.prototype.once=function(u,h,c){return r(this,u,h,c,!0)},o.prototype.removeListener=function(u,h,c,p){var g=t?t+u:u;if(!this._events[g])return this;if(!h)return a(this,g),this;var f=this._events[g];if(f.fn)f.fn===h&&(!p||f.once)&&(!c||f.context===c)&&a(this,g);else{for(var v=0,m=[],w=f.length;v<w;v++)(f[v].fn!==h||p&&!f[v].once||c&&f[v].context!==c)&&m.push(f[v]);m.length?this._events[g]=m.length===1?m[0]:m:a(this,g)}return this},o.prototype.removeAllListeners=function(u){var h;return u?(h=t?t+u:u,this._events[h]&&a(this,h)):(this._events=new s,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=t,o.EventEmitter=o,i.exports=o})(Is);globalThis&&globalThis.__classPrivateFieldGet;globalThis&&globalThis.__classPrivateFieldSet;globalThis&&globalThis.__classPrivateFieldGet;function it(){return it=Object.assign?Object.assign.bind():function(i){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(i[s]=t[s])}return i},it.apply(this,arguments)}const Us={position:"relative",width:"100%",minHeight:"100%"},Ns={position:"absolute",top:0,left:0,width:"100%",overflow:"visible"};class Rs extends he{constructor(e){super(e),this.handleScroll=()=>{this.setState({offset:this.base.scrollTop})},this.handleResize=()=>{this.resize()},this.focusElement=null,this.state={offset:0,height:0}}componentDidMount(){this.resize(),window.addEventListener("resize",this.handleResize)}componentWillUpdate(){this.base.contains(document.activeElement)&&(this.focusElement=document.activeElement)}componentDidUpdate(){this.focusElement&&this.focusElement.parentNode&&document.activeElement!==this.focusElement&&this.focusElement.focus(),this.focusElement=null,this.resize()}componentWillUnmount(){window.removeEventListener("resize",this.handleResize)}resize(){const{height:e}=this.state;e!==this.base.offsetHeight&&this.setState({height:this.base.offsetHeight})}render(e){let{data:t,rowHeight:s,renderRow:n,overscanCount:r=10,...a}=e;const{offset:o,height:d}=this.state;let u=Math.floor(o/s),h=Math.floor(d/s);r&&(u=Math.max(0,u-u%r),h+=r);const c=u+h+4,p=t.slice(u,c),g={...Us,height:t.length*s},f={...Ns,top:u*s};return l("div",it({onScroll:this.handleScroll},a),l("div",{role:"presentation",style:g},l("div",{role:"presentation",style:f},p.map(n))))}}function Ms(){return l("svg",{"aria-hidden":"true",focusable:"false",width:"30",height:"30",viewBox:"0 0 30 30"},l("path",{d:"M15 30c8.284 0 15-6.716 15-15 0-8.284-6.716-15-15-15C6.716 0 0 6.716 0 15c0 8.284 6.716 15 15 15zm4.258-12.676v6.846h-8.426v-6.846H5.204l9.82-12.364 9.82 12.364H19.26z"}))}var Et=Number.isNaN||function(e){return typeof e=="number"&&e!==e};function Ls(i,e){return!!(i===e||Et(i)&&Et(e))}function xs(i,e){if(i.length!==e.length)return!1;for(var t=0;t<i.length;t++)if(!Ls(i[t],e[t]))return!1;return!0}function Dt(i,e){e===void 0&&(e=xs);var t=null;function s(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];if(t&&t.lastThis===this&&e(n,t.lastArgs))return t.lastResult;var a=i.apply(this,n);return t={lastResult:a,lastArgs:n,lastThis:this},a}return s.clear=function(){t=null},s}const ai=['a[href]:not([tabindex^="-"]):not([inert]):not([aria-hidden])','area[href]:not([tabindex^="-"]):not([inert]):not([aria-hidden])',"input:not([disabled]):not([inert]):not([aria-hidden])","select:not([disabled]):not([inert]):not([aria-hidden])","textarea:not([disabled]):not([inert]):not([aria-hidden])","button:not([disabled]):not([inert]):not([aria-hidden])",'iframe:not([tabindex^="-"]):not([inert]):not([aria-hidden])','object:not([tabindex^="-"]):not([inert]):not([aria-hidden])','embed:not([tabindex^="-"]):not([inert]):not([aria-hidden])','[contenteditable]:not([tabindex^="-"]):not([inert]):not([aria-hidden])','[tabindex]:not([tabindex^="-"]):not([inert]):not([aria-hidden])'];function ri(i,e){if(e){const t=i.querySelector(`[data-uppy-paneltype="${e}"]`);if(t)return t}return i}function kt(i,e){const t=e[0];t&&(t.focus(),i.preventDefault())}function _s(i,e){const t=e[e.length-1];t&&(t.focus(),i.preventDefault())}function zs(i){return i.contains(document.activeElement)}function oi(i,e,t){const s=ri(t,e),n=de(s.querySelectorAll(ai)),r=n.indexOf(document.activeElement);zs(s)?i.shiftKey&&r===0?_s(i,n):!i.shiftKey&&r===n.length-1&&kt(i,n):kt(i,n)}function $s(i,e,t){e===null||oi(i,e,t)}function Hs(){let i=!1;return Ai((t,s)=>{const n=ri(t,s),r=n.contains(document.activeElement);if(r&&i)return;const a=n.querySelector("[data-uppy-super-focusable]");if(!(r&&!a))if(a)a.focus({preventScroll:!0}),i=!0;else{const o=n.querySelector(ai);o==null||o.focus({preventScroll:!0}),i=!1}},260)}function Vs(){const i=document.body;return!(!("draggable"in i)||!("ondragstart"in i&&"ondrop"in i)||!("FormData"in window)||!("FileReader"in window))}var qs=function(e,t){if(e===t)return!0;for(var s in e)if(!(s in t))return!1;for(var s in t)if(e[s]!==t[s])return!1;return!0};const js=xt(qs);function Ws(){return l("svg",{"aria-hidden":"true",focusable:"false",width:"25",height:"25",viewBox:"0 0 25 25"},l("g",{fill:"#686DE0",fillRule:"evenodd"},l("path",{d:"M5 7v10h15V7H5zm0-1h15a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V7a1 1 0 0 1 1-1z",fillRule:"nonzero"}),l("path",{d:"M6.35 17.172l4.994-5.026a.5.5 0 0 1 .707 0l2.16 2.16 3.505-3.505a.5.5 0 0 1 .707 0l2.336 2.31-.707.72-1.983-1.97-3.505 3.505a.5.5 0 0 1-.707 0l-2.16-2.159-3.938 3.939-1.409.026z",fillRule:"nonzero"}),l("circle",{cx:"7.5",cy:"9.5",r:"1.5"})))}function Gs(){return l("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"25",height:"25",viewBox:"0 0 25 25"},l("path",{d:"M9.5 18.64c0 1.14-1.145 2-2.5 2s-2.5-.86-2.5-2c0-1.14 1.145-2 2.5-2 .557 0 1.079.145 1.5.396V7.25a.5.5 0 0 1 .379-.485l9-2.25A.5.5 0 0 1 18.5 5v11.64c0 1.14-1.145 2-2.5 2s-2.5-.86-2.5-2c0-1.14 1.145-2 2.5-2 .557 0 1.079.145 1.5.396V8.67l-8 2v7.97zm8-11v-2l-8 2v2l8-2zM7 19.64c.855 0 1.5-.484 1.5-1s-.645-1-1.5-1-1.5.484-1.5 1 .645 1 1.5 1zm9-2c.855 0 1.5-.484 1.5-1s-.645-1-1.5-1-1.5.484-1.5 1 .645 1 1.5 1z",fill:"#049BCF",fillRule:"nonzero"}))}function Ks(){return l("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"25",height:"25",viewBox:"0 0 25 25"},l("path",{d:"M16 11.834l4.486-2.691A1 1 0 0 1 22 10v6a1 1 0 0 1-1.514.857L16 14.167V17a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V9a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v2.834zM15 9H5v8h10V9zm1 4l5 3v-6l-5 3z",fill:"#19AF67",fillRule:"nonzero"}))}function Xs(){return l("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"25",height:"25",viewBox:"0 0 25 25"},l("path",{d:"M9.766 8.295c-.691-1.843-.539-3.401.747-3.726 1.643-.414 2.505.938 2.39 3.299-.039.79-.194 1.662-.537 3.148.324.49.66.967 1.055 1.51.17.231.382.488.629.757 1.866-.128 3.653.114 4.918.655 1.487.635 2.192 1.685 1.614 2.84-.566 1.133-1.839 1.084-3.416.249-1.141-.604-2.457-1.634-3.51-2.707a13.467 13.467 0 0 0-2.238.426c-1.392 4.051-4.534 6.453-5.707 4.572-.986-1.58 1.38-4.206 4.914-5.375.097-.322.185-.656.264-1.001.08-.353.306-1.31.407-1.737-.678-1.059-1.2-2.031-1.53-2.91zm2.098 4.87c-.033.144-.068.287-.104.427l.033-.01-.012.038a14.065 14.065 0 0 1 1.02-.197l-.032-.033.052-.004a7.902 7.902 0 0 1-.208-.271c-.197-.27-.38-.526-.555-.775l-.006.028-.002-.003c-.076.323-.148.632-.186.8zm5.77 2.978c1.143.605 1.832.632 2.054.187.26-.519-.087-1.034-1.113-1.473-.911-.39-2.175-.608-3.55-.608.845.766 1.787 1.459 2.609 1.894zM6.559 18.789c.14.223.693.16 1.425-.413.827-.648 1.61-1.747 2.208-3.206-2.563 1.064-4.102 2.867-3.633 3.62zm5.345-10.97c.088-1.793-.351-2.48-1.146-2.28-.473.119-.564 1.05-.056 2.405.213.566.52 1.188.908 1.859.18-.858.268-1.453.294-1.984z",fill:"#E2514A",fillRule:"nonzero"}))}function Ys(){return l("svg",{"aria-hidden":"true",focusable:"false",width:"25",height:"25",viewBox:"0 0 25 25"},l("path",{d:"M10.45 2.05h1.05a.5.5 0 0 1 .5.5v.024a.5.5 0 0 1-.5.5h-1.05a.5.5 0 0 1-.5-.5V2.55a.5.5 0 0 1 .5-.5zm2.05 1.024h1.05a.5.5 0 0 1 .5.5V3.6a.5.5 0 0 1-.5.5H12.5a.5.5 0 0 1-.5-.5v-.025a.5.5 0 0 1 .5-.5v-.001zM10.45 0h1.05a.5.5 0 0 1 .5.5v.025a.5.5 0 0 1-.5.5h-1.05a.5.5 0 0 1-.5-.5V.5a.5.5 0 0 1 .5-.5zm2.05 1.025h1.05a.5.5 0 0 1 .5.5v.024a.5.5 0 0 1-.5.5H12.5a.5.5 0 0 1-.5-.5v-.024a.5.5 0 0 1 .5-.5zm-2.05 3.074h1.05a.5.5 0 0 1 .5.5v.025a.5.5 0 0 1-.5.5h-1.05a.5.5 0 0 1-.5-.5v-.025a.5.5 0 0 1 .5-.5zm2.05 1.025h1.05a.5.5 0 0 1 .5.5v.024a.5.5 0 0 1-.5.5H12.5a.5.5 0 0 1-.5-.5v-.024a.5.5 0 0 1 .5-.5zm-2.05 1.024h1.05a.5.5 0 0 1 .5.5v.025a.5.5 0 0 1-.5.5h-1.05a.5.5 0 0 1-.5-.5v-.025a.5.5 0 0 1 .5-.5zm2.05 1.025h1.05a.5.5 0 0 1 .5.5v.025a.5.5 0 0 1-.5.5H12.5a.5.5 0 0 1-.5-.5v-.025a.5.5 0 0 1 .5-.5zm-2.05 1.025h1.05a.5.5 0 0 1 .5.5v.025a.5.5 0 0 1-.5.5h-1.05a.5.5 0 0 1-.5-.5v-.025a.5.5 0 0 1 .5-.5zm2.05 1.025h1.05a.5.5 0 0 1 .5.5v.024a.5.5 0 0 1-.5.5H12.5a.5.5 0 0 1-.5-.5v-.024a.5.5 0 0 1 .5-.5zm-1.656 3.074l-.82 5.946c.52.302 1.174.458 1.976.458.803 0 1.455-.156 1.975-.458l-.82-5.946h-2.311zm0-1.025h2.312c.512 0 .946.378 1.015.885l.82 5.946c.056.412-.142.817-.501 1.026-.686.398-1.515.597-2.49.597-.974 0-1.804-.199-2.49-.597a1.025 1.025 0 0 1-.5-1.026l.819-5.946c.07-.507.503-.885 1.015-.885zm.545 6.6a.5.5 0 0 1-.397-.561l.143-.999a.5.5 0 0 1 .495-.429h.74a.5.5 0 0 1 .495.43l.143.998a.5.5 0 0 1-.397.561c-.404.08-.819.08-1.222 0z",fill:"#00C469",fillRule:"nonzero"}))}function Qs(){return l("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"25",height:"25",viewBox:"0 0 25 25"},l("g",{fill:"#A7AFB7",fillRule:"nonzero"},l("path",{d:"M5.5 22a.5.5 0 0 1-.5-.5v-18a.5.5 0 0 1 .5-.5h10.719a.5.5 0 0 1 .367.16l3.281 3.556a.5.5 0 0 1 .133.339V21.5a.5.5 0 0 1-.5.5h-14zm.5-1h13V7.25L16 4H6v17z"}),l("path",{d:"M15 4v3a1 1 0 0 0 1 1h3V7h-3V4h-1z"})))}function Js(){return l("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"25",height:"25",viewBox:"0 0 25 25"},l("path",{d:"M4.5 7h13a.5.5 0 1 1 0 1h-13a.5.5 0 0 1 0-1zm0 3h15a.5.5 0 1 1 0 1h-15a.5.5 0 1 1 0-1zm0 3h15a.5.5 0 1 1 0 1h-15a.5.5 0 1 1 0-1zm0 3h10a.5.5 0 1 1 0 1h-10a.5.5 0 1 1 0-1z",fill:"#5A5E69",fillRule:"nonzero"}))}function lt(i){const e={color:"#838999",icon:Qs()};if(!i)return e;const t=i.split("/")[0],s=i.split("/")[1];return t==="text"?{color:"#5a5e69",icon:Js()}:t==="image"?{color:"#686de0",icon:Ws()}:t==="audio"?{color:"#068dbb",icon:Gs()}:t==="video"?{color:"#19af67",icon:Ks()}:t==="application"&&s==="pdf"?{color:"#e25149",icon:Xs()}:t==="application"&&["zip","x-7z-compressed","x-zip-compressed","x-rar-compressed","x-tar","x-gzip","x-apple-diskimage"].indexOf(s)!==-1?{color:"#00C469",icon:Ys()}:e}function li(i){const{file:e}=i;if(e.preview)return l("img",{className:"uppy-Dashboard-Item-previewImg",alt:e.name,src:e.preview});const{color:t,icon:s}=lt(e.type);return l("div",{className:"uppy-Dashboard-Item-previewIconWrap"},l("span",{className:"uppy-Dashboard-Item-previewIcon",style:{color:t}},s),l("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-Dashboard-Item-previewIconBg",width:"58",height:"76",viewBox:"0 0 58 76"},l("rect",{fill:"#FFF",width:"58",height:"76",rx:"3",fillRule:"evenodd"})))}const Zs=(i,e)=>(typeof e=="function"?e():e).filter(n=>n.id===i)[0].name;function di(i){const{file:e,toggleFileCard:t,i18n:s,metaFields:n}=i,{missingRequiredMetaFields:r}=e;if(!(r!=null&&r.length))return null;const a=r.map(o=>Zs(o,n)).join(", ");return l("div",{className:"uppy-Dashboard-Item-errorMessage"},s("missingRequiredMetaFields",{smart_count:r.length,fields:a})," ",l("button",{type:"button",class:"uppy-u-reset uppy-Dashboard-Item-errorMessageBtn",onClick:()=>t(!0,e.id)},s("editFile")))}function en(i){const{file:e,i18n:t,toggleFileCard:s,metaFields:n,showLinkToFileUploadResult:r}=i,a="rgba(255, 255, 255, 0.5)",o=e.preview?a:lt(e.type).color;return l("div",{className:"uppy-Dashboard-Item-previewInnerWrap",style:{backgroundColor:o}},r&&e.uploadURL&&l("a",{className:"uppy-Dashboard-Item-previewLink",href:e.uploadURL,rel:"noreferrer noopener",target:"_blank","aria-label":e.meta.name},l("span",{hidden:!0},e.meta.name)),l(li,{file:e}),l(di,{file:e,i18n:t,toggleFileCard:s,metaFields:n}))}function tn(i){if(!i.isUploaded){if(i.error&&!i.hideRetryButton){i.uppy.retryUpload(i.file.id);return}i.resumableUploads&&!i.hidePauseResumeButton?i.uppy.pauseResume(i.file.id):i.individualCancellation&&!i.hideCancelButton&&i.uppy.removeFile(i.file.id)}}function Ot(i){return i.isUploaded?i.i18n("uploadComplete"):i.error?i.i18n("retryUpload"):i.resumableUploads?i.file.isPaused?i.i18n("resumeUpload"):i.i18n("pauseUpload"):i.individualCancellation?i.i18n("cancelUpload"):""}function _e(i){return l("div",{className:"uppy-Dashboard-Item-progress"},l("button",{className:"uppy-u-reset uppy-c-btn uppy-Dashboard-Item-progressIndicator",type:"button","aria-label":Ot(i),title:Ot(i),onClick:()=>tn(i)},i.children))}function Pe(i){let{children:e}=i;return l("svg",{"aria-hidden":"true",focusable:"false",width:"70",height:"70",viewBox:"0 0 36 36",className:"uppy-c-icon uppy-Dashboard-Item-progressIcon--circle"},e)}function ze(i){let{progress:e}=i;const t=2*Math.PI*15;return l("g",null,l("circle",{className:"uppy-Dashboard-Item-progressIcon--bg",r:"15",cx:"18",cy:"18","stroke-width":"2",fill:"none"}),l("circle",{className:"uppy-Dashboard-Item-progressIcon--progress",r:"15",cx:"18",cy:"18",transform:"rotate(-90, 18, 18)",fill:"none","stroke-width":"2","stroke-dasharray":t,"stroke-dashoffset":t-t/100*e}))}function sn(i){if(!i.file.progress.uploadStarted)return null;if(i.isUploaded)return l("div",{className:"uppy-Dashboard-Item-progress"},l("div",{className:"uppy-Dashboard-Item-progressIndicator"},l(Pe,null,l("circle",{r:"15",cx:"18",cy:"18",fill:"#1bb240"}),l("polygon",{className:"uppy-Dashboard-Item-progressIcon--check",transform:"translate(2, 3)",points:"14 22.5 7 15.2457065 8.99985857 13.1732815 14 18.3547104 22.9729883 9 25 11.1005634"}))));if(!i.recoveredState)return i.error&&!i.hideRetryButton?l(_e,i,l("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon uppy-Dashboard-Item-progressIcon--retry",width:"28",height:"31",viewBox:"0 0 16 19"},l("path",{d:"M16 11a8 8 0 1 1-8-8v2a6 6 0 1 0 6 6h2z"}),l("path",{d:"M7.9 3H10v2H7.9z"}),l("path",{d:"M8.536.5l3.535 3.536-1.414 1.414L7.12 1.914z"}),l("path",{d:"M10.657 2.621l1.414 1.415L8.536 7.57 7.12 6.157z"}))):i.resumableUploads&&!i.hidePauseResumeButton?l(_e,i,l(Pe,null,l(ze,{progress:i.file.progress.percentage}),i.file.isPaused?l("polygon",{className:"uppy-Dashboard-Item-progressIcon--play",transform:"translate(3, 3)",points:"12 20 12 10 20 15"}):l("g",{className:"uppy-Dashboard-Item-progressIcon--pause",transform:"translate(14.5, 13)"},l("rect",{x:"0",y:"0",width:"2",height:"10",rx:"0"}),l("rect",{x:"5",y:"0",width:"2",height:"10",rx:"0"})))):!i.resumableUploads&&i.individualCancellation&&!i.hideCancelButton?l(_e,i,l(Pe,null,l(ze,{progress:i.file.progress.percentage}),l("polygon",{className:"cancel",transform:"translate(2, 2)",points:"19.8856516 11.0625 16 14.9481516 12.1019737 11.0625 11.0625 12.1143484 14.9481516 16 11.0625 19.8980263 12.1019737 20.9375 16 17.0518484 19.8856516 20.9375 20.9375 19.8980263 17.0518484 16 20.9375 12"}))):l("div",{className:"uppy-Dashboard-Item-progress"},l("div",{className:"uppy-Dashboard-Item-progressIndicator"},l(Pe,null,l(ze,{progress:i.file.progress.percentage}))))}const $e="...";function ui(i,e){if(e===0)return"";if(i.length<=e)return i;if(e<=$e.length+1)return`${i.slice(0,e-1)}…`;const t=e-$e.length,s=Math.ceil(t/2),n=Math.floor(t/2);return i.slice(0,s)+$e+i.slice(-n)}const nn=i=>{const{author:e,name:t}=i.file.meta;function s(){return i.isSingleFile&&i.containerHeight>=350?90:i.containerWidth<=352?35:i.containerWidth<=576?60:e?20:30}return l("div",{className:"uppy-Dashboard-Item-name",title:t},ui(t,s()))},an=i=>{var e;const{author:t}=i.file.meta,s=(e=i.file.remote)==null?void 0:e.providerName,n="·";return t?l("div",{className:"uppy-Dashboard-Item-author"},l("a",{href:`${t.url}?utm_source=Companion&utm_medium=referral`,target:"_blank",rel:"noopener noreferrer"},ui(t.name,13)),s?l(Se,null,` ${n} `,s,` ${n} `):null):null},rn=i=>i.file.size&&l("div",{className:"uppy-Dashboard-Item-statusSize"},Ye(i.file.size)),on=i=>i.file.isGhost&&l("span",null," • ",l("button",{className:"uppy-u-reset uppy-c-btn uppy-Dashboard-Item-reSelect",type:"button",onClick:i.toggleAddFilesPanel},i.i18n("reSelect"))),ln=i=>{let{file:e,onClick:t}=i;return e.error?l("button",{className:"uppy-u-reset uppy-c-btn uppy-Dashboard-Item-errorDetails","aria-label":e.error,"data-microtip-position":"bottom","data-microtip-size":"medium",onClick:t,type:"button"},"?"):null};function dn(i){const{file:e}=i;return l("div",{className:"uppy-Dashboard-Item-fileInfo","data-uppy-file-source":e.source},l("div",{className:"uppy-Dashboard-Item-fileName"},nn(i),l(ln,{file:i.file,onClick:()=>alert(i.file.error)})),l("div",{className:"uppy-Dashboard-Item-status"},an(i),rn(i),on(i)),l(di,{file:i.file,i18n:i.i18n,toggleFileCard:i.toggleFileCard,metaFields:i.metaFields}))}function un(i,e){return e===void 0&&(e="Copy the URL below"),new Promise(t=>{const s=document.createElement("textarea");s.setAttribute("style",{position:"fixed",top:0,left:0,width:"2em",height:"2em",padding:0,border:"none",outline:"none",boxShadow:"none",background:"transparent"}),s.value=i,document.body.appendChild(s),s.select();const n=()=>{document.body.removeChild(s),window.prompt(e,i),t()};try{return document.execCommand("copy")?(document.body.removeChild(s),t()):n()}catch{return document.body.removeChild(s),n()}})}function hn(i){let{file:e,uploadInProgressOrComplete:t,metaFields:s,canEditFile:n,i18n:r,onClick:a}=i;return!t&&s&&s.length>0||!t&&n(e)?l("button",{className:"uppy-u-reset uppy-c-btn uppy-Dashboard-Item-action uppy-Dashboard-Item-action--edit",type:"button","aria-label":r("editFileWithFilename",{file:e.meta.name}),title:r("editFileWithFilename",{file:e.meta.name}),onClick:()=>a()},l("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"14",height:"14",viewBox:"0 0 14 14"},l("g",{fillRule:"evenodd"},l("path",{d:"M1.5 10.793h2.793A1 1 0 0 0 5 10.5L11.5 4a1 1 0 0 0 0-1.414L9.707.793a1 1 0 0 0-1.414 0l-6.5 6.5A1 1 0 0 0 1.5 8v2.793zm1-1V8L9 1.5l1.793 1.793-6.5 6.5H2.5z",fillRule:"nonzero"}),l("rect",{x:"1",y:"12.293",width:"11",height:"1",rx:".5"}),l("path",{fillRule:"nonzero",d:"M6.793 2.5L9.5 5.207l.707-.707L7.5 1.793z"})))):null}function cn(i){let{i18n:e,onClick:t,file:s}=i;return l("button",{className:"uppy-u-reset uppy-Dashboard-Item-action uppy-Dashboard-Item-action--remove",type:"button","aria-label":e("removeFile",{file:s.meta.name}),title:e("removeFile",{file:s.meta.name}),onClick:()=>t()},l("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"18",height:"18",viewBox:"0 0 18 18"},l("path",{d:"M9 0C4.034 0 0 4.034 0 9s4.034 9 9 9 9-4.034 9-9-4.034-9-9-9z"}),l("path",{fill:"#FFF",d:"M13 12.222l-.778.778L9 9.778 5.778 13 5 12.222 8.222 9 5 5.778 5.778 5 9 8.222 12.222 5l.778.778L9.778 9z"})))}const pn=(i,e)=>{un(e.file.uploadURL,e.i18n("copyLinkToClipboardFallback")).then(()=>{e.uppy.log("Link copied to clipboard."),e.uppy.info(e.i18n("copyLinkToClipboardSuccess"),"info",3e3)}).catch(e.uppy.log).then(()=>i.target.focus({preventScroll:!0}))};function fn(i){const{i18n:e}=i;return l("button",{className:"uppy-u-reset uppy-Dashboard-Item-action uppy-Dashboard-Item-action--copyLink",type:"button","aria-label":e("copyLink"),title:e("copyLink"),onClick:t=>pn(t,i)},l("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"14",height:"14",viewBox:"0 0 14 12"},l("path",{d:"M7.94 7.703a2.613 2.613 0 0 1-.626 2.681l-.852.851a2.597 2.597 0 0 1-1.849.766A2.616 2.616 0 0 1 2.764 7.54l.852-.852a2.596 2.596 0 0 1 2.69-.625L5.267 7.099a1.44 1.44 0 0 0-.833.407l-.852.851a1.458 1.458 0 0 0 1.03 2.486c.39 0 .755-.152 1.03-.426l.852-.852c.231-.231.363-.522.406-.824l1.04-1.038zm4.295-5.937A2.596 2.596 0 0 0 10.387 1c-.698 0-1.355.272-1.849.766l-.852.851a2.614 2.614 0 0 0-.624 2.688l1.036-1.036c.041-.304.173-.6.407-.833l.852-.852c.275-.275.64-.426 1.03-.426a1.458 1.458 0 0 1 1.03 2.486l-.852.851a1.442 1.442 0 0 1-.824.406l-1.04 1.04a2.596 2.596 0 0 0 2.683-.628l.851-.85a2.616 2.616 0 0 0 0-3.697zm-6.88 6.883a.577.577 0 0 0 .82 0l3.474-3.474a.579.579 0 1 0-.819-.82L5.355 7.83a.579.579 0 0 0 0 .819z"})))}function mn(i){const{uppy:e,file:t,uploadInProgressOrComplete:s,canEditFile:n,metaFields:r,showLinkToFileUploadResult:a,showRemoveButton:o,i18n:d,toggleFileCard:u,openFileEditor:h}=i;return l("div",{className:"uppy-Dashboard-Item-actionWrapper"},l(hn,{i18n:d,file:t,uploadInProgressOrComplete:s,canEditFile:n,metaFields:r,onClick:()=>{r&&r.length>0?u(!0,t.id):h(t)}}),a&&t.uploadURL?l(fn,{file:t,uppy:e,i18n:d}):null,o?l(cn,{i18n:d,file:t,uppy:e,onClick:()=>e.removeFile(t.id,"removed-by-user")}):null)}class gn extends he{componentDidMount(){const{file:e}=this.props;e.preview||this.props.handleRequestThumbnail(e)}shouldComponentUpdate(e){return!js(this.props,e)}componentDidUpdate(){const{file:e}=this.props;e.preview||this.props.handleRequestThumbnail(e)}componentWillUnmount(){const{file:e}=this.props;e.preview||this.props.handleCancelThumbnail(e)}render(){const{file:e}=this.props,t=e.progress.preprocess||e.progress.postprocess,s=e.progress.uploadComplete&&!t&&!e.error,n=e.progress.uploadStarted||t,r=e.progress.uploadStarted&&!e.progress.uploadComplete||t,a=e.error||!1,{isGhost:o}=e;let d=(this.props.individualCancellation||!r)&&!s;s&&this.props.showRemoveButtonAfterComplete&&(d=!0);const u=x({"uppy-Dashboard-Item":!0,"is-inprogress":r&&!this.props.recoveredState,"is-processing":t,"is-complete":s,"is-error":!!a,"is-resumable":this.props.resumableUploads,"is-noIndividualCancellation":!this.props.individualCancellation,"is-ghost":o});return l("div",{className:u,id:`uppy_${e.id}`,role:this.props.role},l("div",{className:"uppy-Dashboard-Item-preview"},l(en,{file:e,showLinkToFileUploadResult:this.props.showLinkToFileUploadResult,i18n:this.props.i18n,toggleFileCard:this.props.toggleFileCard,metaFields:this.props.metaFields}),l(sn,{uppy:this.props.uppy,file:e,error:a,isUploaded:s,hideRetryButton:this.props.hideRetryButton,hideCancelButton:this.props.hideCancelButton,hidePauseResumeButton:this.props.hidePauseResumeButton,recoveredState:this.props.recoveredState,showRemoveButtonAfterComplete:this.props.showRemoveButtonAfterComplete,resumableUploads:this.props.resumableUploads,individualCancellation:this.props.individualCancellation,i18n:this.props.i18n})),l("div",{className:"uppy-Dashboard-Item-fileInfoAndButtons"},l(dn,{file:e,id:this.props.id,acquirers:this.props.acquirers,containerWidth:this.props.containerWidth,containerHeight:this.props.containerHeight,i18n:this.props.i18n,toggleAddFilesPanel:this.props.toggleAddFilesPanel,toggleFileCard:this.props.toggleFileCard,metaFields:this.props.metaFields,isSingleFile:this.props.isSingleFile}),l(mn,{file:e,metaFields:this.props.metaFields,showLinkToFileUploadResult:this.props.showLinkToFileUploadResult,showRemoveButton:d,canEditFile:this.props.canEditFile,uploadInProgressOrComplete:n,toggleFileCard:this.props.toggleFileCard,openFileEditor:this.props.openFileEditor,uppy:this.props.uppy,i18n:this.props.i18n})))}}function yn(i,e){const t=[];let s=[];return i.forEach(n=>{s.length<e?s.push(n):(t.push(s),s=[n])}),s.length&&t.push(s),t}function bn(i){let{id:e,error:t,i18n:s,uppy:n,files:r,acquirers:a,resumableUploads:o,hideRetryButton:d,hidePauseResumeButton:u,hideCancelButton:h,showLinkToFileUploadResult:c,showRemoveButtonAfterComplete:p,isWide:g,metaFields:f,isSingleFile:v,toggleFileCard:m,handleRequestThumbnail:w,handleCancelThumbnail:C,recoveredState:y,individualCancellation:F,itemsPerRow:T,openFileEditor:U,canEditFile:A,toggleAddFilesPanel:E,containerWidth:B,containerHeight:ce}=i;const De=T===1?71:200,pe=Pi(()=>{const J=(ke,ie)=>r[ie].isGhost-r[ke].isGhost,W=Object.keys(r);return y&&W.sort(J),yn(W,T)},[r,T,y]),fe=J=>l("div",{class:"uppy-Dashboard-filesInner",role:"presentation",key:J[0]},J.map(W=>l(gn,{key:W,uppy:n,id:e,error:t,i18n:s,acquirers:a,resumableUploads:o,individualCancellation:F,hideRetryButton:d,hidePauseResumeButton:u,hideCancelButton:h,showLinkToFileUploadResult:c,showRemoveButtonAfterComplete:p,isWide:g,metaFields:f,recoveredState:y,isSingleFile:v,containerWidth:B,containerHeight:ce,toggleFileCard:m,handleRequestThumbnail:w,handleCancelThumbnail:C,role:"listitem",openFileEditor:U,canEditFile:A,toggleAddFilesPanel:E,file:r[W]})));return v?l("div",{class:"uppy-Dashboard-files"},fe(pe[0])):l(Rs,{class:"uppy-Dashboard-files",role:"list",data:pe,renderRow:fe,rowHeight:De})}let hi;hi=Symbol.for("uppy test: disable unused locale key warning");class ci extends he{constructor(){super(...arguments),this.triggerFileInputClick=()=>{this.fileInput.click()},this.triggerFolderInputClick=()=>{this.folderInput.click()},this.triggerVideoCameraInputClick=()=>{this.mobileVideoFileInput.click()},this.triggerPhotoCameraInputClick=()=>{this.mobilePhotoFileInput.click()},this.onFileInputChange=e=>{this.props.handleInputChange(e),e.target.value=null},this.renderHiddenInput=(e,t)=>l("input",{className:"uppy-Dashboard-input",hidden:!0,"aria-hidden":"true",tabIndex:-1,webkitdirectory:e,type:"file",name:"files[]",multiple:this.props.maxNumberOfFiles!==1,onChange:this.onFileInputChange,accept:this.props.allowedFileTypes,ref:t}),this.renderHiddenCameraInput=(e,t,s)=>{const r={photo:"image/*",video:"video/*"}[e];return l("input",{className:"uppy-Dashboard-input",hidden:!0,"aria-hidden":"true",tabIndex:-1,type:"file",name:`camera-${e}`,onChange:this.onFileInputChange,capture:t,accept:r,ref:s})},this.renderMyDeviceAcquirer=()=>l("div",{className:"uppy-DashboardTab",role:"presentation","data-uppy-acquirer-id":"MyDevice"},l("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-DashboardTab-btn",role:"tab",tabIndex:0,"data-uppy-super-focusable":!0,onClick:this.triggerFileInputClick},l("div",{className:"uppy-DashboardTab-inner"},l("svg",{className:"uppy-DashboardTab-iconMyDevice","aria-hidden":"true",focusable:"false",width:"32",height:"32",viewBox:"0 0 32 32"},l("path",{d:"M8.45 22.087l-1.305-6.674h17.678l-1.572 6.674H8.45zm4.975-12.412l1.083 1.765a.823.823 0 00.715.386h7.951V13.5H8.587V9.675h4.838zM26.043 13.5h-1.195v-2.598c0-.463-.336-.75-.798-.75h-8.356l-1.082-1.766A.823.823 0 0013.897 8H7.728c-.462 0-.815.256-.815.718V13.5h-.956a.97.97 0 00-.746.37.972.972 0 00-.19.81l1.724 8.565c.095.44.484.755.933.755H24c.44 0 .824-.3.929-.727l2.043-8.568a.972.972 0 00-.176-.825.967.967 0 00-.753-.38z",fill:"currentcolor","fill-rule":"evenodd"}))),l("div",{className:"uppy-DashboardTab-name"},this.props.i18n("myDevice")))),this.renderPhotoCamera=()=>l("div",{className:"uppy-DashboardTab",role:"presentation","data-uppy-acquirer-id":"MobilePhotoCamera"},l("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-DashboardTab-btn",role:"tab",tabIndex:0,"data-uppy-super-focusable":!0,onClick:this.triggerPhotoCameraInputClick},l("div",{className:"uppy-DashboardTab-inner"},l("svg",{"aria-hidden":"true",focusable:"false",width:"32",height:"32",viewBox:"0 0 32 32"},l("path",{d:"M23.5 9.5c1.417 0 2.5 1.083 2.5 2.5v9.167c0 1.416-1.083 2.5-2.5 2.5h-15c-1.417 0-2.5-1.084-2.5-2.5V12c0-1.417 1.083-2.5 2.5-2.5h2.917l1.416-2.167C13 7.167 13.25 7 13.5 7h5c.25 0 .5.167.667.333L20.583 9.5H23.5zM16 11.417a4.706 4.706 0 00-4.75 4.75 4.704 4.704 0 004.75 4.75 4.703 4.703 0 004.75-4.75c0-2.663-2.09-4.75-4.75-4.75zm0 7.825c-1.744 0-3.076-1.332-3.076-3.074 0-1.745 1.333-3.077 3.076-3.077 1.744 0 3.074 1.333 3.074 3.076s-1.33 3.075-3.074 3.075z",fill:"#02B383","fill-rule":"nonzero"}))),l("div",{className:"uppy-DashboardTab-name"},this.props.i18n("takePictureBtn")))),this.renderVideoCamera=()=>l("div",{className:"uppy-DashboardTab",role:"presentation","data-uppy-acquirer-id":"MobileVideoCamera"},l("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-DashboardTab-btn",role:"tab",tabIndex:0,"data-uppy-super-focusable":!0,onClick:this.triggerVideoCameraInputClick},l("div",{className:"uppy-DashboardTab-inner"},l("svg",{"aria-hidden":"true",width:"32",height:"32",viewBox:"0 0 32 32"},l("path",{fill:"#FF675E",fillRule:"nonzero",d:"m21.254 14.277 2.941-2.588c.797-.313 1.243.818 1.09 1.554-.01 2.094.02 4.189-.017 6.282-.126.915-1.145 1.08-1.58.34l-2.434-2.142c-.192.287-.504 1.305-.738.468-.104-1.293-.028-2.596-.05-3.894.047-.312.381.823.426 1.069.063-.384.206-.744.362-1.09zm-12.939-3.73c3.858.013 7.717-.025 11.574.02.912.129 1.492 1.237 1.351 2.217-.019 2.412.04 4.83-.03 7.239-.17 1.025-1.166 1.59-2.029 1.429-3.705-.012-7.41.025-11.114-.019-.913-.129-1.492-1.237-1.352-2.217.018-2.404-.036-4.813.029-7.214.136-.82.83-1.473 1.571-1.454z "}))),l("div",{className:"uppy-DashboardTab-name"},this.props.i18n("recordVideoBtn")))),this.renderBrowseButton=(e,t)=>{const s=this.props.acquirers.length;return l("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-Dashboard-browse",onClick:t,"data-uppy-super-focusable":s===0},e)},this.renderDropPasteBrowseTagline=e=>{const t=this.renderBrowseButton(this.props.i18n("browseFiles"),this.triggerFileInputClick),s=this.renderBrowseButton(this.props.i18n("browseFolders"),this.triggerFolderInputClick),n=this.props.fileManagerSelectionType,r=n.charAt(0).toUpperCase()+n.slice(1);return l("div",{class:"uppy-Dashboard-AddFiles-title"},this.props.disableLocalFiles?this.props.i18n("importFiles"):e>0?this.props.i18nArray(`dropPasteImport${r}`,{browseFiles:t,browseFolders:s,browse:t}):this.props.i18nArray(`dropPaste${r}`,{browseFiles:t,browseFolders:s,browse:t}))},this.renderAcquirer=e=>{var t;return l("div",{className:"uppy-DashboardTab",role:"presentation","data-uppy-acquirer-id":e.id},l("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-DashboardTab-btn",role:"tab",tabIndex:0,"data-cy":e.id,"aria-controls":`uppy-DashboardContent-panel--${e.id}`,"aria-selected":((t=this.props.activePickerPanel)==null?void 0:t.id)===e.id,"data-uppy-super-focusable":!0,onClick:()=>this.props.showPanel(e.id)},l("div",{className:"uppy-DashboardTab-inner"},e.icon()),l("div",{className:"uppy-DashboardTab-name"},e.name)))},this.renderAcquirers=e=>{const t=[...e],s=t.splice(e.length-2,e.length);return l(Se,null,t.map(n=>this.renderAcquirer(n)),l("span",{role:"presentation",style:{"white-space":"nowrap"}},s.map(n=>this.renderAcquirer(n))))},this.renderSourcesList=(e,t)=>{const{showNativePhotoCameraButton:s,showNativeVideoCameraButton:n}=this.props;let r=[];const a="myDevice";t||r.push({key:a,elements:this.renderMyDeviceAcquirer()}),s&&r.push({key:"nativePhotoCameraButton",elements:this.renderPhotoCamera()}),n&&r.push({key:"nativePhotoCameraButton",elements:this.renderVideoCamera()}),r.push(...e.map(c=>({key:c.id,elements:this.renderAcquirer(c)}))),r.length===1&&r[0].key===a&&(r=[]);const d=[...r],u=d.splice(r.length-2,r.length),h=c=>c.map(p=>{let{key:g,elements:f}=p;return l(Se,{key:g},f)});return l(Se,null,this.renderDropPasteBrowseTagline(r.length),l("div",{className:"uppy-Dashboard-AddFiles-list",role:"tablist"},h(d),l("span",{role:"presentation",style:{"white-space":"nowrap"}},h(u))))}}[hi](){this.props.i18nArray("dropPasteBoth"),this.props.i18nArray("dropPasteFiles"),this.props.i18nArray("dropPasteFolders"),this.props.i18nArray("dropPasteImportBoth"),this.props.i18nArray("dropPasteImportFiles"),this.props.i18nArray("dropPasteImportFolders")}renderPoweredByUppy(){const{i18nArray:e}=this.props,t=l("span",null,l("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon uppy-Dashboard-poweredByIcon",width:"11",height:"11",viewBox:"0 0 11 11"},l("path",{d:"M7.365 10.5l-.01-4.045h2.612L5.5.806l-4.467 5.65h2.604l.01 4.044h3.718z",fillRule:"evenodd"})),l("span",{className:"uppy-Dashboard-poweredByUppy"},"Uppy")),s=e("poweredBy",{uppy:t});return l("a",{tabIndex:-1,href:"https://uppy.io",rel:"noreferrer noopener",target:"_blank",className:"uppy-Dashboard-poweredBy"},s)}render(){const{showNativePhotoCameraButton:e,showNativeVideoCameraButton:t,nativeCameraFacingMode:s}=this.props;return l("div",{className:"uppy-Dashboard-AddFiles"},this.renderHiddenInput(!1,n=>{this.fileInput=n}),this.renderHiddenInput(!0,n=>{this.folderInput=n}),e&&this.renderHiddenCameraInput("photo",s,n=>{this.mobilePhotoFileInput=n}),t&&this.renderHiddenCameraInput("video",s,n=>{this.mobileVideoFileInput=n}),this.renderSourcesList(this.props.acquirers,this.props.disableLocalFiles),l("div",{className:"uppy-Dashboard-AddFiles-info"},this.props.note&&l("div",{className:"uppy-Dashboard-note"},this.props.note),this.props.proudlyDisplayPoweredByUppy&&this.renderPoweredByUppy(this.props)))}}const vn=i=>l("div",{className:x("uppy-Dashboard-AddFilesPanel",i.className),"data-uppy-panelType":"AddFiles","aria-hidden":!i.showAddFilesPanel},l("div",{className:"uppy-DashboardContent-bar"},l("div",{className:"uppy-DashboardContent-title",role:"heading","aria-level":"1"},i.i18n("addingMoreFiles")),l("button",{className:"uppy-DashboardContent-back",type:"button",onClick:()=>i.toggleAddFilesPanel(!1)},i.i18n("back"))),l(ci,i));function j(i){const{tagName:e}=i.target;if(e==="INPUT"||e==="TEXTAREA"){i.stopPropagation();return}i.preventDefault(),i.stopPropagation()}function wn(i){let{activePickerPanel:e,className:t,hideAllPanels:s,i18n:n,state:r,uppy:a}=i;return l("div",{className:x("uppy-DashboardContent-panel",t),role:"tabpanel","data-uppy-panelType":"PickerPanel",id:`uppy-DashboardContent-panel--${e.id}`,onDragOver:j,onDragLeave:j,onDrop:j,onPaste:j},l("div",{className:"uppy-DashboardContent-bar"},l("div",{className:"uppy-DashboardContent-title",role:"heading","aria-level":"1"},n("importFrom",{name:e.name})),l("button",{className:"uppy-DashboardContent-back",type:"button",onClick:s},n("cancel"))),l("div",{className:"uppy-DashboardContent-panelBody"},a.getPlugin(e.id).render(r)))}function Fn(i){const e=i.files[i.fileCardFor],t=()=>{i.uppy.emit("file-editor:cancel",e),i.closeFileEditor()};return l("div",{className:x("uppy-DashboardContent-panel",i.className),role:"tabpanel","data-uppy-panelType":"FileEditor",id:"uppy-DashboardContent-panel--editor"},l("div",{className:"uppy-DashboardContent-bar"},l("div",{className:"uppy-DashboardContent-title",role:"heading","aria-level":"1"},i.i18nArray("editing",{file:l("span",{className:"uppy-DashboardContent-titleFile"},e.meta?e.meta.name:e.name)})),l("button",{className:"uppy-DashboardContent-back",type:"button",onClick:t},i.i18n("cancel")),l("button",{className:"uppy-DashboardContent-save",type:"button",onClick:i.saveFileEditor},i.i18n("save"))),l("div",{className:"uppy-DashboardContent-panelBody"},i.editors.map(s=>i.uppy.getPlugin(s.id).render(i.state))))}const z={STATE_ERROR:"error",STATE_WAITING:"waiting",STATE_PREPROCESSING:"preprocessing",STATE_UPLOADING:"uploading",STATE_POSTPROCESSING:"postprocessing",STATE_COMPLETE:"complete",STATE_PAUSED:"paused"};function Pn(i,e,t,s){if(s===void 0&&(s={}),i)return z.STATE_ERROR;if(e)return z.STATE_COMPLETE;if(t)return z.STATE_PAUSED;let n=z.STATE_WAITING;const r=Object.keys(s);for(let a=0;a<r.length;a++){const{progress:o}=s[r[a]];if(o.uploadStarted&&!o.uploadComplete)return z.STATE_UPLOADING;o.preprocess&&n!==z.STATE_UPLOADING&&(n=z.STATE_PREPROCESSING),o.postprocess&&n!==z.STATE_UPLOADING&&n!==z.STATE_PREPROCESSING&&(n=z.STATE_POSTPROCESSING)}return n}function Cn(i){let{files:e,i18n:t,isAllComplete:s,isAllErrored:n,isAllPaused:r,inProgressNotPausedFiles:a,newFiles:o,processingFiles:d}=i;switch(Pn(n,s,r,e)){case"uploading":return t("uploadingXFiles",{smart_count:a.length});case"preprocessing":case"postprocessing":return t("processingXFiles",{smart_count:d.length});case"paused":return t("uploadPaused");case"waiting":return t("xFilesSelected",{smart_count:o.length});case"complete":return t("uploadComplete");case"error":return t("error")}}function Sn(i){const{i18n:e,isAllComplete:t,hideCancelButton:s,maxNumberOfFiles:n,toggleAddFilesPanel:r,uppy:a}=i;let{allowNewUpload:o}=i;return o&&n&&(o=i.totalFileCount<i.maxNumberOfFiles),l("div",{className:"uppy-DashboardContent-bar"},!t&&!s?l("button",{className:"uppy-DashboardContent-back",type:"button",onClick:()=>a.cancelAll()},e("cancel")):l("div",null),l("div",{className:"uppy-DashboardContent-title",role:"heading","aria-level":"1"},l(Cn,i)),o?l("button",{className:"uppy-DashboardContent-addMore",type:"button","aria-label":e("addMoreFiles"),title:e("addMoreFiles"),onClick:()=>r(!0)},l("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon",width:"15",height:"15",viewBox:"0 0 15 15"},l("path",{d:"M8 6.5h6a.5.5 0 0 1 .5.5v.5a.5.5 0 0 1-.5.5H8v6a.5.5 0 0 1-.5.5H7a.5.5 0 0 1-.5-.5V8h-6a.5.5 0 0 1-.5-.5V7a.5.5 0 0 1 .5-.5h6v-6A.5.5 0 0 1 7 0h.5a.5.5 0 0 1 .5.5v6z"})),l("span",{className:"uppy-DashboardContent-addMoreCaption"},e("addMore"))):l("div",null))}function Tn(i){const{computedMetaFields:e,requiredMetaFields:t,updateMeta:s,form:n,formState:r}=i,a={text:"uppy-u-reset uppy-c-textInput uppy-Dashboard-FileCard-input"};return e.map(o=>{const d=`uppy-Dashboard-FileCard-input-${o.id}`,u=t.includes(o.id);return l("fieldset",{key:o.id,className:"uppy-Dashboard-FileCard-fieldset"},l("label",{className:"uppy-Dashboard-FileCard-label",htmlFor:d},o.name),o.render!==void 0?o.render({value:r[o.id],onChange:h=>s(h,o.id),fieldCSSClasses:a,required:u,form:n.id},l):l("input",{className:a.text,id:d,form:n.id,type:o.type||"text",required:u,value:r[o.id],placeholder:o.placeholder,onInput:h=>s(h.target.value,o.id),"data-uppy-super-focusable":!0}))})}function An(i){var e;const{files:t,fileCardFor:s,toggleFileCard:n,saveFileCard:r,metaFields:a,requiredMetaFields:o,openFileEditor:d,i18n:u,i18nArray:h,className:c,canEditFile:p}=i,g=()=>typeof a=="function"?a(t[s]):a,f=t[s],v=(e=g())!=null?e:[],m=p(f),w={};v.forEach(E=>{var B;w[E.id]=(B=f.meta[E.id])!=null?B:""});const[C,y]=Te(w),F=Ci(E=>{E.preventDefault(),r(C,s)},[r,C,s]),T=(E,B)=>{y({...C,[B]:E})},U=()=>{n(!1)},[A]=Te(()=>{const E=document.createElement("form");return E.setAttribute("tabindex","-1"),E.id=_t(),E});return Xe(()=>(document.body.appendChild(A),A.addEventListener("submit",F),()=>{A.removeEventListener("submit",F),document.body.removeChild(A)}),[A,F]),l("div",{className:x("uppy-Dashboard-FileCard",c),"data-uppy-panelType":"FileCard",onDragOver:j,onDragLeave:j,onDrop:j,onPaste:j},l("div",{className:"uppy-DashboardContent-bar"},l("div",{className:"uppy-DashboardContent-title",role:"heading","aria-level":"1"},h("editing",{file:l("span",{className:"uppy-DashboardContent-titleFile"},f.meta?f.meta.name:f.name)})),l("button",{className:"uppy-DashboardContent-back",type:"button",form:A.id,title:u("finishEditingFile"),onClick:U},u("cancel"))),l("div",{className:"uppy-Dashboard-FileCard-inner"},l("div",{className:"uppy-Dashboard-FileCard-preview",style:{backgroundColor:lt(f.type).color}},l(li,{file:f}),m&&l("button",{type:"button",className:"uppy-u-reset uppy-c-btn uppy-Dashboard-FileCard-edit",onClick:E=>{F(E),d(f)}},u("editImage"))),l("div",{className:"uppy-Dashboard-FileCard-info"},l(Tn,{computedMetaFields:v,requiredMetaFields:o,updateMeta:T,form:A,formState:C})),l("div",{className:"uppy-Dashboard-FileCard-actions"},l("button",{className:"uppy-u-reset uppy-c-btn uppy-c-btn-primary uppy-Dashboard-FileCard-actionsBtn",type:"submit",form:A.id},u("saveChanges")),l("button",{className:"uppy-u-reset uppy-c-btn uppy-c-btn-link uppy-Dashboard-FileCard-actionsBtn",type:"button",onClick:U,form:A.id},u("cancel")))))}const Z="uppy-transition-slideDownUp",Bt=250;function Ce(i){let{children:e}=i;const[t,s]=Te(null),[n,r]=Te(""),a=Be(),o=Be(),d=Be(),u=()=>{r(`${Z}-enter`),cancelAnimationFrame(d.current),clearTimeout(o.current),o.current=void 0,d.current=requestAnimationFrame(()=>{r(`${Z}-enter ${Z}-enter-active`),a.current=setTimeout(()=>{r("")},Bt)})},h=()=>{r(`${Z}-leave`),cancelAnimationFrame(d.current),clearTimeout(a.current),a.current=void 0,d.current=requestAnimationFrame(()=>{r(`${Z}-leave ${Z}-leave-active`),o.current=setTimeout(()=>{s(null),r("")},Bt)})};return Xe(()=>{const c=Y(e)[0];t!==c&&(c&&!t?u():t&&!c&&!o.current&&h(),s(c))},[e,t]),Xe(()=>()=>{clearTimeout(a.current),clearTimeout(o.current),cancelAnimationFrame(d.current)},[]),t?Lt(t,{className:x(n,t.props.className)}):null}function Q(){return Q=Object.assign?Object.assign.bind():function(i){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(i[s]=t[s])}return i},Q.apply(this,arguments)}const It=900,Ut=700,He=576,Nt=330;function En(i){const e=i.totalFileCount===0,t=i.totalFileCount===1,s=i.containerWidth>He,n=i.containerHeight>Nt,r=x({"uppy-Dashboard":!0,"uppy-Dashboard--isDisabled":i.disabled,"uppy-Dashboard--animateOpenClose":i.animateOpenClose,"uppy-Dashboard--isClosing":i.isClosing,"uppy-Dashboard--isDraggingOver":i.isDraggingOver,"uppy-Dashboard--modal":!i.inline,"uppy-size--md":i.containerWidth>He,"uppy-size--lg":i.containerWidth>Ut,"uppy-size--xl":i.containerWidth>It,"uppy-size--height-md":i.containerHeight>Nt,"uppy-Dashboard--isAddFilesPanelVisible":i.showAddFilesPanel,"uppy-Dashboard--isInnerWrapVisible":i.areInsidesReadyToBeVisible,"uppy-Dashboard--singleFile":i.singleFileFullScreen&&t&&n});let a=1;i.containerWidth>It?a=5:i.containerWidth>Ut?a=4:i.containerWidth>He&&(a=3);const o=i.showSelectedFiles&&!e,d=i.recoveredState?Object.keys(i.recoveredState.files).length:null,u=i.files?Object.keys(i.files).filter(p=>i.files[p].isGhost).length:null,h=()=>u>0?i.i18n("recoveredXFiles",{smart_count:u}):i.i18n("recoveredAllFiles");return l("div",{className:r,"data-uppy-theme":i.theme,"data-uppy-num-acquirers":i.acquirers.length,"data-uppy-drag-drop-supported":!i.disableLocalFiles&&Vs(),"aria-hidden":i.inline?"false":i.isHidden,"aria-disabled":i.disabled,"aria-label":i.inline?i.i18n("dashboardTitle"):i.i18n("dashboardWindowTitle"),onPaste:i.handlePaste,onDragOver:i.handleDragOver,onDragLeave:i.handleDragLeave,onDrop:i.handleDrop},l("div",{"aria-hidden":"true",className:"uppy-Dashboard-overlay",tabIndex:-1,onClick:i.handleClickOutside}),l("div",{className:"uppy-Dashboard-inner","aria-modal":!i.inline&&"true",role:i.inline?void 0:"dialog",style:{width:i.inline&&i.width?i.width:"",height:i.inline&&i.height?i.height:""}},i.inline?null:l("button",{className:"uppy-u-reset uppy-Dashboard-close",type:"button","aria-label":i.i18n("closeModal"),title:i.i18n("closeModal"),onClick:i.closeModal},l("span",{"aria-hidden":"true"},"×")),l("div",{className:"uppy-Dashboard-innerWrap"},l("div",{className:"uppy-Dashboard-dropFilesHereHint"},i.i18n("dropHint")),o&&l(Sn,i),d&&l("div",{className:"uppy-Dashboard-serviceMsg"},l("svg",{className:"uppy-Dashboard-serviceMsg-icon","aria-hidden":"true",focusable:"false",width:"21",height:"16",viewBox:"0 0 24 19"},l("g",{transform:"translate(0 -1)",fill:"none",fillRule:"evenodd"},l("path",{d:"M12.857 1.43l10.234 17.056A1 1 0 0122.234 20H1.766a1 1 0 01-.857-1.514L11.143 1.429a1 1 0 011.714 0z",fill:"#FFD300"}),l("path",{fill:"#000",d:"M11 6h2l-.3 8h-1.4z"}),l("circle",{fill:"#000",cx:"12",cy:"17",r:"1"}))),l("strong",{className:"uppy-Dashboard-serviceMsg-title"},i.i18n("sessionRestored")),l("div",{className:"uppy-Dashboard-serviceMsg-text"},h())),o?l(bn,{id:i.id,error:i.error,i18n:i.i18n,uppy:i.uppy,files:i.files,acquirers:i.acquirers,resumableUploads:i.resumableUploads,hideRetryButton:i.hideRetryButton,hidePauseResumeButton:i.hidePauseResumeButton,hideCancelButton:i.hideCancelButton,showLinkToFileUploadResult:i.showLinkToFileUploadResult,showRemoveButtonAfterComplete:i.showRemoveButtonAfterComplete,isWide:i.isWide,metaFields:i.metaFields,toggleFileCard:i.toggleFileCard,handleRequestThumbnail:i.handleRequestThumbnail,handleCancelThumbnail:i.handleCancelThumbnail,recoveredState:i.recoveredState,individualCancellation:i.individualCancellation,openFileEditor:i.openFileEditor,canEditFile:i.canEditFile,toggleAddFilesPanel:i.toggleAddFilesPanel,isSingleFile:t,itemsPerRow:a}):l(ci,Q({},i,{isSizeMD:s})),l(Ce,null,i.showAddFilesPanel?l(vn,Q({key:"AddFiles"},i,{isSizeMD:s})):null),l(Ce,null,i.fileCardFor?l(An,Q({key:"FileCard"},i)):null),l(Ce,null,i.activePickerPanel?l(wn,Q({key:"Picker"},i)):null),l(Ce,null,i.showFileEditor?l(Fn,Q({key:"Editor"},i)):null),l("div",{className:"uppy-Dashboard-progressindicators"},i.progressindicators.map(p=>i.uppy.getPlugin(p.id).render(i.state))))))}const Dn={strings:{closeModal:"Close Modal",addMoreFiles:"Add more files",addingMoreFiles:"Adding more files",importFrom:"Import from %{name}",dashboardWindowTitle:"Uppy Dashboard Window (Press escape to close)",dashboardTitle:"Uppy Dashboard",copyLinkToClipboardSuccess:"Link copied to clipboard.",copyLinkToClipboardFallback:"Copy the URL below",copyLink:"Copy link",back:"Back",removeFile:"Remove file",editFile:"Edit file",editImage:"Edit image",editing:"Editing %{file}",error:"Error",finishEditingFile:"Finish editing file",saveChanges:"Save changes",myDevice:"My Device",dropHint:"Drop your files here",uploadComplete:"Upload complete",uploadPaused:"Upload paused",resumeUpload:"Resume upload",pauseUpload:"Pause upload",retryUpload:"Retry upload",cancelUpload:"Cancel upload",xFilesSelected:{0:"%{smart_count} file selected",1:"%{smart_count} files selected"},uploadingXFiles:{0:"Uploading %{smart_count} file",1:"Uploading %{smart_count} files"},processingXFiles:{0:"Processing %{smart_count} file",1:"Processing %{smart_count} files"},poweredBy:"Powered by %{uppy}",addMore:"Add more",editFileWithFilename:"Edit file %{file}",save:"Save",cancel:"Cancel",dropPasteFiles:"Drop files here or %{browseFiles}",dropPasteFolders:"Drop files here or %{browseFolders}",dropPasteBoth:"Drop files here, %{browseFiles} or %{browseFolders}",dropPasteImportFiles:"Drop files here, %{browseFiles} or import from:",dropPasteImportFolders:"Drop files here, %{browseFolders} or import from:",dropPasteImportBoth:"Drop files here, %{browseFiles}, %{browseFolders} or import from:",importFiles:"Import files from:",browseFiles:"browse files",browseFolders:"browse folders",recoveredXFiles:{0:"We could not fully recover 1 file. Please re-select it and resume the upload.",1:"We could not fully recover %{smart_count} files. Please re-select them and resume the upload."},recoveredAllFiles:"We restored all files. You can now resume the upload.",sessionRestored:"Session restored",reSelect:"Re-select",missingRequiredMetaFields:{0:"Missing required meta field: %{fields}.",1:"Missing required meta fields: %{fields}."},takePictureBtn:"Take Picture",recordVideoBtn:"Record Video"}};function P(i,e){if(!Object.prototype.hasOwnProperty.call(i,e))throw new TypeError("attempted to use private field on non-instance");return i}var kn=0;function _(i){return"__private_"+kn+++"_"+i}const On={version:"3.9.1"},Ve=Dt.default||Dt,Rt=9,Bn=27;function Mt(){const i={};return i.promise=new Promise((e,t)=>{i.resolve=e,i.reject=t}),i}const In={target:"body",metaFields:[],inline:!1,width:750,height:550,thumbnailWidth:280,thumbnailType:"image/jpeg",waitForThumbnailsBeforeUpload:!1,defaultPickerIcon:Ms,showLinkToFileUploadResult:!1,showProgressDetails:!1,hideUploadButton:!1,hideCancelButton:!1,hideRetryButton:!1,hidePauseResumeButton:!1,hideProgressAfterFinish:!1,note:null,closeModalOnClickOutside:!1,closeAfterFinish:!1,singleFileFullScreen:!0,disableStatusBar:!1,disableInformer:!1,disableThumbnailGenerator:!1,disablePageScrollWhenModalOpen:!0,animateOpenClose:!0,fileManagerSelectionType:"files",proudlyDisplayPoweredByUppy:!0,showSelectedFiles:!0,showRemoveButtonAfterComplete:!1,browserBackButtonClose:!1,showNativePhotoCameraButton:!1,showNativeVideoCameraButton:!1,theme:"light",autoOpen:null,autoOpenFileEditor:!1,disabled:!1,disableLocalFiles:!1,doneButtonHandler:void 0,onRequestCloseModal:null};var G=_("disabledNodes"),H=_("generateLargeThumbnailIfSingleFile"),re=_("openFileEditorWhenFilesAdded"),K=_("attachRenderFunctionToTarget"),qe=_("isTargetSupported"),je=_("getAcquirers"),We=_("getProgressIndicators"),V=_("getEditors"),Ge=_("addSpecifiedPluginsFromOptions"),Ke=_("autoDiscoverPlugins"),X=_("addSupportedPluginIfNoTarget");let Un=class extends Ee{constructor(e,t){var s,n;let r;t?t.autoOpen===void 0?r=t.autoOpenFileEditor?"imageEditor":null:r=t.autoOpen:r=null,super(e,{...In,...t,autoOpen:r}),Object.defineProperty(this,G,{writable:!0,value:void 0}),this.modalName=`uppy-Dashboard-${_t()}`,this.superFocus=Hs(),this.ifFocusedOnUppyRecently=!1,this.removeTarget=a=>{const d=this.getPluginState().targets.filter(u=>u.id!==a.id);this.setPluginState({targets:d})},this.addTarget=a=>{const o=a.id||a.constructor.name,d=a.title||o,u=a.type;if(u!=="acquirer"&&u!=="progressindicator"&&u!=="editor"){const g="Dashboard: can only be targeted by plugins of types: acquirer, progressindicator, editor";return this.uppy.log(g,"error"),null}const h={id:o,name:d,type:u},p=this.getPluginState().targets.slice();return p.push(h),this.setPluginState({targets:p}),this.el},this.hideAllPanels=()=>{var a;const o=this.getPluginState(),d={activePickerPanel:void 0,showAddFilesPanel:!1,activeOverlayType:null,fileCardFor:null,showFileEditor:!1};o.activePickerPanel===d.activePickerPanel&&o.showAddFilesPanel===d.showAddFilesPanel&&o.showFileEditor===d.showFileEditor&&o.activeOverlayType===d.activeOverlayType||(this.setPluginState(d),this.uppy.emit("dashboard:close-panel",(a=o.activePickerPanel)==null?void 0:a.id))},this.showPanel=a=>{const{targets:o}=this.getPluginState(),d=o.find(u=>u.type==="acquirer"&&u.id===a);this.setPluginState({activePickerPanel:d,activeOverlayType:"PickerPanel"}),this.uppy.emit("dashboard:show-panel",a)},this.canEditFile=a=>{const{targets:o}=this.getPluginState();return P(this,V)[V](o).some(u=>this.uppy.getPlugin(u.id).canEditFile(a))},this.openFileEditor=a=>{const{targets:o}=this.getPluginState(),d=P(this,V)[V](o);this.setPluginState({showFileEditor:!0,fileCardFor:a.id||null,activeOverlayType:"FileEditor"}),d.forEach(u=>{this.uppy.getPlugin(u.id).selectFile(a)})},this.closeFileEditor=()=>{const{metaFields:a}=this.getPluginState();a&&a.length>0?this.setPluginState({showFileEditor:!1,activeOverlayType:"FileCard"}):this.setPluginState({showFileEditor:!1,fileCardFor:null,activeOverlayType:"AddFiles"})},this.saveFileEditor=()=>{const{targets:a}=this.getPluginState();P(this,V)[V](a).forEach(d=>{this.uppy.getPlugin(d.id).save()}),this.closeFileEditor()},this.openModal=()=>{const{promise:a,resolve:o}=Mt();if(this.savedScrollPosition=window.pageYOffset,this.savedActiveElement=document.activeElement,this.opts.disablePageScrollWhenModalOpen&&document.body.classList.add("uppy-Dashboard-isFixed"),this.opts.animateOpenClose&&this.getPluginState().isClosing){const d=()=>{this.setPluginState({isHidden:!1}),this.el.removeEventListener("animationend",d,!1),o()};this.el.addEventListener("animationend",d,!1)}else this.setPluginState({isHidden:!1}),o();return this.opts.browserBackButtonClose&&this.updateBrowserHistory(),document.addEventListener("keydown",this.handleKeyDownInModal),this.uppy.emit("dashboard:modal-open"),a},this.closeModal=a=>{var o;const d=(o=a==null?void 0:a.manualClose)!=null?o:!0,{isHidden:u,isClosing:h}=this.getPluginState();if(u||h)return;const{promise:c,resolve:p}=Mt();if(this.opts.disablePageScrollWhenModalOpen&&document.body.classList.remove("uppy-Dashboard-isFixed"),this.opts.animateOpenClose){this.setPluginState({isClosing:!0});const f=()=>{this.setPluginState({isHidden:!0,isClosing:!1}),this.superFocus.cancel(),this.savedActiveElement.focus(),this.el.removeEventListener("animationend",f,!1),p()};this.el.addEventListener("animationend",f,!1)}else this.setPluginState({isHidden:!0}),this.superFocus.cancel(),this.savedActiveElement.focus(),p();if(document.removeEventListener("keydown",this.handleKeyDownInModal),d&&this.opts.browserBackButtonClose){var g;(g=history.state)!=null&&g[this.modalName]&&history.back()}return this.uppy.emit("dashboard:modal-closed"),c},this.isModalOpen=()=>!this.getPluginState().isHidden||!1,this.requestCloseModal=()=>this.opts.onRequestCloseModal?this.opts.onRequestCloseModal():this.closeModal(),this.setDarkModeCapability=a=>{const{capabilities:o}=this.uppy.getState();this.uppy.setState({capabilities:{...o,darkMode:a}})},this.handleSystemDarkModeChange=a=>{const o=a.matches;this.uppy.log(`[Dashboard] Dark mode is ${o?"on":"off"}`),this.setDarkModeCapability(o)},this.toggleFileCard=(a,o)=>{const d=this.uppy.getFile(o);a?this.uppy.emit("dashboard:file-edit-start",d):this.uppy.emit("dashboard:file-edit-complete",d),this.setPluginState({fileCardFor:a?o:null,activeOverlayType:a?"FileCard":null})},this.toggleAddFilesPanel=a=>{this.setPluginState({showAddFilesPanel:a,activeOverlayType:a?"AddFiles":null})},this.addFiles=a=>{const o=a.map(d=>({source:this.id,name:d.name,type:d.type,data:d,meta:{relativePath:d.relativePath||d.webkitRelativePath||null}}));try{this.uppy.addFiles(o)}catch(d){this.uppy.log(d)}},this.startListeningToResize=()=>{this.resizeObserver=new ResizeObserver(a=>{const o=a[0],{width:d,height:u}=o.contentRect;this.setPluginState({containerWidth:d,containerHeight:u,areInsidesReadyToBeVisible:!0})}),this.resizeObserver.observe(this.el.querySelector(".uppy-Dashboard-inner")),this.makeDashboardInsidesVisibleAnywayTimeout=setTimeout(()=>{const a=this.getPluginState(),o=!this.opts.inline&&a.isHidden;!a.areInsidesReadyToBeVisible&&!o&&(this.uppy.log("[Dashboard] resize event didn’t fire on time: defaulted to mobile layout","warning"),this.setPluginState({areInsidesReadyToBeVisible:!0}))},1e3)},this.stopListeningToResize=()=>{this.resizeObserver.disconnect(),clearTimeout(this.makeDashboardInsidesVisibleAnywayTimeout)},this.recordIfFocusedOnUppyRecently=a=>{this.el.contains(a.target)?this.ifFocusedOnUppyRecently=!0:(this.ifFocusedOnUppyRecently=!1,this.superFocus.cancel())},this.disableInteractiveElements=a=>{var o;const d=["a[href]","input:not([disabled])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])",'[role="button"]:not([disabled])'],u=(o=P(this,G)[G])!=null?o:de(this.el.querySelectorAll(d)).filter(h=>!h.classList.contains("uppy-Dashboard-close"));for(const h of u)h.tagName==="A"?h.setAttribute("aria-disabled",a):h.disabled=a;a?P(this,G)[G]=u:P(this,G)[G]=null,this.dashboardIsDisabled=a},this.updateBrowserHistory=()=>{var a;(a=history.state)!=null&&a[this.modalName]||history.pushState({...history.state,[this.modalName]:!0},""),window.addEventListener("popstate",this.handlePopState,!1)},this.handlePopState=a=>{var o;this.isModalOpen()&&(!a.state||!a.state[this.modalName])&&this.closeModal({manualClose:!1}),!this.isModalOpen()&&(o=a.state)!=null&&o[this.modalName]&&history.back()},this.handleKeyDownInModal=a=>{a.keyCode===Bn&&this.requestCloseModal(),a.keyCode===Rt&&oi(a,this.getPluginState().activeOverlayType,this.el)},this.handleClickOutside=()=>{this.opts.closeModalOnClickOutside&&this.requestCloseModal()},this.handlePaste=a=>{this.uppy.iteratePlugins(d=>{d.type==="acquirer"&&(d.handleRootPaste==null||d.handleRootPaste(a))});const o=de(a.clipboardData.files);o.length>0&&(this.uppy.log("[Dashboard] Files pasted"),this.addFiles(o))},this.handleInputChange=a=>{a.preventDefault();const o=de(a.target.files);o.length>0&&(this.uppy.log("[Dashboard] Files selected through input"),this.addFiles(o))},this.handleDragOver=a=>{var o,d;a.preventDefault(),a.stopPropagation();const u=()=>{let g=!0;return this.uppy.iteratePlugins(f=>{f.canHandleRootDrop!=null&&f.canHandleRootDrop(a)&&(g=!0)}),g},h=()=>{const{types:g}=a.dataTransfer;return g.some(f=>f==="Files")},c=u(),p=h();if(!c&&!p||this.opts.disabled||this.opts.disableLocalFiles&&(p||!c)||!this.uppy.getState().allowNewUpload){a.dataTransfer.dropEffect="none",clearTimeout(this.removeDragOverClassTimeout);return}a.dataTransfer.dropEffect="copy",clearTimeout(this.removeDragOverClassTimeout),this.setPluginState({isDraggingOver:!0}),(o=(d=this.opts).onDragOver)==null||o.call(d,a)},this.handleDragLeave=a=>{var o,d;a.preventDefault(),a.stopPropagation(),clearTimeout(this.removeDragOverClassTimeout),this.removeDragOverClassTimeout=setTimeout(()=>{this.setPluginState({isDraggingOver:!1})},50),(o=(d=this.opts).onDragLeave)==null||o.call(d,a)},this.handleDrop=async a=>{var o,d;a.preventDefault(),a.stopPropagation(),clearTimeout(this.removeDragOverClassTimeout),this.setPluginState({isDraggingOver:!1}),this.uppy.iteratePlugins(p=>{p.type==="acquirer"&&(p.handleRootDrop==null||p.handleRootDrop(a))});let u=!1;const h=p=>{this.uppy.log(p,"error"),u||(this.uppy.info(p.message,"error"),u=!0)};this.uppy.log("[Dashboard] Processing dropped files");const c=await Bs(a.dataTransfer,{logDropError:h});c.length>0&&(this.uppy.log("[Dashboard] Files dropped"),this.addFiles(c)),(o=(d=this.opts).onDrop)==null||o.call(d,a)},this.handleRequestThumbnail=a=>{this.opts.waitForThumbnailsBeforeUpload||this.uppy.emit("thumbnail:request",a)},this.handleCancelThumbnail=a=>{this.opts.waitForThumbnailsBeforeUpload||this.uppy.emit("thumbnail:cancel",a)},this.handleKeyDownInInline=a=>{a.keyCode===Rt&&$s(a,this.getPluginState().activeOverlayType,this.el)},this.handlePasteOnBody=a=>{this.el.contains(document.activeElement)&&this.handlePaste(a)},this.handleComplete=a=>{let{failed:o}=a;this.opts.closeAfterFinish&&!(o!=null&&o.length)&&this.requestCloseModal()},this.handleCancelRestore=()=>{this.uppy.emit("restore-canceled")},Object.defineProperty(this,H,{writable:!0,value:()=>{if(this.opts.disableThumbnailGenerator)return;const a=600,o=this.uppy.getFiles();if(o.length===1){const d=this.uppy.getPlugin(`${this.id}:ThumbnailGenerator`);d==null||d.setOptions({thumbnailWidth:a});const u={...o[0],preview:void 0};d==null||d.requestThumbnail(u).then(()=>{d==null||d.setOptions({thumbnailWidth:this.opts.thumbnailWidth})})}}}),Object.defineProperty(this,re,{writable:!0,value:a=>{const o=a[0],{metaFields:d}=this.getPluginState(),u=d&&d.length>0,h=this.canEditFile(o);u&&this.opts.autoOpen==="metaEditor"?this.toggleFileCard(!0,o.id):h&&this.opts.autoOpen==="imageEditor"&&this.openFileEditor(o)}}),this.initEvents=()=>{if(this.opts.trigger&&!this.opts.inline){const a=At(this.opts.trigger);a?a.forEach(o=>o.addEventListener("click",this.openModal)):this.uppy.log("Dashboard modal trigger not found. Make sure `trigger` is set in Dashboard options, unless you are planning to call `dashboard.openModal()` method yourself","warning")}this.startListeningToResize(),document.addEventListener("paste",this.handlePasteOnBody),this.uppy.on("plugin-added",P(this,X)[X]),this.uppy.on("plugin-remove",this.removeTarget),this.uppy.on("file-added",this.hideAllPanels),this.uppy.on("dashboard:modal-closed",this.hideAllPanels),this.uppy.on("complete",this.handleComplete),this.uppy.on("files-added",P(this,H)[H]),this.uppy.on("file-removed",P(this,H)[H]),document.addEventListener("focus",this.recordIfFocusedOnUppyRecently,!0),document.addEventListener("click",this.recordIfFocusedOnUppyRecently,!0),this.opts.inline&&this.el.addEventListener("keydown",this.handleKeyDownInInline),this.opts.autoOpen&&this.uppy.on("files-added",P(this,re)[re])},this.removeEvents=()=>{const a=At(this.opts.trigger);!this.opts.inline&&a&&a.forEach(o=>o.removeEventListener("click",this.openModal)),this.stopListeningToResize(),document.removeEventListener("paste",this.handlePasteOnBody),window.removeEventListener("popstate",this.handlePopState,!1),this.uppy.off("plugin-added",P(this,X)[X]),this.uppy.off("plugin-remove",this.removeTarget),this.uppy.off("file-added",this.hideAllPanels),this.uppy.off("dashboard:modal-closed",this.hideAllPanels),this.uppy.off("complete",this.handleComplete),this.uppy.off("files-added",P(this,H)[H]),this.uppy.off("file-removed",P(this,H)[H]),document.removeEventListener("focus",this.recordIfFocusedOnUppyRecently),document.removeEventListener("click",this.recordIfFocusedOnUppyRecently),this.opts.inline&&this.el.removeEventListener("keydown",this.handleKeyDownInInline),this.opts.autoOpen&&this.uppy.off("files-added",P(this,re)[re])},this.superFocusOnEachUpdate=()=>{const a=this.el.contains(document.activeElement),o=document.activeElement===document.body||document.activeElement===null,d=this.uppy.getState().info.length===0,u=!this.opts.inline;d&&(u||a||o&&this.ifFocusedOnUppyRecently)?this.superFocus(this.el,this.getPluginState().activeOverlayType):this.superFocus.cancel()},this.afterUpdate=()=>{if(this.opts.disabled&&!this.dashboardIsDisabled){this.disableInteractiveElements(!0);return}!this.opts.disabled&&this.dashboardIsDisabled&&this.disableInteractiveElements(!1),this.superFocusOnEachUpdate()},this.saveFileCard=(a,o)=>{this.uppy.setFileMeta(o,a),this.toggleFileCard(!1,o)},Object.defineProperty(this,K,{writable:!0,value:a=>{const o=this.uppy.getPlugin(a.id);return{...a,icon:o.icon||this.opts.defaultPickerIcon,render:o.render}}}),Object.defineProperty(this,qe,{writable:!0,value:a=>{const o=this.uppy.getPlugin(a.id);return typeof o.isSupported!="function"?!0:o.isSupported()}}),Object.defineProperty(this,je,{writable:!0,value:Ve(a=>a.filter(o=>o.type==="acquirer"&&P(this,qe)[qe](o)).map(P(this,K)[K]))}),Object.defineProperty(this,We,{writable:!0,value:Ve(a=>a.filter(o=>o.type==="progressindicator").map(P(this,K)[K]))}),Object.defineProperty(this,V,{writable:!0,value:Ve(a=>a.filter(o=>o.type==="editor").map(P(this,K)[K]))}),this.render=a=>{const o=this.getPluginState(),{files:d,capabilities:u,allowNewUpload:h}=a,{newFiles:c,uploadStartedFiles:p,completeFiles:g,erroredFiles:f,inProgressFiles:v,inProgressNotPausedFiles:m,processingFiles:w,isUploadStarted:C,isAllComplete:y,isAllErrored:F,isAllPaused:T}=this.uppy.getObjectOfFilesPerState(),U=P(this,je)[je](o.targets),A=P(this,We)[We](o.targets),E=P(this,V)[V](o.targets);let B;return this.opts.theme==="auto"?B=u.darkMode?"dark":"light":B=this.opts.theme,["files","folders","both"].indexOf(this.opts.fileManagerSelectionType)<0&&(this.opts.fileManagerSelectionType="files",console.warn(`Unsupported option for "fileManagerSelectionType". Using default of "${this.opts.fileManagerSelectionType}".`)),En({state:a,isHidden:o.isHidden,files:d,newFiles:c,uploadStartedFiles:p,completeFiles:g,erroredFiles:f,inProgressFiles:v,inProgressNotPausedFiles:m,processingFiles:w,isUploadStarted:C,isAllComplete:y,isAllErrored:F,isAllPaused:T,totalFileCount:Object.keys(d).length,totalProgress:a.totalProgress,allowNewUpload:h,acquirers:U,theme:B,disabled:this.opts.disabled,disableLocalFiles:this.opts.disableLocalFiles,direction:this.opts.direction,activePickerPanel:o.activePickerPanel,showFileEditor:o.showFileEditor,saveFileEditor:this.saveFileEditor,closeFileEditor:this.closeFileEditor,disableInteractiveElements:this.disableInteractiveElements,animateOpenClose:this.opts.animateOpenClose,isClosing:o.isClosing,progressindicators:A,editors:E,autoProceed:this.uppy.opts.autoProceed,id:this.id,closeModal:this.requestCloseModal,handleClickOutside:this.handleClickOutside,handleInputChange:this.handleInputChange,handlePaste:this.handlePaste,inline:this.opts.inline,showPanel:this.showPanel,hideAllPanels:this.hideAllPanels,i18n:this.i18n,i18nArray:this.i18nArray,uppy:this.uppy,note:this.opts.note,recoveredState:a.recoveredState,metaFields:o.metaFields,resumableUploads:u.resumableUploads||!1,individualCancellation:u.individualCancellation,isMobileDevice:u.isMobileDevice,fileCardFor:o.fileCardFor,toggleFileCard:this.toggleFileCard,toggleAddFilesPanel:this.toggleAddFilesPanel,showAddFilesPanel:o.showAddFilesPanel,saveFileCard:this.saveFileCard,openFileEditor:this.openFileEditor,canEditFile:this.canEditFile,width:this.opts.width,height:this.opts.height,showLinkToFileUploadResult:this.opts.showLinkToFileUploadResult,fileManagerSelectionType:this.opts.fileManagerSelectionType,proudlyDisplayPoweredByUppy:this.opts.proudlyDisplayPoweredByUppy,hideCancelButton:this.opts.hideCancelButton,hideRetryButton:this.opts.hideRetryButton,hidePauseResumeButton:this.opts.hidePauseResumeButton,showRemoveButtonAfterComplete:this.opts.showRemoveButtonAfterComplete,containerWidth:o.containerWidth,containerHeight:o.containerHeight,areInsidesReadyToBeVisible:o.areInsidesReadyToBeVisible,isTargetDOMEl:this.isTargetDOMEl,parentElement:this.el,allowedFileTypes:this.uppy.opts.restrictions.allowedFileTypes,maxNumberOfFiles:this.uppy.opts.restrictions.maxNumberOfFiles,requiredMetaFields:this.uppy.opts.restrictions.requiredMetaFields,showSelectedFiles:this.opts.showSelectedFiles,showNativePhotoCameraButton:this.opts.showNativePhotoCameraButton,showNativeVideoCameraButton:this.opts.showNativeVideoCameraButton,nativeCameraFacingMode:this.opts.nativeCameraFacingMode,singleFileFullScreen:this.opts.singleFileFullScreen,handleCancelRestore:this.handleCancelRestore,handleRequestThumbnail:this.handleRequestThumbnail,handleCancelThumbnail:this.handleCancelThumbnail,isDraggingOver:o.isDraggingOver,handleDragOver:this.handleDragOver,handleDragLeave:this.handleDragLeave,handleDrop:this.handleDrop})},Object.defineProperty(this,Ge,{writable:!0,value:()=>{(this.opts.plugins||[]).forEach(o=>{const d=this.uppy.getPlugin(o);d?d.mount(this,d):this.uppy.log(`[Uppy] Dashboard could not find plugin '${o}', make sure to uppy.use() the plugins you are specifying`,"warning")})}}),Object.defineProperty(this,Ke,{writable:!0,value:()=>{this.uppy.iteratePlugins(P(this,X)[X])}}),Object.defineProperty(this,X,{writable:!0,value:a=>{var o;const d=["acquirer","editor"];a&&!((o=a.opts)!=null&&o.target)&&d.includes(a.type)&&(this.getPluginState().targets.some(h=>a.id===h.id)||a.mount(this,a))}}),this.install=()=>{this.setPluginState({isHidden:!0,fileCardFor:null,activeOverlayType:null,showAddFilesPanel:!1,activePickerPanel:void 0,showFileEditor:!1,metaFields:this.opts.metaFields,targets:[],areInsidesReadyToBeVisible:!1,isDraggingOver:!1});const{inline:a,closeAfterFinish:o}=this.opts;if(a&&o)throw new Error("[Dashboard] `closeAfterFinish: true` cannot be used on an inline Dashboard, because an inline Dashboard cannot be closed at all. Either set `inline: false`, or disable the `closeAfterFinish` option.");const{allowMultipleUploads:d,allowMultipleUploadBatches:u}=this.uppy.opts;(d||u)&&o&&this.uppy.log("[Dashboard] When using `closeAfterFinish`, we recommended setting the `allowMultipleUploadBatches` option to `false` in the Uppy constructor. See https://uppy.io/docs/uppy/#allowMultipleUploads-true","warning");const{target:h}=this.opts;h&&this.mount(h,this),this.opts.disableStatusBar||this.uppy.use(qt,{id:`${this.id}:StatusBar`,target:this,hideUploadButton:this.opts.hideUploadButton,hideRetryButton:this.opts.hideRetryButton,hidePauseResumeButton:this.opts.hidePauseResumeButton,hideCancelButton:this.opts.hideCancelButton,showProgressDetails:this.opts.showProgressDetails,hideAfterFinish:this.opts.hideProgressAfterFinish,locale:this.opts.locale,doneButtonHandler:this.opts.doneButtonHandler}),this.opts.disableInformer||this.uppy.use(Wt,{id:`${this.id}:Informer`,target:this}),this.opts.disableThumbnailGenerator||this.uppy.use(ti,{id:`${this.id}:ThumbnailGenerator`,thumbnailWidth:this.opts.thumbnailWidth,thumbnailHeight:this.opts.thumbnailHeight,thumbnailType:this.opts.thumbnailType,waitForThumbnailsBeforeUpload:this.opts.waitForThumbnailsBeforeUpload,lazy:!this.opts.waitForThumbnailsBeforeUpload}),this.darkModeMediaQuery=typeof window<"u"&&window.matchMedia?window.matchMedia("(prefers-color-scheme: dark)"):null;const c=this.darkModeMediaQuery?this.darkModeMediaQuery.matches:!1;if(this.uppy.log(`[Dashboard] Dark mode is ${c?"on":"off"}`),this.setDarkModeCapability(c),this.opts.theme==="auto"){var p;(p=this.darkModeMediaQuery)==null||p.addListener(this.handleSystemDarkModeChange)}P(this,Ge)[Ge](),P(this,Ke)[Ke](),this.initEvents()},this.uninstall=()=>{if(!this.opts.disableInformer){const d=this.uppy.getPlugin(`${this.id}:Informer`);d&&this.uppy.removePlugin(d)}if(!this.opts.disableStatusBar){const d=this.uppy.getPlugin(`${this.id}:StatusBar`);d&&this.uppy.removePlugin(d)}if(!this.opts.disableThumbnailGenerator){const d=this.uppy.getPlugin(`${this.id}:ThumbnailGenerator`);d&&this.uppy.removePlugin(d)}if((this.opts.plugins||[]).forEach(d=>{const u=this.uppy.getPlugin(d);u&&u.unmount()}),this.opts.theme==="auto"){var o;(o=this.darkModeMediaQuery)==null||o.removeListener(this.handleSystemDarkModeChange)}this.opts.disablePageScrollWhenModalOpen&&document.body.classList.remove("uppy-Dashboard-isFixed"),this.unmount(),this.removeEvents()},this.id=this.opts.id||"Dashboard",this.title="Dashboard",this.type="orchestrator",this.defaultLocale=Dn,this.opts.doneButtonHandler===void 0&&(this.opts.doneButtonHandler=()=>{this.uppy.clearUploadedFiles(),this.requestCloseModal()}),(n=(s=this.opts).onRequestCloseModal)!=null||(s.onRequestCloseModal=()=>this.closeModal()),this.i18nInit()}};Un.VERSION=On.version;export{Un as D,qt as S,Bs as g,Vs as i,de as t};
