import{R as i}from"./vendor-f36d475e.js";const w=i.createContext({}),x=!0;function C({baseColor:o,highlightColor:a,width:s,height:f,borderRadius:r,circle:u,direction:m,duration:c,enableAnimation:p=x,customHighlightBackground:l}){const e={};return m==="rtl"&&(e["--animation-direction"]="reverse"),typeof c=="number"&&(e["--animation-duration"]=`${c}s`),p||(e["--pseudo-element-display"]="none"),(typeof s=="string"||typeof s=="number")&&(e.width=s),(typeof f=="string"||typeof f=="number")&&(e.height=f),(typeof r=="string"||typeof r=="number")&&(e.borderRadius=r),u&&(e.borderRadius="50%"),typeof o<"u"&&(e["--base-color"]=o),typeof a<"u"&&(e["--highlight-color"]=a),typeof l=="string"&&(e["--custom-highlight-background"]=l),e}function A({count:o=1,wrapper:a,className:s,containerClassName:f,containerTestId:r,circle:u=!1,style:m,...c}){var p,l,e;const _=i.useContext(w),h={...c};for(const[t,n]of Object.entries(c))typeof n>"u"&&delete h[t];const d={..._,...h,circle:u},g={...m,...C(d)};let v="react-loading-skeleton";s&&(v+=` ${s}`);const O=(p=d.inline)!==null&&p!==void 0?p:!1,y=[],b=Math.ceil(o);for(let t=0;t<b;t++){let n=g;if(b>o&&t===b-1){const k=(l=n.width)!==null&&l!==void 0?l:"100%",S=o%1,$=typeof k=="number"?k*S:`calc(${k} * ${S})`;n={...n,width:$}}const E=i.createElement("span",{className:v,style:n,key:t},"‌");O?y.push(E):y.push(i.createElement(i.Fragment,{key:t},E,i.createElement("br",null)))}return i.createElement("span",{className:f,"data-testid":r,"aria-live":"polite","aria-busy":(e=d.enableAnimation)!==null&&e!==void 0?e:x},a?y.map((t,n)=>i.createElement(a,{key:n},t)):y)}export{A as S};
