import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{h as V,r as l,R as p}from"./vendor-f36d475e.js";import"./index-5deedf4a.js";import{M as Z,A as q,G as H,s as U}from"./index-cf5e6bc7.js";import{A as E,a as O}from"./index-55e4d382.js";import{B as R,a as J}from"./index-895fa99b.js";import{u as G}from"./react-hook-form-ff037c98.js";import{o as L}from"./yup-afe5cf51.js";import{c as T,a as $}from"./yup-2f6e2476.js";import{A as K}from"./index-ad319f83.js";import{M as N}from"./index-bf8d79cc.js";import{S as Q}from"./index-65bc3378.js";import{t as s}from"./i18next-7389dd8c.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-1e3e6bc5.js";import"./@hookform/resolvers-eb417cd0.js";const x=new Z,_e=()=>{V();const{state:o,dispatch:y}=l.useContext(q);l.useContext(H);const[u,i]=l.useState(!1),[z,b]=l.useState(!1),[n,f]=l.useState(!1),[d,j]=l.useState(!1),[c,g]=l.useState([]),[_,h]=p.useState(!1),[k,m]=p.useState(!1),S=T({cedula:$().required()}).required(),{register:P,handleSubmit:M,setError:W,setValue:I,formState:{errors:B,isValid:w}}=G({resolver:L(S),mode:"onChange"});p.useEffect(()=>{b(w)},[w]),p.useEffect(()=>{I("cedula",o.userDetails.cedula_number);const a=o.userDetails.cedula_image_link?JSON.parse(o.userDetails.cedula_image_link):{};f(a.front),j(a.back),g(o.userDetails.qr_link?JSON.parse(o.userDetails.qr_link):[])},[]);const D=async a=>{try{i(!0);const t=await x.callRawAPI("/v3/api/custom/chumpchange/provider/proof-of-identity",{cedula_number:a.cedula,cedula_image_link:JSON.stringify({front:n,back:d}),qr_link:JSON.stringify(c)},"POST");i(!1)}catch(t){console.log(t),i(!1),U(y,t==null?void 0:t.message,4e3,"error")}},v=async a=>{try{i(!0);let t=new FormData;t.append("file",a.target.files[0]);const r=await x.uploadImage(t);j(r.url),i(!1)}catch(t){i(!1),console.log(t)}},C=async a=>{try{i(!0);let t=new FormData;t.append("file",a.target.files[0]);const r=await x.uploadImage(t);f(r.url),i(!1)}catch(t){i(!1),console.log(t)}},F=async a=>{try{i(!0);let t=new FormData;t.append("file",a.target.files[0]);const r=await x.uploadImage(t);g(A=>[...A,r.url]),i(!1)}catch(t){i(!1),console.log(t)}};return e.jsxs(E,{children:[u?e.jsx(N,{showCloseButton:!1,children:e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsxs("div",{className:"text-lg font-semibold",children:[s("loading.uploading"),"..."]}),e.jsx("div",{className:"mt-12",children:e.jsx(Q,{})})]})}):"",k&&e.jsx(N,{closeModal:()=>m(!1),children:e.jsx("div",{className:" mt-10 flex h-max w-full flex-col items-center justify-center gap-8 pb-6 ",children:e.jsx("img",{className:" h-auto w-full max-w-[450px] object-cover ",src:_})})}),e.jsx(R,{}),e.jsxs("form",{onSubmit:M(D),className:"",children:[e.jsx(O,{title:s("provider.update_id.title"),className:" -mt-6 "}),e.jsx("div",{className:"flex flex-col gap-4",children:e.jsxs("div",{className:" flex flex-col items-center justify-start",children:[e.jsx("div",{className:"mt-4 font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:s("provider.update_id.sub_title_identity")}),e.jsx("div",{className:" mt-4 text-center font-['Poppins'] text-base font-medium text-black",children:s("provider.update_id.identity_desc")})]})}),e.jsxs("div",{className:" mt-4 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:s("provider.update_id.cedula_number")}),e.jsx(K,{type:"number",length:20,name:"cedula",placeholder:"15688189237",errors:B,register:P,icon:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M10.1331 9.05835C10.0498 9.05002 9.9498 9.05002 9.85814 9.05835C7.8748 8.99169 6.2998 7.36669 6.2998 5.36669C6.2998 3.32502 7.9498 1.66669 9.9998 1.66669C12.0415 1.66669 13.6998 3.32502 13.6998 5.36669C13.6915 7.36669 12.1165 8.99169 10.1331 9.05835Z",fill:"#8181A4",stroke:"#8181A4","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M5.9666 12.1333C3.94993 13.4833 3.94993 15.6833 5.9666 17.025C8.25827 18.5583 12.0166 18.5583 14.3083 17.025C16.3249 15.675 16.3249 13.475 14.3083 12.1333C12.0249 10.6083 8.2666 10.6083 5.9666 12.1333Z",stroke:"#828282","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})})]}),e.jsxs("div",{className:" mt-10 ",children:[e.jsx("div",{className:"font-['Poppins'] text-lg font-medium text-black",children:s("provider.update_id.cedula_card_title")}),e.jsxs("div",{className:"mt-3 flex gap-2",children:[n?e.jsxs("div",{className:"",children:[e.jsx("img",{className:"h-[127px] w-[120px] rounded-[20px]",src:n,onClick:()=>{m(!0),h(n)}}),e.jsx("div",{className:" mt-2 flex justify-center ",children:e.jsxs("span",{className:" relative text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:[s("provider.update_id.change"),e.jsx("input",{type:"file",className:"absolute left-0 top-[-100%] h-[200%] w-full cursor-pointer opacity-0",onChange:C})]})})]}):e.jsxs("div",{className:" relative flex h-[127px] w-[120px] flex-col items-center justify-center overflow-hidden rounded-[20px] bg-[#e0e0e0]",children:[e.jsxs("svg",{width:"48",height:"48",viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"47",height:"47",rx:"23.5",fill:"#56CCF2"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"47",height:"47",rx:"23.5",stroke:"white"}),e.jsx("path",{d:"M22.1667 16.3333C22.1667 15.597 22.7636 15 23.5 15C24.2364 15 24.8333 15.597 24.8333 16.3333V29.6667C24.8333 30.403 24.2364 31 23.5 31C22.7636 31 22.1667 30.403 22.1667 29.6667V16.3333Z",fill:"white"}),e.jsx("path",{d:"M16.8333 24.3333C16.097 24.3333 15.5 23.7364 15.5 23C15.5 22.2636 16.097 21.6667 16.8333 21.6667H30.1667C30.903 21.6667 31.5 22.2636 31.5 23C31.5 23.7364 30.903 24.3333 30.1667 24.3333H16.8333Z",fill:"white"})]}),e.jsx("div",{className:" mt-4 text-center font-['Poppins'] text-xs font-medium text-black ",children:s("provider.update_id.tap_to")}),e.jsx("div",{className:" text-center font-['Poppins'] text-xs font-medium text-black",children:s("provider.update_id.upload")}),e.jsx("input",{type:"file",className:"absolute left-0 top-[-100%] h-[200%] w-full cursor-pointer opacity-0",onChange:C})]}),d?e.jsxs("div",{className:"",children:[e.jsx("img",{className:"h-[127px] w-[120px] rounded-[20px]",src:d,onClick:()=>{m(!0),h(d)}}),e.jsx("div",{className:" mt-2 flex justify-center ",children:e.jsxs("span",{className:" relative text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:[s("provider.update_id.change"),e.jsx("input",{type:"file",className:"absolute left-0 top-[-100%] h-[200%] w-full cursor-pointer opacity-0",onChange:v})]})})]}):e.jsxs("div",{className:" relative flex h-[127px] w-[120px] flex-col items-center justify-center overflow-hidden rounded-[20px] bg-[#e0e0e0]",children:[e.jsxs("svg",{width:"48",height:"48",viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"47",height:"47",rx:"23.5",fill:"#56CCF2"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"47",height:"47",rx:"23.5",stroke:"white"}),e.jsx("path",{d:"M22.1667 16.3333C22.1667 15.597 22.7636 15 23.5 15C24.2364 15 24.8333 15.597 24.8333 16.3333V29.6667C24.8333 30.403 24.2364 31 23.5 31C22.7636 31 22.1667 30.403 22.1667 29.6667V16.3333Z",fill:"white"}),e.jsx("path",{d:"M16.8333 24.3333C16.097 24.3333 15.5 23.7364 15.5 23C15.5 22.2636 16.097 21.6667 16.8333 21.6667H30.1667C30.903 21.6667 31.5 22.2636 31.5 23C31.5 23.7364 30.903 24.3333 30.1667 24.3333H16.8333Z",fill:"white"})]}),e.jsx("div",{className:" mt-4 text-center font-['Poppins'] text-xs font-medium text-black ",children:s("provider.update_id.tap_to")}),e.jsx("div",{className:" text-center font-['Poppins'] text-xs font-medium text-black",children:s("provider.update_id.upload")}),e.jsx("input",{type:"file",className:"absolute left-0 top-[-100%] h-[200%] w-full cursor-pointer opacity-0",onChange:v})]})]})]}),e.jsxs("div",{className:" mt-2 ",children:[e.jsx("div",{className:"font-['Poppins'] text-lg font-medium text-black",children:s("provider.update_id.good_conduct_qr")}),e.jsxs("div",{className:"mt-3 flex gap-2",children:[c==null?void 0:c.map((a,t)=>e.jsx("div",{className:"w-[120px]",children:e.jsx("img",{className:"h-[127px] w-[120px] rounded-[20px]",src:a,onClick:()=>{m(!0),h(a)}})})),e.jsxs("div",{className:" relative flex h-[127px] w-[120px] flex-col items-center justify-center overflow-hidden rounded-[20px] bg-[#e0e0e0]",children:[e.jsxs("svg",{width:"48",height:"48",viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"47",height:"47",rx:"23.5",fill:"#56CCF2"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"47",height:"47",rx:"23.5",stroke:"white"}),e.jsx("path",{d:"M22.1667 16.3333C22.1667 15.597 22.7636 15 23.5 15C24.2364 15 24.8333 15.597 24.8333 16.3333V29.6667C24.8333 30.403 24.2364 31 23.5 31C22.7636 31 22.1667 30.403 22.1667 29.6667V16.3333Z",fill:"white"}),e.jsx("path",{d:"M16.8333 24.3333C16.097 24.3333 15.5 23.7364 15.5 23C15.5 22.2636 16.097 21.6667 16.8333 21.6667H30.1667C30.903 21.6667 31.5 22.2636 31.5 23C31.5 23.7364 30.903 24.3333 30.1667 24.3333H16.8333Z",fill:"white"})]}),e.jsx("div",{className:" mt-4 text-center font-['Poppins'] text-xs font-medium text-black ",children:s("provider.update_id.tap_to")}),e.jsx("div",{className:" text-center font-['Poppins'] text-xs font-medium text-black",children:s("provider.update_id.upload")}),e.jsx("input",{type:"file",className:"absolute left-0 top-[-100%] h-[200%] w-full cursor-pointer opacity-0",onChange:F})]})]})]}),e.jsx("div",{className:" mb-5 mt-10 ",children:e.jsx(J,{loading:u,disabled:u,type:"submit",children:"Update"})})]})]})};export{_e as default};
