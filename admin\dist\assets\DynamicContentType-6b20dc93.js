import{j as a}from"./@react-google-maps/api-ac2f9d6f.js";import{R as h}from"./vendor-4f06b3f4.js";import{M as v,ab as f}from"./index-06b5b6dd.js";import"./react-confirm-alert-525c3702.js";import"./moment-a9aaa855.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";const b=new v,x="https://via.placeholder.com/150?text=%20",E=({contentType:c,contentValue:o,setContentValue:d})=>{const[u,i]=h.useState(x),y=async m=>{const p=new FormData;p.append("file",m.target.files[0]);try{const e=await b.uploadImage(p);i(e.url),d(e.url)}catch(e){console.error(e)}};switch(c){case"text":return a.jsx(a.Fragment,{children:a.jsx("textarea",{className:"shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",rows:15,placeholder:"Content",onChange:m=>d(m.target.value),defaultValue:o})});case"image":return a.jsxs(a.Fragment,{children:[a.jsx("img",{src:f(o)?u:o,alt:"preview",height:150,width:150}),a.jsx("input",{type:"file",onChange:y,className:"shadow appearance-none border block  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"})]});case"number":return a.jsx("input",{type:"number",className:"shadow appearance-none border block  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",onChange:m=>d(m.target.value),defaultValue:o});case"team-list":return a.jsx(N,{setContentValue:d,contentValue:o});case"image-list":return a.jsx(k,{setContentValue:d,contentValue:o});case"captioned-image-list":return a.jsx(j,{setContentValue:d,contentValue:o});case"kvp":return a.jsx(I,{setContentValue:d,contentValue:o})}},k=({contentValue:c,setContentValue:o})=>{let d=[{key:"",value_type:"image",value:null}];f(c)||(d=JSON.parse(c));const[u,i]=h.useState(d),y=async p=>{const e=p.target.getAttribute("listkey"),t=new FormData;t.append("file",p.target.files[0]);try{const s=await b.uploadImage(t);i(r=>r.map((n,g)=>(g==e&&(n.value=s.url),n))),o(JSON.stringify(u))}catch(s){console.error(s)}},m=p=>{const e=p.target.getAttribute("listkey");i(t=>t.map((r,l)=>(l==e&&(r.key=p.target.value),r))),o(JSON.stringify(u))};return a.jsxs("div",{className:"block",children:[u.map((p,e)=>a.jsxs("div",{children:[a.jsx("img",{src:p.value!==null?p.value:x,alt:"preview",height:150,width:150}),a.jsxs("div",{className:"flex",children:[a.jsx("input",{className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",placeholder:"key",listkey:e,onChange:m,defaultValue:p.key}),a.jsx("input",{listkey:e,type:"file",accept:"image/*",onChange:y,className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"})]})]},e*.23)),a.jsx("button",{type:"button",className:"bg-blue-400 hover:bg-blue-700 text-white font-bold py-1 px-2 my-4 rounded focus:outline-none focus:shadow-outline",onClick:p=>i(e=>[...e,{key:"",value_type:"image",value:null}]),children:"+"})]})},j=({setContentValue:c,contentValue:o})=>{let d=[{key:"",value_type:"image",value:null,caption:""}];f(o)||(d=JSON.parse(o));const[u,i]=h.useState(d),y=async e=>{const t=e.target.getAttribute("listkey"),s=new FormData;s.append("file",e.target.files[0]);try{const r=await b.uploadImage(s);i(l=>l.map((g,w)=>(w==t&&(g.value=r.url),g))),c(JSON.stringify(u))}catch(r){console.error(r)}},m=e=>{const t=e.target.getAttribute("listkey");i(s=>s.map((l,n)=>(n==t&&(l.key=e.target.value),l))),c(JSON.stringify(u))},p=e=>{const t=e.target.getAttribute("listkey");i(s=>s.map((l,n)=>(n==t&&(l.caption=e.target.value),l))),c(JSON.stringify(u))};return a.jsxs("div",{className:"block",children:[u.map((e,t)=>a.jsxs("div",{children:[a.jsx("img",{src:e.value!==null?e.value:x,alt:"preview",height:150,width:150}),a.jsxs("div",{className:"flex",children:[a.jsx("input",{className:"shadow appearance-none border rounded w-full py-2 px-3 mr-2 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",placeholder:"Key",listkey:t,onChange:m,defaultValue:e.key}),a.jsx("input",{listkey:t,type:"file",accept:"image/*",onChange:y,className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"})]}),a.jsx("input",{className:"shadow block appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",placeholder:"Caption",listkey:t,onChange:p,defaultValue:e.caption})]},t*.23)),a.jsx("button",{type:"button",className:"bg-blue-400 hover:bg-blue-700 text-white font-bold py-1 px-2 my-4 rounded focus:outline-none focus:shadow-outline",onClick:e=>i(t=>[...t,{key:"",value_type:"image",value:null,caption:""}]),children:"+"})]})},N=({setContentValue:c,contentValue:o})=>{let d=[{name:"",image:null,title:""}];f(o)||(d=JSON.parse(o));const[u,i]=h.useState(d),y=async e=>{const t=e.target.getAttribute("listkey"),s=new FormData;s.append("file",e.target.files[0]);try{const r=await b.uploadImage(s);i(l=>l.map((g,w)=>(w==t&&(g.image=r.url),g))),c(JSON.stringify(u))}catch(r){console.error(r)}},m=e=>{const t=e.target.getAttribute("listkey");i(s=>s.map((l,n)=>(n==t&&(l.name=e.target.value),l))),c(JSON.stringify(u))},p=e=>{const t=e.target.getAttribute("listkey");i(s=>s.map((l,n)=>(n==t&&(l.title=e.target.value),l))),c(JSON.stringify(u))};return a.jsxs("div",{className:"block my-4",children:[u.map((e,t)=>a.jsxs("div",{className:"my-4",children:[a.jsx("input",{className:"shadow block appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",placeholder:"Title",listkey:t,onChange:p,defaultValue:e.title}),a.jsx("input",{className:"shadow appearance-none border rounded w-full py-2 px-3 mr-2 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",placeholder:"Name",listkey:t,onChange:m,defaultValue:e.name}),a.jsx("img",{src:e.image!==null?e.image:x,alt:"preview",height:150,width:150}),a.jsx("input",{listkey:t,type:"file",accept:"image/*",onChange:y,className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"})]},t*.23)),a.jsx("button",{type:"button",className:"bg-blue-400 hover:bg-blue-700 text-white font-bold py-1 px-2 my-4 rounded focus:outline-none focus:shadow-outline",onClick:e=>i(t=>[...t,{name:"",image:null,title:""}]),children:"+"})]})},I=({setContentValue:c,contentValue:o})=>{let d=[{key:"",value_type:"text",value:""}];f(o)||(d=JSON.parse(o));const[u,i]=h.useState(d),y=[{key:"text",value:"Text"},{key:"number",value:"Number"},{key:"json",value:"JSON Object"},{key:"url",value:"URL"}],m=t=>{const s=t.target.getAttribute("listkey");i(r=>r.map((n,g)=>(g==s&&(n.key=t.target.value),n))),c(JSON.stringify(u))},p=t=>{const s=t.target.getAttribute("listkey");i(r=>r.map((n,g)=>(g==s&&(n.value=t.target.value),n))),c(JSON.stringify(u))},e=t=>{const s=t.target.getAttribute("listkey");i(r=>r.map((n,g)=>(g==s&&(n.value_type=t.target.value),n))),c(JSON.stringify(u))};return a.jsxs("div",{className:"block",children:[u.map((t,s)=>a.jsxs("div",{className:"my-4",children:[a.jsx("input",{className:"shadow appearance-none border rounded w-full py-2 px-3 mr-2 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",placeholder:"Key",listkey:s,onChange:m,defaultValue:t.key}),a.jsx("select",{className:"shadow block border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",listkey:s,onChange:e,defaultValue:t.value_type,children:y.map((r,l)=>a.jsx("option",{value:r.key,children:r.value},l*122))}),a.jsx("input",{className:"shadow block appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",required:!0,placeholder:"Value",listkey:s,onChange:p,defaultValue:t.value})]},s*.23)),a.jsx("button",{type:"button",className:"bg-blue-400 hover:bg-blue-700 text-white font-bold py-1 px-2 my-4 rounded focus:outline-none focus:shadow-outline",onClick:t=>i(s=>[...s,{key:"",value_type:"text",value:""}]),children:"+"})]})};export{E as default};
