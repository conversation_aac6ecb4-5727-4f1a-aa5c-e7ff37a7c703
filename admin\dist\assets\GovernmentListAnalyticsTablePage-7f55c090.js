import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as s,b as he}from"./vendor-4f06b3f4.js";import{M as ge,G as fe,A as je,d as be,a as ye,E as ve,t as O}from"./index-06b5b6dd.js";import{o as we}from"./yup-2324a46a.js";import{u as Se}from"./react-hook-form-f3d72793.js";import{c as Ce,a as c}from"./yup-17027d7a.js";import{P as Ne}from"./index-19801678.js";import"./AddButton.module-98aac587.js";import{S as Pe}from"./index-2d8231e7.js";import{B as Ee,R as Ae,A as Fe}from"./index.esm-1a4cea12.js";import{M as z}from"./index-d97c616d.js";import{M as ke}from"./index-f6c8bd1f.js";import{L as Le}from"./index-6416aa2c.js";import{h as Me}from"./moment-a9aaa855.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@hookform/resolvers-1aa18522.js";import"./react-icons-e5379072.js";let m=new ge;const U=[{header:"title",accessor:"title"},{header:"full name",accessor:"full_name"},{header:"completion date",accessor:"completion_date"},{header:"job price",accessor:"job_price"},{header:"job type",accessor:"job_type"}],_e=[{header:"title",accessor:"title"},{header:"full name",accessor:"full_name"},{header:"completion date",accessor:"completion_date"},{header:"job price",accessor:"job_price"},{header:"job type",accessor:"job_type"}],V=["create_at","update_at","completion_date"],pt=()=>{const{dispatch:Y}=s.useContext(fe),{dispatch:w}=s.useContext(je),[u,S]=s.useState([]),[l,B]=s.useState(5),[$,G]=s.useState(0),[H,q]=s.useState(0),[p,K]=s.useState(0),[W,J]=s.useState(!1),[Q,X]=s.useState(!1),[C,N]=s.useState(!1),[P,E]=s.useState(!1),[i,x]=s.useState([]),[Z,h]=s.useState([]),[A,De]=s.useState("eq"),[g,f]=s.useState(!0),[F,ee]=s.useState(!0),[k,L]=s.useState(!1),[te,M]=s.useState(!1),[se,j]=s.useState(!1),[_,ae]=s.useState(null),[re,b]=s.useState(!1),[oe,Te]=s.useState();he();const y=s.useRef(null),ie=Ce({id:c(),email:c(),role:c(),status:c()}),{register:Re,handleSubmit:ne,formState:{errors:Ie}}=Se({resolver:we(ie)});function le(){d(p-1,l)}function de(){d(p+1,l)}const D=(t,r,a)=>{h(o=>{const n={...o};return delete n[t],{...n,[t]:a}})},T=()=>{d(1,l,{},Z)},ce=t=>{d(1,l,{},t)};async function d(t,r,a={},o={}){f(!0),console.log("filters >> ",o);try{m.setTable("user");const n=await m.callRawAPI("/v3/api/custom/chumpchange/government/jobs-and-services-list",{page:t,limit:r,filters:o},"POST");n&&f(!1);const{list:pe,total_count:xe,limit:Oe,num_pages:I,current_page:v}=n.data;S(pe),G(I),K(v),q(xe),J(v>1),X(v+1<=I)}catch(n){f(!1),console.log("ERROR",n),O(w,n.message)}}s.useEffect(()=>{Y({type:"SETPATH",payload:{path:"analytics"}});const r=setTimeout(async()=>{await d(1,l)},700);return()=>{clearTimeout(r)}},[F]);const R=t=>{y.current&&!y.current.contains(t.target)&&N(!1)};s.useEffect(()=>(document.addEventListener("mousedown",R),()=>{document.removeEventListener("mousedown",R)}),[]);const me=()=>{x([]),h([]),d(1,l)},ue=async t=>{try{b(!0),S(r=>r.filter(a=>a.id!==t)),m.setTable("user"),await m.callRestAPI({id:t},"DELETE"),b(!1),j(!1)}catch(r){b(!1),j(!1),console.log("ERROR",r),O(w,r.message)}};return e.jsxs("div",{className:"px-8",children:[e.jsxs("div",{className:"mt-4 inline-flex h-[154px] w-[261px] flex-col items-center justify-center gap-6 rounded-lg border border-[#e4e6eb] bg-white p-4 shadow",children:[e.jsx("div",{className:" font-['Inter'] text-3xl font-semibold leading-[38px] text-[#0f1728]",children:H}),e.jsx("div",{className:" font-['Poppins'] text-base font-medium leading-normal text-[#0f1728]",children:"Total User"})]}),e.jsxs("div",{className:"mt-4 rounded-lg border border-[#eaecf0] ",children:[e.jsxs("div",{className:"flex items-center justify-between px-4 py-4 ",children:[e.jsx("form",{className:"relative rounded bg-white",onSubmit:ne(T),children:e.jsx("div",{className:"flex items-center gap-4 text-nowrap text-gray-700",children:e.jsxs("div",{className:"relative",ref:y,children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-1",onClick:()=>N(!C),children:[e.jsx(Ee,{}),e.jsx("span",{children:"Filters"}),i.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:i.length})]}),C&&e.jsx("div",{className:"top-fill filter-form-holder absolute left-0 z-10 mt-4 w-[500px] min-w-[90%] rounded-md border border-gray-200 bg-white shadow-lg",children:e.jsxs("div",{className:"p-4",children:[i==null?void 0:i.map((t,r)=>e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:" mb-3 w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",children:t}),console.log("optoin",V.includes(t),t),t=="status"?e.jsxs("select",{name:"",id:"",className:" mb-3 w-1/3 rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:a=>D(t,A,a.target.value),children:[e.jsx("option",{value:"",children:"Select"}),e.jsx("option",{value:"0",children:"Inactive"}),e.jsx("option",{value:"1",children:"Active"}),e.jsx("option",{value:"2",children:"Suspend"})]}):e.jsx("input",{type:V.includes(t)?"date":"text",placeholder:"Enter value...",className:" mb-3 w-1/3 rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:a=>D(t,A,a.target.value)}),e.jsx("div",{className:"mt-[-10px] w-1/12",children:e.jsx(Ae,{className:" cursor-pointer text-xl",onClick:()=>{x(a=>a.filter(o=>o!==t)),h(a=>{const o={...a};return delete o[t],ce(o),o})}})})]},r)),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out ",onClick:()=>{E(!P)},children:[e.jsx(Fe,{}),"Add filter"]}),P&&e.jsx("div",{className:"absolute top-11 z-10 bg-white px-5 py-3 text-gray-600 shadow-md",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:_e.map(t=>e.jsx("li",{className:`${i.includes(t.accessor)?"cursor-not-allowed text-gray-400":"cursor-pointer"}`,onClick:()=>{i.includes(t.accessor)||x(r=>[...r,t.accessor]),E(!1)},children:t.header},t.header))})}),i.length>0&&e.jsx("div",{onClick:me,className:"inline-block cursor-pointer  rounded px-6  py-2.5 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out",children:"Clear all filter"})]}),e.jsx("button",{type:"button",onClick:T,className:"mt-4 inline-block cursor-pointer rounded bg-blue-500 px-6 py-2.5 font-medium leading-tight text-white transition duration-150 ease-in-out",children:"Apply Filters"})]})})]})})}),e.jsxs("button",{onClick:()=>{be(u)},className:"rounded-lgflex inline-flex h-10  items-center justify-center gap-2 rounded-lg border border-[#cfd4dc] bg-white px-4 py-2.5 font-['Poppins'] text-sm font-medium leading-tight text-[#344053] shadow",children:[e.jsx("svg",{width:"20",height:"18",viewBox:"0 0 20 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M13.3335 12.3334L10.0002 9.00003M10.0002 9.00003L6.66688 12.3334M10.0002 9.00003V16.5M16.9919 14.325C17.8047 13.8819 18.4467 13.1808 18.8168 12.3322C19.1868 11.4837 19.2637 10.5361 19.0354 9.63894C18.807 8.74182 18.2865 7.94629 17.5558 7.3779C16.8251 6.80951 15.9259 6.50064 15.0002 6.50003H13.9502C13.698 5.5244 13.2278 4.61864 12.5752 3.85085C11.9225 3.08307 11.1042 2.47324 10.182 2.0672C9.25967 1.66116 8.25734 1.46949 7.25031 1.5066C6.24328 1.5437 5.25777 1.80861 4.36786 2.28142C3.47795 2.75422 2.7068 3.42261 2.1124 4.23635C1.51799 5.05008 1.11579 5.98797 0.936028 6.97952C0.756269 7.97107 0.803632 8.99047 1.07456 9.96108C1.34548 10.9317 1.83291 11.8282 2.50021 12.5834",stroke:"#344054",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),"Export as CSV"]})]}),g?e.jsx(Pe,{}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"overflow-x-auto border-b border-gray-200 ",children:[e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:U.map((t,r)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},r))})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:u.map((t,r)=>e.jsx("tr",{children:U.map((a,o)=>a.accessor=="completion_date"||a.accessor=="create_at"||a.accessor=="update_at"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[a.accessor]?Me(t[a.accessor]).format("DD/MM/YYYY"):"N/A"},o):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[a.accessor]&&t[a.accessor]!==" "?t[a.accessor]:"N/A"},o))},r))})]}),g&&e.jsx(e.Fragment,{children:e.jsx("p",{className:" px-10 py-3 text-xl capitalize ",children:"Loading..."})}),!g&&u.length===0&&e.jsx(e.Fragment,{children:e.jsx("p",{className:" px-10 py-3 text-xl capitalize ",children:"You Don't have any User"})})]})})]}),e.jsx(Ne,{currentPage:p,pageCount:$,pageSize:l,canPreviousPage:W,canNextPage:Q,updatePageSize:t=>{B(t),d(1,t)},previousPage:le,nextPage:de}),e.jsx(Le,{children:e.jsx(ke,{open:se,actionHandler:()=>{ue(_)},closeModalFunction:()=>{ae(null),j(!1)},title:"Delete Provider ",message:`You are about to delete Provider ${_}, note that this action is irreversible`,acceptText:"DELETE",rejectText:"CANCEL",loading:re})}),e.jsx(z,{isModalActive:te,closeModalFn:()=>M(!1),children:e.jsx(ye,{setSidebar:M})}),k&&e.jsx(z,{isModalActive:k,closeModalFn:()=>L(!1),children:e.jsx(ve,{activeId:oe,setSidebar:L,setIsUpdate:ee,isUpdate:F})})]})};export{pt as default};
