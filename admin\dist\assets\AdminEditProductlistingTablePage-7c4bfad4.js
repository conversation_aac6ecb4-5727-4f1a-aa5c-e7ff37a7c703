import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as l,b as O,r as s,h as G}from"./vendor-4f06b3f4.js";import{u as Q}from"./react-hook-form-f3d72793.js";import{o as $}from"./yup-2324a46a.js";import{c as B,a as i}from"./yup-17027d7a.js";import{M as H,A as J,G as K,t as V,s as z}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as m}from"./MkdInput-ff3aa862.js";import{I as W}from"./InteractiveButton-8f7d74ee.js";import{S as X}from"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";let f=new H;const Re=u=>{var S,I,P;const{dispatch:w}=l.useContext(J),v=B({product_name:i(),product_id:i(),user_id:i(),price:i(),quantity:i(),item_condition:i(),description:i(),product_images:i(),status:i()}).required(),{dispatch:g}=l.useContext(K),[y,Y]=l.useState({}),[_,b]=l.useState(!1),[E,h]=l.useState(!1);O();const[Z,T]=s.useState(""),[ee,k]=s.useState(0),[te,q]=s.useState(0),[se,D]=s.useState(0),[oe,A]=s.useState(0),[ie,R]=s.useState(""),[ae,C]=s.useState(""),[N,F]=s.useState(""),[re,U]=s.useState(""),{register:r,handleSubmit:L,setError:j,setValue:o,formState:{errors:a}}=Q({resolver:$(v)}),c=G();s.useEffect(function(){(async function(){try{h(!0),f.setTable("productlisting");const e=await f.callRestAPI({id:u.activeId?u.activeId:Number(c==null?void 0:c.id)},"GET");e.error||(o("product_name",e.model.product_name),o("product_id",e.model.product_id),o("user_id",e.model.user_id),o("price",e.model.price),o("quantity",e.model.quantity),o("item_condition",e.model.item_condition),o("description",e.model.description),o("product_images",e.model.product_images),o("status",e.model.status),T(e.model.product_name),k(e.model.product_id),q(e.model.user_id),D(e.model.price),A(e.model.quantity),R(e.model.item_condition),C(e.model.description),F(e.model.product_images),U(e.model.status),h(!1))}catch(e){h(!1),console.log("error",e),V(w,e.message)}})()},[]);const M=async e=>{b(!0);try{f.setTable("productlisting");for(let p in y){let n=new FormData;n.append("file",y[p].file);let x=await f.uploadImage(n);e[p]=x.url}const d=await f.callRestAPI({id:u.activeId?u.activeId:Number(c==null?void 0:c.id),product_name:e.product_name,product_id:e.product_id,user_id:e.user_id,price:e.price,quantity:e.quantity,item_condition:e.item_condition,description:e.description,status:e.status},"PUT");if(!d.error)z(g,"Updated"),g({type:"REFRESH_DATA",payload:{refreshData:!0}}),u.setSidebar(!1);else if(d.validation){const p=Object.keys(d.validation);for(let n=0;n<p.length;n++){const x=p[n];j(x,{type:"manual",message:d.validation[x]})}}b(!1)}catch(d){b(!1),console.log("Error",d),j("product_name",{type:"manual",message:d.message})}};return l.useEffect(()=>{g({type:"SETPATH",payload:{path:"productlisting"}})},[]),t.jsxs("div",{className:" mx-auto rounded   p-5 shadow-md",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Productlisting"}),E?t.jsx(X,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:L(M),children:[t.jsx(m,{type:"text",page:"edit",name:"product_name",errors:a,label:"Product Name",placeholder:"Product Name",register:r,className:""}),t.jsx(m,{type:"number",page:"edit",name:"product_id",errors:a,label:"Product Type Id",placeholder:"Product Type Id",register:r,className:""}),t.jsx(m,{type:"number",page:"edit",name:"user_id",errors:a,label:"User Id",placeholder:"User Id",register:r,className:""}),t.jsx(m,{type:"number",page:"edit",name:"price",errors:a,label:"Price",placeholder:"Price",register:r,className:""}),t.jsx(m,{type:"number",page:"edit",name:"quantity",errors:a,label:"Quantity",placeholder:"Quantity",register:r,className:""}),t.jsx(m,{type:"text",page:"edit",name:"item_condition",errors:a,label:"Item Condition",placeholder:"Item Condition",register:r,className:""}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"description",children:"Description"}),t.jsx("textarea",{placeholder:"Description",...r("description"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(S=a.description)!=null&&S.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-xs italic text-red-500",children:(I=a.description)==null?void 0:I.message})]}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"product_images",children:"Product Images"}),t.jsx("div",{className:"",children:N?(P=JSON.parse(N))==null?void 0:P.map(e=>t.jsx("div",{className:"",children:t.jsx("img",{src:e,alt:"",className:"h-10 w-auto"})})):""})]}),t.jsx(W,{type:"submit",className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",loading:_,disable:_,children:"Submit"})]})]})};export{Re as default};
