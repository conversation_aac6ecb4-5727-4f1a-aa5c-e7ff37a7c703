import{j as t}from"./@react-google-maps/api-afbf18d5.js";import{r as c}from"./vendor-f36d475e.js";import{_ as m}from"./MoonLoader-4d8718ee.js";const b=({loading:e=!1,disabled:s,children:r,type:o="button",className:f,loaderclasses:a,onClick:i,color:n="#ffffff"})=>{const d={borderColor:"#ffffff"},l=c.useId();return t.jsx("button",{type:o,disabled:s,className:`flex h-12 w-full items-center justify-center gap-5 rounded-2xl bg-[#56ccf2] font-['Poppins'] text-base font-semibold text-white disabled:opacity-25 ${f}`,onClick:i,children:t.jsxs(t.Fragment,{children:[t.jsx(m,{color:n,loading:e,cssOverride:d,size:20,className:a,"data-testid":l}),t.jsx("span",{children:r})]})})};export{b as default};
