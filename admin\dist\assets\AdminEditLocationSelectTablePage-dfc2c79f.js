import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as i,b as P,r as n,h as F}from"./vendor-4f06b3f4.js";import{u as C}from"./react-hook-form-f3d72793.js";import{o as M}from"./yup-2324a46a.js";import{c as O,a as g}from"./yup-17027d7a.js";import{M as $,A as B,G as H,t as U,s as q}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as b}from"./MkdInput-ff3aa862.js";import{I as K}from"./InteractiveButton-8f7d74ee.js";import{S as V}from"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";let c=new $;const Ne=l=>{var w,E;const{dispatch:N}=i.useContext(B),v=O({address:g(),latitude:g(),longitude:g(),geolocation:g()}).required(),{dispatch:f}=i.useContext(H),[y,z]=i.useState({}),[S,x]=i.useState(!1),[L,h]=i.useState(!1),A=P(),[J,I]=n.useState(""),[Q,k]=n.useState(""),[W,R]=n.useState(""),[X,T]=n.useState(""),{register:m,handleSubmit:D,setError:j,setValue:u,formState:{errors:r}}=C({resolver:M(v)}),a=F();n.useEffect(function(){(async function(){try{h(!0),c.setTable("location");const e=await c.callRestAPI({id:l.activeId?l.activeId:Number(a==null?void 0:a.id)},"GET");e.error||(u("address",e.model.address),u("latitude",e.model.latitude),u("longitude",e.model.longitude),u("geolocation",e.model.geolocation),I(e.model.address),k(e.model.latitude),R(e.model.longitude),T(e.model.geolocation),h(!1))}catch(e){h(!1),console.log("error",e),U(N,e.message)}})()},[]);const G=async e=>{x(!0);try{c.setTable("location");for(let d in y){let s=new FormData;s.append("file",y[d].file);let p=await c.uploadImage(s);e[d]=p.url}const o=await c.callRestAPI({id:l.activeId?l.activeId:Number(a==null?void 0:a.id),address:e.address,latitude:e.latitude,longitude:e.longitude,geolocation:e.geolocation},"PUT");if(!o.error)q(f,"Updated"),A("/admin/location"),f({type:"REFRESH_DATA",payload:{refreshData:!0}}),l.setSidebar(!1);else if(o.validation){const d=Object.keys(o.validation);for(let s=0;s<d.length;s++){const p=d[s];j(p,{type:"manual",message:o.validation[p]})}}x(!1)}catch(o){x(!1),console.log("Error",o),j("address",{type:"manual",message:o.message})}};return i.useEffect(()=>{f({type:"SETPATH",payload:{path:"location"}})},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Location"}),L?t.jsx(V,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:D(G),children:[t.jsx(b,{type:"text",page:"edit",name:"address",errors:r,label:"Address",placeholder:"Address",register:m,className:""}),t.jsx(b,{page:"edit",name:"latitude",errors:r,label:"Latitude",placeholder:"Latitude",register:m,className:""}),t.jsx(b,{page:"edit",name:"longitude",errors:r,label:"Longitude",placeholder:"Longitude",register:m,className:""}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"geolocation",children:"Geolocation"}),t.jsx("textarea",{placeholder:"Geolocation",...m("geolocation"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(w=r.geolocation)!=null&&w.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(E=r.geolocation)==null?void 0:E.message})]}),t.jsx(K,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:S,disable:S,children:"Submit"})]})]})};export{Ne as default};
