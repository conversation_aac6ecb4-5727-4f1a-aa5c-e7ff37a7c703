import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as i,b as k,r,h as C}from"./vendor-4f06b3f4.js";import{u as F}from"./react-hook-form-f3d72793.js";import{o as L}from"./yup-2324a46a.js";import{c as M,a as c}from"./yup-17027d7a.js";import{M as q,A as G,G as O,t as B,s as H}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as p}from"./MkdInput-ff3aa862.js";import{I as U}from"./InteractiveButton-8f7d74ee.js";import{S as $}from"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";let f=new q;const Ee=n=>{const{dispatch:N}=i.useContext(G),j=M({name:c(),amount:c(),description:c(),status:c().required(),subsidized:c().required()}).required(),{dispatch:x}=i.useContext(O),[g,K]=i.useState({}),[v,S]=i.useState(!1),[E,h]=i.useState(!1),w=k(),[V,A]=r.useState(""),[Y,I]=r.useState(0),[J,z]=r.useState(""),[Q,D]=r.useState(""),[W,P]=r.useState(""),{register:l,handleSubmit:R,setError:y,setValue:m,formState:{errors:d}}=F({resolver:L(j)}),a=C();r.useEffect(function(){(async function(){try{h(!0),f.setTable("plan");const e=await f.callRestAPI({id:n.activeId?n.activeId:Number(a==null?void 0:a.id)},"GET");e.error||(m("name",e.model.name),m("amount",e.model.amount),m("description",e.model.description),m("status",e.model.status),m("subsidized",e.model.subsidized),A(e.model.name),I(e.model.amount),z(e.model.description),D(e.model.status),P(e.model.subsidized),h(!1))}catch(e){h(!1),console.log("error",e),B(N,e.message)}})()},[]);const T=async e=>{S(!0);try{f.setTable("plan");for(let u in g){let o=new FormData;o.append("file",g[u].file);let b=await f.uploadImage(o);e[u]=b.url}const s=await f.callRestAPI({id:n.activeId?n.activeId:Number(a==null?void 0:a.id),name:e.name,amount:e.amount,description:e.description,status:e.status,subsidized:e.subsidized},"PUT");if(!s.error)H(x,"Updated"),w("/admin/plan"),x({type:"REFRESH_DATA",payload:{refreshData:!0}}),n.setSidebar(!1);else if(s.validation){const u=Object.keys(s.validation);for(let o=0;o<u.length;o++){const b=u[o];y(b,{type:"manual",message:s.validation[b]})}}S(!1)}catch(s){S(!1),console.log("Error",s),y("name",{type:"manual",message:s.message})}};return i.useEffect(()=>{x({type:"SETPATH",payload:{path:"plan"}})},[]),t.jsxs("div",{className:" mx-auto rounded   p-5 shadow-md",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Plan"}),E?t.jsx($,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:R(T),children:[t.jsx(p,{type:"text",page:"edit",name:"name",errors:d,label:"Name",placeholder:"Name",register:l,className:""}),t.jsx(p,{type:"number",page:"edit",name:"amount",errors:d,label:"Amount",placeholder:"Amount",register:l,className:""}),t.jsx(p,{type:"text",page:"edit",name:"description",errors:d,label:"Description",placeholder:"Description",register:l,className:""}),t.jsx(p,{type:"dropdown",page:"edit",name:"status",errors:d,label:"Status",placeholder:"Select Status",register:l,className:"",options:[{value:"active",name:"Active"},{value:"inactive",name:"Inactive"}]}),t.jsx(p,{type:"dropdown",page:"edit",name:"subsidized",errors:d,label:"Subsidized",placeholder:"Select Subsidized",register:l,className:"",options:[{value:0,name:"No"},{value:1,name:"Yes"}]}),t.jsx(U,{type:"submit",className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",loading:v,disable:v,children:"Submit"})]})]})};export{Ee as default};
