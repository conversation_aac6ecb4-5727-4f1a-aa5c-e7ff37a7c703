import{j as t}from"./@react-google-maps/api-ee55a349.js";import{i as k,r as m,R as x}from"./vendor-b16525a8.js";import"./index-9ef1c042.js";import{M as A,A as C,G as R,o as S,r as B,t as d}from"./index-09a1718e.js";import{B as w,R as E,a as I}from"./index-d54cffea.js";import{P as z}from"./index-49471902.js";import{S as b}from"./index-5a645c18.js";import{M as T}from"./index-243c4859.js";import{t as r}from"./i18next-7389dd8c.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";const n=new A,re=()=>{var u,f,p,h,j,N;const s=k(),{state:G,dispatch:c}=m.useContext(C);m.useContext(R);const[e,g]=m.useState(null),[v,l]=x.useState(!0),[y,i]=x.useState(!1),o=async()=>{try{l(!0),n.setTable("user_alerts");const a=await n.callRestAPI({id:Number(s==null?void 0:s.id),join:"service_alerts,location,ambulant,address"},"GET");a.error||(g(a.model),l(!1))}catch(a){l(!1),console.log("error",a),d(c,a.message)}},_=async()=>{try{i(!0),(await n.callRawAPI(`/v3/api/custom/chumpchange/user/alert/activate/${s==null?void 0:s.id}`,{},"POST")).error||await o(),i(!1)}catch(a){i(!1),d(c,a.message)}},P=async()=>{console.log("clieck");try{i(!0),(await n.callRawAPI(`/v3/api/custom/chumpchange/user/alert/deactivate/${s==null?void 0:s.id}`,{task_id:Number(s==null?void 0:s.id),provider_status:status},"POST")).error||await o(),i(!1)}catch(a){i(!1),d(c,a.message)}};return x.useEffect(()=>{(async function(){await o()})()},[]),t.jsxs("div",{className:"p-5",children:[y&&t.jsx(T,{showCloseButton:!1,children:t.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[t.jsxs("div",{className:"text-lg font-semibold",children:[r("loading.creating"),"..."]}),t.jsx("div",{className:"mt-12",children:t.jsx(b,{})})]})}),t.jsxs("div",{className:"relative flex w-full items-center justify-center ",children:[t.jsx("div",{className:" absolute left-0 top-0 ",children:t.jsx(w,{link:"/user/my-alerts"})}),t.jsxs("div",{className:"",children:[t.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:r("user.view_alert.title")}),t.jsxs("div",{className:"text-center font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:[r("user.view_alert.active"),":"," ",(u=e==null?void 0:e.service_alerts)!=null&&u.name?(f=e==null?void 0:e.service_alerts)==null?void 0:f.name:"N/A"]})]})]}),t.jsx(z,{}),v?t.jsx("div",{className:" flex h-screen w-full items-center justify-center ",children:t.jsx(b,{})}):t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:" mt-9 ",children:[t.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[t.jsx("div",{className:"font-['Poppins'] text-sm font-medium capitalize text-[#8080a3]",children:r("user.view_alert.alert_if")}),t.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:(p=e==null?void 0:e.ambulant)!=null&&p.name?(h=e==null?void 0:e.ambulant)==null?void 0:h.name:""})]}),t.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[t.jsx("div",{className:"font-['Poppins'] text-sm font-medium capitalize text-[#8080a3]",children:r("user.view_alert.is_within")}),t.jsxs("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:[(j=e==null?void 0:e.service_alerts)!=null&&j.distance?(N=e==null?void 0:e.service_alerts)==null?void 0:N.distance:"N/A"," ",r("user.view_alert.meters")]})]}),t.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[t.jsx("div",{className:"font-['Poppins'] text-sm font-medium capitalize text-[#8080a3]",children:r("user.view_alert.location")}),t.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:t.jsx("button",{onClick:()=>{S(Number(e==null?void 0:e.latitude),Number(e==null?void 0:e.longitude))},className:" text-right text-[#4fa7f9] underline ",children:e!=null&&e.address?B(e==null?void 0:e.address):"N/A"})})]}),t.jsxs("div",{className:"flex justify-between border-t border-[#8181a4]/20 py-3 ",children:[t.jsxs("div",{className:"font-['Poppins'] text-sm font-medium capitalize text-[#8080a3] ",children:[r("user.view_alert.my_message"),":"]}),t.jsx("div",{className:"w-[165px] text-right font-['Poppins'] text-sm font-light leading-[16.80px] text-black",children:e!=null&&e.message?e==null?void 0:e.message:"N/A"})]})]}),t.jsx("div",{className:"my-5 text-left font-['Poppins'] text-xs font-medium tracking-tight text-black",children:r("user.view_alert.info")}),t.jsx("div",{className:"mt-10 flex flex-col gap-3",children:(e==null?void 0:e.status)=="active"?t.jsx(E,{onClick:P,className:"uppercase opacity-[0.85] ",children:r("user.view_alert.inactive")}):t.jsx(I,{onClick:_,className:"uppercase opacity-[0.85] ",children:r("user.view_alert.re_active")})})]})]})};export{re as default};
