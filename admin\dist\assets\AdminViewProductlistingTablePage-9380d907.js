import{j as s}from"./@react-google-maps/api-ac2f9d6f.js";import{R as i,h as o}from"./vendor-4f06b3f4.js";import"./yup-17027d7a.js";import{M as h,G as r,t as f}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{S as j}from"./index-2d8231e7.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";let t=new h;const D=()=>{const{dispatch:m}=i.useContext(r),{dispatch:d}=i.useContext(r),[e,n]=i.useState({}),[x,c]=i.useState(!0),l=o();return i.useEffect(function(){(async function(){try{c(!0),t.setTable("productlisting");const a=await t.callRestAPI({id:Number(l==null?void 0:l.id),join:""},"GET");a.error||(n(a.model),c(!1))}catch(a){c(!1),console.log("error",a),f(d,a.message)}})()},[]),i.useEffect(()=>{m({type:"SHOW_BACKBUTTON",payload:{showBackButton:!0}})},[]),s.jsx("div",{className:" mx-auto rounded  p-5 shadow-md",children:x?s.jsx(j,{}):s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Product Name"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.product_name})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Product Type Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.product_type_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"User Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.user_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Price"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.price})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Quantity"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.quantity})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Item Condition"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.item_condition})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Description"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.description})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Product Images"}),s.jsx("div",{className:"flex-1",children:e!=null&&e.product_images?JSON.parse(e==null?void 0:e.product_images).map(a=>s.jsx("a",{className:"text-blue-500",target:"_blank",href:a,rel:"noreferrer",children:"View"})):"N/A"})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Status"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.status})]})})]})})};export{D as default};
