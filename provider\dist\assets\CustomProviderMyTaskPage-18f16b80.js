import{j as t}from"./@react-google-maps/api-afbf18d5.js";import{h as b,r as s,R as n,L as g}from"./vendor-f36d475e.js";import"./index-5deedf4a.js";import{M as w,A as y,G as N}from"./index-cf5e6bc7.js";import{P as T}from"./index-55e4d382.js";import{u as P}from"./user-a875fff3.js";import{J as m}from"./index-010bc024.js";import{S as C}from"./index-65bc3378.js";import{t as o}from"./i18next-7389dd8c.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-1e3e6bc5.js";const p=new w,Y=()=>{var l;b();const{state:a,dispatch:E}=s.useContext(y),{state:S,dispatch:d}=s.useContext(N),[r,i]=s.useState("active"),[x,f]=s.useState([]),[u,h]=s.useState([]),[v,c]=s.useState(!1),k=async()=>{try{const e=await p.callRawAPI("/v3/api/custom/chumpchange/provider/task/current",{},"GET");e.error||f(e.data)}catch(e){console.error("Error fetching active tasks:",e)}},j=async()=>{try{const e=await p.callRawAPI("/v3/api/custom/chumpchange/provider/task/completed",{},"GET");e.error||h(e.data)}catch(e){return console.error("Error fetching completed tasks:",e),[]}};return n.useEffect(()=>{(async()=>(c(!0),await k(),await j(),c(!1)))()},[]),n.useEffect(()=>{d({type:"SETPATH",payload:{path:"my-task"}})},[]),t.jsxs(T,{className:"",children:[t.jsxs("div",{className:" mt-2 flex items-center justify-between px-10 ",children:[t.jsx("div",{className:"font-['Poppins'] text-lg font-bold text-black",children:o("provider.my_tasks.title")}),t.jsx(g,{to:"/provider/profile",className:" relative z-10 ",children:t.jsx("img",{className:"h-[45px] w-[45px] rounded-full object-cover",src:((l=a==null?void 0:a.userDetails)==null?void 0:l.photo)||P})})]}),t.jsx("div",{className:" mt-14 px-5 ",children:t.jsxs("div",{className:" grid h-12 w-full grid-cols-2 items-center rounded-2xl bg-[#8181a4]/20 px-1",children:[t.jsx("button",{className:`flex h-10 items-center justify-center rounded-2xl font-['Poppins'] text-sm font-medium ${r==="active"?"bg-black text-white":"text-black"}`,onClick:()=>i("active"),children:o("provider.my_tasks.active")}),t.jsx("button",{className:`flex h-10 items-center justify-center rounded-2xl font-['Poppins'] text-sm font-medium ${r==="completed"?"bg-black text-white":"text-black"}`,onClick:()=>i("completed"),children:o("provider.my_tasks.completed")})]})}),t.jsxs("div",{className:" relative left-0 mt-6 flex w-full flex-1 flex-col items-center justify-start overflow-hidden rounded-tl-[32px] rounded-tr-[32px] bg-white",children:[t.jsx("div",{className:" flex h-7 w-full max-w-[335px] items-center justify-center border-b bg-white ",children:t.jsx("div",{className:" h-1 w-[50px] rounded bg-[#f2f2f7]"})}),v?t.jsx("div",{className:"flex h-full w-full items-center justify-center pt-10 ",children:t.jsx(C,{})}):r==="active"?t.jsx(m,{taskData:x}):t.jsx(m,{taskData:u})]})]})};export{Y as default};
