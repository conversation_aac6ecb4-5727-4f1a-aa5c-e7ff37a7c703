import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as i,b as R,r as n,h as k}from"./vendor-4f06b3f4.js";import{u as D}from"./react-hook-form-f3d72793.js";import{o as C}from"./yup-2324a46a.js";import{c as F,a as v}from"./yup-17027d7a.js";import{M as L,A as M,G,t as O,s as B}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as b}from"./MkdInput-ff3aa862.js";import{I as H}from"./InteractiveButton-8f7d74ee.js";import{S as U}from"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";let m=new L;const ge=s=>{const{dispatch:I}=i.useContext(M),S=F({product_type_id:v(),amount:v(),provider_id:v()}).required(),{dispatch:p}=i.useContext(G),[h,$]=i.useState({}),[g,u]=i.useState(!1),[P,c]=i.useState(!1),E=R(),[q,j]=n.useState(0),[K,w]=n.useState(0),[V,A]=n.useState(0),{register:f,handleSubmit:T,setError:x,setValue:y,formState:{errors:_}}=D({resolver:C(S)}),r=k();n.useEffect(function(){(async function(){try{c(!0),m.setTable("provider_product");const e=await m.callRestAPI({id:s.activeId?s.activeId:Number(r==null?void 0:r.id)},"GET");e.error||(y("product_type_id",e.model.product_type_id),y("amount",e.model.amount),y("provider_id",e.model.provider_id),j(e.model.product_type_id),w(e.model.amount),A(e.model.provider_id),c(!1))}catch(e){c(!1),console.log("error",e),O(I,e.message)}})()},[]);const N=async e=>{u(!0);try{m.setTable("provider_product");for(let d in h){let a=new FormData;a.append("file",h[d].file);let l=await m.uploadImage(a);e[d]=l.url}const o=await m.callRestAPI({id:s.activeId?s.activeId:Number(r==null?void 0:r.id),product_type_id:e.product_type_id,amount:e.amount,provider_id:e.provider_id},"PUT");if(!o.error)B(p,"Updated"),E("/admin/provider_product"),p({type:"REFRESH_DATA",payload:{refreshData:!0}}),s.setSidebar(!1);else if(o.validation){const d=Object.keys(o.validation);for(let a=0;a<d.length;a++){const l=d[a];x(l,{type:"manual",message:o.validation[l]})}}u(!1)}catch(o){u(!1),console.log("Error",o),x("product_type_id",{type:"manual",message:o.message})}};return i.useEffect(()=>{p({type:"SETPATH",payload:{path:"provider_product"}})},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Provider Product"}),P?t.jsx(U,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:T(N),children:[t.jsx(b,{type:"number",page:"edit",name:"product_type_id",errors:_,label:"Product Type Id",placeholder:"Product Type Id",register:f,className:""}),t.jsx(b,{type:"number",page:"edit",name:"amount",errors:_,label:"Amount",placeholder:"Amount",register:f,className:""}),t.jsx(b,{type:"number",page:"edit",name:"provider_id",errors:_,label:"Provider Id",placeholder:"Provider Id",register:f,className:""}),t.jsx(H,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:g,disable:g,children:"Submit"})]})]})};export{ge as default};
