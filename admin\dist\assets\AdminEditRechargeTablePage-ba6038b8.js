import{j as a}from"./@react-google-maps/api-ac2f9d6f.js";import{R as s,b as I,h as w,r as R}from"./vendor-4f06b3f4.js";import{u as T}from"./react-hook-form-f3d72793.js";import{o as k}from"./yup-2324a46a.js";import{c as P,a as b}from"./yup-17027d7a.js";import{M as q,A as C,G as D,t as L,s as M}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{M as y}from"./MkdInput-ff3aa862.js";import{I as U}from"./InteractiveButton-8f7d74ee.js";import{S as G}from"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./MoonLoader-16bed42a.js";let i=new q;const le=r=>{const{dispatch:E}=s.useContext(C),S=P({name:b().required("Name is required"),amount:b().required("Amount is required")}).required(),{dispatch:m}=s.useContext(D),[d,n]=s.useState(!1),[v,l]=s.useState(!1),N=I(),{register:u,handleSubmit:A,setError:p,setValue:f,formState:{errors:h}}=T({resolver:k(S)}),o=w();R.useEffect(function(){(async function(){try{l(!0),i.setTable("recharge");const e=await i.callRestAPI({id:r.activeId?r.activeId:Number(o==null?void 0:o.id)},"GET");e.error||(f("name",e.model.name),f("amount",e.model.amount),l(!1))}catch(e){l(!1),console.log("error",e),L(E,e.message)}})()},[]);const j=async e=>{n(!0);try{i.setTable("recharge");const t=await i.callRestAPI({id:r.activeId?r.activeId:Number(o==null?void 0:o.id),name:e.name,amount:e.amount},"PUT");if(!t.error)M(m,"Updated"),N("/admin/recharge"),m({type:"REFRESH_DATA",payload:{refreshData:!0}}),r.setSidebar(!1);else if(t.validation){const g=Object.keys(t.validation);for(let c=0;c<g.length;c++){const x=g[c];p(x,{type:"manual",message:t.validation[x]})}}n(!1)}catch(t){n(!1),console.log("Error",t),p("name",{type:"manual",message:t.message})}};return s.useEffect(()=>{m({type:"SETPATH",payload:{path:"recharge"}})},[]),a.jsxs("div",{className:"mx-auto rounded p-5 shadow-md",children:[a.jsx("h4",{className:"text-2xl font-medium",children:"Edit User Recharge"}),v?a.jsx(G,{}):a.jsxs("form",{className:"w-full max-w-lg",onSubmit:A(j),children:[a.jsx(y,{type:"text",page:"edit",name:"name",errors:h,label:"Name",placeholder:"Name",register:u,className:""}),a.jsx(y,{type:"number",page:"edit",name:"amount",errors:h,label:"Amount",placeholder:"Amount",register:u,className:""}),a.jsx(U,{type:"submit",className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",loading:d,disable:d,children:"Submit"})]})]})};export{le as default};
