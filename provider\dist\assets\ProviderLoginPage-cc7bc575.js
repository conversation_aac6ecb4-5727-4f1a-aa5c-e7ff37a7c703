import{j as t}from"./@react-google-maps/api-afbf18d5.js";import{R as N,r as d,u as T,d as $,L as S}from"./vendor-f36d475e.js";import{u as B}from"./react-hook-form-ff037c98.js";import{o as G}from"./yup-afe5cf51.js";import{c as W,a as I}from"./yup-2f6e2476.js";import{M as q,A as D,G as F,s as i,h as H}from"./index-cf5e6bc7.js";import"./InteractiveButton-303096ac.js";import{A as J,a as U}from"./index-55e4d382.js";import{A as V,P as Z}from"./index-ad319f83.js";import{a as K}from"./index-895fa99b.js";import{u as z}from"./react-i18next-1e3e6bc5.js";import"./@hookform/resolvers-eb417cd0.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./MoonLoader-4d8718ee.js";let Q=new q;const Le=()=>{const A=W({phone:I().required(),password:I().required()}).required(),{dispatch:h}=N.useContext(D),{dispatch:r}=N.useContext(F),[u,n]=d.useState(!1),[p,g]=d.useState(!1),E=T(),f=new URLSearchParams(E.search).get("redirect_uri"),c=$(),{t:s}=z(),{register:x,handleSubmit:M,setError:j,formState:{errors:o}}=B({resolver:G(A)}),O=async a=>{var w,b,k,_,v,y,L,C;if(!p){i(r,s("auth_pages.login.check_agree"),5e3,"error");return}try{n(!0),n(!0);const e=await Q.callRawAPI("/v3/api/custom/chumpchange/provider/login-with-phone",{phone:a.phone,password:a.password},"POST"),R={user_id:e.data.id,...e.data};if(e.error){if(i(r,e.message,5e3,"error"),n(!1),e.validation){const l=Object.keys(e.validation);for(let m=0;m<l.length;m++){const P=l[m];j(P,{type:"manual",message:e.validation[P]})}}}else{if((w=e==null?void 0:e.data)!=null&&w.error&&((b=e==null?void 0:e.data)==null?void 0:b.verify)==0){c(`/provider/sign-up-otp/${a.phone}`);return}h({type:"LOGIN",payload:R}),h({type:"UPDATE_PROFILE",payload:{first_name:e.data.first_name,last_name:e.data.last_name,photo:e.data.photo,user_data:{...e.data}}});try{await H(),i(r,"Successfully Logged In",4e3,"success"),c(f??`/${(k=e==null?void 0:e.data)==null?void 0:k.role}/dashboard`)}catch(l){console.error("Push subscription error:",l),i(r,"Successfully Logged In",4e3,"success"),c(f??`/${(_=e==null?void 0:e.data)==null?void 0:_.role}/dashboard`)}}}catch(e){n(!1),i(r,e,5e3,"error"),console.log("Error",e),j("email",{type:"manual",message:(y=(v=e==null?void 0:e.response)==null?void 0:v.data)!=null&&y.message?(C=(L=e==null?void 0:e.response)==null?void 0:L.data)==null?void 0:C.message:e==null?void 0:e.message})}};return console.log(o),d.useEffect(()=>{localStorage.getItem("token")&&c("/provider/home")},[]),d.useEffect(()=>{const a=localStorage.getItem("checked");a&&g(JSON.parse(a))},[]),t.jsxs(J,{children:[t.jsxs("form",{onSubmit:M(O),children:[t.jsx(U,{title:s("auth_pages.login.title")}),t.jsxs("div",{className:"flex flex-col gap-4",children:[t.jsx("div",{className:" flex h-[70px] flex-col items-center justify-center",children:((o==null?void 0:o.email)||(o==null?void 0:o.password))&&t.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:t.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:s("auth_pages.login.error")})})}),t.jsx(V,{type:"phone",name:"phone",placeholder:s("auth_pages.login.p_phone"),errors:o,register:x,icon:t.jsxs("svg",{width:"20",height:"22",viewBox:"0 0 20 22",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[t.jsx("path",{d:"M18 6V16C18 20 17 21 13 21H7C3 21 2 20 2 16V6C2 2 3 1 7 1H13C17 1 18 2 18 6Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),t.jsx("path",{d:"M12 4.5H8",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),t.jsx("path",{d:"M10.0002 18.1C10.8562 18.1 11.5502 17.406 11.5502 16.55C11.5502 15.694 10.8562 15 10.0002 15C9.14415 15 8.4502 15.694 8.4502 16.55C8.4502 17.406 9.14415 18.1 10.0002 18.1Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),t.jsx(Z,{name:"password",placeholder:s("auth_pages.login.p_password"),errors:o,register:x}),t.jsx("div",{className:"flex justify-end",children:t.jsx(S,{to:"/provider/forgot",className:"font-['Poppins'] text-sm font-medium leading-[18.20px] text-black",children:s("auth_pages.login.forget_password")})}),t.jsxs("div",{className:" mt-8 flex h-28 w-full  flex-col items-center justify-center rounded-2xl border border-[#8181a4]/20",children:[t.jsx("div",{className:"flex h-6 w-6 cursor-pointer items-center justify-center rounded-md border border-[#56ccf2] bg-white",onClick:()=>{const a=!p;g(a),localStorage.setItem("checked",JSON.stringify(a))},children:p&&t.jsx("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M2.33301 7L5.44046 10.5L11.6663 3.5",stroke:"#56CCF2",strokeWidth:"3",strokeLinecap:"round",strokeLinejoin:"round"})})}),t.jsxs("div",{className:"text-center",children:[t.jsxs("span",{className:"font-['Poppins'] text-sm font-medium leading-[18.20px] text-[#8080a3]",children:[s("auth_pages.login.agree_title")," ",t.jsx("br",{})]}),t.jsx("span",{className:"font-['Poppins'] text-sm font-medium leading-[18.20px] text-black",children:s("auth_pages.login.terms_title")})]})]}),t.jsx("div",{className:" mt-5 ",children:t.jsx(K,{loading:u,disabled:u,type:"submit",children:s("auth_pages.login.sign_in")})})]})]}),t.jsxs("div",{className:" mb-6 w-full ",children:[t.jsx("div",{className:"mb-4 mt-6 text-center font-['Poppins'] text-sm font-medium leading-[18.20px] text-[#8080a3] ",children:s("auth_pages.login.dont_have_account")}),t.jsx(S,{to:"/provider/sign-up",className:"flex h-12 w-full items-center justify-center rounded-2xl border-2 border-[#8181a4]/20 font-['Poppins'] text-base font-medium text-black",children:s("auth_pages.login.sing_up")})]})]})};export{Le as default};
