import{j as i}from"./@react-google-maps/api-ee55a349.js";import{t as c}from"./i18next-7389dd8c.js";import{L as m}from"./vendor-b16525a8.js";import{b as o}from"./index-09a1718e.js";import{u as x}from"./react-i18next-4a61273e.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const $=({taskData:n})=>{const{i18n:l}=x();return i.jsx("div",{className:" flex h-full max-h-[400px] w-full flex-col gap-4 overflow-y-auto px-5 py-5 ",children:(n==null?void 0:n.length)===0||!(n!=null&&n.length)?i.jsx("div",{className:"mt-5 w-full text-center font-['Poppins'] text-2xl font-medium text-[#b4b4b4]",dangerouslySetInnerHTML:{__html:c("user.home.no_offer")}}):n==null?void 0:n.map((e,r)=>e!=null&&e.product_name?i.jsxs(m,{to:`/user/view-product-listing/${e.id}`,className:"inline-flex items-start justify-start gap-[5px]",children:[i.jsx("div",{className:"flex h-11 w-11 flex-shrink-0 items-center justify-center overflow-hidden rounded-full bg-[#50a8f9]/10",children:i.jsx("img",{src:e==null?void 0:e.product_logo,alt:"",className:"h-full w-full object-cover"})}),i.jsxs("div",{className:"inline-flex flex-col items-start justify-center pb-2",children:[i.jsxs("div",{className:"inline-flex items-center justify-start gap-1",children:[i.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:e==null?void 0:e.product_type_name}),i.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:"→"}),i.jsxs("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:["$",e==null?void 0:e.price]})]}),i.jsxs("div",{className:"inline-flex flex-wrap items-center justify-start gap-1",children:[i.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:e!=null&&e.city?e==null?void 0:e.city:e!=null&&e.operating_city?e==null?void 0:e.operating_city:"N/A"}),i.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:"•"}),i.jsx("div",{className:"text-right font-['Poppins'] text-xs font-medium text-[#8080a3]",children:o(e==null?void 0:e.date,l.language)})]})]})]},r):i.jsxs(m,{to:`/user/view-service-listing/${e.id}`,className:"inline-flex items-start justify-start gap-[5px]",children:[i.jsx("div",{className:"flex h-11 w-11 flex-shrink-0 items-center justify-center overflow-hidden rounded-full bg-[#50a8f9]/10",children:i.jsx("img",{src:e==null?void 0:e.service_logo,alt:"",className:"h-full w-full object-cover"})}),i.jsxs("div",{className:"inline-flex flex-col items-start justify-center pb-2",children:[i.jsxs("div",{className:"inline-flex flex-wrap items-center justify-start gap-1",children:[i.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:e==null?void 0:e.service_name}),i.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:"→"}),i.jsxs("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:["$",e==null?void 0:e.rate]})]}),i.jsxs("div",{className:"inline-flex items-center justify-start gap-1",children:[i.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:e!=null&&e.city?e==null?void 0:e.city:e!=null&&e.operating_city?e==null?void 0:e.operating_city:"N/A"}),i.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:"•"}),i.jsx("div",{className:"text-right font-['Poppins'] text-xs font-medium text-[#8080a3]",children:o(e==null?void 0:e.date,l.language)})]})]})]},r))})};export{$ as default};
