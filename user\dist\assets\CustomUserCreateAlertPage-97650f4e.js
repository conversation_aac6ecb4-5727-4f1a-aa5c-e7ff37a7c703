import{j as e}from"./@react-google-maps/api-ee55a349.js";import{i as je,r as c,R as i,d as Se}from"./vendor-b16525a8.js";import"./index-9ef1c042.js";import{M as be,A as ke,G as Ne,s as Ce,t as ye}from"./index-09a1718e.js";import{B as $,a as _e}from"./index-d54cffea.js";import{D as _}from"./index-6b1be6a4.js";import{u as Pe}from"./react-hook-form-b6ed2679.js";import{o as Le}from"./yup-3990215a.js";import{c as Ae,a as b}from"./yup-f828ae80.js";import{S as Me}from"./index-5a645c18.js";import{P as Ee}from"./index-d152a0de.js";import{t as l}from"./i18next-7389dd8c.js";import{A as qe}from"./@vis.gl/react-google-maps-934eb5c3.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";import"./@hookform/resolvers-3e831b4a.js";const w=new be,mt=()=>{var V,B,T,I;je();const{state:He,dispatch:z}=c.useContext(ke),{state:De,dispatch:K}=c.useContext(Ne),[U,k]=i.useState(!0),[O,J]=i.useState([]),[Q,X]=i.useState([]),[Y,ee]=i.useState([]),[p,x]=i.useState(null),[f,h]=i.useState(null),[te,P]=i.useState(""),[se,L]=i.useState(""),[ae,A]=i.useState(""),[M,g]=i.useState(""),[oe,N]=i.useState(!1),[Fe,le]=c.useState([]),[ne,C]=c.useState(!1),[E,q]=c.useState({name:"Select"}),[H,D]=c.useState({name:"Select"}),[F,R]=c.useState({name:"Select"}),[v,y]=c.useState([]),j=i.useRef(null);i.useEffect(()=>{if(window.google&&window.google.maps&&window.google.maps.places){const t=new window.google.maps.Map(document.createElement("div"));j.current=new window.google.maps.places.PlacesService(t)}},[]);const re=Se(),ie=Ae({address:b().required(l("user.create_alert.errors.location")),far:b().required(l("user.create_alert.errors.far")),description:b().required(l("user.create_alert.errors.description")),skill:b().required(l("user.create_alert.errors.skill"))}).required(),{register:S,handleSubmit:ce,setError:Re,setValue:d,formState:{errors:s,isValid:Ve}}=Pe({resolver:Le(ie),mode:"onChange"});c.useEffect(()=>{const t=new URLSearchParams(location.search);d("description",t.get("description")||""),d("skill",t.get("skill")||""),d("far",t.get("far")||""),d("location",t.get("location")||""),R({name:t.get("far")||"Select",id:t.get("service_alert_id")||0}),q({name:t.get("skill")||"Select",id:t.get("service_id")||0}),D({name:t.get("location")||"Select",id:t.get("location_id")||0}),x(t.get("lat")||""),h(t.get("lng")||""),P(t.get("city")||""),L(t.get("state")||""),A(t.get("country")||""),g(t.get("address")||"")},[location.search,d]);const de=async t=>{if(console.log("data",t),!p&&!f){Ce(K,l("user.create_request.errors.select_address"),4e3,"error");return}const o=new URLSearchParams({location:t.location,far:t.far,description:t.description,skill:t.skill,service_id:E.id,service_alert_id:F.id,location_id:H.id,lat:p,lng:f,city:te,state:se,country:ae,address:M}).toString();re(`/user/create-alert/confirm?${o}`)},me=t=>{d("skill",t.name)},ue=t=>{d("far",t.name)},pe=t=>{d("location",t.name),g(t.name),x(t.latitude||""),h(t.longtitude||"")},xe=async()=>{var t,o,n;try{k(!0);const r=await w.callRawAPI("/v3/api/custom/chumpchange/common/address/search",{},"GET"),m=await w.callRawAPI("/v3/api/custom/chumpchange/users/service_alert",{},"GET");w.setTable("ambulant");const u=await w.callRestAPI({filter:['status,eq,"active"']},"GETALL");if(console.log("locations",r),console.log("service_alert",m),!u.error){let G=[{name:"Select"}];(t=u==null?void 0:u.list)==null||t.map(a=>{G.push({name:a==null?void 0:a.name,price:a==null?void 0:a.price,id:a.id})}),J(G);let W=[{name:"Select"}];(o=r==null?void 0:r.data)==null||o.map(a=>{W.push({...a,name:a==null?void 0:a.description,id:a.id})}),X(W);let Z=[{name:"Select"}];(n=m==null?void 0:m.data)==null||n.map(a=>{Z.push({name:(a==null?void 0:a.name)+" ("+(a==null?void 0:a.distance)+"m)",id:a.id})}),ee(Z),k(!1)}}catch(r){k(!1),console.log("error",r),ye(z,r.message)}};i.useEffect(()=>{(async()=>xe())()},[]);const fe=(t,o,n,r,m,u)=>{console.log(t,o),x(t||""),h(o||""),P(r||""),L(m||""),A(u||""),g(n||""),N(!1)},he=async t=>{const o=t.target.value;g(o),o?(ge(o),C(!0)):(le([]),C(!1))},ge=t=>{t.length>2?window.google&&new window.google.maps.places.AutocompleteService().getPlacePredictions({input:t},n=>y(n||[])):y([])},ve=t=>new Promise((o,n)=>{j.current&&j.current.getDetails({placeId:t},(r,m)=>{m===window.google.maps.places.PlacesServiceStatus.OK&&r.geometry?o({address:r.formatted_address,lat:r.geometry.location.lat(),lng:r.geometry.location.lng()}):n(`Failed to fetch details for placeId: ${t}`)})}),we=async t=>{try{const o=new window.google.maps.Map(document.createElement("div"));j.current=new window.google.maps.places.PlacesService(o);const n=await ve(t.place_id);console.log("details >> ",n),g(n==null?void 0:n.address),x(n.lat||""),h(n.lng||""),C(!1),y([])}catch(o){console.log("error >>",o)}};return console.log("predictions >> ",v),e.jsxs(e.Fragment,{children:[oe&&e.jsxs("div",{className:"fixed left-0 top-0 z-[9999999] min-h-screen w-full bg-[#F2F2F7] ",children:[e.jsx("div",{className:"p-5",children:e.jsxs("div",{className:"relative flex w-full items-center justify-between ",children:[e.jsx($,{onClick:()=>N(!1)}),e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:"Pick Location"}),e.jsx("div",{className:""})]})}),e.jsx("div",{className:" h-[calc(100vh-88px)] ",children:e.jsx(Ee,{handleChoose:fe,lat:p?Number(p):"",lng:f?Number(f):""})})]}),U?e.jsx("div",{className:"flex min-h-screen w-full items-center justify-center",children:e.jsx(Me,{})}):e.jsxs("form",{onSubmit:ce(de),className:"p-5",children:[e.jsxs("div",{className:"relative flex w-full items-center justify-center ",children:[e.jsx("div",{className:" absolute left-0 top-0 ",children:e.jsx($,{link:"/user/dashboard"})}),e.jsxs("div",{className:"",children:[e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:l("user.create_alert.title")}),e.jsx("div",{className:"text-center font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:l("user.create_alert.sub_title")})]})]}),e.jsxs("div",{className:"mt-7",children:[(s==null?void 0:s.description)&&e.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:e.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:(V=s==null?void 0:s.description)==null?void 0:V.message})}),(s==null?void 0:s.far)&&e.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:e.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:(B=s==null?void 0:s.far)==null?void 0:B.message})}),(s==null?void 0:s.location)&&e.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:e.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:(T=s==null?void 0:s.location)==null?void 0:T.message})}),(s==null?void 0:s.skill)&&e.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:e.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:(I=s==null?void 0:s.skill)==null?void 0:I.message})}),e.jsx("div",{className:" mb-3 mt-6 px-2 font-['Poppins'] text-[22px] font-medium tracking-wide text-black",children:l("user.create_alert.alert_of")}),e.jsx("div",{className:"",children:e.jsx(_,{services:O,onServiceSelect:me,selectedService:E,setSelectedService:q})}),e.jsx("div",{className:" mb-3 mt-6 px-2 font-['Poppins'] text-[22px] font-medium tracking-wide text-black",children:l("user.create_alert.alert_should")}),e.jsx("div",{className:"",children:e.jsx(_,{services:Y,onServiceSelect:ue,selectedService:F,setSelectedService:R})}),e.jsxs("div",{className:"mt-6",children:[e.jsx("div",{className:" px-2 font-['Poppins'] text-[22px] font-medium tracking-wide text-black",children:l("user.create_alert.describe")}),e.jsxs("div",{className:"relative mt-3 h-[104px] w-full overflow-hidden rounded-2xl bg-white",children:[e.jsx("span",{className:"absolute left-[14px] top-[14px] z-[99] h-5 w-5 ",children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M7.08366 15.8332H6.66699C3.33366 15.8332 1.66699 14.9998 1.66699 10.8332V6.6665C1.66699 3.33317 3.33366 1.6665 6.66699 1.6665H13.3337C16.667 1.6665 18.3337 3.33317 18.3337 6.6665V10.8332C18.3337 14.1665 16.667 15.8332 13.3337 15.8332H12.917C12.6587 15.8332 12.4087 15.9582 12.2503 16.1665L11.0003 17.8332C10.4503 18.5665 9.55033 18.5665 9.00033 17.8332L7.75033 16.1665C7.61699 15.9832 7.30866 15.8332 7.08366 15.8332Z",stroke:"#8181A4",strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M13.3301 9.16667H13.3375",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M9.99607 9.16667H10.0036",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M6.66209 9.16667H6.66957",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),e.jsx("textarea",{...S("description"),type:"text",placeholder:l("user.create_alert.p_describe"),className:" absolute left-0 top-0 h-full w-full border-none bg-white  pl-[40px] pt-[14px] text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "})]})]}),e.jsx("div",{className:" mb-3 mt-5 px-2 font-['Poppins'] text-[22px] font-medium tracking-wide text-black ",children:l("user.create_alert.con_lo")}),e.jsx("div",{className:"  ",children:e.jsx(_,{services:Q,onServiceSelect:pe,selectedService:H,setSelectedService:D})}),e.jsxs("div",{className:" relative mt-4 ",children:[e.jsx(qe,{apiKey:w._google_api_key,libraries:["places"],children:e.jsx("input",{type:"text",...S("address",{onChange:he}),value:M,placeholder:l("user.create_request.address"),className:"relative h-12 w-full rounded-2xl border-none bg-white text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "})}),ne&&e.jsx("div",{className:"modal-content absolute bottom-[100%] z-[999999] max-h-[400px] w-full overflow-y-auto rounded-md border border-gray-300 bg-white",children:v.length>0?v==null?void 0:v.map((t,o)=>e.jsx("div",{className:"cursor-pointer p-2 hover:bg-gray-200",onClick:()=>we(t),children:t==null?void 0:t.description},o)):""})]}),e.jsxs("div",{className:"mt-4",children:[e.jsx("input",{type:"text",...S("lat",{onChange:t=>x(t.target.value)}),value:p,placeholder:l("user.create_request.latitude"),className:"relative hidden h-12 w-full rounded-2xl border-none bg-white text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "}),e.jsx("input",{type:"text",...S("lng",{onChange:t=>h(t.target.value)}),value:f,placeholder:l("user.create_request.longitude"),className:"relative mt-4 hidden h-12 w-full rounded-2xl border-none bg-white text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "})]}),e.jsxs("button",{type:"button",onClick:()=>N(!0),className:"relative mt-2 flex h-12 w-full items-center gap-4 rounded-2xl bg-white px-5",children:[e.jsx("svg",{width:"28",height:"28",viewBox:"0 0 28 28",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M14 8.90909C11.2 8.90909 8.90909 11.2 8.90909 14C8.90909 16.8 11.2 19.0909 14 19.0909C16.8 19.0909 19.0909 16.8 19.0909 14C19.0909 11.2 16.8 8.90909 14 8.90909ZM25.3273 12.7273C24.6909 7.38182 20.4909 3.18182 15.2727 2.67273V0H12.7273V2.67273C7.38182 3.18182 3.18182 7.38182 2.67273 12.7273H0V15.2727H2.67273C3.30909 20.6182 7.50909 24.8182 12.7273 25.3273V28H15.2727V25.3273C20.6182 24.6909 24.8182 20.4909 25.3273 15.2727H28V12.7273H25.3273ZM14 22.9091C9.03636 22.9091 5.09091 18.9636 5.09091 14C5.09091 9.03636 9.03636 5.09091 14 5.09091C18.9636 5.09091 22.9091 9.03636 22.9091 14C22.9091 18.9636 18.9636 22.9091 14 22.9091Z",fill:"#56CCF2"})}),e.jsx("div",{className:"font-['Poppins'] text-sm font-normal text-black",children:l("user.create_alert.cur_lo")})]})]}),e.jsx("div",{className:"mt-[58px]",children:e.jsx(_e,{type:"submit",className:" uppercase opacity-[0.85] ",children:l("buttons.next")})})]})]})};export{mt as default};
