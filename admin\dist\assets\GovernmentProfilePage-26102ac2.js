import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as I,r as l}from"./vendor-4f06b3f4.js";import{u as ae}from"./react-hook-form-f3d72793.js";import{o as ie}from"./yup-2324a46a.js";import{c as le,a as oe}from"./yup-17027d7a.js";import{M as ne,A as re,G as me,t as Q,s as M}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{I as W}from"./InteractiveButton-8f7d74ee.js";import de from"./ModalPrompt-820d1959.js";import{S as Y}from"./index-2d8231e7.js";import{F as ce,a as xe,b as V}from"./index.esm-8125abd7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./MoonLoader-16bed42a.js";import"./index-250f6b3d.js";import"./react-icons-e5379072.js";let D=new ne;const ze=()=>{var X,J;const B=le({email:oe().email().required()}).required(),{dispatch:C}=I.useContext(re),[h,g]=l.useState(""),[f,k]=I.useState({}),[P,u]=l.useState(!1),[n,N]=l.useState(!1),[y,S]=l.useState(""),[A,j]=l.useState(!1),[L,v]=l.useState(!1),[r,_]=l.useState("Profile"),[t,T]=l.useState({}),[F,O]=l.useState(!0),[w,$]=l.useState(!1),{dispatch:x}=I.useContext(me),{register:U,handleSubmit:i,setError:b,setValue:G,formState:{errors:H}}=ae({resolver:ie(B)}),ee=()=>{$(!w)},se=(s,o,z=!1)=>{j(!0);let p=f;z?p[s]?p[s]=[...p[s],{file:o.files[0],tempFile:{url:URL.createObjectURL(o.files[0]),name:o.files[0].name,type:o.files[0].type}}]:p[s]=[{file:o.files[0],tempFile:{url:URL.createObjectURL(o.files[0]),name:o.files[0].name,type:o.files[0].type}}]:p[s]={file:o.files[0],tempURL:URL.createObjectURL(o.files[0])},k({...p})};async function Z(){O(!0);try{const s=await D.getProfile();s.error||(T(s),G("email",s==null?void 0:s.email),G("first_name",s==null?void 0:s.first_name),G("last_name",s==null?void 0:s.last_name),g(s==null?void 0:s.email),S(s==null?void 0:s.photo),C({type:"UPDATE_PROFILE",payload:s}),O(!1))}catch(s){Q(C,s.response.data.message?s.response.data.message:s.message)}}const q=async s=>{var o,z,p;if(t.email===s.email&&t.first_name===s.first_name&&t.last_name===s.last_name&&!A&&!s.password)return E(),M(x,"No Changes Available",1e3);T(s);try{if(v(!0),f&&f.photo&&((o=f.photo)!=null&&o.file)){let a=new FormData;a.append("file",(z=f.photo)==null?void 0:z.file);let d=await D.uploadImage(a);s.photo=d.url,M(x,"Profile Photo Updated",1e3)}const m=await D.updateProfile({first_name:s.first_name||(t==null?void 0:t.first_name),last_name:s.last_name||(t==null?void 0:t.last_name),photo:s.photo||y});if(!m.error)M(x,"Profile Updated",4e3),E(),Z();else{if(m.validation){const a=Object.keys(m.validation);for(let d=0;d<a.length;d++){const c=a[d];b(c,{type:"manual",message:m.validation[c]})}}E()}if(h!==s.email){const a=await D.updateEmail(s.email);if(!a.error)M(x,"Email Updated",1e3);else if(a.validation){const d=Object.keys(a.validation);for(let c=0;c<d.length;c++){const R=d[c];b(R,{type:"manual",message:a.validation[R]})}}E()}if(((p=s.password)==null?void 0:p.length)>0){const a=await D.updatePassword(s.password);if(!a.error)M(x,"Password Updated",2e3);else if(a.validation){const d=Object.keys(a.validation);for(let c=0;c<d.length;c++){const R=d[c];b(R,{type:"manual",message:a.validation[R]})}}}s.photo="",await Z(),v(!1)}catch(m){v(!1),b("email",{type:"manual",message:m.response.data.message?m.response.data.message:m.message}),Q(C,m.response.data.message?m.response.data.message:m.message)}},te=async()=>{k({}),S(""),j(!0)};I.useEffect(()=>{x({type:"SETPATH",payload:{path:"profile"}}),Z()},[]);const K=()=>{N(!0)},E=()=>{u(!1),N(!1),S(t==null?void 0:t.photo),k({}),j(!1)};return e.jsxs("div",{className:"w-full",children:[e.jsx("div",{className:"flex items-center border-b border-b-[#E0E0E0] px-8 py-3 text-[#8D8D8D]",children:e.jsxs("div",{className:"flex items-center space-x-6",children:[e.jsx("div",{className:`cursor-pointer rounded-lg px-3 py-1 ${r==="Profile"?"bg-[#f4f4f4] text-[#525252]":""}`,onClick:()=>_("Profile"),children:"Profile"}),e.jsx("div",{className:`cursor-pointer rounded-lg px-3 py-1 ${r==="Security"?"bg-[#f4f4f4] text-[#525252]":""}`,onClick:()=>_("Security"),children:"Security"})]})}),e.jsxs("main",{children:[r==="Profile"&&e.jsx("div",{className:"rounded bg-white",children:e.jsxs("form",{onSubmit:i(q),children:[e.jsx("p",{className:"text-xs italic text-red-500",children:(X=H.photo)==null?void 0:X.message}),e.jsxs("div",{className:"mx-10 mt-4 max-w-lg",children:[e.jsxs("div",{className:"mb-3 flex items-center justify-between",children:[e.jsx("p",{className:"mb-3	text-lg	font-semibold text-gray-900",children:"Personal Details"}),F?e.jsx("div",{className:"h-10",children:e.jsx(Y,{count:1,counts:[1]})}):e.jsx("p",{className:"cursor-pointer	text-base	font-semibold text-indigo-600",onClick:K,children:"Edit"})]}),e.jsx("div",{className:"mb-3 flex items-center justify-between",children:e.jsxs("div",{className:"flex items-start gap-x-24",children:[e.jsx("p",{className:"text-base	font-medium	text-gray-600",children:"Profile Picture"}),F?e.jsx("div",{className:"flex items-center",children:e.jsx("div",{className:"h-[120px] w-[120px] flex-1 overflow-hidden rounded-2xl",children:e.jsx(Y,{count:4,counts:[1]})})}):t!=null&&t.photo?e.jsx("div",{className:"relative flex h-[100px] w-[100px] items-center rounded-lg py-2",children:e.jsx("img",{className:"h-[100px] w-[100px] rounded-lg object-cover",src:t==null?void 0:t.photo,alt:""})}):e.jsx("div",{className:"flex items-center gap-4 py-2",children:e.jsx("div",{className:"flex h-[100px] w-[100px] items-center justify-center rounded-lg border bg-slate-300 object-cover",children:e.jsx("span",{className:"text-xs",children:"No Image"})})})]})}),e.jsx("div",{className:"mb-3 flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center gap-x-[7.5rem]",children:[e.jsx("p",{className:"text-base	font-medium	text-gray-600",children:"First Name"}),e.jsx("p",{className:"text-base	font-medium	text-gray-900",children:t==null?void 0:t.first_name})]})}),e.jsx("div",{className:"mb-3 flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center gap-x-[7.5rem]",children:[e.jsx("p",{className:"text-base	font-medium	text-gray-600",children:"Last Name"}),e.jsx("p",{className:"text-base	font-medium	text-gray-900",children:t==null?void 0:t.last_name})]})}),e.jsx("div",{className:"mb-6 flex items-center justify-between text-left",children:e.jsxs("div",{className:"flex items-center gap-x-40",children:[e.jsx("p",{className:"text-base	font-medium text-gray-600",children:"Email"}),e.jsx("p",{className:"text-base	font-medium	text-gray-900",children:h})]})})]})]})}),r==="Security"&&e.jsx("div",{className:"rounded bg-white px-10 py-6",children:e.jsx("form",{onSubmit:i(q),className:"max-w-lg",children:e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Password"}),e.jsxs("div",{className:"relative w-full md:w-3/4 lg:w-2/3",children:[e.jsx("input",{...U("password"),name:"password",className:"focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",id:"password",placeholder:"********",type:w?"text":"password"}),e.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer",onClick:ee,children:w?e.jsx(ce,{}):e.jsx(xe,{})})]}),e.jsx("p",{className:"text-xs italic text-red-500",children:(J=H.password)==null?void 0:J.message})]}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsx(W,{className:"focus:shadow-outline rounded bg-indigo-600 px-4 py-2 font-bold text-white hover:bg-indigo-600 focus:outline-none disabled:cursor-not-allowed",type:"submit",loading:L,disabled:L,children:"Update"})})]})})}),P&&e.jsx(de,{closeModalFunction:E,title:"Are you sure?",message:"Are you sure you want to delete profile picture? ",acceptText:"DELETE",rejectText:"CANCEL"}),n&&e.jsx(pe,{title:"Edit Personal Details",isOpen:K,onClose:E,handleSubmit:i,onSubmit:q,register:U,submitLoading:L,errors:H,oldPhoto:y,fileObj:f,onDeleteProfile:te,previewImage:se,oldEmail:h})]})]})},pe=B=>{var F,O,w,$,x,U;const{title:C,isOpen:h,onClose:g,handleSubmit:f,onSubmit:k,register:P,submitLoading:u,errors:n,oldPhoto:N,fileObj:y,onDeleteProfile:S,previewImage:A,oldEmail:j}=B,[L,v]=l.useState(!1),[r,_]=l.useState({email:""}),t=I.useRef(null);l.useEffect(()=>{_({...r,email:j}),v(!1)},[j]);const T=i=>b=>{i==="email"&&_({...r,[i]:b.target.value})};return l.useEffect(()=>{const i=b=>{t.current&&!t.current.contains(b.target)&&g()};return h?document.addEventListener("mousedown",i):document.removeEventListener("mousedown",i),()=>{document.removeEventListener("mousedown",i)}},[h,g]),e.jsxs("div",{className:`fixed inset-0 z-[100] ${h?"block":"hidden"}`,children:[e.jsx("div",{className:`fixed inset-0 bg-gray-800 bg-opacity-75 ${h?"block":"hidden"}`}),e.jsx("div",{className:"fixed inset-0 z-20 overflow-y-auto",children:e.jsxs("div",{className:"flex min-h-screen items-end justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 transition-opacity",children:e.jsx("div",{className:"absolute inset-0 bg-gray-500 opacity-75"})}),e.jsx("span",{className:"hidden sm:inline-block sm:h-screen sm:align-middle","aria-hidden":"true",children:"​"}),e.jsxs("div",{ref:t,className:"inline-block transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6 sm:align-middle",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-xl font-semibold leading-6 text-gray-900",children:C}),e.jsx("button",{className:"text-gray-500 hover:text-gray-700 focus:outline-none",onClick:g,children:e.jsx("svg",{className:"h-6 w-6",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsxs("form",{onSubmit:f(k),className:"max-w-lg",children:[e.jsxs("div",{className:"py-5",children:[e.jsx("label",{htmlFor:"first_name",className:"mb-1 block text-sm font-medium text-gray-700",children:"Upload Profile"}),N?e.jsx("div",{children:e.jsxs("div",{className:"hover-container relative flex h-[100px] w-[100px] items-center rounded-lg",children:[e.jsx("img",{className:"h-[100px] w-[100px] rounded-lg object-cover",src:((F=y.photo)==null?void 0:F.tempURL)||N,alt:""}),e.jsxs("button",{className:"file-wrapper absolute bottom-0 left-0 block",disabled:u,children:[e.jsx("div",{className:"!z-40 !cursor-pointer rounded-bl-lg rounded-tr-xl !bg-[#4F46E5] p-1",children:e.jsx(V,{className:"text-[1.3rem] text-white"})}),e.jsx("input",{id:"photo",type:"file",placeholder:"Photo",name:"photo",onChange:i=>A("photo",i.target),className:"absolute left-0 top-0 flex h-full w-full !cursor-pointer opacity-0"})]}),e.jsx("div",{className:"visible-btn absolute right-0 top-0 hidden",children:e.jsx(W,{onClick:S,loading:u,disabled:u,className:"!h-[1.4rem] cursor-pointer !rounded-none !rounded-bl-xl !rounded-tr-lg !bg-red-500 text-sm font-semibold",children:"X"})})]})}):e.jsx("div",{className:"flex h-[100px] w-[100px] items-center justify-center rounded-lg border object-cover",children:e.jsx("div",{className:"relative",children:(O=y.photo)!=null&&O.tempURL?e.jsx("img",{className:"h-[100px] w-[100px] rounded-lg object-cover",src:((w=y.photo)==null?void 0:w.tempURL)||N,alt:""}):e.jsxs("button",{disabled:u,children:[e.jsx("div",{children:e.jsx(V,{className:"text-[35px] text-blue-950"})}),e.jsx("input",{id:"photo",type:"file",placeholder:"Photo",name:"photo",onChange:i=>A("photo",i.target),className:"absolute left-0 top-0 flex h-full w-full !cursor-pointer opacity-0"})]})})}),e.jsxs("div",{className:"mt-3",children:[e.jsx("label",{htmlFor:"first_name",className:"mb-1 block text-sm font-medium text-gray-700",children:"First Name"}),e.jsx("input",{className:"focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",id:"first_name",type:"text",placeholder:"Enter First Name",name:"first_name",...P("first_name")}),e.jsx("p",{className:"text-xs italic text-red-500",children:($=n==null?void 0:n.id)==null?void 0:$.message})]}),e.jsxs("div",{className:"mt-3",children:[e.jsx("label",{htmlFor:"last_name",className:"mb-1 block text-sm font-medium text-gray-700",children:"Last Name"}),e.jsx("input",{className:"focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",id:"last_name",type:"text",placeholder:"Enter last Name",name:"last_name",...P("last_name")}),e.jsx("p",{className:"text-xs italic text-red-500",children:(x=n==null?void 0:n.id)==null?void 0:x.message})]}),L&&j!==r.email?e.jsxs("div",{className:"mt-3 flex",children:[e.jsx("div",{className:"mr-2",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.0003 1.66663C5.39795 1.66663 1.66699 5.39759 1.66699 9.99996C1.66699 14.6023 5.39795 18.3333 10.0003 18.3333C14.6027 18.3333 18.3337 14.6023 18.3337 9.99996C18.3337 5.39759 14.6027 1.66663 10.0003 1.66663ZM8.33366 9.16663C8.33366 8.82145 8.61348 8.54163 8.95866 8.54163H10.0003C10.3455 8.54163 10.6253 8.82145 10.6253 9.16663L10.6253 13.5416C10.6253 13.8868 10.3455 14.1666 10.0003 14.1666C9.65515 14.1666 9.37533 13.8868 9.37533 13.5416L9.37532 9.79163H8.95866C8.61348 9.79163 8.33366 9.5118 8.33366 9.16663ZM10.0003 6.04163C9.65515 6.04163 9.37533 6.32145 9.37533 6.66663C9.37533 7.0118 9.65515 7.29163 10.0003 7.29163C10.3455 7.29163 10.6253 7.0118 10.6253 6.66663C10.6253 6.32145 10.3455 6.04163 10.0003 6.04163Z",fill:"#4F46E5"})})}),e.jsxs("div",{children:[e.jsxs("p",{className:"mb-1	text-sm	font-medium text-gray-600",children:["We've sent an email to: ",r==null?void 0:r.email]}),e.jsx("p",{className:"mb-2	text-sm	font-semibold text-gray-900"}),e.jsx("p",{className:"mb-2	text-sm	font-medium text-gray-600",children:"In order to complete the email update click the confirmation link."}),e.jsx("p",{className:"mb-2	text-sm	font-medium text-gray-600",children:"(the link expires in 24 hours)"})]})]}):e.jsxs("div",{className:"mt-3",children:[e.jsx("label",{htmlFor:"email",className:"mb-1 block text-sm font-medium text-gray-700",children:"Email"}),e.jsx("input",{className:"focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",id:"email",type:"text",placeholder:"Enter Email",name:"email",...P("email"),onChange:T("email")}),e.jsx("p",{className:"text-xs italic text-red-500",children:(U=n==null?void 0:n.id)==null?void 0:U.message})]})]}),e.jsxs("div",{className:"mt-4 flex justify-between gap-10",children:[e.jsx("button",{className:"mr-2 w-full rounded-md border border-gray-300 px-4 py-1.5 text-gray-700	",onClick:g,children:"Cancel"}),e.jsx(W,{className:"focus:shadow-outline !h-[2.5rem] w-full rounded-md bg-indigo-500 px-4 py-2 font-bold text-white hover:bg-indigo-600 focus:outline-none disabled:cursor-not-allowed",type:"submit",loading:u,disabled:u,onClick:()=>v(!0),children:"Save"})]})]})]})]})})]})};export{pe as EditInfoModal,ze as default};
