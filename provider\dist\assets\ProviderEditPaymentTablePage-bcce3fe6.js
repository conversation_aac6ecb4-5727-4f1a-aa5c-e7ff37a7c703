import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{R as i,u as z,d as F,r as n,h as H}from"./vendor-f36d475e.js";import{u as K}from"./react-hook-form-ff037c98.js";import{o as O}from"./yup-afe5cf51.js";import{c as J,a as d}from"./yup-2f6e2476.js";import{M as Q,A as W,G as X,n as w,s as P}from"./index-cf5e6bc7.js";import"./react-quill-3f3a006b.js";/* empty css                   */import"./InteractiveButton-303096ac.js";import"./index-bec80226.js";import{B as Y,a as Z,R as ee}from"./index-895fa99b.js";import k from"./DropDownSelection-d1fecb01.js";import{M as B}from"./index-bf8d79cc.js";import{u as te}from"./react-i18next-1e3e6bc5.js";import{L as ae}from"./@mantine/core-ee88fb98.js";import"./@hookform/resolvers-eb417cd0.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./@craftjs/core-01ce56b9.js";import"./MoonLoader-4d8718ee.js";import"./@emotion/serialize-460cad7f.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-********.js";const E=[{name:"Cash"},{name:"Banco Popular"}],C=[{name:"Savings"},{name:"Business"}];let M=new Q;const Ie=se=>{var _,b,j,g;i.useContext(W);const{t:s}=te(),T=J({method:d().required(s("provider.add_payment.r_method")),type:d().when("method",(t,o)=>t==="Banco Popular"?o.required(s("provider.add_payment.r_banco")):o),account_number:d().when("method",(t,o)=>t==="Banco Popular"?o.required(s("provider.add_payment.r_ac_number")):o),cedula_rnc:d().when("method",(t,o)=>t==="Banco Popular"?o.required(s("provider.add_payment.r_rnc")):o)}).required(),{dispatch:p}=i.useContext(X);i.useState({});const[R,l]=i.useState(!1);i.useState(!1);const[L,m]=i.useState(!1),c=z().state;console.log("payment >>",c);const q=F(),[A,x]=n.useState(E.find(t=>t.name===c.method_type)),[D,f]=n.useState(C.find(t=>t.name===c.account_type));n.useState(0),n.useState(""),n.useState(""),n.useState(""),n.useState(""),n.useState("");const{register:h,handleSubmit:$,setError:v,setValue:r,formState:{errors:a}}=K({resolver:O(T)}),y=H();n.useEffect(()=>{r("method",c.method_type),r("type",c.account_type),r("account_number",c.account_number),r("cedula_rnc",c.cedula_rnc)},[c]);const I=async t=>{l(!0);try{const o=await M.callRawAPI(`/v3/api/custom/chumpchange/provider/payment-method/edit/${y.id}`,{method_type:t.method,account_type:t.type,account_number:t.account_number,cedula_rnc:t.cedula_rnc},"PUT");if(!o.error)P(p,s("toast.updated"));else if(o.validation){const S=Object.keys(o.validation);for(let u=0;u<S.length;u++){const N=S[u];v(N,{type:"manual",message:o.validation[N]})}}l(!1)}catch(o){l(!1),console.log("Error",o),v("product_id",{type:"manual",message:o.message})}},G=t=>{x(t),r("method",t.name)},U=t=>{f(t),r("type",t.name)};i.useEffect(()=>{p({type:"SETPATH",payload:{path:"skill"}})},[]);const V=async()=>{m(!1);try{l(!0),(await M.callRawAPI(`/v3/api/custom/chumpchange/provider/payment-method/delete/${y.id}`,{},"DELETE")).error||(P(p,s("toast.removed")),q("/provider/profile")),l(!1)}catch(t){console.log("Error",t),l(!1)}};return e.jsxs("div",{className:"p-5",children:[R&&e.jsx(B,{showCloseButton:!1,children:e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsxs("div",{className:"text-lg font-semibold",children:[s("loading.uploading"),"..."]}),e.jsx("div",{className:"mt-12",children:e.jsx(ae,{})})]})}),L&&e.jsx(B,{closeModal:()=>m(!1),children:e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsx("div",{className:"text-lg font-semibold",children:s("provider.add_payment.sure")}),e.jsxs("div",{className:"mt-12 flex w-full justify-evenly gap-5 px-5",children:[e.jsx("button",{onClick:V,className:"text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:s("buttons.remove")}),e.jsx("button",{onClick:()=>m(!1),className:"relative flex h-12 w-[163px] items-center justify-center rounded-2xl bg-[#8181a4]/20 text-center font-['Poppins'] text-sm font-medium text-[#8080a3]",children:s("buttons.cancel")})]})]})}),e.jsxs("div",{className:"relative flex w-full items-center justify-center ",children:[e.jsx("div",{className:" absolute left-0 top-0 ",children:e.jsx(Y,{})}),e.jsx("div",{className:"mt-3 text-center font-['Poppins'] text-lg font-medium text-black",children:s("provider.add_payment.e_title")})]}),((a==null?void 0:a.method)||(a==null?void 0:a.type)||(a==null?void 0:a.account_number)||(a==null?void 0:a.cedula_rnc))&&e.jsx("div",{className:"mt-10 flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:e.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium capitalize text-[#f95050] ",children:((_=a==null?void 0:a.method)==null?void 0:_.message)||((b=a==null?void 0:a.type)==null?void 0:b.message)||((j=a==null?void 0:a.account_number)==null?void 0:j.message)||((g=a==null?void 0:a.cedula_rnc)==null?void 0:g.message)})}),e.jsxs("form",{className:" w-full ",onSubmit:$(I),children:[e.jsx("div",{className:"mt-[50px] font-['Poppins'] text-[22px] font-medium text-black",children:s("provider.add_payment.method_accept")}),e.jsxs("div",{className:"mt-[27px] w-full ",children:[e.jsx("div",{className:"mb-1 px-2 font-['Poppins'] text-sm font-medium text-[#8080a3] ",children:s("provider.add_payment.s_method")}),e.jsx(k,{services:E,onServiceSelect:G,selectedService:A,setSelectedService:x})]}),e.jsxs("div",{className:"mt-[27px] w-full ",children:[e.jsx("div",{className:"mb-1 px-2 font-['Poppins'] text-sm font-medium text-[#8080a3] ",children:s("provider.add_payment.a_type")}),e.jsx(k,{services:C,onServiceSelect:U,selectedService:D,setSelectedService:f})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("div",{className:" px-2 font-['Poppins'] text-[22px] font-bold tracking-wide text-black",children:s("provider.add_payment.enter_account")}),e.jsx("input",{type:"text",placeholder:s("provider.add_payment.p_enter_account"),...h("account_number",{onChange:t=>w(t,20)}),className:"relative mt-3 h-12 w-full rounded-2xl border-none bg-white text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("div",{className:" px-2 font-['Poppins'] text-[22px] font-bold tracking-wide text-black",children:s("provider.add_payment.rnc")}),e.jsx("input",{type:"text",placeholder:s("provider.add_payment.p_rnc"),...h("cedula_rnc",{onChange:t=>w(t,20)}),className:"relative mt-3 h-12 w-full rounded-2xl border-none bg-white text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "})]}),e.jsxs("div",{className:"mt-[35px]",children:[e.jsx(Z,{type:"submit",children:s("buttons.save")}),e.jsx(ee,{className:"mt-[17px]",onClick:()=>m(!0),children:s("buttons.remove")})]})]})]})};export{Ie as default};
