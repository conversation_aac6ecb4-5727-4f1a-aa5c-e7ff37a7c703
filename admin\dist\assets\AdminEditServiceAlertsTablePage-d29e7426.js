import{j as a}from"./@react-google-maps/api-ac2f9d6f.js";import{R as r,b as T,r as p,h as k}from"./vendor-4f06b3f4.js";import{u as P}from"./react-hook-form-f3d72793.js";import{o as _}from"./yup-2324a46a.js";import{c as C,a as S}from"./yup-17027d7a.js";import{M as F,A as L,G as M,t as G,s as O}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as y}from"./MkdInput-ff3aa862.js";import{I as B}from"./InteractiveButton-8f7d74ee.js";import{S as H}from"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";let l=new F;const ge=i=>{const{dispatch:E}=r.useContext(L),j=C({name:S(),distance:S()}).required(),{dispatch:c}=r.useContext(M),[f,U]=r.useState({}),[h,d]=r.useState(!1),[N,u]=r.useState(!1),w=T(),[$,I]=p.useState(""),[q,A]=p.useState(""),{register:x,handleSubmit:D,setError:b,setValue:g,formState:{errors:v}}=P({resolver:_(j)}),s=k();p.useEffect(function(){(async function(){try{u(!0),l.setTable("service_alerts");const e=await l.callRestAPI({id:i.activeId?i.activeId:Number(s==null?void 0:s.id)},"GET");e.error||(g("name",e.model.name),g("distance",e.model.distance),I(e.model.name),A(e.model.distance),u(!1))}catch(e){u(!1),console.log("error",e),G(E,e.message)}})()},[]);const R=async e=>{d(!0);try{l.setTable("service_alerts");for(let n in f){let o=new FormData;o.append("file",f[n].file);let m=await l.uploadImage(o);e[n]=m.url}const t=await l.callRestAPI({id:i.activeId?i.activeId:Number(s==null?void 0:s.id),name:e.name,distance:e.distance},"PUT");if(!t.error)O(c,"Updated"),w("/admin/service_alerts"),c({type:"REFRESH_DATA",payload:{refreshData:!0}}),i.setSidebar(!1);else if(t.validation){const n=Object.keys(t.validation);for(let o=0;o<n.length;o++){const m=n[o];b(m,{type:"manual",message:t.validation[m]})}}d(!1)}catch(t){d(!1),console.log("Error",t),b("name",{type:"manual",message:t.message})}};return r.useEffect(()=>{c({type:"SETPATH",payload:{path:"service_alerts"}})},[]),a.jsxs("div",{className:" mx-auto rounded   p-5 shadow-md",children:[a.jsx("h4",{className:"text-2xl font-medium",children:"Edit Service Alerts"}),N?a.jsx(H,{}):a.jsxs("form",{className:" w-full max-w-lg",onSubmit:D(R),children:[a.jsx(y,{type:"text",page:"edit",name:"name",errors:v,label:"Name",placeholder:"Name",register:x,className:""}),a.jsx(y,{type:"number",page:"edit",name:"distance",errors:v,label:"Distance",placeholder:"Distance",register:x,className:""}),a.jsx(B,{type:"submit",className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",loading:h,disable:h,children:"Submit"})]})]})};export{ge as default};
