import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{r as i}from"./vendor-4f06b3f4.js";const c=({isModal:o,closeModal:n,children:r,title:a})=>{const t=i.useRef(null),s=()=>{const l=t.current;l&&(l.style.transform="translateY(100%)",setTimeout(()=>{n()},300))};return o&&e.jsxs("div",{className:"fixed left-0 top-0 z-[9999999999] h-full w-full",children:[e.jsx("div",{className:"h-full w-full",onClick:s,style:{background:"#11111136",backdropFilter:"blur(2.5px)"}}),e.jsxs("div",{ref:t,className:"absolute left-1/2 top-1/2 inline-flex h-auto max-h-[calc(100vh-50px)] min-h-[333px] w-[648px] -translate-x-1/2 -translate-y-1/2 flex-col items-start justify-start gap-8 overflow-y-auto overflow-x-hidden rounded-2xl bg-white  px-10 py-8",children:[e.jsxs("div",{className:"inline-flex w-full items-center justify-between gap-3 self-stretch",children:[e.jsx("div",{className:"h-12 font-['Poppins'] text-2xl font-semibold leading-[50px] text-black",children:a}),e.jsx("button",{className:"",onClick:s,children:e.jsxs("svg",{width:"56",height:"57",viewBox:"0 0 56 57",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"1",width:"55",height:"55",rx:"27.5",stroke:"#E4E7EC"}),e.jsx("path",{d:"M19.2634 37.2383L28.0018 28.5L36.7401 37.2383M36.7401 19.7617L28.0001 28.5L19.2634 19.7617",stroke:"#667085",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})})]}),r]})]})};export{c as default};
