import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{r}from"./vendor-4f06b3f4.js";import{_ as l}from"./qr-scanner-cf010ec4.js";import"./index-250f6b3d.js";const t=r.lazy(()=>l(()=>import("./PublicHeader-2646d9cb.js"),["assets/PublicHeader-2646d9cb.js","assets/@react-google-maps/api-ac2f9d6f.js","assets/vendor-4f06b3f4.js","assets/index-06b5b6dd.js","assets/react-confirm-alert-525c3702.js","assets/moment-a9aaa855.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-d39d893a.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-d923fcf0.js","assets/@fortawesome/react-fontawesome-6b681b2b.js","assets/@fortawesome/fontawesome-svg-core-4fa3e289.js","assets/@fortawesome/free-solid-svg-icons-0a9c4907.js","assets/@fortawesome/free-regular-svg-icons-0a88e957.js","assets/@fortawesome/free-brands-svg-icons-fae0dcac.js","assets/i18next-ec2f391c.js","assets/react-i18next-c78f8e57.js","assets/index-6c569bc9.css"])),a=({children:s})=>e.jsxs("div",{className:"flex w-full flex-col",children:[e.jsx(t,{}),e.jsx("div",{className:"min-h-screen grow",children:e.jsx(r.Suspense,{fallback:e.jsx("div",{className:"flex h-screen w-full items-center justify-center"}),children:s})})]}),n=r.memo(a);export{n as default};
