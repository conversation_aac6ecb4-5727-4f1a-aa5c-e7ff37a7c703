import{j as s}from"./@react-google-maps/api-ac2f9d6f.js";import{R as l,h as f}from"./vendor-4f06b3f4.js";import"./yup-17027d7a.js";import{M as j,G as m,t as o}from"./index-06b5b6dd.js";import{h as r}from"./moment-a9aaa855.js";import{S as N}from"./index-2d8231e7.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";let t=new j;const A=()=>{const{dispatch:d}=l.useContext(m),{dispatch:x}=l.useContext(m),[e,n]=l.useState({}),[h,i]=l.useState(!0),c=f();return l.useEffect(function(){(async function(){try{i(!0),t.setTable("task");const a=await t.callRestAPI({id:Number(c==null?void 0:c.id),join:""},"GET");a.error||(n(a.model),i(!1))}catch(a){i(!1),console.log("error",a),o(x,a.message)}})()},[]),l.useEffect(()=>{d({type:"SHOW_BACKBUTTON",payload:{showBackButton:!0}})},[]),s.jsx("div",{className:" mx-auto rounded  p-5 shadow-md",children:h?s.jsx(N,{}):s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"User Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.user_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Title"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.title})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Service Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.service_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Location Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.location_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Offer"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.offer})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex ",children:[s.jsx("div",{className:"flex-1",children:"Images"}),s.jsx("div",{className:"flex flex-1 gap-1",children:e==null?void 0:e.image.split(",").map(a=>s.jsx("a",{href:a,className:" text-blue-500 ",children:"View"}))})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Description"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.description})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Provider Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.provider_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Status"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.status})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Provider Status"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.provider_status})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Start Datetime"}),s.jsx("div",{className:"flex-1",children:r(e==null?void 0:e.start_datetime).format("DD/MM/YYYY")})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Created at"}),s.jsx("div",{className:"flex-1",children:r(e==null?void 0:e.create_at).format("DD/MM/YYYY")})]})})]})})};export{A as default};
