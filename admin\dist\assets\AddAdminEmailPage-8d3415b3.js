import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as r,b as F}from"./vendor-4f06b3f4.js";import{u as A}from"./react-hook-form-f3d72793.js";import{o as T}from"./yup-2324a46a.js";import{c as q,a as o}from"./yup-17027d7a.js";import{A as $,G as P,M as R,s as B,t as D}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";const ae=({setSidebar:m,getData:E})=>{var h,b,g,f,j,y;const[n,d]=r.useState(!1),k=q({slug:o().required(),subject:o().required(),html:o().required(),tag:o().required()}).required(),{dispatch:C}=r.useContext($),{dispatch:c}=r.useContext(P),S=F(),{register:a,handleSubmit:p,setError:u,formState:{errors:s}}=A({resolver:T(k)}),x=async l=>{let N=new R;d(!0);try{N.setTable("email");const t=await N.callRestAPI({slug:l.slug,subject:l.subject,html:l.html,tag:l.tag},"POST");if(!t.error)S("/admin/email"),B(c,"Added"),E(1,15);else if(t.validation){const w=Object.keys(t.validation);for(let i=0;i<w.length;i++){const v=w[i];u(v,{type:"manual",message:t.validation[v]})}}}catch(t){console.log("Error",t),u("subject",{type:"manual",message:t.message}),D(C,t.message)}d(!1)};return r.useEffect(()=>{c({type:"SETPATH",payload:{path:"email"}})},[]),e.jsxs("div",{className:"rounded  mx-auto",children:[e.jsxs("div",{className:"flex items-center p-3 gap-4 border-b border-b-[#E0E0E0] justify-between",children:[e.jsx("div",{className:"flex items-center gap-3",children:e.jsx("span",{className:"text-lg font-semibold",children:"Add Email"})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center py-2 px-3 border border-[#C6C6C6] rounded-md shadow-sm hover:bg-[#F4F4F4]",onClick:()=>m(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center py-2 px-3 text-white bg-[#4F46E5] rounded-md shadow-sm",onClick:async()=>{await p(x)(),m(!1)},disabled:n,children:n?"Saving...":"Save"})]})]}),e.jsxs("form",{className:" w-full max-w-lg text-left p-4",onSubmit:p(x),children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"slug",children:"Email Type"}),e.jsx("input",{type:"text",placeholder:"Email Type",...a("slug"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline
}`})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"subject",children:"Subject"}),e.jsx("input",{type:"text",placeholder:"subject",...a("subject"),className:`focus: shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(h=s.subject)!=null&&h.message?"border-red-500":""} `}),e.jsx("p",{className:"text-xs italic text-red-500",children:(b=s.subject)==null?void 0:b.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"tag",children:"Tags"}),e.jsx("input",{type:"text",placeholder:"tag",...a("tag"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(g=s.tag)!=null&&g.message?"border-red-500":""} `}),e.jsx("p",{className:"text-xs italic text-red-500",children:(f=s.tag)==null?void 0:f.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"html",children:"Email Body"}),e.jsx("textarea",{placeholder:"Email Body",className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(j=s.html)!=null&&j.message?"border-red-500":""}`,...a("html"),rows:15}),e.jsx("p",{className:"text-xs italic text-red-500",children:(y=s.html)==null?void 0:y.message})]})]})]})};export{ae as default};
