import{j as e}from"./@react-google-maps/api-ee55a349.js";import{r as m,d as w,R as b}from"./vendor-b16525a8.js";import{u as k}from"./react-hook-form-b6ed2679.js";import{o as y}from"./yup-3990215a.js";import{c as L,a as N}from"./yup-f828ae80.js";import{G as S,M as C,s as _}from"./index-09a1718e.js";import"./InteractiveButton-767677a2.js";import{A,a as P}from"./index-dd254604.js";import{B,a as E}from"./index-d54cffea.js";import{A as M}from"./index-4ee87ce5.js";import{t as o}from"./i18next-7389dd8c.js";import"./@hookform/resolvers-3e831b4a.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";import"./MoonLoader-49322f56.js";const R=[{name:"email",type:"email",placeholder:"Email",p:"p_phone",icon:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M14.1667 17.0833H5.83332C3.33332 17.0833 1.66666 15.8333 1.66666 12.9166V7.08329C1.66666 4.16663 3.33332 2.91663 5.83332 2.91663H14.1667C16.6667 2.91663 18.3333 4.16663 18.3333 7.08329V12.9166C18.3333 15.8333 16.6667 17.0833 14.1667 17.0833Z",stroke:"#8181A4",strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M14.1667 7.5L11.5583 9.58333C10.7 10.2667 9.29167 10.2667 8.43334 9.58333L5.83334 7.5",stroke:"#8181A4",strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"})]}),length:12}],re=()=>{var p;const[n,i]=m.useState(!1),[c,d]=m.useState(!1),u=L({email:N().required(o("auth_pages.reset.error_phone"))}).required(),h=w(),{register:x,handleSubmit:f,setError:g,formState:{errors:t,isValid:l}}=k({resolver:y(u),mode:"onChange"});m.useEffect(()=>{d(l)},[l]);const{dispatch:j}=b.useContext(S),v=async s=>{let r=new C;try{i(!0),(await r.callRawAPI("/v3/api/custom/chumpchange/user/password-reset/send-code",{email:s.email},"POST")).error||(_(j,"Reset Code Sent"),h(`/user/verify-code?email=${s.email}`)),i(!1)}catch(a){i(!1),console.log("Error",a),g("phone",{type:"manual",message:a==null?void 0:a.message})}};return e.jsxs(A,{children:[e.jsx(B,{}),e.jsxs("form",{onSubmit:f(v),children:[e.jsx(P,{className:"-mt-6 leading-none mx-auto sm:w-full w-[200px] ",children:o("auth_pages.reset.title")}),e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx("div",{className:" flex h-[70px] flex-col items-center justify-start",children:t!=null&&t.phone?e.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:e.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:(p=t==null?void 0:t.phone)==null?void 0:p.message})}):e.jsx("div",{className:"font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:o("auth_pages.reset.sub_title")})}),R.map((s,r)=>e.jsx(M,{type:s.type,name:s.name,placeholder:o(`auth_pages.reset.${s.p}`),errors:t,register:x,icon:s.icon},r)),e.jsx("div",{className:"",children:e.jsx("div",{className:"font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:o("auth_pages.reset.will_send")})}),e.jsx("div",{className:" mt-5 ",children:e.jsx(E,{loading:n,disabled:!c||n,type:"submit",children:o("buttons.next")})})]})]})]})};export{re as default};
