import{j as i}from"./@react-google-maps/api-afbf18d5.js";import{t}from"./i18next-7389dd8c.js";import{L as m}from"./vendor-f36d475e.js";import{f as o}from"./index-cf5e6bc7.js";import{u as x}from"./react-i18next-1e3e6bc5.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const M=({taskData:n})=>{const{i18n:r}=x();return i.jsx("div",{className:" flex h-full w-full flex-col gap-4 px-5 py-5 ",children:n.length===0?i.jsx("div",{className:"mt-5 w-full text-center font-['Poppins'] text-2xl font-medium text-[#b4b4b4]",dangerouslySetInnerHTML:{__html:t("provider.my_tasks.no_task")}}):n.map((e,s)=>i.jsxs(m,{to:`/${localStorage.getItem("role")}/view-job/${e.id}`,className:"inline-flex items-start justify-start gap-[5px]",children:[i.jsx("div",{className:"flex h-11 w-11 flex-shrink-0 items-center justify-center overflow-hidden rounded-full bg-[#50a8f9]/10",children:i.jsx("img",{src:e==null?void 0:e.service_logo,alt:"",className:"h-full w-full object-cover"})}),i.jsxs("div",{className:"inline-flex w-[283px] flex-col items-start justify-center pb-2",children:[i.jsxs("div",{className:"inline-flex items-center justify-start gap-1",children:[i.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:e==null?void 0:e.service_name}),i.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:"→"}),i.jsxs("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:["$",e==null?void 0:e.offer]})]}),i.jsx("div",{className:"inline-flex items-center w-full",children:i.jsxs("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:[e!=null&&e.address?e==null?void 0:e.address:"N/A",i.jsx("span",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3] ml-1",children:"•"}),i.jsx("span",{className:" text-right font-['Poppins'] text-xs font-medium text-[#8080a3]",children:o(e==null?void 0:e.start_datetime,r.language)})]})}),(e==null?void 0:e.provider_status)=="canceled"&&i.jsxs("div",{className:" inline-flex items-center justify-start gap-1",children:[i.jsxs("div",{className:" flex ",children:[i.jsx("div",{className:" font-['Poppins'] text-xs font-medium text-[#56ccf2]",children:t("provider.my_tasks.status")}),i.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:"•"})]}),i.jsx("div",{className:" text-right font-['Poppins'] text-xs font-medium capitalize text-[#8080a3] ",children:e!=null&&e.provider_status?e==null?void 0:e.provider_status:"On offer"})]}),(e==null?void 0:e.provider_status)!="canceled"&&i.jsxs("div",{className:" inline-flex items-center justify-start gap-1",children:[i.jsx("div",{className:" font-['Poppins'] text-xs font-medium text-[#8080a3]",children:e!=null&&e.rating?t("provider.my_tasks.rating"):t("provider.my_tasks.no_rating")}),i.jsx("div",{className:" font-['Poppins'] text-xs font-medium text-[#8080a3]",children:"•"}),i.jsx("div",{className:"inline-flex items-center justify-start gap-1",children:Array.from({length:5}).map((d,l)=>(e==null?void 0:e.rating)>0&&l<(e==null?void 0:e.rating)?i.jsx("svg",{width:"15",height:"15",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{d:"M5.10938 10.1562L7.5 14.1406L9.89062 10.1562L14.6719 8.5625L11.75 5.375L12.5469 0.59375L7.5 2.45312L2.1875 0.859375L3.51562 5.10938L0.859375 8.5625L5.10938 10.1562Z",fill:"#56CCF2"})},l):i.jsxs("svg",{width:"17",height:"17",viewBox:"0 0 17 17",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[i.jsx("g",{"clip-path":"url(#clip0_7154_4604)",children:i.jsx("path",{d:"M16.1698 9.6203C16.1303 9.8048 15.9853 9.94976 15.814 10.0157L11.3729 11.5048L9.00081 15.8668C8.90856 16.0513 8.71088 16.1567 8.51321 16.1567C8.31553 16.1567 8.11786 16.0513 8.02561 15.8668L5.6667 11.518L1.21243 10.1738C1.02794 10.1211 0.882977 9.97612 0.843442 9.79162C0.790729 9.60713 0.843442 9.40945 0.975225 9.26449L3.90081 6.06216L2.6884 1.54201C2.63569 1.34433 2.70158 1.1203 2.85972 0.988521C3.01786 0.84356 3.24189 0.804025 3.42639 0.883094L8.51321 2.91255L13.6 0.883094C13.6659 0.856738 13.7318 0.84356 13.8109 0.84356C13.9427 0.84356 14.0744 0.896273 14.1799 0.988521C14.338 1.13348 14.4039 1.34433 14.3512 1.54201L13.1388 6.06216L16.038 9.10635C16.1566 9.25131 16.2093 9.43581 16.1698 9.6203ZM12.1241 6.60247C11.9923 6.45751 11.9396 6.25984 11.9923 6.07534L13.007 2.33271L8.72406 4.01953C8.65817 4.04589 8.59228 4.05906 8.51321 4.05906C8.44732 4.05906 8.36825 4.04589 8.30236 4.01953L4.03259 2.31953L5.04732 6.06216C5.10003 6.24666 5.04732 6.44433 4.91553 6.57612L2.37212 9.36992L6.20701 10.5164C6.35197 10.556 6.47057 10.6482 6.53646 10.78L8.51321 14.4304L10.49 10.78C10.5558 10.6614 10.6613 10.5691 10.8062 10.5164L14.6279 9.23813L12.1241 6.60247Z",fill:"#56CCF2"})}),i.jsx("defs",{children:i.jsx("clipPath",{id:"clip0_7154_4604",children:i.jsx("rect",{width:"17",height:"17",fill:"white",transform:"matrix(1 0 0 -1 0 17)"})})})]},l))})]})]})]},s))})};export{M as default};
