import{j as s}from"./@react-google-maps/api-ac2f9d6f.js";import{R as m,b as U,h as C,r as F}from"./vendor-4f06b3f4.js";import{u as M}from"./react-hook-form-f3d72793.js";import{o as G}from"./yup-2324a46a.js";import{c as _,a as c,e as H}from"./yup-17027d7a.js";import{M as S,A as $,G as B,t as W,s as K}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{M as d}from"./MkdInput-ff3aa862.js";import{I as V}from"./InteractiveButton-8f7d74ee.js";import{S as z}from"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./MoonLoader-16bed42a.js";let g=new S;const be=h=>{var I;const{dispatch:N}=m.useContext($),j=_({address:c().required("Address is required"),phone:c().required("Phone is required"),website:c().url("Must be a valid URL"),image:H().required("Image is required"),hours:c(),status:c(),description:c(),type_id:c().required("Type is required")}).required(),{dispatch:b}=m.useContext(B),[a,L]=m.useState({}),[x,y]=m.useState(!1),[R,p]=m.useState(!1),[T,A]=m.useState([]),[w,E]=m.useState("");U();const{register:r,handleSubmit:q,setError:v,setValue:l,formState:{errors:n}}=M({resolver:G(j)}),u=C(),P=async()=>{var e;try{p(!0);let t=new S;t.setTable("nearby_type");const o=await t.callRestAPI({},"GETALL");A((e=o==null?void 0:o.list)==null?void 0:e.map(i=>({value:i.id,name:i.name}))),p(!1)}catch(t){p(!1),console.log("error",t)}};F.useEffect(function(){(async function(){try{await P(),p(!0),g.setTable("nearby_location");const e=await g.callRestAPI({id:h.activeId?h.activeId:Number(u==null?void 0:u.id)},"GET");e.error||(l("address",e.model.address),l("phone",e.model.phone),l("website",e.model.website),l("hours",e.model.hours),l("status",e.model.status),l("description",e.model.description),l("type_id",e.model.type_id),l("image",e.model.image),E(e.model.image),p(!1))}catch(e){p(!1),console.log("error",e),W(N,e.message)}})()},[]);const k=(e,t)=>{let o=a;o[e]={file:t.files[0],tempURL:URL.createObjectURL(t.files[0])},L({...o})},D=async e=>{y(!0);try{g.setTable("nearby_location");for(let o in a){let i=new FormData;i.append("file",a[o].file);let f=await g.uploadImage(i);e[o]=f.url}const t=await g.callRestAPI({id:h.activeId?h.activeId:Number(u==null?void 0:u.id),address:e.address,phone:e.phone,website:e.website,image:e.image,hours:e.hours,status:e.status,description:e.description,type_id:e.type_id},"PUT");if(!t.error)K(b,"Updated"),b({type:"REFRESH_DATA",payload:{refreshData:!0}}),h.setSidebar(!1);else if(t.validation){const o=Object.keys(t.validation);for(let i=0;i<o.length;i++){const f=o[i];v(f,{type:"manual",message:t.validation[f]})}}y(!1)}catch(t){y(!1),console.log("Error",t),v("address",{type:"manual",message:t.message})}};return s.jsxs("div",{className:"mx-auto rounded p-5 shadow-md",children:[s.jsx("h4",{className:"text-2xl font-medium",children:"Edit Location"}),R?s.jsx(z,{}):s.jsxs("form",{className:"w-full max-w-lg",onSubmit:q(D),children:[s.jsx(d,{type:"text",page:"edit",name:"address",errors:n,label:"Address",placeholder:"Address",register:r,className:""}),s.jsx(d,{type:"text",page:"edit",name:"phone",errors:n,label:"Phone",placeholder:"Phone",register:r,className:""}),s.jsx(d,{type:"text",page:"edit",name:"website",errors:n,label:"Website",placeholder:"Website",register:r,className:""}),s.jsx(d,{type:"file",page:"edit",name:"image",errors:n,label:"Image",placeholder:"Upload Image",register:r,onChange:e=>k("image",e.target),className:""}),a!=null&&a.image||w?s.jsx("div",{className:"py-4",children:s.jsx("img",{src:a!=null&&a.image?(I=a==null?void 0:a.image)==null?void 0:I.tempURL:w,alt:"",className:"h-[100px] w-auto"})}):null,s.jsx(d,{type:"text",page:"edit",name:"hours",errors:n,label:"Hours",placeholder:"Hours",register:r,className:""}),s.jsx(d,{type:"dropdown",page:"edit",name:"status",errors:n,label:"Status",placeholder:"Status",register:r,className:"",options:[{value:"inactive",name:"Inactive"},{value:"active",name:"Active"}]}),s.jsx(d,{type:"textarea",page:"edit",name:"description",errors:n,label:"Description",placeholder:"Description",register:r,className:"",rows:"5"}),s.jsx(d,{type:"dropdown",page:"edit",name:"type_id",errors:n,label:"Type",placeholder:"Select Type",register:r,options:T,className:""}),s.jsx(V,{type:"submit",className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",loading:x,disable:x,children:"Submit"})]})]})};export{be as default};
