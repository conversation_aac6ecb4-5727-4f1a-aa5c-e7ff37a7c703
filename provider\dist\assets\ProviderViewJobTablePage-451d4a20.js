import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{R as r,h as E,d as G}from"./vendor-f36d475e.js";import"./yup-2f6e2476.js";import{M as U,G as N,f as F,a as V,r as L,t as b}from"./index-cf5e6bc7.js";import"./index-bec80226.js";import{B as O,a as k,R as v,G as H}from"./index-895fa99b.js";import{M as d}from"./index-bf8d79cc.js";import{U as J}from"./index-04e38e92.js";import{S as y}from"./index-65bc3378.js";import{t as s}from"./i18next-7389dd8c.js";import{u as K}from"./react-i18next-1e3e6bc5.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let x=new U;const pt=()=>{var h,j,g;r.useContext(N);const{dispatch:p}=r.useContext(N),[t,_]=r.useState({}),[P,i]=r.useState(!0),[C,c]=r.useState(!1),[S,o]=r.useState(!1),[B,I]=r.useState(!1),[R,f]=r.useState(!1),n=E(),{i18n:T}=K(),A=G(),u=async()=>{try{i(!0),x.setTable("task");const a=await x.callRestAPI({id:Number(n==null?void 0:n.id),join:"user,location,service"},"GET");a.error||(_(a.model),i(!1))}catch(a){i(!1),console.log("error",a),b(p,a.message)}},m=async a=>{try{i(!0),c(!0),o(!1),(await x.callRawAPI("/v3/api/custom/chumpchange/provider/task/respond",{task_id:Number(n==null?void 0:n.id),provider_status:a},"POST")).error||await u(),i(!1),c(!1)}catch(l){c(!1),i(!1),b(p,l.message)}};r.useEffect(function(){(async function(){await u()})()},[]);const D=(t==null?void 0:t.provider_status)==="completed"?s("provider.task.completed_title"):(t==null?void 0:t.provider_status)==="in-progress"?s("provider.task.in_progress_title"):(t==null?void 0:t.provider_status)==="accepted"?s("provider.task.accepted_title"):s("provider.task.accepted_title");return e.jsxs("div",{className:"p-5",children:[e.jsxs("div",{className:"relative flex w-full items-center justify-center ",children:[e.jsx("div",{className:" absolute left-0 top-0 ",children:e.jsx(O,{link:"/provider/my-tasks"})}),e.jsxs("div",{className:"",children:[e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:D}),e.jsx("div",{className:"text-center font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:t!=null&&t.title?t==null?void 0:t.title:"N/A"})]})]}),P?e.jsx("div",{className:"flex h-full w-full items-center justify-center pt-10 ",children:e.jsx(y,{})}):e.jsxs(e.Fragment,{children:[e.jsx(J,{userData:t==null?void 0:t.user}),e.jsxs("div",{className:" mt-9 ",children:[e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:s("provider.task.help_with")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:t==null?void 0:t.title})]}),e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:s("provider.task.date_time")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:F(t==null?void 0:t.start_datetime,T.language)})]}),e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:s("provider.task.pay_offered")}),e.jsxs("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:["DOP ",t==null?void 0:t.offer]})]}),e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:s("provider.task.get_dir")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:e.jsx("button",{onClick:()=>{V(Number(t==null?void 0:t.latitude),Number(t==null?void 0:t.longitude))},className:" text-[#4fa7f9] underline ",children:t!=null&&t.address?L(t==null?void 0:t.address):"N/A"})})]}),e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:s("provider.task.call_client")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:(h=t==null?void 0:t.user)==null?void 0:h.phone})]}),e.jsxs("div",{className:"flex justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsxs("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:[s("provider.task.description"),":"]}),e.jsx("div",{className:"w-[165px] text-right font-['Poppins'] text-sm font-light leading-[16.80px] text-black",children:t==null?void 0:t.description})]})]}),((j=t==null?void 0:t.image)==null?void 0:j.split(",").length)>0&&e.jsxs("div",{className:"",children:[e.jsx("div",{className:"h-[27px] w-[154px] font-['Poppins'] text-lg font-medium text-black",children:s("provider.task.images")}),e.jsx("div",{className:"scrollbar-hide flex w-full overflow-x-auto",children:e.jsx("div",{className:" mt-5 flex gap-2",children:(g=t==null?void 0:t.image)==null?void 0:g.split(",").map((a,l)=>e.jsx("div",{className:"relative h-[127px] w-[120px]",children:e.jsx("img",{onClick:()=>{f(!0),I(a)},className:" h-full w-full rounded-[20px] object-cover ",src:a})},l))})})]}),e.jsxs("div",{className:"mt-10 flex flex-col items-center justify-center gap-2 ",children:[(t==null?void 0:t.provider_status)==="accepted"&&e.jsxs(e.Fragment,{children:[e.jsx(k,{onClick:()=>m("in-progress"),children:s("provider.task.mark_progress")}),e.jsx(v,{onClick:()=>o(!0),children:s("provider.task.cancel_task")})]}),(t==null?void 0:t.provider_status)==="in-progress"&&e.jsx(k,{disabled:(t==null?void 0:t.status)!=="in-progress",onClick:()=>m("completed"),children:s("provider.task.mark_com")}),(t==null?void 0:t.provider_status)==="completed"&&e.jsx(H,{onClick:()=>A("/provider/my-tasks"),children:s("buttons.done")})]})]}),C&&e.jsx(d,{showCloseButton:!1,children:e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsxs("div",{className:"text-lg font-semibold",children:[" ",s("loading.updating"),"..."]}),e.jsx("div",{className:"mt-12",children:e.jsx(y,{})})]})}),S&&e.jsx(d,{closeModal:()=>o(!1),children:e.jsxs("div",{className:" mt-5 flex h-max flex-col items-center justify-center gap-8 ",children:[e.jsx("div",{className:"text-center font-['Poppins'] text-2xl font-medium leading-[31.20px] text-black",dangerouslySetInnerHTML:{__html:s("provider.task.sure")}}),e.jsx(v,{onClick:()=>m("cancelled"),className:" !w-[163px]",children:s("buttons.cancel")})]})}),R&&e.jsx(d,{closeModal:()=>f(!1),children:e.jsx("div",{className:" mt-10 flex h-max w-full flex-col items-center justify-center gap-8 pb-6 ",children:e.jsx("img",{className:" h-auto w-full max-w-[450px] object-cover ",src:B})})})]})};export{pt as default};
