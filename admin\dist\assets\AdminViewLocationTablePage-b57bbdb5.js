import{j as s}from"./@react-google-maps/api-ac2f9d6f.js";import{R as a,h as o}from"./vendor-4f06b3f4.js";import{M as h,G as m,t as j}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{S as f}from"./index-2d8231e7.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";let r=new h;const L=()=>{const{dispatch:t}=a.useContext(m),{dispatch:d}=a.useContext(m),[e,n]=a.useState({}),[x,l]=a.useState(!0),c=o();return a.useEffect(function(){(async function(){try{l(!0),r.setTable("nearby_location");const i=await r.callRestAPI({id:Number(c==null?void 0:c.id),join:""},"GET");i.error||(n(i.model),l(!1))}catch(i){l(!1),console.log("error",i),j(t,i.message)}})()},[]),a.useEffect(()=>{d({type:"SHOW_BACKBUTTON",payload:{showBackButton:!0}})},[]),s.jsx("div",{className:"mx-auto rounded p-5 shadow-md",children:x?s.jsx(f,{}):s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Address"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.address})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Business Name"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.business_name})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Phone"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.phone})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Website"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.website})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Image"}),s.jsx("div",{className:"flex-1",children:e!=null&&e.image?s.jsx("img",{src:e.image,alt:"Location",className:"h-32 w-32"}):"No image available"})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Hours"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.hours})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Status"}),s.jsx("div",{className:"flex-1",children:e!=null&&e.status?"Acitve":"Inactive"})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Description"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.description})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Type ID"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.type_id})]})})]})})};export{L as default};
