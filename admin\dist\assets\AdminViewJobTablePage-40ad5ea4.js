import{j as s}from"./@react-google-maps/api-ac2f9d6f.js";import{R as a,h as o}from"./vendor-4f06b3f4.js";import"./yup-17027d7a.js";import{M as h,G as m,t as f}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{S as j}from"./index-2d8231e7.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";let t=new h;const I=()=>{const{dispatch:c}=a.useContext(m),{dispatch:d}=a.useContext(m),[e,n]=a.useState({}),[x,r]=a.useState(!0),i=o();return a.useEffect(function(){(async function(){try{r(!0),t.setTable("job");const l=await t.callRestAPI({id:Number(i==null?void 0:i.id),join:""},"GET");l.error||(n(l.model),r(!1))}catch(l){r(!1),console.log("error",l),f(d,l.message)}})()},[]),a.useEffect(()=>{c({type:"SHOW_BACKBUTTON",payload:{showBackButton:!0}})},[]),s.jsx("div",{className:" mx-auto rounded  p-5 shadow-md",children:x?s.jsx(j,{}):s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Task"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.task})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Arguments"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.arguments})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Error Log"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.error_log})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Identifier"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.identifier})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Retries"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.retries})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Retry Count"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.retry_count})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Time Interval"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.time_interval})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Last Run"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.last_run})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Status"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.status})]})})]})})};export{I as default};
