import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as i,b as F,h as A,r as j}from"./vendor-4f06b3f4.js";import{u as $}from"./react-hook-form-f3d72793.js";import{o as q}from"./yup-2324a46a.js";import{c as R,a as T,d as D}from"./yup-17027d7a.js";import{M as G,A as M,G as _,s as v,t as E}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";let N=new G;const ie=({activeId:a,setSidebar:l})=>{var h,b;const w=R({name:T().required(),status:D().required()}).required(),{dispatch:n}=i.useContext(M),{dispatch:r}=i.useContext(_);F(),A();const[H,k]=j.useState(0),[c,m]=j.useState(!1),{register:d,handleSubmit:p,setError:S,setValue:u,formState:{errors:x}}=$({resolver:q(w)}),C=[{key:"0",value:"Inactive"},{key:"1",value:"Active"}],f=async t=>{m(!0);try{const s=await N.updateStripePrice(a,t);if(!s.error)v(r,"Edited",4e3);else if(s.validation){const g=Object.keys(s.validation);for(let o=0;o<g.length;o++){const y=g[o];S(y,{type:"manual",message:s.validation[y]})}}}catch(s){console.log("Error",s),v(r,s.message,4e3),E(n,s.message)}m(!1)};async function P(){try{const t=await N.getStripePrice(a);if(!t.error){const s=t.model.object;u("name",s.nickname),u("status",t.model.status),k(t.model.id)}}catch(t){console.log("Error",t),E(n,t.message)}}return i.useEffect(()=>{r({type:"SETPATH",payload:{path:"prices"}}),P()},[a]),e.jsxs("div",{className:"rounded   mx-auto",children:[e.jsxs("div",{className:"flex items-center p-3 gap-4 border-b border-b-[#E0E0E0] justify-between",children:[e.jsx("div",{className:"flex items-center gap-3",children:e.jsx("span",{className:"text-lg font-semibold",children:"Edit Plan"})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center py-2 px-3 border border-[#C6C6C6] rounded-md shadow-sm hover:bg-[#F4F4F4]",onClick:()=>l(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center py-2 px-3 text-white bg-[#4F46E5] rounded-md shadow-sm",onClick:async()=>{await p(f)(),l(!1)},disabled:c,children:c?"Saving...":"Save"})]})]}),e.jsxs("form",{className:"w-full text-left max-w-lg p-4",onSubmit:p(f),children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"name",children:"Name"}),e.jsx("input",{type:"text",...d("name"),className:`"appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(h=x.name)!=null&&h.message?"border-red-500":""}`}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(b=x.name)==null?void 0:b.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Status"}),e.jsx("select",{className:"appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",...d("status"),children:C.map(t=>e.jsx("option",{value:t.key,children:t.value},t.key))})]})]})]})};export{ie as default};
