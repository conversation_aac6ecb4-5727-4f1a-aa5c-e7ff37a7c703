import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as r,L as c,i as x}from"./vendor-4f06b3f4.js";import{P as d}from"./index.esm-e652e625.js";import{M as m,G as p,A as h,t as f}from"./index-06b5b6dd.js";import{M as u}from"./index.esm-8e8a99ba.js";import"./moment-a9aaa855.js";import"./react-icons-e5379072.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";let j=new m;const v=[{to:"/user/dashboard",text:"Dashboard",icon:e.jsx(u,{className:"text-xl text-[#A8A8A8]"}),value:"admin"},{to:"/user/profile",text:"Profile",icon:e.jsx(d,{className:"text-xl text-[#A8A8A8]"}),value:"profile"}],H=()=>{const{state:{isOpen:s,path:i},dispatch:o}=r.useContext(p),{state:w,dispatch:l}=r.useContext(h);r.useState(!1),r.useState(!1);let n=t=>{o({type:"OPEN_SIDEBAR",payload:{isOpen:t}})};return r.useEffect(()=>{async function t(){try{const a=await j.getProfile();l({type:"UPDATE_PROFILE",payload:a})}catch(a){console.log("Error",a),f(l,a.response.data.message?a.response.data.message:a.message)}}t()},[]),e.jsx(e.Fragment,{children:e.jsxs("div",{className:`z-50 flex max-h-screen flex-1 flex-col border border-[#E0E0E0] bg-white py-4 text-[#A8A8A8] transition-all ${s?"fixed h-screen w-[15rem] min-w-[15rem] max-w-[15rem] md:relative":"relative min-h-screen w-[4.2rem] min-w-[4.2rem] max-w-[4.2rem] bg-black text-white"} `,children:[e.jsxs("div",{className:`text-[#393939] ${s?"flex w-full":"flex items-center justify-center"} `,children:[e.jsx("div",{}),s&&e.jsx("div",{className:"text-2xl font-bold",children:e.jsx(c,{to:"/",children:e.jsxs("h4",{className:"flex cursor-pointer items-center px-4 pb-4 font-sans font-bold",children:["Baas Brand"," "]})})})]}),e.jsx("div",{className:"h-fit w-auto flex-1",children:e.jsx("div",{className:"sidebar-list w-auto",children:e.jsx("ul",{className:"flex flex-wrap px-2 text-sm",children:v.map(t=>e.jsx("li",{className:"block w-full list-none",children:e.jsx(x,{to:t.to,className:`${i==t.value?"active-nav":""} `,children:e.jsxs("div",{className:"flex items-center gap-3",children:[t.icon,s&&e.jsx("span",{children:t.text})]})})},t.value))})})}),e.jsx("div",{className:"flex justify-end",children:e.jsx("div",{className:"mr-3 cursor-pointer rounded-lg border border-[#E0E0E0] bg-white p-2 text-2xl text-gray-400",children:e.jsx("span",{onClick:()=>n(!s),children:e.jsx("svg",{className:`transition-transform ${s?"":"rotate-180"}`,xmlns:"http:www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12ZM10.4142 11L11.7071 9.70711C12.0976 9.31658 12.0976 8.68342 11.7071 8.29289C11.3166 7.90237 10.6834 7.90237 10.2929 8.29289L7.82322 10.7626C7.13981 11.446 7.13981 12.554 7.82322 13.2374L10.2929 15.7071C10.6834 16.0976 11.3166 16.0976 11.7071 15.7071C12.0976 15.3166 12.0976 14.6834 11.7071 14.2929L10.4142 13H16C16.5523 13 17 12.5523 17 12C17 11.4477 16.5523 11 16 11H10.4142Z",fill:"#A8A8A8"})})})})})]})})};export{H as UserHeader,H as default};
