import{j as t}from"./@react-google-maps/api-afbf18d5.js";import{B as S,a as _}from"./index-895fa99b.js";import{A as R,a as k}from"./index-55e4d382.js";import{S as B}from"./index-65bc3378.js";import{M as C}from"./index-bf8d79cc.js";import{M as D,G as L,s as x}from"./index-cf5e6bc7.js";import{r as c,R as M,u as O,h as T,d as A}from"./vendor-f36d475e.js";import{u as E}from"./react-i18next-1e3e6bc5.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";let G=new D;const ie=()=>{const[n,m]=c.useState(["","","",""]),a=c.useRef([]),[g,K]=c.useState(!1),[j,i]=c.useState(!1),[p,$]=c.useState(!1),{dispatch:u}=M.useContext(L),f=O(),d=new URLSearchParams(f.search).get("phone");T();const{t:r}=E(),b=(s,e)=>{if(isNaN(s))return;const o=[...n];o[e]=s,m(o),s&&e<3?a.current[e+1].focus():!s&&e>0&&a.current[e-1].focus()},N=s=>{const e=s.clipboardData.getData("text").split("").filter(l=>!isNaN(l)),o=[...n];e.slice(0,4).forEach((l,h)=>{o[h]=l,a.current[h].value=l}),m(o),e.length>=4?a.current[3].focus():e.length===3?a.current[2].focus():e.length===2?a.current[1].focus():e.length===1&&a.current[0].focus()},v=(s,e)=>{s.key==="Backspace"&&!n[e]&&e>0&&a.current[e-1].focus()},y=A(),P=async s=>{console.log("Phone >> ",d),y(`/provider/reset/${n.join("")}?phone=${d}`,{state:{phone:f.state.phone}})},w=async()=>{console.log("Resend OTP");try{i(!0);const s=await G.callRawAPI("/v3/api/custom/chumpchange/provider/resend-code",{phone:f.state.phone},"POST");x(u,"Sent successfully"),i(!1)}catch(s){i(!1),console.log("error >> ",s),x(u,s.message,5e3,"error")}};return console.log("otp >> ",n.join("")),t.jsxs(R,{children:[j&&t.jsx(C,{showCloseButton:!1,children:t.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[t.jsxs("div",{className:"text-lg font-semibold",children:[r("loading.sending"),"..."]}),t.jsx("div",{className:"mt-12",children:t.jsx(B,{})})]})}),t.jsx(S,{}),t.jsx(k,{className:"mx-auto -mt-6 max-w-[286px] leading-none ",children:r("auth_pages.reset.title")}),t.jsxs("div",{className:"flex flex-col gap-4",children:[t.jsx("div",{className:" flex h-[70px] flex-col items-center justify-start",children:p?t.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:t.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:r("auth_pages.reset.invalid_code")})}):t.jsx("div",{className:"font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:r("auth_pages.reset.v_sub_title")})}),t.jsx("div",{className:"flex w-full items-center justify-between gap-2 ",children:n.map((s,e)=>t.jsx("input",{type:"text",maxLength:"1",value:s,onChange:o=>b(o.target.value,e),onPaste:N,onKeyDown:o=>v(o,e),ref:o=>a.current[e]=o,className:`h-[83px] w-[72px] rounded-2xl border-2  text-center font-['Poppins'] text-2xl  font-medium text-black outline-none focus:outline-none focus:ring-0 ${p?"border-[#ff0000]/5 bg-[#ff0000]/5 focus:border-[#ff0000]/5":"border-[#fff] bg-white focus:border-[#fff] "} `},e))}),t.jsxs("div",{className:" mt-0 flex gap-4 ",children:[t.jsx("p",{className:"text-center font-['Poppins'] text-sm font-medium leading-[18.20px] text-[#8080a3]",children:r("auth_pages.reset.dont_get")}),t.jsx("p",{onClick:w,className:"text-center font-['Poppins'] text-sm font-medium leading-[18.20px] text-[#56ccf2]",children:r("auth_pages.reset.resend")})]}),t.jsx("div",{className:" mt-10 ",children:t.jsx(_,{loading:g,disabled:n.join("").length!==4,type:"submit",onClick:P,children:r("buttons.next")})})]})]})};export{ie as default};
