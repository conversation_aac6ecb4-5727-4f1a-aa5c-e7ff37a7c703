import{j as s}from"./@react-google-maps/api-afbf18d5.js";import{r as v,R as d,h as P,d as N}from"./vendor-f36d475e.js";import{u as y}from"./react-hook-form-ff037c98.js";import{o as S}from"./yup-afe5cf51.js";import{c as A,a as l,b as B}from"./yup-2f6e2476.js";import{A as L,a as T}from"./index-55e4d382.js";import{P as c}from"./index-ad319f83.js";import{B as C,a as G}from"./index-895fa99b.js";import{M as I,A as R,G as k,s as q}from"./index-cf5e6bc7.js";import{u as E}from"./react-i18next-1e3e6bc5.js";import"./@hookform/resolvers-eb417cd0.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";let M=new I;const ds=()=>{var n,p;const[i,o]=v.useState(!1),{state:O,dispatch:u}=d.useContext(R),{dispatch:f}=d.useContext(k),{id:g}=P(),{t:a}=E(),x=N(),h=A({password:l().min(8,a("auth_pages.sign_up.password_long")).required(),confirm_password:l().oneOf([B("password"),null],a("auth_pages.sign_up.password_must")).required()}).required(),{register:r,handleSubmit:_,setError:w,formState:{errors:t}}=y({resolver:S(h)}),j=async m=>{try{o(!0);const e=await M.callRawAPI("/v3/api/custom/chumpchange/provider/signup/step3",{password:m.password,confirmPassword:m.confirm_password,phone:g},"POST"),b={user_id:e.data[0].id,...e.data[0]};console.log("result >> ",e),e.error||(u({type:"LOGIN",payload:b}),x("/provider/upload-id")),o(!1)}catch(e){o(!1),console.log("error >> ",e),q(f,e==null?void 0:e.message,4e3,"error"),w("password",{type:"manual",message:e==null?void 0:e.message})}};return s.jsx(L,{children:s.jsxs("form",{onSubmit:_(j),children:[s.jsx(C,{}),s.jsx(T,{title:a("auth_pages.sign_up.title"),className:" -mt-6 "}),s.jsxs("div",{className:"flex flex-col gap-4",children:[s.jsx("div",{className:" flex h-[70px] flex-col items-center",children:t!=null&&t.password||t!=null&&t.confirm_password?s.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:s.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:((n=t==null?void 0:t.password)==null?void 0:n.message)||((p=t==null?void 0:t.confirm_password)==null?void 0:p.message)})}):s.jsx("div",{className:"font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:a("auth_pages.sign_up.sub_title_password")})}),s.jsx(c,{name:"password",placeholder:a("auth_pages.sign_up.password"),errors:t,register:r}),s.jsx(c,{name:"confirm_password",placeholder:a("auth_pages.sign_up.confirm_password"),errors:t,register:r}),s.jsx("div",{className:"flex justify-center",children:s.jsx("div",{className:"text-center font-['Poppins'] text-sm font-medium leading-snug text-[#8080a3]",dangerouslySetInnerHTML:{__html:a("auth_pages.sign_up.password_des")}})}),s.jsx("div",{className:" mt-10 ",children:s.jsx(G,{loading:i,disabled:i,type:"submit",children:a("auth_pages.sign_up.next")})})]})]})})};export{ds as default};
