import{j as p}from"./@react-google-maps/api-afbf18d5.js";import"./vendor-f36d475e.js";import{S as n}from"./index-cf5e6bc7.js";import{A as u}from"./AddButton-0499cde5.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./react-i18next-1e3e6bc5.js";const O=({actions:i,selectedItems:o})=>p.jsx("div",{className:"flex gap-2",children:Object.keys(i).map(r=>i[r].show).includes(!0)?p.jsx(p.Fragment,{children:Object.keys(i).map(r=>{var a,l;if(i[r].show&&!["select","add","export"].includes(r)){if(o&&(o==null?void 0:o.length)===1&&!((a=i[r])!=null&&a.multiple))return p.jsx(u,{showPlus:!1,className:`cursor-pointer px-2 py-2 text-lg  font-medium leading-loose tracking-wide ${r==="view"?"text-blue-500":r==="delete"?"text-red-500":"text-[#292829fd]"} hover:underline`,onClick:()=>{var t;(t=i[r])!=null&&t.action&&i[r].action(o)},children:n(r,{casetype:"capitalize",separator:" "})},r);if(o&&(o==null?void 0:o.length)>=1&&((l=i[r])!=null&&l.multiple))return p.jsx(u,{showPlus:!1,className:`cursor-pointer px-2 py-2 text-lg  font-medium leading-loose tracking-wide ${r==="view"?"text-blue-500":r==="delete"?"text-red-500":"text-[#292829fd]"} hover:underline`,onClick:()=>{var t;(t=i[r])!=null&&t.action&&i[r].action(o)},children:n(r,{casetype:"capitalize",separator:" "})},r)}}).filter(Boolean)}):null});export{O as default};
