import{j as i}from"./@react-google-maps/api-afbf18d5.js";import{M as c,A as x,f as l}from"./index-cf5e6bc7.js";import{t as d}from"./i18next-7389dd8c.js";import"./moment-a9aaa855.js";import{r as s,L as m}from"./vendor-f36d475e.js";import{u as f}from"./react-i18next-1e3e6bc5.js";import"./react-confirm-alert-2487dba8.js";import"./@react-pdf-viewer/core-9d395990.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";new c;const M=({taskData:r,expired:o=!1})=>{s.useContext(x);const{i18n:n}=f();return i.jsx("div",{className:" flex h-full w-full flex-col gap-4 px-5 py-5 ",children:r.length===0?i.jsx("div",{className:"mt-5 w-full text-center font-['Poppins'] text-2xl font-medium text-[#b4b4b4]",dangerouslySetInnerHTML:{__html:d("provider.listing.no_listing")}}):r.map((e,t)=>e!=null&&e.product_name?i.jsxs(m,{to:`/provider/view-product-listing/${e.id}/${o}`,className:"inline-flex items-start justify-start gap-[5px]",children:[i.jsx("div",{className:"flex h-11 w-11 flex-shrink-0 items-center justify-center overflow-hidden rounded-full bg-[#50a8f9]/10",children:i.jsx("img",{src:e==null?void 0:e.product_logo,alt:"",className:"h-full w-full object-cover"})}),i.jsxs("div",{className:"inline-flex w-[283px] flex-col items-start justify-center pb-2",children:[i.jsxs("div",{className:"inline-flex items-center justify-start gap-1",children:[i.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:e!=null&&e.product_name?e==null?void 0:e.product_name:"N/A"}),i.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:"→"}),i.jsxs("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:["$",e!=null&&e.price?e==null?void 0:e.price:"N/A"]})]}),i.jsxs("div",{className:"inline-flex items-center justify-start gap-1",children:[i.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:e!=null&&e.city?e==null?void 0:e.city:"N/A"}),i.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:"•"}),i.jsxs("div",{className:"text-right font-['Poppins'] text-xs font-medium text-[#8080a3]",children:[l(e==null?void 0:e.start_date,n.language)," to ",l(e==null?void 0:e.end_date,n.language)]})]})]})]},t):i.jsxs(m,{to:`/provider/view-service-listing/${e.id}`,className:"inline-flex items-start justify-start gap-[5px]",children:[i.jsx("div",{className:"flex h-11 w-11 items-center justify-center overflow-hidden rounded-full bg-[#50a8f9]/10",children:i.jsx("img",{src:e==null?void 0:e.service_logo,alt:"",className:"h-full w-full object-cover"})}),i.jsxs("div",{className:"inline-flex w-[283px] flex-col items-start justify-center pb-2",children:[i.jsxs("div",{className:"inline-flex items-center justify-start gap-1",children:[i.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:e!=null&&e.service_name?e==null?void 0:e.service_name:"N/A"}),i.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:"→"}),i.jsxs("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:["$",e!=null&&e.rate?e==null?void 0:e.rate:"N/A"]})]}),i.jsxs("div",{className:"inline-flex items-center justify-start gap-1",children:[i.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:e!=null&&e.city?e==null?void 0:e.city:"N/A"}),i.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:"•"}),i.jsxs("div",{className:"text-right font-['Poppins'] text-xs font-medium text-[#8080a3]",children:[l(e==null?void 0:e.start_date,n.language)," to ",l(e==null?void 0:e.end_date,n.language)]})]})]})]},t))})};export{M as default};
