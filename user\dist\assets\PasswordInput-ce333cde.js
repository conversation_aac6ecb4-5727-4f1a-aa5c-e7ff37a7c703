import{j as e}from"./@react-google-maps/api-ee55a349.js";import{r as h}from"./vendor-b16525a8.js";const x=({name:t,errors:s,register:i,className:d,placeholder:C,disabled:a=!1})=>{var r,n;const[o,l]=h.useState(!1);return e.jsxs("div",{className:`relative flex h-12 w-full items-center overflow-hidden rounded-2xl border-2 ${(r=s[t])!=null&&r.message?" border-[#ff0000]/5 !bg-[#ff0000]/5 text-[#f95050]":"border-white bg-white text-[#8080a3]"}`,children:[e.jsx("span",{className:`absolute left-3 ${(n=s[t])!=null&&n.message?"text-[#f95050]":"text-[#8181A4]"} `,children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M13.9375 7.99998H14.6875C15.2399 7.99998 15.7663 8.21469 16.1519 8.59169C16.5369 8.96813 16.75 9.47505 16.75 9.99998V16.4166C16.75 16.9416 16.5369 17.4485 16.1519 17.8249C15.7663 18.2019 15.2399 18.4166 14.6875 18.4166H5.3125C4.76012 18.4166 4.23367 18.2019 3.8481 17.8249C3.46311 17.4485 3.25 16.9416 3.25 16.4166V9.99998C3.25 9.47505 3.46311 8.96813 3.8481 8.59169C4.23367 8.21469 4.76012 7.99998 5.3125 7.99998H6.0625V7.24998V5.41665C6.0625 4.40549 6.47315 3.43225 7.20978 2.712C7.94697 1.99118 8.95034 1.58331 10 1.58331C11.0497 1.58331 12.053 1.99118 12.7902 2.712C13.5268 3.43225 13.9375 4.40549 13.9375 5.41665V7.24998V7.99998ZM12.8125 7.99998H13.5625V7.24998V5.41665C13.5625 4.48288 13.183 3.59086 12.5131 2.93585C11.8438 2.28139 10.9395 1.91665 10 1.91665C9.06054 1.91665 8.15625 2.28139 7.48692 2.93585C6.81702 3.59086 6.4375 4.48288 6.4375 5.41665V7.24998V7.99998H7.1875H12.8125ZM15.8748 17.6011C16.193 17.2899 16.375 16.8642 16.375 16.4166V9.99998C16.375 9.55245 16.193 9.12674 15.8748 8.81554C15.5571 8.50491 15.1297 8.33331 14.6875 8.33331H5.3125C4.87032 8.33331 4.44295 8.50491 4.12525 8.81554C3.80698 9.12674 3.625 9.55244 3.625 9.99998V16.4166C3.625 16.8642 3.80698 17.2899 4.12525 17.6011C4.44294 17.9117 4.87032 18.0833 5.3125 18.0833H14.6875C15.1297 18.0833 15.5571 17.9117 15.8748 17.6011Z",fill:"currentColor",stroke:"currentColor",strokeWidth:"1.5"})})}),e.jsx("input",{type:o?"text":"password",id:t,disabled:a,placeholder:C,autoComplete:"off",...i(t),className:` h-full w-full border-none bg-transparent pl-[46px] text-sm font-medium outline-none focus:border-none focus:outline-none focus-visible:ring-0 ${d}`}),e.jsx("span",{onClick:()=>l(!o),className:"absolute right-3 cursor-pointer text-gray-400",children:o?e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M12.9833 10C12.9833 11.65 11.6499 12.9833 9.99993 12.9833C8.34993 12.9833 7.0166 11.65 7.0166 10C7.0166 8.35 8.34993 7.01666 9.99993 7.01666C11.6499 7.01666 12.9833 8.35 12.9833 10Z",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M9.99987 16.8916C12.9415 16.8916 15.6832 15.1583 17.5915 12.1583C18.3415 10.9833 18.3415 9.00831 17.5915 7.83331C15.6832 4.83331 12.9415 3.09998 9.99987 3.09998C7.0582 3.09998 4.31654 4.83331 2.4082 7.83331C1.6582 9.00831 1.6582 10.9833 2.4082 12.1583C4.31654 15.1583 7.0582 16.8916 9.99987 16.8916Z",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}):e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M12.1083 7.89166L7.8916 12.1083C7.34994 11.5667 7.0166 10.825 7.0166 10C7.0166 8.35 8.34993 7.01666 9.99993 7.01666C10.8249 7.01666 11.5666 7.35 12.1083 7.89166Z",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M14.8499 4.80834C13.3915 3.70834 11.7249 3.10834 9.99987 3.10834C7.0582 3.10834 4.31654 4.84167 2.4082 7.84167C1.6582 9.01667 1.6582 10.9917 2.4082 12.1667C3.06654 13.2 3.8332 14.0917 4.66654 14.8083",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.0166 16.275C7.9666 16.675 8.97493 16.8917 9.99993 16.8917C12.9416 16.8917 15.6833 15.1583 17.5916 12.1583C18.3416 10.9833 18.3416 9.00834 17.5916 7.83334C17.3166 7.4 17.0166 6.99167 16.7083 6.60834",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M12.9252 10.5833C12.7085 11.7583 11.7502 12.7166 10.5752 12.9333",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.89199 12.1083L1.66699 18.3333",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M18.3334 1.66669L12.1084 7.89169",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})})]})};export{x as default};
