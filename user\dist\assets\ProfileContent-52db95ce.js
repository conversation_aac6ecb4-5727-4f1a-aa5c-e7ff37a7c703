import{j as e}from"./@react-google-maps/api-ee55a349.js";import{M as m,A as n}from"./index-09a1718e.js";import{r as o,R as p}from"./vendor-b16525a8.js";import{u as c}from"./user-a875fff3.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./react-i18next-4a61273e.js";const l=new m,k=({})=>{const{state:i,dispatch:a}=o.useContext(n),[t,r]=p.useState(i.userDetails);return o.useEffect(()=>{(async()=>{const s=await l.callRawAPI("/v3/api/custom/chumpchange/provider/data",{},"GET");s.error||(r(s.data),a({type:"UPDATE_PROFILE",payload:{first_name:s.data.first_name,last_name:s.data.last_name,photo:s.data.photo,user_data:{...s.data}}}))})()},[]),e.jsxs("div",{className:"relative mx-auto mt-14 flex h-[108px] justify-center gap-4 sm:gap-7",children:[e.jsx("div",{className:"",children:e.jsx("img",{className:" h-20 w-20 rounded-[80px]",src:t!=null&&t.photo?t==null?void 0:t.photo:c})}),e.jsxs("div",{className:"flex flex-col items-start justify-start gap-1.5 ",children:[e.jsxs("div",{className:" relative font-['Poppins'] text-[28px] font-semibold leading-7 text-black ",children:[t!=null&&t.first_name?t==null?void 0:t.first_name:"N/A"," ",t==null?void 0:t.last_name]}),e.jsxs("div",{className:" font-['Poppins'] text-sm font-medium text-[#8080a3]",children:["Chiripero since"," ",t!=null&&t.create_at?new Date(t.create_at).getFullYear():"N/A"]}),e.jsx("div",{className:" text-center font-['Poppins'] text-sm font-medium text-[#8080a3]",children:t!=null&&t.operating_city?t==null?void 0:t.operating_city:""})]})]})};export{k as default};
