import{j as e}from"./@react-google-maps/api-ee55a349.js";import{R as _,r as a,u as F,d as G,L as y}from"./vendor-b16525a8.js";import{u as q}from"./react-hook-form-b6ed2679.js";import{o as J}from"./yup-3990215a.js";import{c as T,a as S}from"./yup-f828ae80.js";import{M as $,A as D,G as U,h as M,s as n}from"./index-09a1718e.js";import"./InteractiveButton-767677a2.js";import{A as K,a as z}from"./index-dd254604.js";import{A as Q,P as X}from"./index-4ee87ce5.js";import{a as Y}from"./index-d54cffea.js";import{M as ee}from"./index-243c4859.js";import{S as se}from"./index-5a645c18.js";import{t}from"./i18next-7389dd8c.js";import"./@hookform/resolvers-3e831b4a.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";import"./MoonLoader-49322f56.js";let P=new $;const Pe=()=>{const A=T({email:S().email().required(),password:S().required()}).required(),{dispatch:B}=_.useContext(D),{dispatch:l}=_.useContext(U),[c,h]=a.useState(!1),[I,E]=a.useState(!1),[x,d]=a.useState(!1),[H,u]=a.useState(!1);a.useState(!1);const Z=F(),O=new URLSearchParams(Z.search).get("redirect_uri"),m=G(),{register:f,handleSubmit:R,setError:g,formState:{errors:o}}=q({resolver:J(A)}),V=async i=>{var r,C,w,v,k,b;if(!c){n(l,t("auth_pages.login.check_agree"),5e3,"error");return}try{d(!0);const s=await P.callRawAPI("/v3/api/custom/chumpchange/user/login-with-email",{email:i.email,password:i.password,role:"user"},"POST");if(window.location.reload(),s.error){if(n(l,s.message,5e3,"error"),d(!1),s.validation){const L=Object.keys(s.validation);for(let p=0;p<L.length;p++){const N=L[p];g(N,{type:"manual",message:s.validation[N]})}}}else{if((r=s==null?void 0:s.data)!=null&&r.error&&((C=s==null?void 0:s.data)==null?void 0:C.verify)==0){m(`/user/sign-up-otp/${i.email}`);return}B({type:"LOGIN",payload:s==null?void 0:s.data}),M(),window.location.reload(),n(l,"Succesfully Logged In",4e3,"success"),m(O??"/user/dashboard")}}catch(s){d(!1),n(l,s,5e3,"error"),console.log("Error",s),g("email",{type:"manual",message:(v=(w=s==null?void 0:s.response)==null?void 0:w.data)!=null&&v.message?(b=(k=s==null?void 0:s.response)==null?void 0:k.data)==null?void 0:b.message:s==null?void 0:s.message})}},j=async i=>{try{if(!c){n(l,t("auth_pages.login.check_agree"),5e3,"error");return}u(!0);const r=await P.oauthLoginApi(i,"user");window.open(r,"_self")}catch(r){u(!1),console.log("error >> ",r)}};a.useEffect(()=>{localStorage.getItem("token")&&(M(),m("/user/dashboard"))},[]),a.useEffect(()=>{const i=localStorage.getItem("checked");i&&h(JSON.parse(i))},[]);const W=()=>{const i=!c;h(i),localStorage.setItem("checked",JSON.stringify(i))};return console.log("errors >>",o),e.jsxs(K,{children:[e.jsx(z,{title:t("auth_pages.login.title")}),H&&e.jsx(ee,{showCloseButton:!1,children:e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsxs("div",{className:"text-lg font-semibold",children:[t("loading.updating"),"..."]}),e.jsx("div",{className:"mt-12",children:e.jsx(se,{})})]})}),I?e.jsx("form",{onSubmit:R(V),children:e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx("div",{className:" flex h-[70px] flex-col items-center justify-center",children:((o==null?void 0:o.email)||(o==null?void 0:o.password))&&e.jsxs("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:[e.jsxs("p",{children:["errors : ",JSON.stringify(o)]}),e.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:t("auth_pages.login.error")})]})}),e.jsx(Q,{type:"email",name:"email",placeholder:t("auth_pages.login.p_phone"),errors:o,register:f,icon:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M14.1667 17.0833H5.83332C3.33332 17.0833 1.66666 15.8333 1.66666 12.9166V7.08329C1.66666 4.16663 3.33332 2.91663 5.83332 2.91663H14.1667C16.6667 2.91663 18.3333 4.16663 18.3333 7.08329V12.9166C18.3333 15.8333 16.6667 17.0833 14.1667 17.0833Z",stroke:"#8181A4",strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M14.1667 7.5L11.5583 9.58333C10.7 10.2667 9.29167 10.2667 8.43334 9.58333L5.83334 7.5",stroke:"#8181A4",strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"})]})}),e.jsx(X,{name:"password",placeholder:t("auth_pages.login.p_password"),errors:o,register:f}),e.jsx("div",{className:"flex justify-end",children:e.jsx(y,{to:"/user/forgot",className:"font-['Poppins'] text-sm font-medium leading-[18.20px] text-black",children:t("auth_pages.login.forget_password")})}),e.jsx("div",{className:" mt-5 ",children:e.jsx(Y,{loading:x,disabled:x,type:"submit",children:t("auth_pages.login.sign_in")})})]})}):e.jsxs("div",{className:" mt-[80px] flex flex-col gap-5 ",children:[e.jsxs("div",{onClick:()=>j("apple"),className:"relative flex h-14 w-full cursor-pointer items-center justify-between overflow-hidden rounded-3xl bg-[#8181a4]/20 p-5 ",children:[e.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M11.703 5.61564C11.7321 4.71122 11.9812 3.37888 12.8483 2.5036C13.6531 1.69111 14.6328 1.29513 15.243 1.1141C15.4786 1.04422 15.7046 1.22186 15.693 1.46731C15.6521 2.33311 15.4244 3.6245 14.8371 4.31632C14.2834 4.96848 13.3926 5.83329 12.0879 5.9788C11.8744 6.00262 11.6961 5.8304 11.703 5.61564Z",fill:"black"}),e.jsx("path",{d:"M13.937 22.1695C14.5635 22.4095 15.197 22.6522 15.7703 22.6522C16.9966 22.6522 20.5678 19.8226 20.6964 16.8838C20.703 16.7329 20.6128 16.5981 20.481 16.5243C19.5726 16.016 18.0893 14.7503 18.0376 12.909C17.9775 10.7632 19.0549 9.05879 19.8703 8.35376C20.0538 8.19509 20.1169 7.91531 19.953 7.73638C19.2111 6.92618 17.7186 5.81172 16.3568 5.83908C15.366 5.85898 14.4734 6.22803 13.7074 6.54472C13.1197 6.78768 12.6066 6.99982 12.181 6.99982C11.7778 6.99982 11.2938 6.78904 10.7383 6.54713C9.99015 6.22134 9.11236 5.83908 8.12768 5.83908C6.30374 5.83908 2.69996 7.35156 2.69995 13.1904C2.69995 16.2857 5.89531 22.7343 8.59166 22.6522C9.1739 22.6522 9.79613 22.4111 10.4136 22.1719C11.0216 21.9363 11.625 21.7025 12.181 21.7025C12.7178 21.7025 13.3241 21.9347 13.937 22.1695Z",fill:"black"})]}),e.jsx("div",{className:"font-['Poppins'] text-sm font-semibold text-black",children:t("auth_pages.login.apple")}),e.jsx("div",{className:""})]}),e.jsxs("div",{onClick:()=>j("google"),className:"relative flex h-14 w-full cursor-pointer items-center justify-between overflow-hidden rounded-3xl bg-[#8181a4]/20 p-5 ",children:[e.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M5.87569 14.295L5.1099 17.1538L2.31094 17.213C1.47446 15.6615 1 13.8865 1 12.0001C1 10.176 1.44361 8.4559 2.22994 6.94128H2.23055L4.7224 7.39813L5.81399 9.87504C5.58552 10.5411 5.461 11.2561 5.461 12.0001C5.46108 12.8076 5.60735 13.5812 5.87569 14.295Z",fill:"#FBBB00"}),e.jsx("path",{d:"M21.9798 9.94507C22.4602 9.94507 22.8777 10.2863 22.9312 10.7636C22.9767 11.1695 23.0001 11.5821 23.0001 12C23.0001 12.7876 22.9173 13.5558 22.7595 14.2968C22.224 16.8183 20.8248 19.0201 18.8865 20.5783L15.7473 20.4175L15.3031 17.6445C16.5893 16.8902 17.5944 15.7098 18.1238 14.2968H13.2418C12.6895 14.2968 12.2418 13.849 12.2418 13.2968V10.9451C12.2418 10.3928 12.6895 9.94507 13.2418 9.94507H18.2097H21.9798Z",fill:"#2036EB"}),e.jsx("path",{d:"M18.8862 20.5776L18.8868 20.5782C17.0017 22.0935 14.607 23.0001 12.0003 23.0001C7.81119 23.0001 4.16909 20.6586 2.31116 17.2129L5.87591 14.2949C6.80486 16.7741 9.19647 18.539 12.0003 18.539C13.2054 18.539 14.3345 18.2132 15.3033 17.6445L18.8862 20.5776Z",fill:"#41A563"}),e.jsx("path",{d:"M18.0476 2.81037C18.5725 3.15655 18.5694 3.90246 18.0829 4.30078L16.013 5.99536C15.6812 6.26701 15.2162 6.28973 14.8298 6.10378C13.9737 5.6918 13.0139 5.46104 12.0001 5.46104C9.13283 5.46104 6.69649 7.30686 5.81408 9.87499L2.23059 6.94124C4.06133 3.41155 7.74876 1 12.0001 1C14.2339 1 16.3123 1.66594 18.0476 2.81037Z",fill:"#F95050"})]}),e.jsx("div",{className:"font-['Poppins'] text-sm font-semibold text-black",children:t("auth_pages.login.google")}),e.jsx("div",{className:""})]}),e.jsxs("div",{onClick:()=>E(!0),className:"relative flex h-14 w-full cursor-pointer items-center justify-between overflow-hidden rounded-3xl bg-[#8181a4]/20 p-5 ",children:[e.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M17 20.5H7C4 20.5 2 19 2 15.5V8.5C2 5 4 3.5 7 3.5H17C20 3.5 22 5 22 8.5V15.5C22 19 20 20.5 17 20.5Z",stroke:"#8181A4",strokeWidth:"1.8",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M17 9L13.87 11.5C12.84 12.32 11.15 12.32 10.12 11.5L7 9",stroke:"#8181A4",strokeWidth:"1.8",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"})]}),e.jsx("div",{className:"font-['Poppins'] text-sm font-semibold text-black",children:"Email"}),e.jsx("div",{className:""})]})]}),e.jsxs("div",{className:" mt-8 flex h-28 w-full   flex-col items-center justify-center rounded-2xl border border-[#8181a4]/20",children:[e.jsx("div",{className:"flex h-6 w-6 cursor-pointer items-center justify-center rounded-md border border-[#56ccf2] bg-white",onClick:W,children:c&&e.jsx("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M2.33301 7L5.44046 10.5L11.6663 3.5",stroke:"#56CCF2",strokeWidth:"3",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsxs("div",{className:"text-center",children:[e.jsxs("span",{className:"font-['Poppins'] text-sm font-medium leading-[18.20px] text-[#8080a3]",children:[t("auth_pages.login.agree_title")," ",e.jsx("br",{})]}),e.jsx("span",{className:"font-['Poppins'] text-sm font-medium leading-[18.20px] text-black",children:t("auth_pages.login.terms_title")})]})]}),e.jsxs("div",{className:" mb-6 w-full ",children:[e.jsx("div",{className:"mb-4 mt-6 text-center font-['Poppins'] text-sm font-medium leading-[18.20px] text-[#8080a3] ",children:t("auth_pages.login.dont_have_account")}),e.jsx(y,{to:"/user/sign-up",className:"flex h-12 w-full items-center justify-center rounded-2xl border-2 border-[#8181a4]/20 font-['Poppins'] text-base font-medium text-black",children:t("auth_pages.login.sing_up")})]})]})};export{Pe as default};
