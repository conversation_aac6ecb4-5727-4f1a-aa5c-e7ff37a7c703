import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as a,b as N}from"./vendor-4f06b3f4.js";import{u as j}from"./react-hook-form-f3d72793.js";import{o as E}from"./yup-2324a46a.js";import{c as v,a as f}from"./yup-17027d7a.js";import{G as w,A as R,M as k,s as T,t as q}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{M as h}from"./MkdInput-ff3aa862.js";import{I}from"./InteractiveButton-8f7d74ee.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./MoonLoader-16bed42a.js";const te=({setSidebar:g})=>{const{dispatch:o}=a.useContext(w),x=v({name:f().required("Name is required"),amount:f().required("Amount is required")}).required(),{dispatch:b}=a.useContext(R),[m,r]=a.useState(!1),y=N(),{register:i,handleSubmit:A,setError:n,formState:{errors:l}}=j({resolver:E(x)}),S=async d=>{let u=new k;r(!0);try{u.setTable("recharge");const e=await u.callRestAPI({name:d.name,amount:d.amount},"POST");if(!e.error)T(o,"Added"),y("/admin/recharge"),g(!1),o({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(e.validation){const p=Object.keys(e.validation);for(let s=0;s<p.length;s++){const c=p[s];n(c,{type:"manual",message:e.validation[c]})}}r(!1)}catch(e){r(!1),console.log("Error",e),n("amount",{type:"manual",message:e.message}),q(b,e.message)}};return a.useEffect(()=>{o({type:"SETPATH",payload:{path:"recharge"}})},[]),t.jsxs("div",{className:" mx-auto rounded  p-5 shadow-md",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Add User Recharge"}),t.jsxs("form",{className:" w-full max-w-lg",onSubmit:A(S),children:[t.jsx(h,{type:"text",page:"add",name:"name",errors:l,label:"Name",placeholder:"Name",register:i,className:""}),t.jsx(h,{type:"number",page:"add",name:"amount",errors:l,label:"Amount",placeholder:"Amount",register:i,className:""}),t.jsx(I,{type:"submit",loading:m,disabled:m,className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",children:"Submit"})]})]})};export{te as default};
