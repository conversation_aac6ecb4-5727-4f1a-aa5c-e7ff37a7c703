import{j as e}from"./@react-google-maps/api-ee55a349.js";import{B as y,a as A}from"./index-d54cffea.js";import{T as P}from"./index-6b1be6a4.js";import{S as W}from"./index-5a645c18.js";import{M as d}from"./index-243c4859.js";import{P as B}from"./index-49471902.js";import{M as V,A as Z,G as D,s as p}from"./index-09a1718e.js";import{t as o}from"./i18next-7389dd8c.js";import{r as n,R as H,d as T,L as u}from"./vendor-b16525a8.js";import{u as _}from"./react-i18next-4a61273e.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const r=[{id:1,lang:"en",name:"English",icon:e.jsxs("svg",{width:"20",height:"21",viewBox:"0 0 20 21",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M9.99984 18.8334C14.6022 18.8334 18.3332 15.1025 18.3332 10.5001C18.3332 5.89771 14.6022 2.16675 9.99984 2.16675C5.39746 2.16675 1.6665 5.89771 1.6665 10.5001C1.6665 15.1025 5.39746 18.8334 9.99984 18.8334Z",stroke:"#8181A4",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M6.66667 3H7.5C5.875 7.86667 5.875 13.1333 7.5 18H6.66667",stroke:"#8181A4",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M12.5 3C14.125 7.86667 14.125 13.1333 12.5 18",stroke:"#8181A4",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M2.5 13.8333V13C7.36667 14.625 12.6333 14.625 17.5 13V13.8333",stroke:"#8181A4",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M2.5 8C7.36667 6.375 12.6333 6.375 17.5 8",stroke:"#8181A4",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),tran:"en"},{id:2,lang:"es",tran:"es",name:"Spanish",icon:e.jsxs("svg",{width:"20",height:"21",viewBox:"0 0 20 21",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M9.99984 18.8334C14.6022 18.8334 18.3332 15.1025 18.3332 10.5001C18.3332 5.89771 14.6022 2.16675 9.99984 2.16675C5.39746 2.16675 1.6665 5.89771 1.6665 10.5001C1.6665 15.1025 5.39746 18.8334 9.99984 18.8334Z",stroke:"#8181A4",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M6.66667 3H7.5C5.875 7.86667 5.875 13.1333 7.5 18H6.66667",stroke:"#8181A4",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M12.5 3C14.125 7.86667 14.125 13.1333 12.5 18",stroke:"#8181A4",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M2.5 13.8333V13C7.36667 14.625 12.6333 14.625 17.5 13V13.8333",stroke:"#8181A4",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M2.5 8C7.36667 6.375 12.6333 6.375 17.5 8",stroke:"#8181A4",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})}],E=[{to:"/user/edit-profile",icon:e.jsxs("svg",{width:"20",height:"21",viewBox:"0 0 20 21",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M11.0524 5.03249L4.21073 12.2742C3.9524 12.5492 3.7024 13.0908 3.6524 13.4658L3.34407 16.1658C3.23573 17.1408 3.93573 17.8075 4.9024 17.6408L7.58573 17.1825C7.96073 17.1158 8.48573 16.8408 8.74407 16.5575L15.5857 9.31582C16.7691 8.06582 17.3024 6.64082 15.4607 4.89916C13.6274 3.17416 12.2357 3.78249 11.0524 5.03249Z",stroke:"#56CCF2",strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M9.91113 6.24072C10.2695 8.54072 12.1361 10.2991 14.4528 10.5324",stroke:"#56CCF2",strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"})]}),text:"e_profile"},{to:"/user/forgot",icon:e.jsx("svg",{width:"20",height:"21",viewBox:"0 0 20 21",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M14.6875 7.74992V5.91658C14.6875 4.70101 14.1936 3.53522 13.3146 2.67568C12.4355 1.81614 11.2432 1.33325 10 1.33325C8.7568 1.33325 7.56451 1.81614 6.68544 2.67568C5.80636 3.53522 5.3125 4.70101 5.3125 5.91658V7.74992C4.56658 7.74992 3.85121 8.03965 3.32376 8.55537C2.79632 9.0711 2.5 9.77057 2.5 10.4999V16.9166C2.5 17.6459 2.79632 18.3454 3.32376 18.8611C3.85121 19.3769 4.56658 19.6666 5.3125 19.6666H14.6875C15.4334 19.6666 16.1488 19.3769 16.6762 18.8611C17.2037 18.3454 17.5 17.6459 17.5 16.9166V10.4999C17.5 9.77057 17.2037 9.0711 16.6762 8.55537C16.1488 8.03965 15.4334 7.74992 14.6875 7.74992ZM7.1875 5.91658C7.1875 5.18724 7.48382 4.48777 8.01126 3.97204C8.53871 3.45632 9.25408 3.16659 10 3.16659C10.7459 3.16659 11.4613 3.45632 11.9887 3.97204C12.5162 4.48777 12.8125 5.18724 12.8125 5.91658V7.74992H7.1875V5.91658ZM15.625 16.9166C15.625 17.1597 15.5262 17.3929 15.3504 17.5648C15.1746 17.7367 14.9361 17.8333 14.6875 17.8333H5.3125C5.06386 17.8333 4.8254 17.7367 4.64959 17.5648C4.47377 17.3929 4.375 17.1597 4.375 16.9166V10.4999C4.375 10.2568 4.47377 10.0236 4.64959 9.85174C4.8254 9.67983 5.06386 9.58325 5.3125 9.58325H14.6875C14.9361 9.58325 15.1746 9.67983 15.3504 9.85174C15.5262 10.0236 15.625 10.2568 15.625 10.4999V16.9166Z",fill:"#56CCF2"})}),text:"r_password"}];let F=new V;const ce=()=>{const{state:f,dispatch:c}=n.useContext(Z),{state:U,dispatch:x}=n.useContext(D),[t,G]=H.useState(f.userDetails),[j,l]=n.useState(!1),[g,a]=n.useState(!1),[C,i]=n.useState(!1),k=localStorage.getItem("lang")||r[0].lang,v=r.find(s=>s.lang===k)||r[0],[w,m]=n.useState(v),h=T(),{i18n:L}=_(),N=async()=>{try{i(!1),a(!0);const s=await F.callRawAPI("/v3/api/custom/chumpchange/user/delete-account",{},"DELETE");console.log(s),c({type:"LOGOUT"}),h("/user/login"),p(x,"Deleted successfully"),a(!1)}catch(s){a(!1),console.log(s),p(x,s.message,5e3,"error")}},b=()=>{c({type:"LOGOUT"}),h("/user/login")},M=s=>{m(s),console.log(s),localStorage.setItem("lang",s.lang),L.changeLanguage(s.lang)};return e.jsxs("div",{className:"p-5",children:[g&&e.jsx(d,{showCloseButton:!1,children:e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsxs("div",{className:"text-lg font-semibold",children:[o("loading.deleting"),"..."]}),e.jsx("div",{className:"mt-12",children:e.jsx(W,{})})]})}),e.jsxs("div",{className:"relative flex w-full items-center justify-between ",children:[e.jsx("div",{className:" ",children:e.jsx(y,{link:"/user/dashboard"})}),e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:o("user.profile.title")}),e.jsx("div",{className:" "})]}),e.jsx(B,{}),e.jsxs("div",{className:" mt-9 ",children:[e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:o("user.profile.number")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:t!=null&&t.phone?t==null?void 0:t.phone:"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:o("user.profile.email")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:e.jsx("a",{href:`mailto:${t==null?void 0:t.email}`,className:" text-[#4fa7f9] underline ",children:t!=null&&t.email?t==null?void 0:t.email:"N/A"})})]}),e.jsx("div",{className:"mt-[29px]",children:e.jsxs(u,{to:"/user/address",className:"relative flex h-12 w-full items-center justify-between gap-3 rounded-2xl bg-[#8181a4]/10 px-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("svg",{width:"20",height:"21",viewBox:"0 0 20 21",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M10 11.6917C11.436 11.6917 12.6 10.5276 12.6 9.0917C12.6 7.65576 11.436 6.4917 10 6.4917C8.56408 6.4917 7.40002 7.65576 7.40002 9.0917C7.40002 10.5276 8.56408 11.6917 10 11.6917Z",stroke:"#8181A4",strokeWidth:"2"}),e.jsx("path",{d:"M3.01663 7.57508C4.65829 0.358417 15.35 0.36675 16.9833 7.58342C17.9416 11.8167 15.3083 15.4001 13 17.6168C11.325 19.2334 8.67496 19.2334 6.99163 17.6168C4.69163 15.4001 2.05829 11.8084 3.01663 7.57508Z",stroke:"#8181A4",strokeWidth:"2"})]}),e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:o("user.profile.my_addresses")})]}),e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M14.6479 9.07273C15.1171 9.58485 15.1171 10.4152 14.6479 10.9273L9.5507 16.4909C9.08152 17.003 8.32082 17.003 7.85164 16.4909C7.38246 15.9788 7.38246 15.1485 7.85164 14.6364L12.9488 9.07273C13.418 8.56061 14.1787 8.56061 14.6479 9.07273Z",fill:"#8181A4"}),e.jsx("path",{d:"M7.85164 3.50909C8.32082 2.99697 9.08152 2.99697 9.5507 3.50909L14.6479 9.07273C15.1171 9.58485 15.1171 10.4152 14.6479 10.9273C14.1787 11.4394 13.418 11.4394 12.9488 10.9273L7.85164 5.36364C7.38246 4.85152 7.38246 4.02121 7.85164 3.50909Z",fill:"#8181A4"})]})]})}),e.jsx("div",{className:"mt-[21px]",children:e.jsx(P,{services:r,onServiceSelect:M,selectedService:w,setSelectedService:m,className:"!bg-[#8181a4]/10"})}),e.jsxs("div",{className:"mt-[21px] flex flex-col gap-2 ",children:[E.map((s,S)=>e.jsxs(u,{to:s.to,className:" flex h-12 w-full items-center gap-3 rounded-2xl border-2 border-transparent bg-[#50a8f9]/10 px-4 py-[14px] duration-200 active:bg-[#50a8f9] ",children:[e.jsx("div",{className:" h-5 w-5",children:s.icon}),e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:o(`user.profile.${s.text}`)})]},S)),e.jsxs("button",{onClick:()=>{i(!0)},className:" flex h-12 w-full items-center gap-3 rounded-2xl border-2 border-transparent bg-[#50a8f9]/10 px-4 py-[14px] duration-200 active:bg-[#50a8f9] ",children:[e.jsx("div",{className:" h-5 w-5",children:e.jsxs("svg",{width:"20",height:"21",viewBox:"0 0 20 21",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M10 18.8334C14.5833 18.8334 18.3333 15.0834 18.3333 10.5001C18.3333 5.91675 14.5833 2.16675 10 2.16675C5.41667 2.16675 1.66667 5.91675 1.66667 10.5001C1.66667 15.0834 5.41667 18.8334 10 18.8334Z",stroke:"#FD5D5D",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M10 7.16675V11.3334",stroke:"#FD5D5D",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M10 13.8333H10.0067",stroke:"#FD5D5D",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})}),e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#fd5d5d]",children:o("user.profile.delete_account")})]})]}),e.jsx("div",{className:" mt-[73px] ",children:e.jsx(A,{onClick:()=>l(!0),children:o("user.profile.log_out")})})]}),j&&e.jsx(d,{closeModal:()=>l(!1),children:e.jsxs("div",{className:" flex flex-col items-center justify-center pt-8 ",children:[e.jsx("div",{className:" text-center font-['Poppins'] text-2xl font-medium leading-[31.20px] text-black",dangerouslySetInnerHTML:{__html:o("user.profile.sure")}}),e.jsxs("div",{className:"px5 mt-[90px] grid grid-cols-2 ",children:[e.jsx("div",{className:" flex items-center justify-center ",children:e.jsx("button",{onClick:b,className:"text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:o("user.profile.log_out")})}),e.jsx("div",{className:"",onClick:()=>l(!1),children:e.jsx("button",{className:"relative h-12 w-[163px] rounded-2xl bg-[#8181a4]/20 text-center font-['Poppins'] text-sm font-medium text-[#8080a3]",children:o("buttons.cancel")})})]})]})}),C&&e.jsx(d,{closeModal:()=>i(!1),children:e.jsxs("div",{className:" flex flex-col items-center justify-center pt-8 ",children:[e.jsxs("div",{className:" text-center font-['Poppins'] text-2xl font-medium leading-[31.20px] text-black",children:["Are you sure you ",e.jsx("br",{})," want to delete ",e.jsx("br",{})," account?"]}),e.jsxs("div",{className:"px5 mt-[90px] grid grid-cols-2 ",children:[e.jsx("div",{className:" flex items-center justify-center ",children:e.jsx("button",{onClick:N,className:"text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:"Delete forever"})}),e.jsx("div",{className:"",onClick:()=>i(!1),children:e.jsx("button",{className:"relative h-12 w-[163px] rounded-2xl bg-[#8181a4]/20 text-center font-['Poppins'] text-sm font-medium text-[#8080a3]",children:"Cancel"})})]})]})})]})};export{ce as default};
