import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{R as l,r as j,h as B,d as E}from"./vendor-f36d475e.js";import"./index-5deedf4a.js";import{M as I,A as R,G as D,f as T,o as U,r as w,t as b,s as F}from"./index-cf5e6bc7.js";import{B as G,a as L}from"./index-895fa99b.js";import{U as O}from"./index-04e38e92.js";import{M as N}from"./index-bf8d79cc.js";import{S as V}from"./index-65bc3378.js";import{t as r}from"./i18next-7389dd8c.js";import{u as $}from"./react-i18next-1e3e6bc5.js";import{L as H}from"./@mantine/core-ee88fb98.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@emotion/serialize-460cad7f.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-47271980.js";const c=new I,Ne=()=>{var x,f,u,h;const[t,g]=l.useState({}),[v,o]=l.useState(!0),[y,n]=l.useState(!1),[P,_]=l.useState(!1),[k,m]=l.useState(!1),{state:K,dispatch:d}=j.useContext(R),{state:z,dispatch:q}=j.useContext(D),s=B(),p=E(),{i18n:S}=$(),C=async()=>{var a;try{o(!0),c.setTable("task");const i=await c.callRestAPI({id:Number(s==null?void 0:s.id),join:"user,location,service"},"GET");i.error||((a=i.model)!=null&&a.provider_id&&p(`/provider/view-job/${s==null?void 0:s.id}`),g(i.model),o(!1))}catch(i){o(!1),console.log("error",i),b(d,i.message)}},A=async()=>{try{o(!0),n(!0),(await c.callRawAPI("/v3/api/custom/chumpchange/provider/task/respond",{task_id:Number(s==null?void 0:s.id),provider_status:"accepted"},"POST")).error||p(`/provider/view-job/${s==null?void 0:s.id}`),F(q,"Update"),o(!1),n(!1)}catch(a){n(!1),o(!1),b(d,a.message)}};return l.useEffect(()=>{C()},[]),e.jsxs("div",{className:"p-5",children:[e.jsxs("div",{className:"relative flex w-full items-center justify-center ",children:[e.jsx("div",{className:" absolute left-0 top-0 ",children:e.jsx(G,{})}),e.jsxs("div",{className:"",children:[e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:r("provider.help_request.title")}),e.jsx("div",{className:"text-center font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:(x=t==null?void 0:t.service)==null?void 0:x.name})]})]}),v?e.jsx("div",{className:"flex h-full w-full items-center justify-center pt-10 ",children:e.jsx(V,{})}):e.jsxs(e.Fragment,{children:[e.jsx(O,{userData:t==null?void 0:t.user}),e.jsxs("div",{className:" mt-9 ",children:[e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:r("provider.help_request.help_with")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:t==null?void 0:t.title})]}),e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:r("provider.help_request.date_time")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:T(t==null?void 0:t.start_datetime,S.language)})]}),e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:r("provider.help_request.pay_offered")}),e.jsxs("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:["DOP ",t==null?void 0:t.offer]})]}),e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:r("provider.help_request.location")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:e.jsx("button",{onClick:()=>{U(Number(t==null?void 0:t.latitude),Number(t==null?void 0:t.longitude))},className:" text-[#4fa7f9] underline text-right ",children:t!=null&&t.address?w(t==null?void 0:t.address):"N/A"})})]}),e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:r("provider.help_request.con_me")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:(f=t==null?void 0:t.user)==null?void 0:f.phone})]}),e.jsxs("div",{className:"flex justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsxs("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:[r("provider.help_request.description"),":"]}),e.jsx("div",{className:"w-[165px] text-right font-['Poppins'] text-sm font-light leading-[16.80px] text-black",children:t==null?void 0:t.description})]})]}),((u=t==null?void 0:t.image)==null?void 0:u.split(",").length)>0&&e.jsxs("div",{className:"",children:[e.jsx("div",{className:"h-[27px] w-[154px] font-['Poppins'] text-lg font-medium text-black",children:r("provider.help_request.images")}),e.jsx("div",{className:"scrollbar-hide flex w-full overflow-x-auto",children:e.jsx("div",{className:" mt-5 flex gap-2",children:(h=t==null?void 0:t.image)==null?void 0:h.split(",").map((a,i)=>e.jsx("div",{className:"relative h-[127px] w-[120px]",children:e.jsx("img",{onClick:()=>{m(!0),_(a)},className:" h-full w-full rounded-[20px] object-cover  ",src:a})},i))})})]}),e.jsx("div",{className:"mt-10",children:!(t!=null&&t.provider_id)&&e.jsx(L,{onClick:()=>A(),children:r("provider.help_request.accept_request")})})]}),y&&e.jsx(N,{showCloseButton:!1,children:e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsxs("div",{className:"text-lg font-semibold",children:[r("provider.help_request.accepting"),"..."]}),e.jsx("div",{className:"mt-12",children:e.jsx(H,{})})]})}),k&&e.jsx(N,{closeModal:()=>m(!1),children:e.jsx("div",{className:" mt-10 flex h-max w-full flex-col items-center justify-center gap-8 pb-6 ",children:e.jsx("img",{className:" h-auto w-full max-w-[450px] object-cover ",src:P})})})]})};export{Ne as default};
