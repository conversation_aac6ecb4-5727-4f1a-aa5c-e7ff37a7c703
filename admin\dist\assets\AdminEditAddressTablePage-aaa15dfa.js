import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as u,b as F,r as s,h as M}from"./vendor-4f06b3f4.js";import{u as G}from"./react-hook-form-f3d72793.js";import{o as O}from"./yup-2324a46a.js";import{c as B,a as l}from"./yup-17027d7a.js";import{M as H,A as $,G as q,t as K,s as V}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as r}from"./MkdInput-ff3aa862.js";import{I as z}from"./InteractiveButton-8f7d74ee.js";import{S as J}from"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";let f=new H;const Re=c=>{const{dispatch:N}=u.useContext($),I=B({name:l(),user_id:l(),description:l(),city:l(),latitude:l(),longtitude:l(),state:l(),country:l(),is_default:l()}).required(),{dispatch:g}=u.useContext(q),[h,Q]=u.useState({}),[S,x]=u.useState(!1),[E,b]=u.useState(!1),v=F(),[W,D]=s.useState(""),[X,w]=s.useState(0),[Y,A]=s.useState(""),[Z,C]=s.useState(""),[ee,L]=s.useState(""),[te,_]=s.useState(""),[se,R]=s.useState(""),[ae,T]=s.useState(""),[oe,k]=s.useState(0),{register:a,handleSubmit:P,setError:j,setValue:o,formState:{errors:i}}=G({resolver:O(I)}),n=M();s.useEffect(function(){(async function(){try{b(!0),f.setTable("address");const e=await f.callRestAPI({id:c.activeId?c.activeId:Number(n==null?void 0:n.id)},"GET");e.error||(o("name",e.model.name),o("user_id",e.model.user_id),o("description",e.model.description),o("city",e.model.city),o("latitude",e.model.latitude),o("longtitude",e.model.longtitude),o("state",e.model.state),o("country",e.model.country),o("is_default",e.model.is_default),D(e.model.name),w(e.model.user_id),A(e.model.description),C(e.model.city),L(e.model.latitude),_(e.model.longtitude),R(e.model.state),T(e.model.country),k(e.model.is_default),b(!1))}catch(e){b(!1),console.log("error",e),K(N,e.message)}})()},[]);const U=async e=>{x(!0);try{f.setTable("address");for(let p in h){let m=new FormData;m.append("file",h[p].file);let y=await f.uploadImage(m);e[p]=y.url}const d=await f.callRestAPI({id:c.activeId?c.activeId:Number(n==null?void 0:n.id),name:e.name,user_id:e.user_id,description:e.description,city:e.city,latitude:e.latitude,longtitude:e.longtitude,state:e.state,country:e.country,is_default:e.is_default},"PUT");if(!d.error)V(g,"Updated"),v("/admin/address"),g({type:"REFRESH_DATA",payload:{refreshData:!0}}),c.setSidebar(!1);else if(d.validation){const p=Object.keys(d.validation);for(let m=0;m<p.length;m++){const y=p[m];j(y,{type:"manual",message:d.validation[y]})}}x(!1)}catch(d){x(!1),console.log("Error",d),j("name",{type:"manual",message:d.message})}};return u.useEffect(()=>{g({type:"SETPATH",payload:{path:"address"}})},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Address"}),E?t.jsx(J,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:P(U),children:[t.jsx(r,{type:"text",page:"edit",name:"name",errors:i,label:"Name",placeholder:"Name",register:a,className:""}),t.jsx(r,{type:"number",page:"edit",name:"user_id",errors:i,label:"User Id",placeholder:"User Id",register:a,className:""}),t.jsx(r,{type:"text",page:"edit",name:"description",errors:i,label:"Description",placeholder:"Description",register:a,className:""}),t.jsx(r,{type:"text",page:"edit",name:"city",errors:i,label:"City",placeholder:"City",register:a,className:""}),t.jsx(r,{type:"text",page:"edit",name:"latitude",errors:i,label:"Latitude",placeholder:"Latitude",register:a,className:""}),t.jsx(r,{type:"text",page:"edit",name:"longtitude",errors:i,label:"Longtitude",placeholder:"Longtitude",register:a,className:""}),t.jsx(r,{type:"text",page:"edit",name:"state",errors:i,label:"State",placeholder:"State",register:a,className:""}),t.jsx(r,{type:"text",page:"edit",name:"country",errors:i,label:"Country",placeholder:"Country",register:a,className:""}),t.jsx(r,{type:"number",page:"edit",name:"is_default",errors:i,label:"Is Default",placeholder:"Is Default",register:a,className:""}),t.jsx(z,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:S,disable:S,children:"Submit"})]})]})};export{Re as default};
