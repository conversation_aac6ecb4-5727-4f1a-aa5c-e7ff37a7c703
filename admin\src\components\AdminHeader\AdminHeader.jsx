import React from "react";
import { Link, NavLink } from "react-router-dom";
import { PiUsersThreeFill } from "react-icons/pi";
import MkdSDK from "Utils/MkdSDK";
import { MdDashboard } from "react-icons/md";
import { GlobalContext } from "Context/Global";
import { AuthContext, tokenExpireError } from "Context/Auth";
import icon from "../../assets/images/admin.png";
let sdk = new MkdSDK();

const NAV_ITEMS = [
  {
    to: "/admin/dashboard",
    text: "Dashboard",
    icon: (
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M18 20V10M12 20V4M6 20V14"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    value: "admin",
  },
  {
    to: "/admin/providers",
    text: "Providers",
    icon: (
      <svg
        width="23"
        height="22"
        viewBox="0 0 23 22"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M22.8725 9.0245L22.1225 7.7255L20 8.951V6.5H18.5V8.951L16.3775 7.7255L15.6275 9.0245L17.75 10.25L15.6275 11.4755L16.3775 12.7745L18.5 11.549V14H20V11.549L22.1225 12.7745L22.8725 11.4755L20.75 10.25L22.8725 9.0245ZM15.5 21.5H14V17.75C13.9988 16.7558 13.6033 15.8027 12.9003 15.0997C12.1973 14.3967 11.2442 14.0012 10.25 14H5.75C4.7558 14.0012 3.80267 14.3967 3.09966 15.0997C2.39666 15.8027 2.00119 16.7558 2 17.75V21.5H0.5V17.75C0.501588 16.3581 1.05522 15.0237 2.03944 14.0394C3.02367 13.0552 4.3581 12.5016 5.75 12.5H10.25C11.6419 12.5016 12.9763 13.0552 13.9606 14.0394C14.9448 15.0237 15.4984 16.3581 15.5 17.75V21.5ZM8 2C8.99456 2 9.94839 2.39509 10.6517 3.09835C11.3549 3.80161 11.75 4.75544 11.75 5.75C11.75 6.74456 11.3549 7.69839 10.6517 8.40165C9.94839 9.10491 8.99456 9.5 8 9.5C7.00544 9.5 6.05161 9.10491 5.34835 8.40165C4.64509 7.69839 4.25 6.74456 4.25 5.75C4.25 4.75544 4.64509 3.80161 5.34835 3.09835C6.05161 2.39509 7.00544 2 8 2ZM8 0.5C6.60761 0.5 5.27226 1.05312 4.28769 2.03769C3.30312 3.02226 2.75 4.35761 2.75 5.75C2.75 7.14239 3.30312 8.47774 4.28769 9.46231C5.27226 10.4469 6.60761 11 8 11C9.39239 11 10.7277 10.4469 11.7123 9.46231C12.6969 8.47774 13.25 7.14239 13.25 5.75C13.25 4.35761 12.6969 3.02226 11.7123 2.03769C10.7277 1.05312 9.39239 0.5 8 0.5Z"
          fill="currentColor"
        />
      </svg>
    ),
    value: "providers",
  },
  {
    to: "/admin/users",
    text: "App users",
    icon: (
      <svg
        width="23"
        height="22"
        viewBox="0 0 23 22"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M22.8725 9.0245L22.1225 7.7255L20 8.951V6.5H18.5V8.951L16.3775 7.7255L15.6275 9.0245L17.75 10.25L15.6275 11.4755L16.3775 12.7745L18.5 11.549V14H20V11.549L22.1225 12.7745L22.8725 11.4755L20.75 10.25L22.8725 9.0245ZM15.5 21.5H14V17.75C13.9988 16.7558 13.6033 15.8027 12.9003 15.0997C12.1973 14.3967 11.2442 14.0012 10.25 14H5.75C4.7558 14.0012 3.80267 14.3967 3.09966 15.0997C2.39666 15.8027 2.00119 16.7558 2 17.75V21.5H0.5V17.75C0.501588 16.3581 1.05522 15.0237 2.03944 14.0394C3.02367 13.0552 4.3581 12.5016 5.75 12.5H10.25C11.6419 12.5016 12.9763 13.0552 13.9606 14.0394C14.9448 15.0237 15.4984 16.3581 15.5 17.75V21.5ZM8 2C8.99456 2 9.94839 2.39509 10.6517 3.09835C11.3549 3.80161 11.75 4.75544 11.75 5.75C11.75 6.74456 11.3549 7.69839 10.6517 8.40165C9.94839 9.10491 8.99456 9.5 8 9.5C7.00544 9.5 6.05161 9.10491 5.34835 8.40165C4.64509 7.69839 4.25 6.74456 4.25 5.75C4.25 4.75544 4.64509 3.80161 5.34835 3.09835C6.05161 2.39509 7.00544 2 8 2ZM8 0.5C6.60761 0.5 5.27226 1.05312 4.28769 2.03769C3.30312 3.02226 2.75 4.35761 2.75 5.75C2.75 7.14239 3.30312 8.47774 4.28769 9.46231C5.27226 10.4469 6.60761 11 8 11C9.39239 11 10.7277 10.4469 11.7123 9.46231C12.6969 8.47774 13.25 7.14239 13.25 5.75C13.25 4.35761 12.6969 3.02226 11.7123 2.03769C10.7277 1.05312 9.39239 0.5 8 0.5Z"
          fill="currentColor"
        />
      </svg>
    ),
    value: "users",
  },
  {
    to: "/admin/govt",
    text: "Govt. users",
    icon: (
      <svg
        width="23"
        height="22"
        viewBox="0 0 23 22"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M22.8725 9.0245L22.1225 7.7255L20 8.951V6.5H18.5V8.951L16.3775 7.7255L15.6275 9.0245L17.75 10.25L15.6275 11.4755L16.3775 12.7745L18.5 11.549V14H20V11.549L22.1225 12.7745L22.8725 11.4755L20.75 10.25L22.8725 9.0245ZM15.5 21.5H14V17.75C13.9988 16.7558 13.6033 15.8027 12.9003 15.0997C12.1973 14.3967 11.2442 14.0012 10.25 14H5.75C4.7558 14.0012 3.80267 14.3967 3.09966 15.0997C2.39666 15.8027 2.00119 16.7558 2 17.75V21.5H0.5V17.75C0.501588 16.3581 1.05522 15.0237 2.03944 14.0394C3.02367 13.0552 4.3581 12.5016 5.75 12.5H10.25C11.6419 12.5016 12.9763 13.0552 13.9606 14.0394C14.9448 15.0237 15.4984 16.3581 15.5 17.75V21.5ZM8 2C8.99456 2 9.94839 2.39509 10.6517 3.09835C11.3549 3.80161 11.75 4.75544 11.75 5.75C11.75 6.74456 11.3549 7.69839 10.6517 8.40165C9.94839 9.10491 8.99456 9.5 8 9.5C7.00544 9.5 6.05161 9.10491 5.34835 8.40165C4.64509 7.69839 4.25 6.74456 4.25 5.75C4.25 4.75544 4.64509 3.80161 5.34835 3.09835C6.05161 2.39509 7.00544 2 8 2ZM8 0.5C6.60761 0.5 5.27226 1.05312 4.28769 2.03769C3.30312 3.02226 2.75 4.35761 2.75 5.75C2.75 7.14239 3.30312 8.47774 4.28769 9.46231C5.27226 10.4469 6.60761 11 8 11C9.39239 11 10.7277 10.4469 11.7123 9.46231C12.6969 8.47774 13.25 7.14239 13.25 5.75C13.25 4.35761 12.6969 3.02226 11.7123 2.03769C10.7277 1.05312 9.39239 0.5 8 0.5Z"
          fill="currentColor"
        />
      </svg>
    ),
    value: "user",
  },

  {
    to: "/admin/job_requests",
    text: "Job requests/Tasks",
    value: "task",
  },
  {
    to: "/admin/user_alerts",
    text: "Alert requests",
    value: "user_alerts",
  },
  {
    to: "/admin/service_alerts",
    text: "Service Alerts",
    // icon: <MdDashboard className="text-xl text-[#A8A8A8]" />,
    value: "service_alerts",
  },
  {
    to: "/admin/service_listing",
    text: "Listings Services",
    value: "servicelisting",
  },
  {
    to: "/admin/product_listing",
    text: "Listings Products",
    value: "productlisting",
  },
  {
    to: "/admin/product",
    text: "Listing Product Types",
    value: "product",
  },
  {
    to: "/admin/service",
    text: "Listings Services Types",
    value: "service",
  },
  {
    to: "/admin/provider_service",
    text: "Provider Services types",
    value: "provider_service",
  },
  {
    to: "/admin/provider_ambulant_product",
    text: "Provider Ambulant products",
    value: "provider_ambulant_product",
  },
  // {
  //   to: "/admin/provider_product",
  //   text: "Ambulant/Seller Products",
  //   value: "provider_product",
  // },
  {
    to: "/admin/location",
    text: "Nearby locations",
    value: "nearby_location",
  },
  {
    to: "/admin/location_type",
    text: "Nearby  Types",
    value: "location_type",
  },
  {
    to: "/admin/plan",
    text: "Subscription Plans",
    value: "plan",
  },
  {
    to: "/admin/active_subscriptions",
    text: "Active Subscriptions",
    value: "active_subscriptions",
  },
  {
    to: "/admin/activation_requests",
    text: "Activation Requests",
    value: "activation_requests",
  },
  {
    to: "/admin/issue",
    text: "User Reports",
    value: "issue",
  },
  {
    to: "/admin/recharge",
    text: "Listing recharge amounts",
    value: "recharge",
  },
  {
    to: "/admin/user_recharge",
    text: "Listing Fee balances",
    value: "user_recharge",
  },
  {
    to: "/admin/notification",
    text: "Notifications",
    value: "notification",
  },
  {
    to: "/admin/transaction",
    text: "Transactions",
    value: "transaction",
  },
  {
    to: "/admin/earnings",
    text: "Provider Earnings",
    value: "earnings",
  },
  {
    to: "/admin/location_select",
    text: "Locations",
    value: "location",
  },
  {
    to: "/admin/city",
    text: "User City",
    value: "city",
  },
  {
    to: "/admin/address",
    text: "User Addresses",
    value: "address",
  },
  {
    to: "/admin/review",
    text: "Ratings",
    value: "review",
  },
  {
    to: "/admin/contact",
    text: "Contacts",
    value: "contact",
  },
  {
    to: "/admin/analytics",
    text: "Analytics",
    value: "analytics",
  },

  // {
  //   to: "/admin/email",
  //   text: "  Emails",
  //   icon: <MdDashboard className="text-xl text-[#A8A8A8]" />,
  //   value: "email",
  // },

  // {
  //   to: "/admin/photo",
  //   text: "  Photos",
  //   icon: <MdDashboard className="text-xl text-[#A8A8A8]" />,
  //   value: "photo",
  // },

  // {
  //   to: "/admin/user",
  //   text: "  Users",
  //   icon: <MdDashboard className="text-xl text-[#A8A8A8]" />,
  //   value: "user",
  // },

  // {
  //   to: "/admin/notification",
  //   text: "  Notifications",
  //   icon: <MdDashboard className="text-xl text-[#A8A8A8]" />,
  //   value: "notification",
  // },

  // {
  //   to: "/admin/users",
  //   text: "  Users",
  //   icon: <MdDashboard className="text-xl text-[#A8A8A8]" />,
  //   value: "users",
  // },
  {
    to: "/admin/setting",
    text: "Setting",
    // icon: <MdDashboard className="text-xl text-[#A8A8A8]" />,
    value: "setting",
  },
  {
    to: "/admin/profile",
    text: "Profile",
    icon: <PiUsersThreeFill className="text-xl text-[currentColor]" />,
    value: "profile",
  },
];

export const AdminHeader = () => {
  const {
    state: { isOpen, path },
    dispatch: gobalDispatch,
  } = React.useContext(GlobalContext);
  const { state: authState, dispatch } = React.useContext(AuthContext);
  const [openDropdown, setOpenDropdown] = React.useState(false);
  const [isHovering, setIsHovering] = React.useState(false);

  console.log("path", path);

  // const handleMouseOver = () => {
  //   setIsHovering(true);
  // };

  // const handleMouseOut = () => {
  //   setIsHovering(false);
  // };
  let toggleOpen = (open) => {
    gobalDispatch({
      type: "OPEN_SIDEBAR",
      payload: { isOpen: open },
    });
  };

  React.useEffect(() => {
    async function fetchData() {
      try {
        const result = await sdk.getProfile();
        dispatch({
          type: "UPDATE_PROFILE",
          payload: result,
        });
      } catch (error) {
        console.log("Error", error);
        tokenExpireError(
          dispatch,
          error.response.data.message
            ? error.response.data.message
            : error.message
        );
      }
    }

    fetchData();
  }, []);
  // sidebar-holder
  return (
    <>
      <div
        className={`z-50 flex max-h-screen flex-1 flex-col overflow-y-auto border border-[#E0E0E0] bg-white py-4 text-[#A8A8A8] transition-all ${
          isOpen
            ? "fixed h-screen w-[15rem] min-w-[15rem] max-w-[15rem] md:relative"
            : "relative min-h-screen w-[4.2rem] min-w-[4.2rem] max-w-[4.2rem] bg-black text-white"
        } `}
      >
        <div
          className={`text-[#393939] ${
            isOpen ? "flex w-full" : "flex items-center justify-center"
          } `}
        >
          <div></div>
          {isOpen && (
            <div className="flex w-full items-center justify-center text-2xl font-bold ">
              <Link to="/">
                <h4 className="inline-flex h-[46px] w-[114px] items-center justify-between rounded-[100px] bg-[#42cbee]/20 pl-2 pr-2">
                  {/* Chump Change */}
                  <img src={icon} alt="" />
                  <div className=" font-['Poppins'] text-xs font-semibold leading-[18px] text-black">
                    CHIRIPERO
                  </div>
                </h4>
              </Link>
            </div>
          )}
        </div>

        <div className="mt-4 h-fit w-auto flex-1 ">
          <div className="sidebar-list w-auto">
            <ul className="flex flex-wrap px-2 text-sm">
              {NAV_ITEMS.map((item, i) => (
                <li className="block w-full list-none" key={i}>
                  <NavLink
                    to={item.to}
                    className={` font-['Poppins'] text-base font-normal leading-normal ${
                      path == item.value
                        ? "active-nav !bg-[#56ccf2] !text-[#fff] "
                        : " !text-[#8181A4] "
                    } `}
                  >
                    <div className="flex items-center gap-3">
                      {item.icon}
                      {isOpen && <span>{item.text}</span>}
                    </div>
                  </NavLink>
                </li>
              ))}
            </ul>
          </div>
        </div>
        <div className="flex justify-end">
          <div className="mr-3 cursor-pointer rounded-lg border border-[#E0E0E0] bg-white p-2 text-2xl text-gray-400">
            <span onClick={() => toggleOpen(!isOpen)}>
              <svg
                className={`transition-transform ${
                  !isOpen ? "rotate-180" : ""
                }`}
                xmlns="http:www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12ZM10.4142 11L11.7071 9.70711C12.0976 9.31658 12.0976 8.68342 11.7071 8.29289C11.3166 7.90237 10.6834 7.90237 10.2929 8.29289L7.82322 10.7626C7.13981 11.446 7.13981 12.554 7.82322 13.2374L10.2929 15.7071C10.6834 16.0976 11.3166 16.0976 11.7071 15.7071C12.0976 15.3166 12.0976 14.6834 11.7071 14.2929L10.4142 13H16C16.5523 13 17 12.5523 17 12C17 11.4477 16.5523 11 16 11H10.4142Z"
                  fill="#A8A8A8"
                />
              </svg>
            </span>
          </div>
        </div>
      </div>
    </>
  );
};

export default AdminHeader;
