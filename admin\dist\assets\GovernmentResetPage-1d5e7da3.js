import{j as s}from"./@react-google-maps/api-ac2f9d6f.js";import{R as A,r as B,b as D,L as I}from"./vendor-4f06b3f4.js";import{u as M}from"./react-hook-form-f3d72793.js";import{o as O}from"./yup-2324a46a.js";import{c as T,a as n,b as _}from"./yup-17027d7a.js";import{A as G,M as K,s as U,t as Y}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{I as z}from"./InteractiveButton-8f7d74ee.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./MoonLoader-16bed42a.js";const he=()=>{var m,c,p,u,x,g;const{dispatch:i}=A.useContext(G),[l,a]=B.useState(!1),E=window.location.search,R=new URLSearchParams(E).get("token"),F=T({code:n().required(),password:n().required(),confirmPassword:n().oneOf([_("password"),null],"Passwords must match")}).required(),L=D(),{register:o,handleSubmit:$,setError:d,formState:{errors:t}}=M({resolver:O(F)}),C=async h=>{var f,b,w,y,j,N,v,k;let q=new K;try{a(!0);const e=await q.reset(R,h.code,h.password);if(!e.error)U(i,"Password Reset"),setTimeout(()=>{L("/government/login")},2e3);else if(e.validation){const P=Object.keys(e.validation);for(let r=0;r<P.length;r++){const S=P[r];d(S,{type:"manual",message:e.validation[S]})}}a(!1)}catch(e){a(!1),console.log("Error",e),d("code",{type:"manual",message:(b=(f=e==null?void 0:e.response)==null?void 0:f.data)!=null&&b.message?(y=(w=e==null?void 0:e.response)==null?void 0:w.data)==null?void 0:y.message:e==null?void 0:e.message}),Y(i,(N=(j=e==null?void 0:e.response)==null?void 0:j.data)!=null&&N.message?(k=(v=e==null?void 0:e.response)==null?void 0:v.data)==null?void 0:k.message:e==null?void 0:e.message)}};return s.jsx(s.Fragment,{children:s.jsxs("div",{className:"w-full max-w-xs mx-auto",children:[s.jsxs("form",{onSubmit:$(C),className:"bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4 mt-8 ",children:[s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"code",children:"Code"}),s.jsx("input",{type:"text",placeholder:"Enter code sent to your email",...o("code"),className:`"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(m=t.code)!=null&&m.message?"border-red-500":""}`}),s.jsx("p",{className:"text-red-500 text-xs italic",children:(c=t.code)==null?void 0:c.message})]}),s.jsxs("div",{className:"mb-6",children:[s.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"password",children:"Password"}),s.jsx("input",{type:"password",placeholder:"******************",...o("password"),className:`shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline ${(p=t.password)!=null&&p.message?"border-red-500":""}`}),s.jsx("p",{className:"text-red-500 text-xs italic",children:(u=t.password)==null?void 0:u.message})]}),s.jsxs("div",{className:"mb-6",children:[s.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"confirmPassword",children:"Confirm Password"}),s.jsx("input",{type:"password",placeholder:"******************",...o("confirmPassword"),className:`shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline ${(x=t.confirmPassword)!=null&&x.message?"border-red-500":""}`}),s.jsx("p",{className:"text-red-500 text-xs italic",children:(g=t.confirmPassword)==null?void 0:g.message})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(z,{className:"bg-primaryBlue disabled:cursor-not-allowed text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",type:"submit",loading:l,disabled:l,children:"Reset Password"}),s.jsx(I,{className:"inline-block align-baseline font-bold text-sm text-primaryBlue",to:"/admin/login",children:"Login?"})]})]}),s.jsxs("p",{className:"text-center text-gray-500 text-xs",children:["© ",new Date().getFullYear()," manaknightdigital inc. All rights reserved."]})]})})};export{he as default};
