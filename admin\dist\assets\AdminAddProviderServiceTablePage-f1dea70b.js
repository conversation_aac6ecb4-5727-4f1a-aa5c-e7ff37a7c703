import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as a,b as N}from"./vendor-4f06b3f4.js";import{u as A}from"./react-hook-form-f3d72793.js";import{o as E}from"./yup-2324a46a.js";import{c as k,a as h}from"./yup-17027d7a.js";import{G as R,A as D,M as I,s as P,t as T}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as F}from"./MkdInput-ff3aa862.js";import{I as C}from"./InteractiveButton-8f7d74ee.js";import"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";const de=({setSidebar:b})=>{var g,x;const{dispatch:i}=a.useContext(R),v=k({name:h(),logo:h()}).required(),{dispatch:y}=a.useContext(D),[p,L]=a.useState({}),[c,l]=a.useState(!1),S=N(),{register:u,handleSubmit:j,setError:f,setValue:M,formState:{errors:m}}=A({resolver:E(v)});a.useState([]);const w=async n=>{let d=new I;l(!0);try{for(let s in p){let o=new FormData;o.append("file",p[s].file);let r=await d.uploadImage(o);n[s]=r.url}d.setTable("provider_service");const t=await d.callRestAPI({name:n.name,logo:n.logo},"POST");if(!t.error)P(i,"Added"),S("/admin/provider_service"),b(!1),i({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(t.validation){const s=Object.keys(t.validation);for(let o=0;o<s.length;o++){const r=s[o];f(r,{type:"manual",message:t.validation[r]})}}l(!1)}catch(t){l(!1),console.log("Error",t),f("name",{type:"manual",message:t.message}),T(y,t.message)}};return a.useEffect(()=>{i({type:"SETPATH",payload:{path:"provider_service"}})},[]),e.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Provider Service"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:j(w),children:[e.jsx(F,{type:"text",page:"add",name:"name",errors:m,label:"Name",placeholder:"Name",register:u,className:""}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"logo",children:"Logo"}),e.jsx("textarea",{placeholder:"Logo",...u("logo"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(g=m.logo)!=null&&g.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(x=m.logo)==null?void 0:x.message})]}),e.jsx(C,{type:"submit",loading:c,disabled:c,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{de as default};
