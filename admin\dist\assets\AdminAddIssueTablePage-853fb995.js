import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as r,b as I}from"./vendor-4f06b3f4.js";import{u as A}from"./react-hook-form-f3d72793.js";import{o as T}from"./yup-2324a46a.js";import{c as v,a as m}from"./yup-17027d7a.js";import{G as E,A as R,M as D,s as _,t as F}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as n}from"./MkdInput-ff3aa862.js";import{I as C}from"./InteractiveButton-8f7d74ee.js";import"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";const ne=({setSidebar:y})=>{var b,g;const{dispatch:p}=r.useContext(E),j=v({title:m(),reporter_id:m(),task_id:m(),description:m(),status:m()}).required(),{dispatch:S}=r.useContext(R),[f,M]=r.useState({}),[x,c]=r.useState(!1),w=I(),{register:i,handleSubmit:k,setError:h,setValue:O,formState:{errors:s}}=A({resolver:T(j)});r.useState([]);const N=async a=>{let u=new D;c(!0);try{for(let l in f){let o=new FormData;o.append("file",f[l].file);let d=await u.uploadImage(o);a[l]=d.url}u.setTable("issue");const t=await u.callRestAPI({title:a.title,reporter_id:a.reporter_id,task_id:a.task_id,description:a.description,status:a.status},"POST");if(!t.error)_(p,"Added"),w("/admin/issue"),y(!1),p({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(t.validation){const l=Object.keys(t.validation);for(let o=0;o<l.length;o++){const d=l[o];h(d,{type:"manual",message:t.validation[d]})}}c(!1)}catch(t){c(!1),console.log("Error",t),h("title",{type:"manual",message:t.message}),F(S,t.message)}};return r.useEffect(()=>{p({type:"SETPATH",payload:{path:"issue"}})},[]),e.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Issue"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:k(N),children:[e.jsx(n,{type:"text",page:"add",name:"title",errors:s,label:"Title",placeholder:"Title",register:i,className:""}),e.jsx(n,{type:"number",page:"add",name:"reporter_id",errors:s,label:"Reporter Id",placeholder:"Reporter Id",register:i,className:""}),e.jsx(n,{type:"number",page:"add",name:"task_id",errors:s,label:"Task Id",placeholder:"Task Id",register:i,className:""}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"description",children:"Description"}),e.jsx("textarea",{placeholder:"Description",...i("description"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(b=s.description)!=null&&b.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(g=s.description)==null?void 0:g.message})]}),e.jsx(n,{type:"text",page:"add",name:"status",errors:s,label:"Status",placeholder:"Status",register:i,className:""}),e.jsx(C,{type:"submit",loading:x,disabled:x,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{ne as default};
