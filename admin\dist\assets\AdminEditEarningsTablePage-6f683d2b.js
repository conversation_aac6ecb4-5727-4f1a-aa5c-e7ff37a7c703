import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as r,b as P,r as l,h as R}from"./vendor-4f06b3f4.js";import{u as D}from"./react-hook-form-f3d72793.js";import{o as C}from"./yup-2324a46a.js";import{c as F,a as h}from"./yup-17027d7a.js";import{M as L,A as M,G,t as O,s as B}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as v}from"./MkdInput-ff3aa862.js";import{I as H}from"./InteractiveButton-8f7d74ee.js";import{S as U}from"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";let d=new L;const ke=i=>{const{dispatch:I}=r.useContext(M),S=F({provider_id:h(),task_id:h(),amount:h()}).required(),{dispatch:u}=r.useContext(G),[x,$]=r.useState({}),[k,c]=r.useState(!1),[_,p]=r.useState(!1),E=P(),[q,j]=l.useState(0),[K,w]=l.useState(0),[V,A]=l.useState(0),{register:f,handleSubmit:T,setError:y,setValue:g,formState:{errors:b}}=D({resolver:C(S)}),o=R();l.useEffect(function(){(async function(){try{p(!0),d.setTable("earnings");const e=await d.callRestAPI({id:i.activeId?i.activeId:Number(o==null?void 0:o.id)},"GET");e.error||(g("provider_id",e.model.provider_id),g("task_id",e.model.task_id),g("amount",e.model.amount),j(e.model.provider_id),w(e.model.task_id),A(e.model.amount),p(!1))}catch(e){p(!1),console.log("error",e),O(I,e.message)}})()},[]);const N=async e=>{c(!0);try{d.setTable("earnings");for(let n in x){let s=new FormData;s.append("file",x[n].file);let m=await d.uploadImage(s);e[n]=m.url}const a=await d.callRestAPI({id:i.activeId?i.activeId:Number(o==null?void 0:o.id),provider_id:e.provider_id,task_id:e.task_id,amount:e.amount},"PUT");if(!a.error)B(u,"Updated"),E("/admin/earnings"),u({type:"REFRESH_DATA",payload:{refreshData:!0}}),i.setSidebar(!1);else if(a.validation){const n=Object.keys(a.validation);for(let s=0;s<n.length;s++){const m=n[s];y(m,{type:"manual",message:a.validation[m]})}}c(!1)}catch(a){c(!1),console.log("Error",a),y("provider_id",{type:"manual",message:a.message})}};return r.useEffect(()=>{u({type:"SETPATH",payload:{path:"earnings"}})},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Earnings"}),_?t.jsx(U,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:T(N),children:[t.jsx(v,{type:"number",page:"edit",name:"provider_id",errors:b,label:"Provider Id",placeholder:"Provider Id",register:f,className:""}),t.jsx(v,{type:"number",page:"edit",name:"task_id",errors:b,label:"Task Id",placeholder:"Task Id",register:f,className:""}),t.jsx(v,{type:"number",page:"edit",name:"amount",errors:b,label:"Amount",placeholder:"Amount",register:f,className:""}),t.jsx(H,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:k,disable:k,children:"Submit"})]})]})};export{ke as default};
