import{R as E,r as s,b as j}from"../vendor-f36d475e.js";import{e as K}from"../@googlemaps/markerclusterer-2f9a3a53.js";function M(){return M=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)({}).hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},M.apply(null,arguments)}function k(t,e){if(t==null)return{};var n={};for(var o in t)if({}.hasOwnProperty.call(t,o)){if(e.includes(o))continue;n[o]=t[o]}return n}function q(t,e){if(typeof t!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var o=n.call(t,e||"default");if(typeof o!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function W(t){var e=q(t,"string");return typeof e=="symbol"?e:e+""}const O={NOT_LOADED:"NOT_LOADED",LOADING:"LOADING",LOADED:"LOADED",FAILED:"FAILED",AUTH_FAILURE:"AUTH_FAILURE"},Y="https://maps.googleapis.com/maps/api/js";class P{static async load(e,n){var o;const i=e.libraries?e.libraries.split(","):[],r=this.serializeParams(e);this.listeners.push(n),(o=window.google)!=null&&(o=o.maps)!=null&&o.importLibrary?(this.serializedApiParams||(this.loadingStatus=O.LOADED),this.notifyLoadingStatusListeners()):(this.serializedApiParams=r,this.initImportLibrary(e)),this.serializedApiParams&&this.serializedApiParams!==r&&console.warn("[google-maps-api-loader] The maps API has already been loaded with different parameters and will not be loaded again. Refresh the page for new values to have effect.");const l=["maps",...i];await Promise.all(l.map(a=>google.maps.importLibrary(a)))}static serializeParams(e){return[e.v,e.key,e.language,e.region,e.authReferrerPolicy,e.solutionChannel].join("/")}static initImportLibrary(e){if(window.google||(window.google={}),window.google.maps||(window.google.maps={}),window.google.maps.importLibrary){console.error("[google-maps-api-loader-internal]: initImportLibrary must only be called once");return}let n=null;const o=()=>n||(n=new Promise((i,r)=>{var l;const a=document.createElement("script"),d=new URLSearchParams;for(const[f,c]of Object.entries(e)){const m=f.replace(/[A-Z]/g,h=>"_"+h[0].toLowerCase());d.set(m,String(c))}d.set("loading","async"),d.set("callback","__googleMapsCallback__"),a.async=!0,a.src=Y+"?"+d.toString(),a.nonce=((l=document.querySelector("script[nonce]"))==null?void 0:l.nonce)||"",a.onerror=()=>{this.loadingStatus=O.FAILED,this.notifyLoadingStatusListeners(),r(new Error("The Google Maps JavaScript API could not load."))},window.__googleMapsCallback__=()=>{this.loadingStatus=O.LOADED,this.notifyLoadingStatusListeners(),i()},window.gm_authFailure=()=>{this.loadingStatus=O.AUTH_FAILURE,this.notifyLoadingStatusListeners()},this.loadingStatus=O.LOADING,this.notifyLoadingStatusListeners(),document.head.append(a)}),n);google.maps.importLibrary=i=>o().then(()=>google.maps.importLibrary(i))}static notifyLoadingStatusListeners(){for(const e of this.listeners)e(this.loadingStatus)}}P.loadingStatus=O.NOT_LOADED;P.serializedApiParams=void 0;P.listeners=[];const Q=["onLoad","onError","apiKey","version","libraries"],X=["children"],ee="GMP_visgl_rgmlibrary_v1_default",D=E.createContext(null);function te(){const[t,e]=s.useState({});return{mapInstances:t,addMapInstance:(r,l="default")=>{e(a=>M({},a,{[l]:r}))},removeMapInstance:(r="default")=>{e(l=>k(l,[r].map(W)))},clearMapInstances:()=>{e({})}}}function ne(t){const{onLoad:e,onError:n,apiKey:o,version:i,libraries:r=[]}=t,l=k(t,Q),[a,d]=s.useState(P.loadingStatus),[f,c]=s.useReducer((u,p)=>u[p.name]?u:M({},u,{[p.name]:p.value}),{}),m=s.useMemo(()=>r==null?void 0:r.join(","),[r]),h=s.useMemo(()=>JSON.stringify(M({apiKey:o,version:i},l)),[o,i,l]),g=s.useCallback(async u=>{var p;if(f[u])return f[u];if(!((p=google)!=null&&(p=p.maps)!=null&&p.importLibrary))throw new Error("[api-provider-internal] importLibrary was called before google.maps.importLibrary was defined.");const L=await window.google.maps.importLibrary(u);return c({name:u,value:L}),L},[f]);return s.useEffect(()=>{(async()=>{try{const u=M({key:o},l);i&&(u.v=i),(m==null?void 0:m.length)>0&&(u.libraries=m),(u.channel===void 0||u.channel<0||u.channel>999)&&delete u.channel,u.solutionChannel===void 0?u.solutionChannel=ee:u.solutionChannel===""&&delete u.solutionChannel,await P.load(u,p=>d(p));for(const p of["core","maps",...r])await g(p);e&&e()}catch(u){n?n(u):console.error("<ApiProvider> failed to load the Google Maps JavaScript API",u)}})()},[o,m,h]),{status:a,loadedLibraries:f,importLibrary:g}}const Ne=t=>{const{children:e}=t,n=k(t,X),{mapInstances:o,addMapInstance:i,removeMapInstance:r,clearMapInstances:l}=te(),{status:a,loadedLibraries:d,importLibrary:f}=ne(n),c=s.useMemo(()=>({mapInstances:o,addMapInstance:i,removeMapInstance:r,clearMapInstances:l,status:a,loadedLibraries:d,importLibrary:f}),[o,i,r,l,a,d,f]);return E.createElement(D.Provider,{value:c},e)};function oe(t,e){for(const n of ie){const o=e[n],i=G[n];s.useEffect(()=>{if(!t||!o)return;const r=google.maps.event.addListener(t,i,l=>{o(re(i,t,l))});return()=>r.remove()},[t,i,o])}}function re(t,e,n){const o={type:t,map:e,detail:{},stoppable:!1,stop:()=>{}};if(ae.includes(t)){const r=o,l=e.getCenter(),a=e.getZoom(),d=e.getHeading()||0,f=e.getTilt()||0,c=e.getBounds();return(!l||!c||!Number.isFinite(a))&&console.warn("[createEvent] at least one of the values from the map returned undefined. This is not expected to happen. Please report an issue at https://github.com/visgl/react-google-maps/issues/new"),r.detail={center:(l==null?void 0:l.toJSON())||{lat:0,lng:0},zoom:a||0,heading:d,tilt:f,bounds:(c==null?void 0:c.toJSON())||{north:90,east:180,south:-90,west:-180}},r}else if(se.includes(t)){var i;if(!n)throw new Error("[createEvent] mouse events must provide a srcEvent");const r=o;return r.domEvent=n.domEvent,r.stoppable=!0,r.stop=()=>n.stop(),r.detail={latLng:((i=n.latLng)==null?void 0:i.toJSON())||null,placeId:n.placeId},r}return o}const G={onBoundsChanged:"bounds_changed",onCenterChanged:"center_changed",onClick:"click",onContextmenu:"contextmenu",onDblclick:"dblclick",onDrag:"drag",onDragend:"dragend",onDragstart:"dragstart",onHeadingChanged:"heading_changed",onIdle:"idle",onIsFractionalZoomEnabledChanged:"isfractionalzoomenabled_changed",onMapCapabilitiesChanged:"mapcapabilities_changed",onMapTypeIdChanged:"maptypeid_changed",onMousemove:"mousemove",onMouseout:"mouseout",onMouseover:"mouseover",onProjectionChanged:"projection_changed",onRenderingTypeChanged:"renderingtype_changed",onTilesLoaded:"tilesloaded",onTiltChanged:"tilt_changed",onZoomChanged:"zoom_changed",onCameraChanged:"bounds_changed"},ae=["bounds_changed","center_changed","heading_changed","tilt_changed","zoom_changed"],se=["click","contextmenu","dblclick","mousemove","mouseout","mouseover"],ie=Object.keys(G);function le(t,e){const n=s.useRef(void 0);(!n.current||!K(e,n.current))&&(n.current=e),s.useEffect(t,n.current)}const ue=new Set(["backgroundColor","clickableIcons","controlSize","disableDefaultUI","disableDoubleClickZoom","draggable","draggableCursor","draggingCursor","fullscreenControl","fullscreenControlOptions","gestureHandling","headingInteractionEnabled","isFractionalZoomEnabled","keyboardShortcuts","mapTypeControl","mapTypeControlOptions","mapTypeId","maxZoom","minZoom","noClear","panControl","panControlOptions","restriction","rotateControl","rotateControlOptions","scaleControl","scaleControlOptions","scrollwheel","streetView","streetViewControl","streetViewControlOptions","styles","tiltInteractionEnabled","zoomControl","zoomControlOptions"]);function ce(t,e){const n={},o=Object.keys(e);for(const i of o)ue.has(i)&&(n[i]=e[i]);le(()=>{t&&t.setOptions(n)},[n])}function U(){var t;return((t=s.useContext(D))==null?void 0:t.status)||O.NOT_LOADED}function de(t,e){const{viewport:n,viewState:o}=e,i=!!n;return s.useLayoutEffect(()=>{if(!t||!o)return;const{latitude:r,longitude:l,bearing:a,pitch:d,zoom:f}=o;t.moveCamera({center:{lat:r,lng:l},heading:a,tilt:d,zoom:f+1})},[t,o]),i}function fe(t){return!t||typeof t!="object"||!("lat"in t&&"lng"in t)?!1:Number.isFinite(t.lat)&&Number.isFinite(t.lng)}function Z(t){return fe(t)?t:t.toJSON()}function ge(t,e,n){const o=n.center?Z(n.center):null;let i=null,r=null;o&&Number.isFinite(o.lat)&&Number.isFinite(o.lng)&&(i=o.lat,r=o.lng);const l=Number.isFinite(n.zoom)?n.zoom:null,a=Number.isFinite(n.heading)?n.heading:null,d=Number.isFinite(n.tilt)?n.tilt:null;s.useLayoutEffect(()=>{if(!t)return;const f={};let c=!1;i!==null&&r!==null&&(e.current.center.lat!==i||e.current.center.lng!==r)&&(f.center={lat:i,lng:r},c=!0),l!==null&&e.current.zoom!==l&&(f.zoom=l,c=!0),a!==null&&e.current.heading!==a&&(f.heading=a,c=!0),d!==null&&e.current.tilt!==d&&(f.tilt=d,c=!0),c&&t.moveCamera(f)})}const me=()=>{const t={position:"absolute",top:0,left:0,bottom:0,right:0,zIndex:999,display:"flex",flexFlow:"column nowrap",textAlign:"center",justifyContent:"center",fontSize:".8rem",color:"rgba(0,0,0,0.6)",background:"#dddddd",padding:"1rem 1.5rem"};return E.createElement("div",{style:t},E.createElement("h2",null,"Error: AuthFailure"),E.createElement("p",null,"A problem with your API key prevents the map from rendering correctly. Please make sure the value of the ",E.createElement("code",null,"APIProvider.apiKey")," prop is correct. Check the error-message in the console for further details."))};function pe(){const[t,e]=s.useState(null),n=s.useCallback(o=>e(o),[e]);return[t,n]}function $(){return U()===O.LOADED}function he(){const[,t]=s.useReducer(e=>e+1,0);return t}function ve(t,e){const n=t.getCenter(),o=t.getZoom(),i=t.getHeading()||0,r=t.getTilt()||0,l=t.getBounds();(!n||!l||!Number.isFinite(o))&&console.warn("[useTrackedCameraState] at least one of the values from the map returned undefined. This is not expected to happen. Please report an issue at https://github.com/visgl/react-google-maps/issues/new"),Object.assign(e.current,{center:(n==null?void 0:n.toJSON())||{lat:0,lng:0},zoom:o||0,heading:i,tilt:r})}function Ee(t){const e=he(),n=s.useRef({center:{lat:0,lng:0},heading:0,tilt:0,zoom:0});return s.useEffect(()=>{if(!t)return;const o=google.maps.event.addListener(t,"bounds_changed",()=>{ve(t,n),e()});return()=>o.remove()},[t,e]),n}const Ce=["id","defaultBounds","defaultCenter","defaultZoom","defaultHeading","defaultTilt","reuseMaps","renderingType","colorScheme"],Le=["padding"];class R{static has(e){return this.entries[e]&&this.entries[e].length>0}static pop(e){return this.entries[e]&&this.entries[e].pop()||null}static push(e,n){this.entries[e]||(this.entries[e]=[]),this.entries[e].push(n)}}R.entries={};function be(t,e){const n=$(),[o,i]=s.useState(null),[r,l]=pe(),a=Ee(o),{id:d,defaultBounds:f,defaultCenter:c,defaultZoom:m,defaultHeading:h,defaultTilt:g,reuseMaps:u,renderingType:p,colorScheme:L}=t,v=k(t,Ce),T=t.zoom!==void 0||t.defaultZoom!==void 0,_=t.center!==void 0||t.defaultCenter!==void 0;!f&&(!T||!_)&&console.warn("<Map> component is missing configuration. You have to provide zoom and center (via the `zoom`/`defaultZoom` and `center`/`defaultCenter` props) or specify the region to show using `defaultBounds`. See https://visgl.github.io/react-google-maps/docs/api-reference/components/map#required"),!v.center&&c&&(v.center=c),!v.zoom&&Number.isFinite(m)&&(v.zoom=m),!v.heading&&Number.isFinite(h)&&(v.heading=h),!v.tilt&&Number.isFinite(g)&&(v.tilt=g);for(const b of Object.keys(v))v[b]===void 0&&delete v[b];const S=s.useRef();return s.useEffect(()=>{if(!r||!n)return;const{addMapInstance:b,removeMapInstance:y}=e,{mapId:I}=t,A=`${I||"default"}:${p||"default"}:${L||"LIGHT"}`;let w,C;if(u&&R.has(A)?(C=R.pop(A),w=C.getDiv(),r.appendChild(w),C.setOptions(v),setTimeout(()=>C.setCenter(C.getCenter()),0)):(w=document.createElement("div"),w.style.height="100%",r.appendChild(w),C=new google.maps.Map(w,M({},v,p?{renderingType:p}:{},L?{colorScheme:L}:{}))),i(C),b(C,d),f){const{padding:z}=f,F=k(f,Le);C.fitBounds(F,z)}else(!T||!_)&&C.fitBounds({east:180,west:-180,south:-90,north:90});if(S.current){const{mapId:z,cameraState:F}=S.current;z!==I&&C.setOptions(F)}return()=>{S.current={mapId:I,cameraState:a.current},w.remove(),u?R.push(A,C):google.maps.event.clearInstanceListeners(C),i(null),y(d)}},[r,n,d,t.mapId,t.renderingType,t.colorScheme]),[o,l,a]}const J=E.createContext(null),xe={DARK:"DARK",LIGHT:"LIGHT",FOLLOW_SYSTEM:"FOLLOW_SYSTEM"},Re={VECTOR:"VECTOR",RASTER:"RASTER",UNINITIALIZED:"UNINITIALIZED"},ye=t=>{const{children:e,id:n,className:o,style:i}=t,r=s.useContext(D),l=U();if(!r)throw new Error("<Map> can only be used inside an <ApiProvider> component.");const[a,d,f]=be(t,r);ge(a,f,t),oe(a,t),ce(a,t);const c=de(a,t),m=!!t.controlled;s.useEffect(()=>{if(a)return c&&a.setOptions({disableDefaultUI:!0}),(c||m)&&a.setOptions({gestureHandling:"none",keyboardShortcuts:!1}),()=>{a.setOptions({gestureHandling:t.gestureHandling,keyboardShortcuts:t.keyboardShortcuts})}},[a,c,m,t.gestureHandling,t.keyboardShortcuts]);const h=t.center?Z(t.center):null;let g=null,u=null;h&&Number.isFinite(h.lat)&&Number.isFinite(h.lng)&&(g=h.lat,u=h.lng);const p=s.useMemo(()=>{var T,_,S,b,y;return{center:{lat:(T=g)!=null?T:0,lng:(_=u)!=null?_:0},zoom:(S=t.zoom)!=null?S:0,heading:(b=t.heading)!=null?b:0,tilt:(y=t.tilt)!=null?y:0}},[g,u,t.zoom,t.heading,t.tilt]);s.useLayoutEffect(()=>{if(!a||!m)return;a.moveCamera(p);const T=a.addListener("bounds_changed",()=>{a.moveCamera(p)});return()=>T.remove()},[a,m,p]);const L=s.useMemo(()=>M({width:"100%",height:"100%",position:"relative",zIndex:c?-1:0},i),[i,c]),v=s.useMemo(()=>({map:a}),[a]);return l===O.AUTH_FAILURE?E.createElement("div",{style:M({position:"relative"},o?{}:L),className:o},E.createElement(me,null)):E.createElement("div",M({ref:d,"data-testid":"map",style:o?void 0:L,className:o},n?{id:n}:{}),a?E.createElement(J.Provider,{value:v},e):null)};ye.deckGLViewProps=!0;const H=new Set;function Me(...t){const e=JSON.stringify(t);H.has(e)||(H.add(e),console.error(...t))}const V=(t=null)=>{const e=s.useContext(D),{map:n}=s.useContext(J)||{};if(e===null)return Me("useMap(): failed to retrieve APIProviderContext. Make sure that the <APIProvider> component exists and that the component you are calling `useMap()` from is a sibling of the <APIProvider>."),null;const{mapInstances:o}=e;return t!==null?o[t]||null:n||o.default||null};function Te(t){const e=$(),n=s.useContext(D);return s.useEffect(()=>{!e||!n||n.importLibrary(t)},[e,n,t]),(n==null?void 0:n.loadedLibraries[t])||null}function N(t,e,n){s.useEffect(()=>{if(!t||!e||!n)return;const o=google.maps.event.addListener(t,e,n);return()=>o.remove()},[t,e,n])}function x(t,e,n){s.useEffect(()=>{t&&(t[e]=n)},[t,e,n])}function B(t,e,n){s.useEffect(()=>{if(!(!t||!e||!n))return t.addEventListener(e,n),()=>t.removeEventListener(e,n)},[t,e,n])}function Ie(t){return t.nodeType===Node.ELEMENT_NODE}const Oe=E.createContext(null),_e={TOP_LEFT:["0%","0%"],TOP_CENTER:["50%","0%"],TOP:["50%","0%"],TOP_RIGHT:["100%","0%"],LEFT_CENTER:["0%","50%"],LEFT_TOP:["0%","0%"],LEFT:["0%","50%"],LEFT_BOTTOM:["0%","100%"],RIGHT_TOP:["100%","0%"],RIGHT:["100%","50%"],RIGHT_CENTER:["100%","50%"],RIGHT_BOTTOM:["100%","100%"],BOTTOM_LEFT:["0%","100%"],BOTTOM_CENTER:["50%","100%"],BOTTOM:["50%","100%"],BOTTOM_RIGHT:["100%","100%"],CENTER:["50%","50%"]},Se=({children:t,styles:e,className:n,anchorPoint:o})=>{const[i,r]=o??_e.BOTTOM,l=`translate(50%, 100%) translate(-${i}, -${r})`;return E.createElement("div",{style:{transform:l}},E.createElement("div",{className:n,style:e},t))};function we(t){const[e,n]=s.useState(null),[o,i]=s.useState(null),r=V(),l=Te("marker"),{children:a,onClick:d,className:f,onMouseEnter:c,onMouseLeave:m,onDrag:h,onDragStart:g,onDragEnd:u,collisionBehavior:p,clickable:L,draggable:v,position:T,title:_,zIndex:S}=t,b=s.Children.count(a);return s.useEffect(()=>{if(!r||!l)return;const y=new l.AdvancedMarkerElement;y.map=r,n(y);let I=null;return b>0&&(I=document.createElement("div"),I.isCustomMarker=!0,y.content=I,i(I)),()=>{var A;y.map=null,(A=I)==null||A.remove(),n(null),i(null)}},[r,l,b]),s.useEffect(()=>{!e||!e.content||b>0||(e.content.className=f||"")},[e,f,b]),x(e,"position",T),x(e,"title",_??""),x(e,"zIndex",S),x(e,"collisionBehavior",p),s.useEffect(()=>{e&&(v!==void 0?e.gmpDraggable=v:h||g||u?e.gmpDraggable=!0:e.gmpDraggable=!1)},[e,v,h,u,g]),s.useEffect(()=>{if(!e)return;const y=L!==void 0||!!d||!!c||!!m;e.gmpClickable=y,y&&e!=null&&e.content&&Ie(e.content)&&(e.content.style.pointerEvents="none",e.content.firstElementChild&&(e.content.firstElementChild.style.pointerEvents="all"))},[e,L,d,c,m]),N(e,"click",d),N(e,"drag",h),N(e,"dragstart",g),N(e,"dragend",u),B(e==null?void 0:e.element,"mouseenter",c),B(e==null?void 0:e.element,"mouseleave",m),[e,o]}const ze=s.forwardRef((t,e)=>{const{children:n,style:o,className:i,anchorPoint:r}=t,[l,a]=we(t),d=s.useMemo(()=>l?{marker:l}:null,[l]);return s.useImperativeHandle(e,()=>l,[l]),a?E.createElement(Oe.Provider,{value:d},j.createPortal(E.createElement(Se,{anchorPoint:r,styles:o,className:i},n),a)):null}),Ae=["onClick","onDrag","onDragStart","onDragEnd","onMouseOver","onMouseOut"];function ke(t){const[e,n]=s.useState(null),o=V(),{onClick:i,onDrag:r,onDragStart:l,onDragEnd:a,onMouseOver:d,onMouseOut:f}=t,c=k(t,Ae),{position:m,draggable:h}=c;return s.useEffect(()=>{if(!o){o===void 0&&console.error("<Marker> has to be inside a Map component.");return}const g=new google.maps.Marker(c);return g.setMap(o),n(g),()=>{g.setMap(null),n(null)}},[o]),s.useEffect(()=>{if(!e)return;const g=e,u=google.maps.event;return i&&u.addListener(g,"click",i),r&&u.addListener(g,"drag",r),l&&u.addListener(g,"dragstart",l),a&&u.addListener(g,"dragend",a),d&&u.addListener(g,"mouseover",d),f&&u.addListener(g,"mouseout",f),e.setDraggable(!!h),()=>{u.clearInstanceListeners(g)}},[e,h,i,r,l,a,d,f]),s.useEffect(()=>{e&&c&&e.setOptions(c)},[e,c]),s.useEffect(()=>{h||!m||!e||e.setPosition(m)},[h,m,e]),e}const Fe=s.forwardRef((t,e)=>{const n=ke(t);return s.useImperativeHandle(e,()=>n,[n]),E.createElement(E.Fragment,null)});export{Ne as A,xe as C,ye as M,Re as R,Fe as a,ze as b,V as u};
