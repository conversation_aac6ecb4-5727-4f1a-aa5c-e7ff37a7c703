import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{r as m,R as u,b as W}from"./vendor-4f06b3f4.js";import{u as Y}from"./react-hook-form-f3d72793.js";import{o as z}from"./yup-2324a46a.js";import{c as J,a as r}from"./yup-17027d7a.js";import{A as Q,G as X,M as L,s as p,t as Z}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";const we=({setSidebar:i})=>{var v,j,w,N,k,_,S,C,P,A,E,F,T,$;const[M,R]=m.useState("one_time"),[U,I]=m.useState([]),[x,h]=m.useState(!1),D=J({product_id:r().required(),name:r().required(),amount:r().required(),type:r().required(),interval:r().when("type",{is:"recurring",then:t=>t.required(),otherwise:t=>t.notRequired()}),interval_count:r(),usage_type:r().when("type",{is:"recurring",then:t=>t.required(),otherwise:t=>t.notRequired()}),usage_limit:r(),trial_days:r()}).required(),{dispatch:g}=u.useContext(Q),{dispatch:n}=u.useContext(X),G=W(),{register:l,handleSubmit:y,setError:H,setValue:b,trigger:O,resetField:ee,getValues:te,formState:{errors:s}}=Y({resolver:z(D)}),V=[{key:0,value:"",display:"Nothing Selected"},{key:1,value:"one_time",display:"One Time"},{key:2,value:"recurring",display:"Recurring"}],B=[{key:0,value:"",display:"Nothing Selected"},{key:1,value:"licenced",display:"Upfront"},{key:2,value:"metered",display:"Metered"}],K=[{key:0,value:"",display:"Nothing Selected"},{key:1,value:"day",display:"Day"},{key:2,value:"week",display:"Week"},{key:3,value:"month",display:"Month"},{key:4,value:"year",display:"Year"},{key:5,value:"lifetime",display:"Lifetime"}],f=async t=>{let o=new L;console.log(t),h(!0);try{const a=await o.addStripePrice(t);if(!a.error)p(n,"Price Added"),G("/admin/prices");else if(a.validation){const q=Object.keys(a.validation);for(let d=0;d<q.length;d++){const c=q[d];console.log(c),H(c,{type:"manual",message:a.validation[c]})}}}catch(a){console.log("Error",a),p(n,a.message),Z(g,a.message)}h(!1)};return u.useEffect(()=>{n({type:"SETPATH",payload:{path:"prices"}}),(async()=>{let t=new L;const{list:o,error:a}=await t.getStripeProducts({limit:"all"});if(a){p(g,"Something went wrong while fetching products list");return}I(o)})()},[]),e.jsxs("div",{className:" mx-auto  rounded",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:["(/* ",e.jsx("svg",{onClick:()=>i(!1),xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218",stroke:"#A8A8A8","stroke-width":"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})," */)",e.jsx("span",{className:"text-lg font-semibold",children:"Add Price"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 shadow-sm hover:bg-[#f4f4f4]",onClick:()=>i(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center rounded-md bg-[#4F46E5] px-3 py-2 text-white shadow-sm",onClick:async()=>{await y(f)(),i(!1)},disabled:x,children:x?"Saving...":"Save"})]})]}),e.jsxs("form",{className:" w-full p-4 text-left",onSubmit:y(f),children:[e.jsxs("div",{className:"mb-4 ",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"product_id",children:"Product"}),e.jsxs("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...l("product_id"),children:[e.jsx("option",{value:"",children:"Nothing selected"},"prod_default"),U.map((t,o)=>e.jsx("option",{value:t.id,children:t.name},`prod_${t.id}`))]}),e.jsx("p",{className:"text-xs italic text-red-500",children:(v=s.product_id)==null?void 0:v.message})]}),e.jsxs("div",{className:"mb-4 ",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"name",children:"Name"}),e.jsx("input",{type:"text",placeholder:"Name",...l("name"),className:`"shadow focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 focus:outline-none ${(j=s.name)!=null&&j.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(w=s.name)==null?void 0:w.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"amount",children:"Amount"}),e.jsx("input",{type:"number",min:.1,step:"any",placeholder:"Amount",...l("amount"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(N=s.amount)!=null&&N.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(k=s.amount)==null?void 0:k.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"type",children:"Type"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...l("type"),onChange:t=>{const o=t.target.value;R(o),b("type",o),O("type")},children:V.map(t=>e.jsx("option",{value:t.value.toLowerCase(),children:t.display},`interval_${t.key}`))}),e.jsx("p",{className:"text-xs italic text-red-500",children:(_=s.type)==null?void 0:_.message})]}),M==="recurring"?e.jsxs("div",{className:"ml-6",children:[e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"interval",children:"Interval"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...l("interval"),placeholder:"Select",children:K.map(t=>e.jsx("option",{value:t.value.toLowerCase(),children:t.display},`interval_${t.key}`))}),e.jsx("p",{className:"text-xs italic text-red-500",children:(S=s.interval)==null?void 0:S.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"interval_count",children:"Interval Count"}),e.jsx("input",{type:"number",step:"1",placeholder:"Interval Count",...l("interval_count"),...b("interval_count",1),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(C=s.interval_count)!=null&&C.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(P=s.interval_count)==null?void 0:P.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"usage_type",children:"Usage Type"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...l("usage_type"),placeholder:"Select",children:B.map(t=>e.jsx("option",{value:t.value.toLowerCase(),children:t.display},`interval_${t.key}`))}),e.jsx("p",{className:"text-xs italic text-red-500",children:(A=s.usage_type)==null?void 0:A.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"trial_days",children:"Trial Days"}),e.jsx("input",{type:"number",step:"1",placeholder:"0",...l("trial_days"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(E=s.trial_days)!=null&&E.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(F=s.trial_days)==null?void 0:F.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"trial_days",children:"Usage Limit"}),e.jsx("input",{type:"number",step:"1",placeholder:"1000",...l("usage_limit"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(T=s.usage_limit)!=null&&T.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:($=s.usage_limit)==null?void 0:$.message})]})]}):""]})]})};export{we as default};
