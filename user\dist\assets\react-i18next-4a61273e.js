import{r as l}from"./vendor-b16525a8.js";const U=(...e)=>{console!=null&&console.warn&&(g(e[0])&&(e[0]=`react-i18next:: ${e[0]}`),console.warn(...e))},j={},b=(...e)=>{g(e[0])&&j[e[0]]||(g(e[0])&&(j[e[0]]=new Date),U(...e))},H=(e,n)=>()=>{if(e.isInitialized)n();else{const s=()=>{setTimeout(()=>{e.off("initialized",s)},0),n()};e.on("initialized",s)}},S=(e,n,s)=>{e.loadNamespaces(n,H(e,s))},k=(e,n,s,o)=>{if(g(s)&&(s=[s]),e.options.preload&&e.options.preload.indexOf(n)>-1)return S(e,s,o);s.forEach(p=>{e.options.ns.indexOf(p)<0&&e.options.ns.push(p)}),e.loadLanguages(n,H(e,o))},$=(e,n,s={})=>!n.languages||!n.languages.length?(b("i18n.languages were undefined or empty",n.languages),!0):n.hasLoadedNamespace(e,{lng:s.lng,precheck:(o,p)=>{var t;if(((t=s.bindI18n)==null?void 0:t.indexOf("languageChanging"))>-1&&o.services.backendConnector.backend&&o.isLanguageChangingTo&&!p(o.isLanguageChangingTo,e))return!1}}),g=e=>typeof e=="string",q=e=>typeof e=="object"&&e!==null,A=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,B={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},W=e=>B[e],G=e=>e.replace(A,W);let T={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:G};const J=(e={})=>{T={...T,...e}},K=()=>T;let M;const Y=e=>{M=e},Q=()=>M,ee={type:"3rdParty",init(e){J(e.options.react),Y(e)}},X=l.createContext();class Z{constructor(){this.usedNamespaces={}}addUsedNamespaces(n){n.forEach(s=>{this.usedNamespaces[s]||(this.usedNamespaces[s]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const _=(e,n)=>{const s=l.useRef();return l.useEffect(()=>{s.current=n?s.current:e},[e,n]),s.current},P=(e,n,s,o)=>e.getFixedT(n,s,o),D=(e,n,s,o)=>l.useCallback(P(e,n,s,o),[e,n,s,o]),te=(e,n={})=>{var z,F,L,O;const{i18n:s}=n,{i18n:o,defaultNS:p}=l.useContext(X)||{},t=s||o||Q();if(t&&!t.reportNamespaces&&(t.reportNamespaces=new Z),!t){b("You will need to pass in an i18next instance by using initReactI18next");const i=(c,u)=>g(u)?u:q(u)&&g(u.defaultValue)?u.defaultValue:Array.isArray(c)?c[c.length-1]:c,r=[i,{},!1];return r.t=i,r.i18n={},r.ready=!1,r}(z=t.options.react)!=null&&z.wait&&b("It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const m={...K(),...t.options.react,...n},{useSuspense:C,keyPrefix:w}=m;let a=e||p||((F=t.options)==null?void 0:F.defaultNS);a=g(a)?[a]:a||["translation"],(O=(L=t.reportNamespaces).addUsedNamespaces)==null||O.call(L,a);const d=(t.isInitialized||t.initializedStoreOnce)&&a.every(i=>$(i,t,m)),v=D(t,n.lng||null,m.nsMode==="fallback"?a:a[0],w),E=()=>v,x=()=>P(t,n.lng||null,m.nsMode==="fallback"?a:a[0],w),[I,N]=l.useState(E);let h=a.join();n.lng&&(h=`${n.lng}${h}`);const R=_(h),f=l.useRef(!0);l.useEffect(()=>{const{bindI18n:i,bindI18nStore:r}=m;f.current=!0,!d&&!C&&(n.lng?k(t,n.lng,a,()=>{f.current&&N(x)}):S(t,a,()=>{f.current&&N(x)})),d&&R&&R!==h&&f.current&&N(x);const c=()=>{f.current&&N(x)};return i&&(t==null||t.on(i,c)),r&&(t==null||t.store.on(r,c)),()=>{f.current=!1,t&&(i==null||i.split(" ").forEach(u=>t.off(u,c))),r&&t&&r.split(" ").forEach(u=>t.store.off(u,c))}},[t,h]),l.useEffect(()=>{f.current&&d&&N(E)},[t,w,d]);const y=[I,t,d];if(y.t=I,y.i18n=t,y.ready=d,d||!d&&!C)return y;throw new Promise(i=>{n.lng?k(t,n.lng,a,()=>i()):S(t,a,()=>i())})};export{ee as i,te as u};
