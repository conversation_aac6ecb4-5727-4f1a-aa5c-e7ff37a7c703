import{j as e}from"./@react-google-maps/api-ee55a349.js";import{R as a,i as b,d as f}from"./vendor-b16525a8.js";import{u as w}from"./react-hook-form-b6ed2679.js";import{o as j}from"./yup-3990215a.js";import{c as y,a as N}from"./yup-f828ae80.js";import{A as v,G as S,M as A,s as k}from"./index-09a1718e.js";import"./@hookform/resolvers-3e831b4a.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./react-i18next-4a61273e.js";const O=()=>{var i,m;const l=y({email:N().email().required()}).required();a.useContext(v);const{dispatch:n}=a.useContext(S),[c,s]=a.useState(!1),o=b();f();const{register:p,handleSubmit:u,setError:d,formState:{errors:r}}=w({resolver:j(l)}),x=async g=>{let h=new A;try{s(!0);const t=await h.magicLoginAttempt(g.email,o==null?void 0:o.role);t.error||(s(!1),console.log(t),k(n,"Please check your mail to complete login attempt"))}catch(t){s(!1),console.log("Error",t),d("email",{type:"manual",message:t.message})}};return e.jsxs("div",{className:"w-full max-w-xs mx-auto",children:[e.jsxs("form",{onSubmit:u(x),className:"bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4 mt-8 ",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"email",children:"Email"}),e.jsx("input",{type:"email",placeholder:"Email",...p("email"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(i=r.email)!=null&&i.message?"border-red-500":""}`}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(m=r.email)==null?void 0:m.message})]}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("button",{type:"submit",className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:[c?"Attempting Log In...":"Sign In"," "]})})]}),e.jsxs("p",{className:"text-center text-gray-500 text-xs",children:["© ",new Date().getFullYear()," manaknightdigital inc. All rights reserved."]})]})};export{O as default};
