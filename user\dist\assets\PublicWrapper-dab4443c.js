import{j as e}from"./@react-google-maps/api-ee55a349.js";import{r}from"./vendor-b16525a8.js";import{_ as l}from"./qr-scanner-cf010ec4.js";import"./index-32785bdc.js";const t=r.lazy(()=>l(()=>import("./PublicHeader-6a552df3.js"),["assets/PublicHeader-6a552df3.js","assets/@react-google-maps/api-ee55a349.js","assets/vendor-b16525a8.js","assets/index-09a1718e.js","assets/react-confirm-alert-c06b7fb4.js","assets/moment-a9aaa855.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-518241d3.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-20049f1e.js","assets/@fortawesome/react-fontawesome-88fe485e.js","assets/@fortawesome/fontawesome-svg-core-4fa3e289.js","assets/@fortawesome/free-solid-svg-icons-0a9c4907.js","assets/@fortawesome/free-regular-svg-icons-0a88e957.js","assets/@fortawesome/free-brands-svg-icons-fae0dcac.js","assets/i18next-7389dd8c.js","assets/react-i18next-4a61273e.js","assets/index-8ff8a2aa.css"])),a=({children:s})=>e.jsxs("div",{className:"flex w-full flex-col",children:[e.jsx(t,{}),e.jsx("div",{className:"min-h-screen grow",children:e.jsx(r.Suspense,{fallback:e.jsx("div",{className:"flex h-screen w-full items-center justify-center"}),children:s})})]}),n=r.memo(a);export{n as default};
