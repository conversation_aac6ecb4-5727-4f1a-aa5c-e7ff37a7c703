import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as d,b as T}from"./vendor-4f06b3f4.js";import{aa as L}from"./index-06b5b6dd.js";import{S as $}from"./index-250f6b3d.js";import{L as p}from"./index-6416aa2c.js";import k from"./MkdListTableRow-d0b8fd19.js";import z from"./MkdListTableHead-3ce11554.js";import{M as C}from"./index-f6c8bd1f.js";import"./react-confirm-alert-525c3702.js";import"./moment-a9aaa855.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";const H={primary:"#0ea5e9",signup:"signup",add:"add",edit:"edit",search:"search",custom:"custom",secondary:"#F594C9",lightInfo:"#29282980"},de=({table:h,tableTitle:_,onSort:I,loading:u,columns:x,actions:a,actionPostion:g,tableRole:Y,deleteItem:M,deleteLoading:N,actionId:j="id",showDeleteModal:S,currentTableData:n,setShowDeleteModal:w,handleTableCellChange:E,setSelectedItems:o})=>{const[y,b]=d.useState(null),[q,c]=d.useState(!1),[R,m]=d.useState(!1),[r,i]=d.useState([]);function A(e){var v;c(!0);const s=r;if((v=a==null?void 0:a.select)!=null&&v.multiple)if(s.includes(e)){const l=s.filter(f=>f!==e);i(()=>[...l]),o(l)}else{const l=[...s,e];i(()=>[...l]),o(l)}else if(s.includes(e)){const l=s.filter(f=>f!==e);i(()=>[...l]),o(l)}else{const l=[e];i(()=>[...l]),o(l)}console.log(e)}const F=()=>{if(m(e=>!e),R)i([]),o([]);else{const e=n.map(s=>s[j]);i(e),o(e)}};T();const O=async e=>{console.log("id >>",e),w(!0),b(e)};return d.useEffect(()=>{r.length<=0&&(c(!1),m(!1)),r.length===n.length&&(m(!0),c(!0)),r.length<n.length&&r.length>0&&m(!1)},[r,n]),t.jsxs(t.Fragment,{children:[t.jsx("div",{className:`${u?"":"overflow-x-auto"} border-b border-gray-200 shadow`,children:u?t.jsx("div",{className:"flex max-h-fit min-h-fit min-w-fit max-w-full items-center justify-center  py-5",children:t.jsx($,{size:50,color:H.primary})}):t.jsx(t.Fragment,{children:t.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[t.jsx("thead",{className:"bg-gray-50",children:t.jsx(p,{children:t.jsx(z,{onSort:I,columns:x,actions:a,actionPostion:g,areAllRowsSelected:R,handleSelectAll:F})})}),t.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:n.map((e,s)=>t.jsx(p,{children:t.jsx(k,{i:s,row:e,columns:x,actions:a,actionPostion:g,actionId:j,handleTableCellChange:E,handleSelectRow:A,selectedIds:r,setDeleteId:O},s)},s))})]})})}),t.jsx(p,{children:t.jsx(C,{open:S,actionHandler:()=>{M(y)},closeModalFunction:()=>{b(null),w(!1)},title:`Delete ${L(h)} `,message:`You are about to delete ${L(h)} ${y}, note that this action is irreversible`,acceptText:"DELETE",rejectText:"CANCEL",loading:N})})]})};export{de as default};
