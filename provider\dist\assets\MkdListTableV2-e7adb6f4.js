import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{r as He,R as n}from"./vendor-f36d475e.js";import{u as Me}from"./react-hook-form-ff037c98.js";import{o as Ve}from"./yup-afe5cf51.js";import{c as qe}from"./yup-2f6e2476.js";import{M as $e,A as _e,G as ze,g as te,t as H,i as Be}from"./index-cf5e6bc7.js";import{_ as Oe}from"./qr-scanner-cf010ec4.js";import{T as Ze}from"./index-6c24bd04.js";import{S as Ge}from"./index-bec80226.js";import{G as M}from"./react-icons-2eae9d1f.js";import{A as Ke}from"./AddButton-0499cde5.js";import{L as C}from"./index-5deedf4a.js";import Ue from"./MkdListTableHead-8c16ca40.js";import We from"./MkdListTableRow-2b89275e.js";import{a as Ye}from"./index-bf8d79cc.js";import"./index-905b6573.js";import"./@hookform/resolvers-eb417cd0.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./react-i18next-1e3e6bc5.js";const Je=He.lazy(()=>Oe(()=>import("./PaginationBar-beb3d005.js"),["assets/PaginationBar-beb3d005.js","assets/@react-google-maps/api-afbf18d5.js","assets/vendor-f36d475e.js"]));function Qe(o){return M({tag:"svg",attr:{viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M21 3H5a1 1 0 0 0-1 1v2.59c0 .523.213 1.037.583 1.407L10 13.414V21a1.001 1.001 0 0 0 1.447.895l4-2c.339-.17.553-.516.553-.895v-5.586l5.417-5.417c.37-.37.583-.884.583-1.407V4a1 1 0 0 0-1-1zm-6.707 9.293A.996.996 0 0 0 14 13v5.382l-2 1V13a.996.996 0 0 0-.293-.707L6 6.59V5h14.001l.002 1.583-5.71 5.71z"}}]})(o)}function Xe(o){return M({tag:"svg",attr:{viewBox:"0 0 1024 1024"},child:[{tag:"path",attr:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8Z"}},{tag:"path",attr:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8Z"}}]})(o)}function et(o){return M({tag:"svg",attr:{viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M4 8H20V21C20 21.5523 19.5523 22 19 22H5C4.44772 22 4 21.5523 4 21V8ZM6 10V20H18V10H6ZM9 12H11V18H9V12ZM13 12H15V18H13V12ZM7 5V3C7 2.44772 7.44772 2 8 2H16C16.5523 2 17 2.44772 17 3V5H22V7H2V5H7ZM9 4V5H15V4H9Z"}}]})(o)}const tt=({onClick:o,className:a})=>e.jsx(e.Fragment,{children:e.jsxs("button",{onClick:o,className:`relative flex h-[2.125rem] w-fit min-w-fit  items-center justify-center overflow-hidden rounded-md border border-primaryBlue bg-indigo-600 px-[.6125rem]  py-[.5625rem] font-['Inter'] text-sm font-medium leading-none text-white shadow-md shadow-indigo-600 ${a}`,children:[e.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),e.jsx("span",{children:"Export"})]})});let m=new $e;const Ft=({columns:o=[],actions:a={view:{show:!0,multiple:!0,action:null},edit:{show:!0,multiple:!0,action:null},delete:{show:!0,multiple:!0,action:null},select:{show:!0,multiple:!0,action:null},add:{show:!0,multiple:!0,action:null,showChildren:!0,children:"Add New"},export:{show:!0,multiple:!0,action:null}},actionPostion:E="ontable",actionId:V="id",table:h,tableRole:se,tableTitle:A="",hasFilter:re=!0,schemaFields:st=[],showPagination:rt=!0,refreshRef:q=null})=>{var Y,J,Q,X;const{dispatch:T}=n.useContext(_e),[j,y]=n.useState([]),[d,$]=n.useState(10),[le,ae]=n.useState(0),[D,ne]=n.useState(0),[ie,oe]=n.useState(!1),[de,ce]=n.useState(!1),[ue,b]=n.useState(!1),[me,k]=n.useState(!1),[_,z]=n.useState(!1),[B,O]=n.useState(!1),[p,P]=n.useState([]),[he,R]=n.useState([]),[v,x]=n.useState([]),[pe,fe]=n.useState("eq"),[Z,I]=n.useState(!0),L=n.useRef(null),xe=qe({}),[G,K]=n.useState(null),[U,N]=n.useState(!1),[f,g]=n.useState([]),{state:ge,dispatch:we}=n.useContext(ze);function je(t){var l;const s=f;if((l=a==null?void 0:a.select)!=null&&l.multiple)if(s.includes(t)){const r=s.filter(i=>i!==t);g(()=>[...r]),x(r)}else{const r=[...s,t];g(()=>[...r]),x(r)}else if(s.includes(t)){const r=s.filter(i=>i!==t);g(()=>[...r]),x(r)}else{const r=[t];g(()=>[...r]),x(r)}}const ve=()=>{if(N(t=>!t),U)g([]),x([]);else{const t=j.map(s=>s[V]);g(t),x(t)}},ye=async t=>{b(!0),K(t)};n.useEffect(()=>{f.length<=0&&N(!1),f.length===j.length&&N(!0),f.length<j.length&&f.length>0&&N(!1)},[f,j]);const{handleSubmit:be,reset:lt}=Me({resolver:Ve(xe)});function Ne(t){o[t].isSorted?o[t].isSortedDesc=!o[t].isSortedDesc:(o.map(s=>s.isSorted=!1),o.map(s=>s.isSortedDesc=!1),o[t].isSorted=!0),async function(){await u(0,d)}()}function Se(){u(D-1,d)}function Ce(){u(D+1,d)}const Ee=(t,s,l)=>{const r=s==="eq"&&isNaN(l)?`${l}`:l,i=`${t},${s},${r}`;R(c=>[...c.filter(w=>!w.includes(t)),i])},Ae=()=>{u(0,d,{},he)},Te=t=>{u(0,d,{},t)};async function u(t,s,l={},r=[]){I(!0);try{m.setTable(h);const i=await m.callRestAPI({payload:{...l},page:t,limit:s,filter:r},"PAGINATE");i&&I(!1);const{list:c,total:ee,limit:w,num_pages:S,page:F}=i;y(c),$(w),ae(S),ne(F),oe(F>1),ce(F+1<=S)}catch(i){I(!1),console.log("ERROR",i),H(T,i.message)}}async function De(t,s,l={},r=[]){m.setTable(h);const i=await m.callRestAPI({payload:{...l},page:t,limit:s,filter:r},"PAGINATE"),{list:c}=i;y(c),we({type:"REFRESH_DATA",payload:{refreshData:!1}})}const ke=async t=>{async function s(l){try{k(!0),m.setTable(h);const r=await m.callRestAPI({id:l},"DELETE");r!=null&&r.error||(y(i=>i.filter(c=>Number(c.id)!==Number(l))),k(!1),b(!1))}catch(r){throw k(!1),b(!1),H(T,r==null?void 0:r.message),new Error(r)}}typeof t=="object"?t.forEach(async l=>{await s(l)}):typeof t=="number"&&await s(t)},Pe=async t=>{try{m.setTable(h);const s=await m.exportCSV()}catch(s){throw new Error(s)}},Re=t=>{const s=o.filter(l=>l.accessor).map(l=>{const r=Be(t[l.accessor]);return r?`${l.accessor},cs,${r}`:null}).filter(Boolean);u(0,d,{},s)};async function Ie(t,s,l){try{m.setTable(h);const r=await m.callRestAPI({id:t,[s]:l},"PUT")}catch(r){console.log("ERROR",r),H(T,r.message)}}const Le=()=>{P([]),R([]),u(1,d)};async function Fe(t,s,l,r){let i;s=isNaN(Number.parseInt(s))?s:Number.parseInt(s);try{clearTimeout(i),i=setTimeout(async()=>{await Ie(t,r,s)},200),y(c=>c.map((w,S)=>S===l?{...w,[r]:s}:w))}catch(c){console.error(c)}}n.useEffect(()=>{var t;(t=a==null?void 0:a.select)!=null&&t.action&&a.select.action()},[v.length]),n.useEffect(()=>{const s=setTimeout(async()=>{await u(1,d)},700);return()=>{clearTimeout(s)}},[]),n.useEffect(()=>{De(1,d)},[ge.refreshData]);const W=t=>{L.current&&!L.current.contains(t.target)&&z(!1)};return n.useEffect(()=>(document.addEventListener("mousedown",W),()=>{document.removeEventListener("mousedown",W)}),[]),e.jsxs("div",{className:"px-8",children:[q&&e.jsx("button",{ref:q,onClick:()=>u(1,d),className:"hidden"}),e.jsxs("div",{className:`flex gap-3 ${A?"flex-col":"h-fit items-center"}`,children:[re?e.jsx("div",{className:"flex w-auto items-center justify-between ",children:e.jsx("form",{className:"relative rounded bg-white",onSubmit:be(Re),children:e.jsx("div",{className:"flex items-center gap-4 text-nowrap text-gray-700",children:e.jsxs("div",{className:"relative",ref:L,children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-1",onClick:()=>z(!_),children:[e.jsx(Qe,{}),e.jsx("span",{children:"Filters"}),p.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:p.length})]}),_&&e.jsx("div",{className:"top-fill filter-form-holder absolute left-0 z-10 mt-4 w-[500px] min-w-[90%] rounded-md border border-gray-200 bg-white shadow-lg",children:e.jsxs("div",{className:"p-4",children:[p==null?void 0:p.map((t,s)=>e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:" mb-3 w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",children:t}),e.jsxs("select",{className:"mb-3 w-[40%] rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:l=>{fe(l.target.value)},children:[e.jsx("option",{value:"eq",selected:!0,children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),e.jsx("input",{type:"text",placeholder:"Enter value...",className:" mb-3 w-1/3 rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:l=>Ee(t,pe,l.target.value),onKeyDown:l=>{l.key==="Enter"&&l.preventDefault()}}),e.jsx("div",{className:"mt-[-10px] w-1/12",children:e.jsx(et,{className:" cursor-pointer text-xl",onClick:()=>{P(l=>l.filter(r=>r!==t)),R(l=>{const r=l.filter(i=>!i.includes(t));return Te(r),r})}})})]},s)),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out ",onClick:()=>{O(!B)},children:[e.jsx(Xe,{}),"Add filter"]}),B&&e.jsx("div",{className:"absolute top-11 z-10 bg-white px-5 py-3 text-gray-600 shadow-md",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:o.slice(0,-1).map(t=>e.jsx("li",{className:`${p.includes(t.accessor)?"cursor-not-allowed text-gray-400":"cursor-pointer"}`,onClick:()=>{p.includes(t.accessor)||P(s=>[...s,t.accessor]),O(!1)},children:t.header},t.accessor))})}),p.length>0&&e.jsx("div",{onClick:Le,className:"inline-block cursor-pointer  rounded px-6  py-2.5 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out",children:"Clear all filter"})]}),e.jsx("button",{type:"button",onClick:Ae,className:"mt-4 inline-block cursor-pointer rounded bg-blue-500 px-6 py-2.5 font-medium leading-tight text-white transition duration-150 ease-in-out",children:"Apply Filters"})]})})]})})})}):null,e.jsxs("div",{className:"flex h-fit w-full justify-between text-center",children:[e.jsx("h4",{className:"text-2xl font-medium capitalize",children:A||""}),e.jsxs("div",{className:"flex h-full gap-2",children:[v!=null&&v.length&&E==="abovetable"?e.jsx(C,{children:e.jsx(Ze,{actions:a,selectedItems:v})}):null,((Y=a==null?void 0:a.export)==null?void 0:Y.show)&&e.jsx(tt,{showText:!1,onClick:Pe,className:"mx-1"}),((J=a==null?void 0:a.add)==null?void 0:J.show)&&e.jsx(Ke,{onClick:()=>{var t,s;(t=a==null?void 0:a.add)!=null&&t.action&&((s=a==null?void 0:a.add)==null||s.action())},showChildren:(Q=a==null?void 0:a.add)==null?void 0:Q.showChildren,children:(X=a==null?void 0:a.add)==null?void 0:X.children})]})]})]}),e.jsx("div",{className:"overflow-x-auto  rounded bg-white p-5 px-0",children:e.jsxs(e.Fragment,{children:[e.jsx("div",{className:`${Z?"":"overflow-x-auto"} border-b border-gray-200 shadow`,children:Z?e.jsx("div",{className:"flex max-h-fit min-h-fit min-w-fit max-w-full items-center justify-center  py-5",children:e.jsx(Ge,{})}):e.jsx(e.Fragment,{children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx(C,{children:e.jsx(Ue,{onSort:Ne,columns:o,actions:a,actionPostion:E,areAllRowsSelected:U,handleSelectAll:ve})})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:j.map((t,s)=>e.jsx(C,{children:e.jsx(We,{i:s,row:t,columns:o,actions:a,actionPostion:E,actionId:V,handleTableCellChange:Fe,handleSelectRow:je,selectedIds:f,setDeleteId:ye,table:h,tableRole:se},s)},s))})]})})}),e.jsx(C,{children:e.jsx(Ye,{open:ue,actionHandler:()=>{ke(G)},closeModalFunction:()=>{K(null),b(!1)},title:`Delete ${te(h)} `,message:`You are about to delete ${te(h)} ${G}, note that this action is irreversible`,acceptText:"DELETE",rejectText:"CANCEL",loading:me})})]})}),e.jsx(Je,{currentPage:D,pageCount:le,pageSize:d,canPreviousPage:ie,canNextPage:de,updatePageSize:t=>{$(t),u(1,t)},previousPage:Se,nextPage:Ce})]})};export{Ft as default};
