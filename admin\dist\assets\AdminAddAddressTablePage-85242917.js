import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as d,b as A}from"./vendor-4f06b3f4.js";import{u as w}from"./react-hook-form-f3d72793.js";import{o as D}from"./yup-2324a46a.js";import{c as I,a as o}from"./yup-17027d7a.js";import{G as E,A as v,M as C,s as R,t as k}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as r}from"./MkdInput-ff3aa862.js";import{I as L}from"./InteractiveButton-8f7d74ee.js";import"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";const de=({setSidebar:g})=>{const{dispatch:p}=d.useContext(E),b=I({name:o(),user_id:o(),description:o(),city:o(),latitude:o(),longtitude:o(),state:o(),country:o(),is_default:o()}).required(),{dispatch:h}=d.useContext(v),[f,T]=d.useState({}),[x,u]=d.useState(!1),j=A(),{register:a,handleSubmit:N,setError:y,setValue:F,formState:{errors:s}}=w({resolver:D(b)});d.useState([]);const S=async t=>{let c=new C;u(!0);try{for(let m in f){let i=new FormData;i.append("file",f[m].file);let n=await c.uploadImage(i);t[m]=n.url}c.setTable("address");const l=await c.callRestAPI({name:t.name,user_id:t.user_id,description:t.description,city:t.city,latitude:t.latitude,longtitude:t.longtitude,state:t.state,country:t.country,is_default:t.is_default},"POST");if(!l.error)R(p,"Added"),j("/admin/address"),g(!1),p({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(l.validation){const m=Object.keys(l.validation);for(let i=0;i<m.length;i++){const n=m[i];y(n,{type:"manual",message:l.validation[n]})}}u(!1)}catch(l){u(!1),console.log("Error",l),y("name",{type:"manual",message:l.message}),k(h,l.message)}};return d.useEffect(()=>{p({type:"SETPATH",payload:{path:"address"}})},[]),e.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Address"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:N(S),children:[e.jsx(r,{type:"text",page:"add",name:"name",errors:s,label:"Name",placeholder:"Name",register:a,className:""}),e.jsx(r,{type:"number",page:"add",name:"user_id",errors:s,label:"User Id",placeholder:"User Id",register:a,className:""}),e.jsx(r,{type:"text",page:"add",name:"description",errors:s,label:"Description",placeholder:"Description",register:a,className:""}),e.jsx(r,{type:"text",page:"add",name:"city",errors:s,label:"City",placeholder:"City",register:a,className:""}),e.jsx(r,{type:"text",page:"add",name:"latitude",errors:s,label:"Latitude",placeholder:"Latitude",register:a,className:""}),e.jsx(r,{type:"text",page:"add",name:"longtitude",errors:s,label:"Longtitude",placeholder:"Longtitude",register:a,className:""}),e.jsx(r,{type:"text",page:"add",name:"state",errors:s,label:"State",placeholder:"State",register:a,className:""}),e.jsx(r,{type:"text",page:"add",name:"country",errors:s,label:"Country",placeholder:"Country",register:a,className:""}),e.jsx(r,{type:"number",page:"add",name:"is_default",errors:s,label:"Is Default",placeholder:"Is Default",register:a,className:""}),e.jsx(L,{type:"submit",loading:x,disabled:x,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{de as default};
