import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as d,b as I}from"./vendor-4f06b3f4.js";import{u as w}from"./react-hook-form-f3d72793.js";import{o as k}from"./yup-2324a46a.js";import{c as A,a as r}from"./yup-17027d7a.js";import{G as D,A as T,M as E,s as P,t as R}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as o}from"./MkdInput-ff3aa862.js";import{I as O}from"./InteractiveButton-8f7d74ee.js";import"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";const pe=({setSidebar:v})=>{var b,h;const{dispatch:n}=d.useContext(D),y=A({user_id:r(),title:r(),service_id:r(),location_id:r(),offer:r(),image:r(),description:r(),provider_id:r(),status:r(),provider_status:r(),start_datetime:r()}).required(),{dispatch:j}=d.useContext(T),[f,F]=d.useState({}),[x,c]=d.useState(!1),S=I(),{register:s,handleSubmit:N,setError:g,setValue:C,formState:{errors:t}}=w({resolver:k(y)});d.useState([]);const _=async a=>{let u=new E;c(!0);try{for(let m in f){let l=new FormData;l.append("file",f[m].file);let p=await u.uploadImage(l);a[m]=p.url}u.setTable("task");const i=await u.callRestAPI({user_id:a.user_id,title:a.title,service_id:a.service_id,location_id:a.location_id,offer:a.offer,image:a.image,description:a.description,provider_id:a.provider_id,status:a.status,provider_status:a.provider_status,start_datetime:a.start_datetime},"POST");if(!i.error)P(n,"Added"),S("/admin/job_requests"),v(!1),n({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(i.validation){const m=Object.keys(i.validation);for(let l=0;l<m.length;l++){const p=m[l];g(p,{type:"manual",message:i.validation[p]})}}c(!1)}catch(i){c(!1),console.log("Error",i),g("user_id",{type:"manual",message:i.message}),R(j,i.message)}};return d.useEffect(()=>{n({type:"SETPATH",payload:{path:"task"}})},[]),e.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Task"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:N(_),children:[e.jsx(o,{type:"number",page:"add",name:"user_id",errors:t,label:"User Id",placeholder:"User Id",register:s,className:""}),e.jsx(o,{type:"text",page:"add",name:"title",errors:t,label:"Title",placeholder:"Title",register:s,className:""}),e.jsx(o,{type:"number",page:"add",name:"service_id",errors:t,label:"Service Id",placeholder:"Service Id",register:s,className:""}),e.jsx(o,{type:"number",page:"add",name:"location_id",errors:t,label:"Location Id",placeholder:"Location Id",register:s,className:""}),e.jsx(o,{type:"number",page:"add",name:"offer",errors:t,label:"Offer",placeholder:"Offer",register:s,className:""}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"image",children:"Image"}),e.jsx("textarea",{placeholder:"Image",...s("image"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(b=t.image)!=null&&b.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(h=t.image)==null?void 0:h.message})]}),e.jsx(o,{type:"text",page:"add",name:"description",errors:t,label:"Description",placeholder:"Description",register:s,className:""}),e.jsx(o,{type:"number",page:"add",name:"provider_id",errors:t,label:"Provider Id",placeholder:"Provider Id",register:s,className:""}),e.jsx(o,{type:"text",page:"add",name:"status",errors:t,label:"Status",placeholder:"Status",register:s,className:""}),e.jsx(o,{type:"text",page:"add",name:"provider_status",errors:t,label:"Provider Status",placeholder:"Provider Status",register:s,className:""}),e.jsx(o,{type:"text",page:"add",name:"start_datetime",errors:t,label:"Start Datetime",placeholder:"Start Datetime",register:s,className:""}),e.jsx(O,{type:"submit",loading:x,disabled:x,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{pe as default};
