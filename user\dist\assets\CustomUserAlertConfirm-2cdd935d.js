import{j as t}from"./@react-google-maps/api-ee55a349.js";import{r as m,d as A,u as R,R as h}from"./vendor-b16525a8.js";import{u as B}from"./user-a875fff3.js";import{B as D,a as E,R as L}from"./index-d54cffea.js";import{M,A as U,G as T,o as G,r as I,s as g}from"./index-09a1718e.js";import{P as q}from"./index-49471902.js";import{M as O}from"./index-243c4859.js";import{S as $}from"./index-5a645c18.js";import{t as s}from"./i18next-7389dd8c.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";let d=new M;const xt=()=>{const{state:K,dispatch:v}=m.useContext(U),{state:W,dispatch:j}=m.useContext(T),x=A(),u=R(),[N,o]=m.useState(!1),[b,_]=h.useState(null);console.log("location.search >> ",u.search);const e=new URLSearchParams(u.search),p=e.get("description"),f=e.get("far");e.get("location");const i=e.get("skill"),y=e.get("service_id"),w=e.get("service_alert_id"),P=e.get("location_id"),n=e.get("lat"),c=e.get("lng"),k=e.get("city"),C=e.get("state");e.get("country");const r=e.get("address"),S=async()=>{try{o(!0),await await d.callRawAPI("/v3/api/custom/chumpchange/user/address/create",{description:r,latitude:String(n),longtitude:String(c)},"POST");const a=await d.callRawAPI("/v3/api/custom/chumpchange/user/alert/create",{service_id:Number(y),service_alert_id:Number(w),location_id:Number(P),message:p,latitude:n,longtitude:c,state:C,city:k,address:r,status:"active"},"POST");a.error||(g(j,"Alert Created"),x(`/user/my-alert-view/${a.data}`)),o(!1)}catch(a){o(!1),console.log("Error",a),g(v,(a==null?void 0:a.message)||"Error Creating Listing",4e3,"error")}};return h.useEffect(()=>{(async()=>{const l=await d.callRawAPI("/v3/api/custom/chumpchange/provider/data",{},"GET");console.log("result >>",l),l.error||_(l.data)})()},[]),t.jsxs("div",{className:"p-5",children:[N&&t.jsx(O,{showCloseButton:!1,children:t.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[t.jsxs("div",{className:"text-lg font-semibold",children:[s("loading.creating"),"..."]}),t.jsx("div",{className:"mt-12",children:t.jsx($,{})})]})}),t.jsxs("div",{className:"relative flex w-full items-center justify-center ",children:[t.jsx("div",{className:" absolute left-0 top-0 ",children:t.jsx(D,{link:`/user/create-alert?${e.toString()}`})}),t.jsxs("div",{className:"",children:[t.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:s("user.con_alert.title")}),t.jsx("div",{className:"text-center font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:i||"N/A"})]})]}),t.jsx(q,{user:B,userData:b}),t.jsxs("div",{className:" mt-9 ",children:[t.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[t.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:s("user.con_alert.alert_if")}),t.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:i||"N/A"})]}),t.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[t.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:s("user.con_alert.is_within")}),t.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:f||"N/A"})]}),t.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[t.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:s("user.con_alert.location")}),t.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:t.jsx("button",{onClick:()=>{G(Number(n),Number(c))},className:" text-right text-[#4fa7f9] underline ",children:r?I(r):""})})]}),t.jsxs("div",{className:"flex justify-between border-t border-[#8181a4]/20 py-3 ",children:[t.jsxs("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:[s("user.con_alert.description"),":"]}),t.jsx("div",{className:"w-[165px] text-right font-['Poppins'] text-sm font-light leading-[16.80px] text-black",children:p})]})]}),t.jsx("div",{className:"my-5 font-['Poppins'] text-xs font-medium tracking-tight text-black",children:s("user.con_alert.info")}),t.jsxs("div",{className:"mt-10 flex flex-col gap-3",children:[t.jsx(E,{onClick:S,className:" opacity-[0.85] ",children:s("user.con_alert.create_rquest")}),t.jsx(L,{onClick:()=>x("/user/home"),className:" uppercase opacity-[0.85] ",children:s("buttons.cancel")})]})]})};export{xt as default};
