import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{r,R as o}from"./vendor-4f06b3f4.js";import{_ as s}from"./qr-scanner-cf010ec4.js";const i=r.lazy(()=>s(()=>import("./Loader-2ebacb80.js"),["assets/Loader-2ebacb80.js","assets/@react-google-maps/api-ac2f9d6f.js","assets/vendor-4f06b3f4.js","assets/qr-scanner-cf010ec4.js"]));r.lazy(()=>s(()=>import("./Spinner-c2fb6dd6.js"),["assets/Spinner-c2fb6dd6.js","assets/@react-google-maps/api-ac2f9d6f.js","assets/vendor-4f06b3f4.js","assets/react-loader-spinner-5cc4e6ba.js"]));const u=()=>{const[t,a]=o.useState(!0);return console.log(t),o.useEffect(()=>{setTimeout(()=>{a(!1)},5e3)},[]),e.jsx(e.Fragment,{children:t?e.jsx(i,{}):e.jsx("div",{className:"w-full flex justify-center items-center text-7xl h-screen text-gray-700 ",children:"Not Found"})})};export{u as default};
