import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import"./vendor-4f06b3f4.js";const g=({onSort:s,columns:i,actions:r,actionPostion:S,areAllRowsSelected:f,handleSelectAll:j})=>t.jsx(t.Fragment,{children:t.jsx("tr",{children:i.map((e,d)=>{var p,x,l,h;if((e==null?void 0:e.accessor)===""){if([(p=r==null?void 0:r.select)==null?void 0:p.show,(x=r==null?void 0:r.view)==null?void 0:x.show,(l=r==null?void 0:r.edit)==null?void 0:l.show,(h=r==null?void 0:r.delete)==null?void 0:h.show].includes(!0))return t.jsxs("th",{scope:"col",className:`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 ${e.isSorted?"cursor-pointer":""} `,onClick:e.isSorted?()=>s(d):void 0,children:[S==="ontable"&&e.header,t.jsx("span",{children:e.isSorted?e.isSortedDesc?" ▼":" ▲":""})]},d)}else return t.jsxs("th",{scope:"col",className:`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 ${e.isSorted?"cursor-pointer":""} `,onClick:e.isSorted?()=>s(d):void 0,children:[e.header,t.jsx("span",{children:e.isSorted?e.isSortedDesc?" ▼":" ▲":""})]},d);return null})})});export{g as default};
