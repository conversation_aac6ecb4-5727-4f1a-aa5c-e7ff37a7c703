import{j as e}from"./@react-google-maps/api-ee55a349.js";import{i as b,r}from"./vendor-b16525a8.js";import"./index-9ef1c042.js";import{M as g,A as w,G as v,s as k,t as y}from"./index-09a1718e.js";import{B as N}from"./index-d54cffea.js";import{S}from"./index-e25e12e5.js";import{S as C}from"./index-5a645c18.js";import{t as a}from"./i18next-7389dd8c.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";const L=new g,Q=()=>{b();const{state:T,dispatch:d}=r.useContext(w),{state:P,dispatch:m}=r.useContext(v),[o,x]=r.useState([]),[u,i]=r.useState(!0),[n,h]=r.useState(""),p=async s=>{i(!0);try{const t=await L.callRawAPI(`/v3/api/custom/chumpchange/user/nearby-locations/search?searchTerm=${s}`,{},"GET");t.error?k(m,t.message,4e3,"error"):x(t.data)}catch(t){console.log("error >> ",t),y(d,t.message)}finally{i(!1)}},f=(s,t)=>{let l;return(...j)=>{l&&clearTimeout(l),l=setTimeout(()=>{s(...j)},t)}},c=r.useCallback(f(s=>{p(s)},1e3),[]);return r.useEffect(()=>{c(n)},[n,c]),e.jsxs("div",{className:"mx-auto flex min-h-screen max-w-full flex-col justify-between pb-[0]",children:[e.jsxs("div",{className:"px-5 pt-5",children:[e.jsxs("div",{className:"relative flex w-full items-center justify-center ",children:[e.jsx("div",{className:" absolute left-0 top-0 ",children:e.jsx(N,{link:"/user/dashboard"})}),e.jsxs("div",{className:"",children:[e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:a("user.nearby.title")}),e.jsx("div",{className:"text-center font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:a("user.nearby.sub_title")})]})]}),e.jsxs("div",{className:"relative mt-[47px] h-12 w-full rounded-2xl bg-[#8181a4]/20",children:[e.jsx("div",{className:" absolute left-[14px] top-1/2 h-5 w-5 -translate-y-1/2 ",children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M9.58341 17.5C13.9557 17.5 17.5001 13.9556 17.5001 9.58335C17.5001 5.2111 13.9557 1.66669 9.58341 1.66669C5.21116 1.66669 1.66675 5.2111 1.66675 9.58335C1.66675 13.9556 5.21116 17.5 9.58341 17.5Z",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M18.3333 18.3333L15.8333 15.8333",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),e.jsx("input",{placeholder:a("user.nearby.place"),className:"absolute left-0 top-0 h-full w-full rounded-2xl border-none bg-transparent pl-[50px] font-['Poppins'] text-sm font-medium text-black outline-none focus:border-none focus:ring-0",onChange:s=>h(s.target.value),value:n})]}),o.length>0?e.jsxs("div",{className:"mt-5 font-['Poppins'] text-lg font-medium text-black ",children:[a("user.nearby.showing")," ",o.length," ",a("user.nearby.results"),".."]}):""]}),u?e.jsx("div",{className:"flex h-full w-full flex-1 items-center justify-center ",children:e.jsx(C,{width:"80"})}):e.jsx(e.Fragment,{children:o.length===0?e.jsxs("div",{className:" flex flex-1 flex-col items-center justify-center gap-3 ",children:[e.jsx("div",{className:"relative flex h-20 w-20 items-center justify-center rounded-[80px] bg-[#ff0000]/5",children:e.jsxs("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M15.3334 28C22.329 28 28.0001 22.329 28.0001 15.3334C28.0001 8.33775 22.329 2.66669 15.3334 2.66669C8.33781 2.66669 2.66675 8.33775 2.66675 15.3334C2.66675 22.329 8.33781 28 15.3334 28Z",stroke:"#F95050",strokeWidth:"3",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M29.3333 29.3334L25.3333 25.3334",stroke:"#F95050",strokeWidth:"3",strokeLinecap:"round",strokeLinejoin:"round"})]})}),e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium leading-normal text-black",dangerouslySetInnerHTML:{__html:a("user.nearby.results")}})]}):e.jsxs("div",{className:" relative left-0 mt-6 flex w-full flex-1 flex-col items-center justify-start overflow-hidden rounded-tl-[32px] rounded-tr-[32px] bg-white",children:[e.jsx("div",{className:" flex h-7 w-full max-w-[335px] items-center justify-center border-b bg-white ",children:e.jsx("div",{className:" h-1 w-[50px] rounded bg-[#f2f2f7]"})}),e.jsx(S,{taskData:o})]})})]})};export{Q as default};
