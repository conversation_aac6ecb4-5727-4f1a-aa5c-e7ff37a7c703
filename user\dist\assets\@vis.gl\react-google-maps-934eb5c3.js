import{g as V,R as E,r as l,b as K}from"../vendor-b16525a8.js";var q=function t(e,n){if(e===n)return!0;if(e&&n&&typeof e=="object"&&typeof n=="object"){if(e.constructor!==n.constructor)return!1;var o,r,a;if(Array.isArray(e)){if(o=e.length,o!=n.length)return!1;for(r=o;r--!==0;)if(!t(e[r],n[r]))return!1;return!0}if(e.constructor===RegExp)return e.source===n.source&&e.flags===n.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===n.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===n.toString();if(a=Object.keys(e),o=a.length,o!==Object.keys(n).length)return!1;for(r=o;r--!==0;)if(!Object.prototype.hasOwnProperty.call(n,a[r]))return!1;for(r=o;r--!==0;){var i=a[r];if(!t(e[i],n[i]))return!1}return!0}return e!==e&&n!==n};const W=V(q);function O(){return O=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)({}).hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},O.apply(null,arguments)}function k(t,e){if(t==null)return{};var n={};for(var o in t)if({}.hasOwnProperty.call(t,o)){if(e.includes(o))continue;n[o]=t[o]}return n}function Y(t,e){if(typeof t!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var o=n.call(t,e||"default");if(typeof o!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Q(t){var e=Y(t,"string");return typeof e=="symbol"?e:e+""}const I={NOT_LOADED:"NOT_LOADED",LOADING:"LOADING",LOADED:"LOADED",FAILED:"FAILED",AUTH_FAILURE:"AUTH_FAILURE"},X="https://maps.googleapis.com/maps/api/js";class D{static async load(e,n){var o;const r=e.libraries?e.libraries.split(","):[],a=this.serializeParams(e);this.listeners.push(n),(o=window.google)!=null&&(o=o.maps)!=null&&o.importLibrary?(this.serializedApiParams||(this.loadingStatus=I.LOADED),this.notifyLoadingStatusListeners()):(this.serializedApiParams=a,this.initImportLibrary(e)),this.serializedApiParams&&this.serializedApiParams!==a&&console.warn("[google-maps-api-loader] The maps API has already been loaded with different parameters and will not be loaded again. Refresh the page for new values to have effect.");const i=["maps",...r];await Promise.all(i.map(s=>google.maps.importLibrary(s)))}static serializeParams(e){return[e.v,e.key,e.language,e.region,e.authReferrerPolicy,e.solutionChannel].join("/")}static initImportLibrary(e){if(window.google||(window.google={}),window.google.maps||(window.google.maps={}),window.google.maps.importLibrary){console.error("[google-maps-api-loader-internal]: initImportLibrary must only be called once");return}let n=null;const o=()=>n||(n=new Promise((r,a)=>{var i;const s=document.createElement("script"),d=new URLSearchParams;for(const[f,c]of Object.entries(e)){const m=f.replace(/[A-Z]/g,h=>"_"+h[0].toLowerCase());d.set(m,String(c))}d.set("loading","async"),d.set("callback","__googleMapsCallback__"),s.async=!0,s.src=X+"?"+d.toString(),s.nonce=((i=document.querySelector("script[nonce]"))==null?void 0:i.nonce)||"",s.onerror=()=>{this.loadingStatus=I.FAILED,this.notifyLoadingStatusListeners(),a(new Error("The Google Maps JavaScript API could not load."))},window.__googleMapsCallback__=()=>{this.loadingStatus=I.LOADED,this.notifyLoadingStatusListeners(),r()},window.gm_authFailure=()=>{this.loadingStatus=I.AUTH_FAILURE,this.notifyLoadingStatusListeners()},this.loadingStatus=I.LOADING,this.notifyLoadingStatusListeners(),document.head.append(s)}),n);google.maps.importLibrary=r=>o().then(()=>google.maps.importLibrary(r))}static notifyLoadingStatusListeners(){for(const e of this.listeners)e(this.loadingStatus)}}D.loadingStatus=I.NOT_LOADED;D.serializedApiParams=void 0;D.listeners=[];const ee=["onLoad","onError","apiKey","version","libraries"],te=["children"],ne="GMP_visgl_rgmlibrary_v1_default",P=E.createContext(null);function oe(){const[t,e]=l.useState({});return{mapInstances:t,addMapInstance:(a,i="default")=>{e(s=>O({},s,{[i]:a}))},removeMapInstance:(a="default")=>{e(i=>k(i,[a].map(Q)))},clearMapInstances:()=>{e({})}}}function re(t){const{onLoad:e,onError:n,apiKey:o,version:r,libraries:a=[]}=t,i=k(t,ee),[s,d]=l.useState(D.loadingStatus),[f,c]=l.useReducer((u,p)=>u[p.name]?u:O({},u,{[p.name]:p.value}),{}),m=l.useMemo(()=>a==null?void 0:a.join(","),[a]),h=l.useMemo(()=>JSON.stringify(O({apiKey:o,version:r},i)),[o,r,i]),g=l.useCallback(async u=>{var p;if(f[u])return f[u];if(!((p=google)!=null&&(p=p.maps)!=null&&p.importLibrary))throw new Error("[api-provider-internal] importLibrary was called before google.maps.importLibrary was defined.");const C=await window.google.maps.importLibrary(u);return c({name:u,value:C}),C},[f]);return l.useEffect(()=>{(async()=>{try{const u=O({key:o},i);r&&(u.v=r),(m==null?void 0:m.length)>0&&(u.libraries=m),(u.channel===void 0||u.channel<0||u.channel>999)&&delete u.channel,u.solutionChannel===void 0?u.solutionChannel=ne:u.solutionChannel===""&&delete u.solutionChannel,await D.load(u,p=>d(p));for(const p of["core","maps",...a])await g(p);e&&e()}catch(u){n?n(u):console.error("<ApiProvider> failed to load the Google Maps JavaScript API",u)}})()},[o,m,h]),{status:s,loadedLibraries:f,importLibrary:g}}const xe=t=>{const{children:e}=t,n=k(t,te),{mapInstances:o,addMapInstance:r,removeMapInstance:a,clearMapInstances:i}=oe(),{status:s,loadedLibraries:d,importLibrary:f}=re(n),c=l.useMemo(()=>({mapInstances:o,addMapInstance:r,removeMapInstance:a,clearMapInstances:i,status:s,loadedLibraries:d,importLibrary:f}),[o,r,a,i,s,d,f]);return E.createElement(P.Provider,{value:c},e)};function ae(t,e){for(const n of ue){const o=e[n],r=G[n];l.useEffect(()=>{if(!t||!o)return;const a=google.maps.event.addListener(t,r,i=>{o(se(r,t,i))});return()=>a.remove()},[t,r,o])}}function se(t,e,n){const o={type:t,map:e,detail:{},stoppable:!1,stop:()=>{}};if(ie.includes(t)){const a=o,i=e.getCenter(),s=e.getZoom(),d=e.getHeading()||0,f=e.getTilt()||0,c=e.getBounds();return(!i||!c||!Number.isFinite(s))&&console.warn("[createEvent] at least one of the values from the map returned undefined. This is not expected to happen. Please report an issue at https://github.com/visgl/react-google-maps/issues/new"),a.detail={center:(i==null?void 0:i.toJSON())||{lat:0,lng:0},zoom:s||0,heading:d,tilt:f,bounds:(c==null?void 0:c.toJSON())||{north:90,east:180,south:-90,west:-180}},a}else if(le.includes(t)){var r;if(!n)throw new Error("[createEvent] mouse events must provide a srcEvent");const a=o;return a.domEvent=n.domEvent,a.stoppable=!0,a.stop=()=>n.stop(),a.detail={latLng:((r=n.latLng)==null?void 0:r.toJSON())||null,placeId:n.placeId},a}return o}const G={onBoundsChanged:"bounds_changed",onCenterChanged:"center_changed",onClick:"click",onContextmenu:"contextmenu",onDblclick:"dblclick",onDrag:"drag",onDragend:"dragend",onDragstart:"dragstart",onHeadingChanged:"heading_changed",onIdle:"idle",onIsFractionalZoomEnabledChanged:"isfractionalzoomenabled_changed",onMapCapabilitiesChanged:"mapcapabilities_changed",onMapTypeIdChanged:"maptypeid_changed",onMousemove:"mousemove",onMouseout:"mouseout",onMouseover:"mouseover",onProjectionChanged:"projection_changed",onRenderingTypeChanged:"renderingtype_changed",onTilesLoaded:"tilesloaded",onTiltChanged:"tilt_changed",onZoomChanged:"zoom_changed",onCameraChanged:"bounds_changed"},ie=["bounds_changed","center_changed","heading_changed","tilt_changed","zoom_changed"],le=["click","contextmenu","dblclick","mousemove","mouseout","mouseover"],ue=Object.keys(G);function ce(t,e){const n=l.useRef(void 0);(!n.current||!W(e,n.current))&&(n.current=e),l.useEffect(t,n.current)}const de=new Set(["backgroundColor","clickableIcons","controlSize","disableDefaultUI","disableDoubleClickZoom","draggable","draggableCursor","draggingCursor","fullscreenControl","fullscreenControlOptions","gestureHandling","headingInteractionEnabled","isFractionalZoomEnabled","keyboardShortcuts","mapTypeControl","mapTypeControlOptions","mapTypeId","maxZoom","minZoom","noClear","panControl","panControlOptions","restriction","rotateControl","rotateControlOptions","scaleControl","scaleControlOptions","scrollwheel","streetView","streetViewControl","streetViewControlOptions","styles","tiltInteractionEnabled","zoomControl","zoomControlOptions"]);function fe(t,e){const n={},o=Object.keys(e);for(const r of o)de.has(r)&&(n[r]=e[r]);ce(()=>{t&&t.setOptions(n)},[n])}function U(){var t;return((t=l.useContext(P))==null?void 0:t.status)||I.NOT_LOADED}function ge(t,e){const{viewport:n,viewState:o}=e,r=!!n;return l.useLayoutEffect(()=>{if(!t||!o)return;const{latitude:a,longitude:i,bearing:s,pitch:d,zoom:f}=o;t.moveCamera({center:{lat:a,lng:i},heading:s,tilt:d,zoom:f+1})},[t,o]),r}function me(t){return!t||typeof t!="object"||!("lat"in t&&"lng"in t)?!1:Number.isFinite(t.lat)&&Number.isFinite(t.lng)}function j(t){return me(t)?t:t.toJSON()}function pe(t,e,n){const o=n.center?j(n.center):null;let r=null,a=null;o&&Number.isFinite(o.lat)&&Number.isFinite(o.lng)&&(r=o.lat,a=o.lng);const i=Number.isFinite(n.zoom)?n.zoom:null,s=Number.isFinite(n.heading)?n.heading:null,d=Number.isFinite(n.tilt)?n.tilt:null;l.useLayoutEffect(()=>{if(!t)return;const f={};let c=!1;r!==null&&a!==null&&(e.current.center.lat!==r||e.current.center.lng!==a)&&(f.center={lat:r,lng:a},c=!0),i!==null&&e.current.zoom!==i&&(f.zoom=i,c=!0),s!==null&&e.current.heading!==s&&(f.heading=s,c=!0),d!==null&&e.current.tilt!==d&&(f.tilt=d,c=!0),c&&t.moveCamera(f)})}const he=()=>{const t={position:"absolute",top:0,left:0,bottom:0,right:0,zIndex:999,display:"flex",flexFlow:"column nowrap",textAlign:"center",justifyContent:"center",fontSize:".8rem",color:"rgba(0,0,0,0.6)",background:"#dddddd",padding:"1rem 1.5rem"};return E.createElement("div",{style:t},E.createElement("h2",null,"Error: AuthFailure"),E.createElement("p",null,"A problem with your API key prevents the map from rendering correctly. Please make sure the value of the ",E.createElement("code",null,"APIProvider.apiKey")," prop is correct. Check the error-message in the console for further details."))};function ve(){const[t,e]=l.useState(null),n=l.useCallback(o=>e(o),[e]);return[t,n]}function Z(){return U()===I.LOADED}function Ee(){const[,t]=l.useReducer(e=>e+1,0);return t}function ye(t,e){const n=t.getCenter(),o=t.getZoom(),r=t.getHeading()||0,a=t.getTilt()||0,i=t.getBounds();(!n||!i||!Number.isFinite(o))&&console.warn("[useTrackedCameraState] at least one of the values from the map returned undefined. This is not expected to happen. Please report an issue at https://github.com/visgl/react-google-maps/issues/new"),Object.assign(e.current,{center:(n==null?void 0:n.toJSON())||{lat:0,lng:0},zoom:o||0,heading:r,tilt:a})}function Ce(t){const e=Ee(),n=l.useRef({center:{lat:0,lng:0},heading:0,tilt:0,zoom:0});return l.useEffect(()=>{if(!t)return;const o=google.maps.event.addListener(t,"bounds_changed",()=>{ye(t,n),e()});return()=>o.remove()},[t,e]),n}const Le=["id","defaultBounds","defaultCenter","defaultZoom","defaultHeading","defaultTilt","reuseMaps","renderingType","colorScheme"],be=["padding"];class R{static has(e){return this.entries[e]&&this.entries[e].length>0}static pop(e){return this.entries[e]&&this.entries[e].pop()||null}static push(e,n){this.entries[e]||(this.entries[e]=[]),this.entries[e].push(n)}}R.entries={};function Oe(t,e){const n=Z(),[o,r]=l.useState(null),[a,i]=ve(),s=Ce(o),{id:d,defaultBounds:f,defaultCenter:c,defaultZoom:m,defaultHeading:h,defaultTilt:g,reuseMaps:u,renderingType:p,colorScheme:C}=t,v=k(t,Le),M=t.zoom!==void 0||t.defaultZoom!==void 0,S=t.center!==void 0||t.defaultCenter!==void 0;!f&&(!M||!S)&&console.warn("<Map> component is missing configuration. You have to provide zoom and center (via the `zoom`/`defaultZoom` and `center`/`defaultCenter` props) or specify the region to show using `defaultBounds`. See https://visgl.github.io/react-google-maps/docs/api-reference/components/map#required"),!v.center&&c&&(v.center=c),!v.zoom&&Number.isFinite(m)&&(v.zoom=m),!v.heading&&Number.isFinite(h)&&(v.heading=h),!v.tilt&&Number.isFinite(g)&&(v.tilt=g);for(const L of Object.keys(v))v[L]===void 0&&delete v[L];const _=l.useRef();return l.useEffect(()=>{if(!a||!n)return;const{addMapInstance:L,removeMapInstance:b}=e,{mapId:T}=t,A=`${T||"default"}:${p||"default"}:${C||"LIGHT"}`;let w,y;if(u&&R.has(A)?(y=R.pop(A),w=y.getDiv(),a.appendChild(w),y.setOptions(v),setTimeout(()=>y.setCenter(y.getCenter()),0)):(w=document.createElement("div"),w.style.height="100%",a.appendChild(w),y=new google.maps.Map(w,O({},v,p?{renderingType:p}:{},C?{colorScheme:C}:{}))),r(y),L(y,d),f){const{padding:z}=f,F=k(f,be);y.fitBounds(F,z)}else(!M||!S)&&y.fitBounds({east:180,west:-180,south:-90,north:90});if(_.current){const{mapId:z,cameraState:F}=_.current;z!==T&&y.setOptions(F)}return()=>{_.current={mapId:T,cameraState:s.current},w.remove(),u?R.push(A,y):google.maps.event.clearInstanceListeners(y),r(null),b(d)}},[a,n,d,t.mapId,t.renderingType,t.colorScheme]),[o,i,s]}const $=E.createContext(null),Re={DARK:"DARK",LIGHT:"LIGHT",FOLLOW_SYSTEM:"FOLLOW_SYSTEM"},ze={VECTOR:"VECTOR",RASTER:"RASTER",UNINITIALIZED:"UNINITIALIZED"},Me=t=>{const{children:e,id:n,className:o,style:r}=t,a=l.useContext(P),i=U();if(!a)throw new Error("<Map> can only be used inside an <ApiProvider> component.");const[s,d,f]=Oe(t,a);pe(s,f,t),ae(s,t),fe(s,t);const c=ge(s,t),m=!!t.controlled;l.useEffect(()=>{if(s)return c&&s.setOptions({disableDefaultUI:!0}),(c||m)&&s.setOptions({gestureHandling:"none",keyboardShortcuts:!1}),()=>{s.setOptions({gestureHandling:t.gestureHandling,keyboardShortcuts:t.keyboardShortcuts})}},[s,c,m,t.gestureHandling,t.keyboardShortcuts]);const h=t.center?j(t.center):null;let g=null,u=null;h&&Number.isFinite(h.lat)&&Number.isFinite(h.lng)&&(g=h.lat,u=h.lng);const p=l.useMemo(()=>{var M,S,_,L,b;return{center:{lat:(M=g)!=null?M:0,lng:(S=u)!=null?S:0},zoom:(_=t.zoom)!=null?_:0,heading:(L=t.heading)!=null?L:0,tilt:(b=t.tilt)!=null?b:0}},[g,u,t.zoom,t.heading,t.tilt]);l.useLayoutEffect(()=>{if(!s||!m)return;s.moveCamera(p);const M=s.addListener("bounds_changed",()=>{s.moveCamera(p)});return()=>M.remove()},[s,m,p]);const C=l.useMemo(()=>O({width:"100%",height:"100%",position:"relative",zIndex:c?-1:0},r),[r,c]),v=l.useMemo(()=>({map:s}),[s]);return i===I.AUTH_FAILURE?E.createElement("div",{style:O({position:"relative"},o?{}:C),className:o},E.createElement(he,null)):E.createElement("div",O({ref:d,"data-testid":"map",style:o?void 0:C,className:o},n?{id:n}:{}),s?E.createElement($.Provider,{value:v},e):null)};Me.deckGLViewProps=!0;const H=new Set;function Te(...t){const e=JSON.stringify(t);H.has(e)||(H.add(e),console.error(...t))}const J=(t=null)=>{const e=l.useContext(P),{map:n}=l.useContext($)||{};if(e===null)return Te("useMap(): failed to retrieve APIProviderContext. Make sure that the <APIProvider> component exists and that the component you are calling `useMap()` from is a sibling of the <APIProvider>."),null;const{mapInstances:o}=e;return t!==null?o[t]||null:n||o.default||null};function Ie(t){const e=Z(),n=l.useContext(P);return l.useEffect(()=>{!e||!n||n.importLibrary(t)},[e,n,t]),(n==null?void 0:n.loadedLibraries[t])||null}function N(t,e,n){l.useEffect(()=>{if(!t||!e||!n)return;const o=google.maps.event.addListener(t,e,n);return()=>o.remove()},[t,e,n])}function x(t,e,n){l.useEffect(()=>{t&&(t[e]=n)},[t,e,n])}function B(t,e,n){l.useEffect(()=>{if(!(!t||!e||!n))return t.addEventListener(e,n),()=>t.removeEventListener(e,n)},[t,e,n])}function Se(t){return t.nodeType===Node.ELEMENT_NODE}const _e=E.createContext(null),we={TOP_LEFT:["0%","0%"],TOP_CENTER:["50%","0%"],TOP:["50%","0%"],TOP_RIGHT:["100%","0%"],LEFT_CENTER:["0%","50%"],LEFT_TOP:["0%","0%"],LEFT:["0%","50%"],LEFT_BOTTOM:["0%","100%"],RIGHT_TOP:["100%","0%"],RIGHT:["100%","50%"],RIGHT_CENTER:["100%","50%"],RIGHT_BOTTOM:["100%","100%"],BOTTOM_LEFT:["0%","100%"],BOTTOM_CENTER:["50%","100%"],BOTTOM:["50%","100%"],BOTTOM_RIGHT:["100%","100%"],CENTER:["50%","50%"]},Ae=({children:t,styles:e,className:n,anchorPoint:o})=>{const[r,a]=o??we.BOTTOM,i=`translate(50%, 100%) translate(-${r}, -${a})`;return E.createElement("div",{style:{transform:i}},E.createElement("div",{className:n,style:e},t))};function ke(t){const[e,n]=l.useState(null),[o,r]=l.useState(null),a=J(),i=Ie("marker"),{children:s,onClick:d,className:f,onMouseEnter:c,onMouseLeave:m,onDrag:h,onDragStart:g,onDragEnd:u,collisionBehavior:p,clickable:C,draggable:v,position:M,title:S,zIndex:_}=t,L=l.Children.count(s);return l.useEffect(()=>{if(!a||!i)return;const b=new i.AdvancedMarkerElement;b.map=a,n(b);let T=null;return L>0&&(T=document.createElement("div"),T.isCustomMarker=!0,b.content=T,r(T)),()=>{var A;b.map=null,(A=T)==null||A.remove(),n(null),r(null)}},[a,i,L]),l.useEffect(()=>{!e||!e.content||L>0||(e.content.className=f||"")},[e,f,L]),x(e,"position",M),x(e,"title",S??""),x(e,"zIndex",_),x(e,"collisionBehavior",p),l.useEffect(()=>{e&&(v!==void 0?e.gmpDraggable=v:h||g||u?e.gmpDraggable=!0:e.gmpDraggable=!1)},[e,v,h,u,g]),l.useEffect(()=>{if(!e)return;const b=C!==void 0||!!d||!!c||!!m;e.gmpClickable=b,b&&e!=null&&e.content&&Se(e.content)&&(e.content.style.pointerEvents="none",e.content.firstElementChild&&(e.content.firstElementChild.style.pointerEvents="all"))},[e,C,d,c,m]),N(e,"click",d),N(e,"drag",h),N(e,"dragstart",g),N(e,"dragend",u),B(e==null?void 0:e.element,"mouseenter",c),B(e==null?void 0:e.element,"mouseleave",m),[e,o]}const Fe=l.forwardRef((t,e)=>{const{children:n,style:o,className:r,anchorPoint:a}=t,[i,s]=ke(t),d=l.useMemo(()=>i?{marker:i}:null,[i]);return l.useImperativeHandle(e,()=>i,[i]),s?E.createElement(_e.Provider,{value:d},K.createPortal(E.createElement(Ae,{anchorPoint:a,styles:o,className:r},n),s)):null}),De=["onClick","onDrag","onDragStart","onDragEnd","onMouseOver","onMouseOut"];function Pe(t){const[e,n]=l.useState(null),o=J(),{onClick:r,onDrag:a,onDragStart:i,onDragEnd:s,onMouseOver:d,onMouseOut:f}=t,c=k(t,De),{position:m,draggable:h}=c;return l.useEffect(()=>{if(!o){o===void 0&&console.error("<Marker> has to be inside a Map component.");return}const g=new google.maps.Marker(c);return g.setMap(o),n(g),()=>{g.setMap(null),n(null)}},[o]),l.useEffect(()=>{if(!e)return;const g=e,u=google.maps.event;return r&&u.addListener(g,"click",r),a&&u.addListener(g,"drag",a),i&&u.addListener(g,"dragstart",i),s&&u.addListener(g,"dragend",s),d&&u.addListener(g,"mouseover",d),f&&u.addListener(g,"mouseout",f),e.setDraggable(!!h),()=>{u.clearInstanceListeners(g)}},[e,h,r,a,i,s,d,f]),l.useEffect(()=>{e&&c&&e.setOptions(c)},[e,c]),l.useEffect(()=>{h||!m||!e||e.setPosition(m)},[h,m,e]),e}const He=l.forwardRef((t,e)=>{const n=Pe(t);return l.useImperativeHandle(e,()=>n,[n]),E.createElement(E.Fragment,null)});export{xe as A,Re as C,Me as M,ze as R,He as a,Fe as b};
