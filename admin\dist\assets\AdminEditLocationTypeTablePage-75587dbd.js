import{j as a}from"./@react-google-maps/api-ac2f9d6f.js";import{R as r,b as j,h as w,r as A}from"./vendor-4f06b3f4.js";import{u as k}from"./react-hook-form-f3d72793.js";import{o as R}from"./yup-2324a46a.js";import{c as T,a as b}from"./yup-17027d7a.js";import{M as L,A as q,G as P,t as C,s as D}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{M as y}from"./MkdInput-ff3aa862.js";import{I as M}from"./InteractiveButton-8f7d74ee.js";import{S as G}from"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./MoonLoader-16bed42a.js";let l=new L;const ne=o=>{const{dispatch:v}=r.useContext(q),S=T({status:b().required("Status is required"),name:b().required("Name is required")}).required(),{dispatch:c}=r.useContext(P),[d,i]=r.useState(!1),[E,m]=r.useState(!1);j();const{register:u,handleSubmit:N,setError:p,setValue:f,formState:{errors:x}}=k({resolver:R(S)}),s=w();A.useEffect(function(){(async function(){try{m(!0),l.setTable("nearby_type");const e=await l.callRestAPI({id:o.activeId?o.activeId:Number(s==null?void 0:s.id)},"GET");e.error||(f("status",e.model.status),f("name",e.model.name),m(!1))}catch(e){m(!1),console.log("error",e),C(v,e.message)}})()},[]);const I=async e=>{i(!0);try{const t=await l.callRestAPI({id:o.activeId?o.activeId:Number(s==null?void 0:s.id),status:e.status,name:e.name},"PUT");if(!t.error)D(c,"Updated"),c({type:"REFRESH_DATA",payload:{refreshData:!0}}),o.setSidebar(!1);else if(t.validation){const h=Object.keys(t.validation);for(let n=0;n<h.length;n++){const g=h[n];p(g,{type:"manual",message:t.validation[g]})}}i(!1)}catch(t){i(!1),console.log("Error",t),p("name",{type:"manual",message:t.message})}};return a.jsxs("div",{className:"mx-auto rounded p-5 shadow-md",children:[a.jsx("h4",{className:"text-2xl font-medium",children:"Edit Location Type"}),E?a.jsx(G,{}):a.jsxs("form",{className:"w-full max-w-lg",onSubmit:N(I),children:[a.jsx(y,{type:"text",page:"edit",name:"name",errors:x,label:"Name",placeholder:"Name",register:u,className:""}),a.jsx(y,{type:"dropdown",page:"edit",name:"status",errors:x,label:"Status",placeholder:"Status",register:u,className:"",options:[{value:"inactive",name:"Inactive"},{value:"active",name:"Active"}]}),a.jsx(M,{type:"submit",className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",loading:d,disable:d,children:"Submit"})]})]})};export{ne as default};
