import{j as e}from"./@react-google-maps/api-ee55a349.js";import{r as o,d as K}from"./vendor-b16525a8.js";import{A as B,M as F,a as G}from"./@vis.gl/react-google-maps-934eb5c3.js";import{a as O}from"./index-d54cffea.js";import{M as D}from"./index-09a1718e.js";import{S as T}from"./index-5a645c18.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./react-i18next-4a61273e.js";const q={center:{lat:40.7,lng:-74},zoom:12},h=(c,i)=>{const a=c.find(n=>n.types.includes(i));return a?a.long_name:null},H=new D,mt=({handleChoose:c,lat:i,lng:a})=>{const[n,l]=o.useState(),[v,x]=o.useState(!0);o.useState(15);const[y,C]=o.useState(""),[S,_]=o.useState(""),[k,L]=o.useState(""),[A,b]=o.useState(""),d=o.useRef(null),r=o.useRef(null),[M,m]=o.useState(q),N=o.useCallback(t=>m(t.detail));K();const R=t=>{d.current=t,r.current=new window.google.maps.Marker({position:n,map:t,draggable:!0}),r.current.addListener("dragend",s=>{const u={lat:s.latLng.lat(),lng:s.latLng.lng()};l(u)})},z=t=>{const s={lat:t.detail.latLng.lat,lng:t.detail.latLng.lng};l(s),r.current&&r.current.setPosition(s),g(s)},g=async t=>{const{lat:s,lng:u}=t;new window.google.maps.Geocoder().geocode({location:{lat:s,lng:u}},(p,I)=>{if(I==="OK"&&p[0]){C(p[0].formatted_address);const f=p[0],w=h(f.address_components,"locality"),j=h(f.address_components,"administrative_area_level_1"),P=h(f.address_components,"country");_(w),L(j),b(P),console.log("City:",w),console.log("State:",j),console.log("Country:",P)}else C("No address found")})},E=()=>{if(x(!0),i&&a){const t={lat:i,lng:a};l(t),g(t),m({center:t,zoom:15});return}navigator.geolocation?navigator.geolocation.getCurrentPosition(t=>{const s={lat:t.coords.latitude,lng:t.coords.longitude};l(s),g(s),m({center:s,zoom:15})},t=>{console.log(t.message)}):console.error("Geolocation is not supported by this browser."),x(!1)};return o.useEffect(()=>{E()},[]),o.useEffect(()=>{d.current&&(d.current.setCenter(n),r.current&&r.current.setPosition(n))},[n]),!n&&v?e.jsx(e.Fragment,{children:e.jsx(T,{})}):e.jsx("div",{className:"h-full w-full",children:e.jsx(B,{apiKey:H._google_api_key,children:e.jsxs("div",{className:"relative",style:{width:"100%",height:"100%"},children:[e.jsx("div",{className:"relative",style:{width:"100%",height:"70%"},children:e.jsx(F,{options:{zoomControl:!0},style:{width:"100%",height:"100%"},onLoad:R,onClick:z,...M,onCameraChanged:N,children:e.jsx(G,{position:n})})}),e.jsxs("div",{className:"absolute bottom-0 left-0 z-[99999999999999] w-full rounded-xl bg-white px-[17px] py-[30px]",children:[e.jsx("div",{className:"text-center font-['Poppins'] text-[22px] font-semibold leading-normal text-[#56ccf2]",children:y}),e.jsx("div",{children:e.jsx(O,{onClick:()=>c(n.lat,n.lng,y,S,k,A),children:"Choose this address"})})]})]})})})};export{mt as default};
