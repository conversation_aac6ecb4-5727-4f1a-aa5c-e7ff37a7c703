import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{h as D,r as o,d as U}from"./vendor-f36d475e.js";import"./index-5deedf4a.js";import{M as W,A as V,G as F,s as v}from"./index-cf5e6bc7.js";import{B as T}from"./index-895fa99b.js";import{u as Z}from"./user-a875fff3.js";import{u as q}from"./react-hook-form-ff037c98.js";import{o as H}from"./yup-afe5cf51.js";import{c as R,a as l}from"./yup-2f6e2476.js";import{A as G}from"./index-ad319f83.js";import{M as b}from"./index-bf8d79cc.js";import{S as p}from"./index-65bc3378.js";import{u as $}from"./react-i18next-1e3e6bc5.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./@hookform/resolvers-eb417cd0.js";const n=new W,O=[{name:"first_name",placeholder:"first_name_label",label:"first_name_label",icon:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M10.1331 9.05841C10.0498 9.05008 9.9498 9.05008 9.85814 9.05841C7.8748 8.99175 6.2998 7.36675 6.2998 5.36675C6.2998 3.32508 7.9498 1.66675 9.9998 1.66675C12.0415 1.66675 13.6998 3.32508 13.6998 5.36675C13.6915 7.36675 12.1165 8.99175 10.1331 9.05841Z",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M5.9666 12.1333C3.94993 13.4833 3.94993 15.6833 5.9666 17.0249C8.25827 18.5583 12.0166 18.5583 14.3083 17.0249C16.3249 15.6749 16.3249 13.4749 14.3083 12.1333C12.0249 10.6083 8.2666 10.6083 5.9666 12.1333Z",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})},{name:"last_name",placeholder:"last_name_label",label:"last_name_label",icon:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M10.1331 9.05841C10.0498 9.05008 9.9498 9.05008 9.85814 9.05841C7.8748 8.99175 6.2998 7.36675 6.2998 5.36675C6.2998 3.32508 7.9498 1.66675 9.9998 1.66675C12.0415 1.66675 13.6998 3.32508 13.6998 5.36675C13.6915 7.36675 12.1165 8.99175 10.1331 9.05841Z",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M5.9666 12.1333C3.94993 13.4833 3.94993 15.6833 5.9666 17.0249C8.25827 18.5583 12.0166 18.5583 14.3083 17.0249C16.3249 15.6749 16.3249 13.4749 14.3083 12.1333C12.0249 10.6083 8.2666 10.6083 5.9666 12.1333Z",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})},{name:"phone",type:"phone",placeholder:"phone_number_label",label:"phone_number_label",icon:e.jsxs("svg",{width:"20",height:"22",viewBox:"0 0 20 22",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M18 6V16C18 20 17 21 13 21H7C3 21 2 20 2 16V6C2 2 3 1 7 1H13C17 1 18 2 18 6Z",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M12 4.5H8",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M10.0002 18.1C10.8562 18.1 11.5502 17.406 11.5502 16.55C11.5502 15.694 10.8562 15 10.0002 15C9.14415 15 8.4502 15.694 8.4502 16.55C8.4502 17.406 9.14415 18.1 10.0002 18.1Z",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),length:15},{name:"email",placeholder:"email_label",label:"email_label",icon:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M14.1665 17.0834H5.83317C3.33317 17.0834 1.6665 15.8334 1.6665 12.9167V7.08341C1.6665 4.16675 3.33317 2.91675 5.83317 2.91675H14.1665C16.6665 2.91675 18.3332 4.16675 18.3332 7.08341V12.9167C18.3332 15.8334 16.6665 17.0834 14.1665 17.0834Z",stroke:"#8181A4",strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M14.1668 7.5L11.5585 9.58333C10.7002 10.2667 9.29183 10.2667 8.43349 9.58333L5.8335 7.5",stroke:"#8181A4",strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"})]})}],Ne=()=>{const{t:a}=$();D();const{state:w,dispatch:C}=o.useContext(V),{state:K,dispatch:u}=o.useContext(F),[k,h]=o.useState(!1),[_,f]=o.useState(!0),[y,c]=o.useState(!1),[z,N]=o.useState(!1),[J,L]=o.useState(null),[d,m]=o.useState(null),[x,g]=o.useState(),[Q,A]=o.useState([]);console.log("state >>",w);const S=R({first_name:l().required(),last_name:l().required(),phone:l().required(),email:l().required().email()}).required();U();const{register:M,handleSubmit:P,setValue:r,setError:X,formState:{errors:I,isValid:j}}=q({resolver:H(S),mode:"onChange"});o.useEffect(()=>{N(j)},[j]);const E=async s=>{try{h(!0);const t=await n.callRawAPI("/v3/api/custom/chumpchange/provider/update-profile",{firstName:s.first_name,lastName:s.last_name,phone:s.phone,email:s.email,operating_city:x,photo:d},"POST");h(!1),v(u,a("provider.edit_profile.success"))}catch{v(u,"Failed to update profile",5e3,"error")}};o.useEffect(()=>{(async()=>{f(!0);const t=await n.callRawAPI("/v3/api/custom/chumpchange/provider/data",{},"GET");n.setTable("city");const i=await n.callRestAPI({},"GETALL");console.log("City >> ",i),console.log("result >>",t),t.error||(A(i.list),L(t.data),r("first_name",t.data.first_name),r("last_name",t.data.last_name),r("phone",t.data.phone),r("email",t.data.email),r("operating_city",t.data.operating_city),g(t.data.operating_city),m(t.data.photo),C({type:"UPDATE_PROFILE",payload:{first_name:t.data.first_name,last_name:t.data.last_name,photo:t.data.photo,user_data:{...t.data}}})),f(!1)})()},[r]);const B=async s=>{console.log("e >>",s.target.files[0]);try{c(!0);const t=new FormData;t.append("file",s.target.files[0]);const i=await n.uploadImage(t);console.log("result >>",i),m(i.url),c(!1)}catch(t){c(!1),console.log("error >>",t)}};return console.log("imageUrl >> ",e.jsx("imageUrl",{})),e.jsxs(e.Fragment,{children:[y&&e.jsx(b,{showCloseButton:!1,children:e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsxs("div",{className:"text-lg font-semibold",children:[a("loading.adding"),"..."]}),e.jsx("div",{className:"mt-12",children:e.jsx(p,{})})]})}),k&&e.jsx(b,{showCloseButton:!1,children:e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsxs("div",{className:"text-lg font-semibold",children:[a("loading.updating"),"..."]}),e.jsx("div",{className:"mt-12",children:e.jsx(p,{})})]})}),_?e.jsx("div",{className:"flex min-h-screen w-full items-center justify-center",children:e.jsx(p,{})}):e.jsxs("form",{onSubmit:P(E),className:"p-5",children:[e.jsxs("div",{className:"relative flex w-full items-center justify-between ",children:[e.jsx("div",{className:" ",children:e.jsx(T,{})}),e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:a("provider.edit_profile.title")}),e.jsx("div",{className:" ",children:e.jsx("button",{className:"relative flex h-10 w-[67px] items-center justify-center rounded-[32px] bg-white/60 font-['Poppins'] text-sm font-semibold text-[#828282] backdrop-blur-[20px]",children:a("buttons.save")})})]}),e.jsxs("div",{className:" mt-5 flex flex-col items-center justify-center",children:[e.jsx("img",{className:"relative h-20 w-20 rounded-[100px]",src:d||Z}),e.jsxs("div",{className:"mt-5 flex w-full items-center justify-around gap-2",children:[e.jsx("button",{type:"button",onClick:()=>{m(null),console.log(d)},className:"relative flex h-12 w-[110px] items-center justify-center rounded-2xl bg-[#ff0000]/5 text-center font-['Poppins'] text-sm font-semibold text-[#f95050]",children:a("buttons.remove")}),e.jsxs("div",{className:"relative flex h-12 w-[110px] items-center justify-center rounded-2xl border-2 bg-[#50a8f9]/10 text-center font-['Poppins'] text-sm font-semibold text-[#56ccf2]",children:[e.jsx("input",{type:"file",className:" absolute left-0 top-[-100%] h-[200%] w-full cursor-pointer opacity-0",onChange:B}),a("buttons.change")]})]})]}),e.jsxs("div",{className:" mt-7 flex flex-col gap-4",children:[O.map((s,t)=>e.jsxs("div",{className:"w-full",children:[e.jsx("div",{className:"mb-1 px-2 font-['Poppins'] text-sm font-medium text-[#8080a3] ",children:a(`provider.edit_profile.${s.label}`)}),e.jsx(G,{type:s.type,name:s.name,placeholder:a(`provider.edit_profile.${s.placeholder}`),errors:I,register:M,icon:s.icon})]},t)),e.jsxs("div",{className:"w-full",children:[e.jsx("div",{className:"mb-1 px-2 font-['Poppins'] text-sm font-medium text-[#8080a3] ",children:a("provider.edit_profile.city_o")}),e.jsx("input",{type:"text",value:x,placeholder:a("user.add_address.enter_city"),onChange:s=>g(s.target.value),className:"relative mt-1 h-12 w-full rounded-2xl border-none bg-white text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "})]})]})]})]})};export{Ne as default};
