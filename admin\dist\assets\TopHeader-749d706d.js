import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{r as d,R as r,u as g,L as u}from"./vendor-4f06b3f4.js";import{M as f,A as C,t as v,G as j,S as A}from"./index-06b5b6dd.js";import{_ as w}from"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-525c3702.js";import"./moment-a9aaa855.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";const L=d.lazy(()=>w(()=>import("./BackButton-065e4358.js"),["assets/BackButton-065e4358.js","assets/@react-google-maps/api-ac2f9d6f.js","assets/vendor-4f06b3f4.js","assets/index-250f6b3d.js","assets/qr-scanner-cf010ec4.js"])),b=(c=!1)=>{const p=new f,{state:{profile:l},dispatch:a}=r.useContext(C),[i,o]=r.useState(null),h=r.useCallback(()=>{(async()=>{try{const s=await p.getProfile();console.log(s),s!=null&&s.error||(o(()=>s),a({type:"UPDATE_PROFILE",payload:s}))}catch(s){console.log(s.message),v(a,s.message)}})()},[i]);return r.useEffect(()=>{!l||c?h():o(l)},[l]),[i,o]},T=()=>{var x;const{state:c,dispatch:p}=r.useContext(j),{state:l,dispatch:a}=r.useContext(C),[i,o]=d.useState(""),{isOpen:h,showBackButton:s}=c,m=g(),[t]=b();return d.useEffect(()=>{const n=m.pathname.split("/");n[1]!=="government"&&n[1]!=="admin"?o(n[1]):o(n[2])},[m]),e.jsxs("div",{className:"sticky right-0 top-0 z-20 flex h-14 max-h-14 w-full items-center justify-between bg-white px-6 py-4 shadow-sm",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[localStorage.getItem("role")!=="government"&&s&&e.jsx(L,{}),e.jsx("h1",{className:"text-xl capitalize",children:A(i,{casetype:"capitalize",separator:" "})})]}),e.jsx("div",{className:"flex items-center gap-3 pr-2",children:e.jsxs("div",{children:[e.jsx("button",{title:"Profile",className:"peer",children:(x=t==null?void 0:t.photo)!=null&&x.length?e.jsx("img",{className:"h-[40px] w-[40px] rounded-lg object-cover",src:t==null?void 0:t.photo,alt:`${t==null?void 0:t.first_name} ${t==null?void 0:t.last_name}`}):e.jsx("div",{className:"mt-1 h-[40px] w-[40px] rounded-lg bg-gray-400 shadow-md"})}),e.jsxs("ul",{className:"absolute right-5 top-[80%] z-20 hidden rounded-lg border border-[#a8a8a8] bg-white p-2 text-sm text-[#525252] shadow-md hover:block focus:block peer-focus:block peer-focus-visible:block",children:[e.jsx("li",{children:e.jsxs(u,{className:"hover:text[#262626] flex cursor-pointer items-center rounded-md px-4 py-3 hover:bg-[#F4F4F4]",to:`/${t==null?void 0:t.role}/profile`,children:[e.jsx("span",{className:"mr-2",children:e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:[e.jsx("path",{d:"M13.3333 5.41666C13.3333 7.25761 11.8409 8.74999 9.99997 8.74999C8.15902 8.74999 6.66664 7.25761 6.66664 5.41666C6.66664 3.57571 8.15902 2.08332 9.99997 2.08332C11.8409 2.08332 13.3333 3.57571 13.3333 5.41666Z",fill:"#A8A8A8"}),e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.99997 2.49999C8.38914 2.49999 7.08331 3.80583 7.08331 5.41666C7.08331 7.02749 8.38914 8.33332 9.99997 8.33332C11.6108 8.33332 12.9166 7.02749 12.9166 5.41666C12.9166 3.80583 11.6108 2.49999 9.99997 2.49999ZM6.24997 5.41666C6.24997 3.34559 7.9289 1.66666 9.99997 1.66666C12.071 1.66666 13.75 3.34559 13.75 5.41666C13.75 7.48772 12.071 9.16666 9.99997 9.16666C7.9289 9.16666 6.24997 7.48772 6.24997 5.41666Z",fill:"#A8A8A8"}),e.jsx("path",{d:"M9.99997 10.4167C6.27535 10.4167 3.66126 13.3457 3.33331 17.0833H16.6666C16.3387 13.3457 13.7246 10.4167 9.99997 10.4167Z",fill:"#A8A8A8"}),e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.80032 16.6667H16.1996C15.725 13.323 13.3164 10.8333 9.99997 10.8333C6.68352 10.8333 4.27494 13.323 3.80032 16.6667ZM2.91823 17.0469C3.26095 13.1409 6.01533 9.99999 9.99997 9.99999C13.9846 9.99999 16.739 13.1409 17.0817 17.0469L17.1215 17.5H2.87848L2.91823 17.0469Z",fill:"#A8A8A8"})]})}),e.jsx("span",{children:"Account"})]})}),e.jsx("li",{children:e.jsxs(u,{className:"hover:text[#262626] group flex cursor-pointer items-center rounded-md px-4 py-3 hover:bg-[#F4F4F4] hover:text-red-500",to:`/${t==null?void 0:t.role}/login`,onClick:()=>a({type:"LOGOUT"}),children:[e.jsx("span",{className:"mr-2",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:e.jsx("path",{className:"group-hover:fill-[#ef4444] group-hover:stroke-[#ef4444]",fillRule:"evenodd",clipRule:"evenodd",d:"M3.75 3.33333C3.51988 3.33333 3.33333 3.51988 3.33333 3.75L3.33333 16.25C3.33333 16.4801 3.51988 16.6667 3.75 16.6667H9.58333C9.81345 16.6667 10 16.8532 10 17.0833C10 17.3135 9.81345 17.5 9.58333 17.5H3.75C3.05964 17.5 2.5 16.9404 2.5 16.25L2.5 3.75C2.5 3.05964 3.05964 2.5 3.75 2.5L9.58333 2.5C9.81345 2.5 10 2.68655 10 2.91667C10 3.14679 9.81345 3.33333 9.58333 3.33333L3.75 3.33333ZM13.0387 5.95537C13.2014 5.79265 13.4652 5.79265 13.628 5.95537L17.378 9.70536C17.5407 9.86808 17.5407 10.1319 17.378 10.2946L13.628 14.0446C13.4652 14.2073 13.2014 14.2073 13.0387 14.0446C12.876 13.8819 12.876 13.6181 13.0387 13.4554L16.0774 10.4167L7.91667 10.4167C7.68655 10.4167 7.5 10.2301 7.5 9.99999C7.5 9.76987 7.68655 9.58332 7.91667 9.58332L16.0774 9.58332L13.0387 6.54463C12.876 6.38191 12.876 6.11809 13.0387 5.95537Z",fill:"#A8A8A8",stroke:"#A8A8A8",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("span",{children:"Logout"})]})})]})]})})]})};export{T as default};
