import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as i,b as E}from"./vendor-4f06b3f4.js";import{u as I}from"./react-hook-form-f3d72793.js";import{o as T}from"./yup-2324a46a.js";import{c as F,a as r}from"./yup-17027d7a.js";import{G as C,A as D,M as L,s as $,t as M}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as n}from"./MkdInput-ff3aa862.js";import{I as O}from"./InteractiveButton-8f7d74ee.js";import"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";const xe=({setSidebar:k})=>{var f,h,y,j,N,w;const{dispatch:c}=i.useContext(C),v=F({task:r(),arguments:r(),error_log:r(),identifier:r(),retries:r(),retry_count:r(),time_interval:r(),last_run:r(),status:r()}).required(),{dispatch:S}=i.useContext(D),[x,P]=i.useState({}),[g,u]=i.useState(!1),R=E(),{register:s,handleSubmit:_,setError:b,setValue:B,formState:{errors:t}}=I({resolver:T(v)});i.useState([]);const A=async a=>{let p=new L;u(!0);try{for(let m in x){let l=new FormData;l.append("file",x[m].file);let d=await p.uploadImage(l);a[m]=d.url}p.setTable("job");const o=await p.callRestAPI({task:a.task,arguments:a.arguments,error_log:a.error_log,identifier:a.identifier,retries:a.retries,retry_count:a.retry_count,time_interval:a.time_interval,last_run:a.last_run,status:a.status},"POST");if(!o.error)$(c,"Added"),R("/admin/job"),k(!1),c({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(o.validation){const m=Object.keys(o.validation);for(let l=0;l<m.length;l++){const d=m[l];b(d,{type:"manual",message:o.validation[d]})}}u(!1)}catch(o){u(!1),console.log("Error",o),b("task",{type:"manual",message:o.message}),M(S,o.message)}};return i.useEffect(()=>{c({type:"SETPATH",payload:{path:"job"}})},[]),e.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Job"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:_(A),children:[e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"task",children:"Task"}),e.jsx("textarea",{placeholder:"Task",...s("task"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(f=t.task)!=null&&f.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(h=t.task)==null?void 0:h.message})]}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"arguments",children:"Arguments"}),e.jsx("textarea",{placeholder:"Arguments",...s("arguments"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(y=t.arguments)!=null&&y.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(j=t.arguments)==null?void 0:j.message})]}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"error_log",children:"Error Log"}),e.jsx("textarea",{placeholder:"Error Log",...s("error_log"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(N=t.error_log)!=null&&N.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(w=t.error_log)==null?void 0:w.message})]}),e.jsx(n,{type:"text",page:"add",name:"identifier",errors:t,label:"Identifier",placeholder:"Identifier",register:s,className:""}),e.jsx(n,{type:"number",page:"add",name:"retries",errors:t,label:"Retries",placeholder:"Retries",register:s,className:""}),e.jsx(n,{type:"number",page:"add",name:"retry_count",errors:t,label:"Retry Count",placeholder:"Retry Count",register:s,className:""}),e.jsx(n,{type:"text",page:"add",name:"time_interval",errors:t,label:"Time Interval",placeholder:"Time Interval",register:s,className:""}),e.jsx(n,{type:"datetime-local",page:"add",name:"last_run",errors:t,label:"Last Run",placeholder:"Last Run",register:s,className:""}),e.jsx(n,{type:"text",page:"add",name:"status",errors:t,label:"Status",placeholder:"Status",register:s,className:""}),e.jsx(O,{type:"submit",loading:g,disabled:g,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{xe as default};
