import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as d,b as D}from"./vendor-4f06b3f4.js";import{u as _}from"./react-hook-form-f3d72793.js";import{o as E}from"./yup-2324a46a.js";import{c as A,a as i}from"./yup-17027d7a.js";import{G as I,A as R,M as k,s as T,t as F}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as o}from"./MkdInput-ff3aa862.js";import{I as C}from"./InteractiveButton-8f7d74ee.js";import"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";const me=({setSidebar:y})=>{var g,h;const{dispatch:p}=d.useContext(I),S=A({user_id:i(),description:i(),rate:i(),duration:i(),end_date:i(),start_date:i(),status:i(),service_id:i()}).required(),{dispatch:j}=d.useContext(R),[x,M]=d.useState({}),[f,c]=d.useState(!1),v=D(),{register:s,handleSubmit:N,setError:b,setValue:O,formState:{errors:t}}=_({resolver:E(S)});d.useState([]);const w=async a=>{let u=new k;c(!0);try{for(let n in x){let l=new FormData;l.append("file",x[n].file);let m=await u.uploadImage(l);a[n]=m.url}u.setTable("servicelisting");const r=await u.callRestAPI({user_id:a.user_id,description:a.description,rate:a.rate,duration:a.duration,end_date:a.end_date,start_date:a.start_date,status:a.status,service_id:a.service_id},"POST");if(!r.error)T(p,"Added"),v("/admin/servicelisting"),y(!1),p({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(r.validation){const n=Object.keys(r.validation);for(let l=0;l<n.length;l++){const m=n[l];b(m,{type:"manual",message:r.validation[m]})}}c(!1)}catch(r){c(!1),console.log("Error",r),b("user_id",{type:"manual",message:r.message}),F(j,r.message)}};return d.useEffect(()=>{p({type:"SETPATH",payload:{path:"servicelisting"}})},[]),e.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Servicelisting"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:N(w),children:[e.jsx(o,{type:"number",page:"add",name:"user_id",errors:t,label:"User Id",placeholder:"User Id",register:s,className:""}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"description",children:"Description"}),e.jsx("textarea",{placeholder:"Description",...s("description"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(g=t.description)!=null&&g.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(h=t.description)==null?void 0:h.message})]}),e.jsx(o,{type:"number",page:"add",name:"rate",errors:t,label:"Rate",placeholder:"Rate",register:s,className:""}),e.jsx(o,{type:"number",page:"add",name:"duration",errors:t,label:"Duration",placeholder:"Duration",register:s,className:""}),e.jsx(o,{type:"date",page:"add",name:"end_date",errors:t,label:"End Date",placeholder:"End Date",register:s,className:""}),e.jsx(o,{type:"date",page:"add",name:"start_date",errors:t,label:"Start Date",placeholder:"Start Date",register:s,className:""}),e.jsx(o,{type:"text",page:"add",name:"status",errors:t,label:"Status",placeholder:"Status",register:s,className:""}),e.jsx(o,{type:"number",page:"add",name:"service_id",errors:t,label:"Service Id",placeholder:"Service Id",register:s,className:""}),e.jsx(C,{type:"submit",loading:f,disabled:f,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{me as default};
