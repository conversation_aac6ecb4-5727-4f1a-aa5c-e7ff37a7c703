import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as g,b as J,h as Q,r as d}from"./vendor-4f06b3f4.js";import{u as W}from"./react-hook-form-f3d72793.js";import{o as X}from"./yup-2324a46a.js";import{c as Y,a as p}from"./yup-17027d7a.js";import{M as Z,A as ee,G as se,t as D,T as ae,s as q}from"./index-06b5b6dd.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./moment-a9aaa855.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";let i=new Z;const Ee=({activeId:c,setSidebar:b,isUpdate:M,setIsUpdate:O})=>{var _,S,C,P,A,F,I,R,T,$;const U=Y({first_name:p().required(),last_name:p().required(),email:p().email().required(),phone:p().required(),password:p()}),{dispatch:y}=g.useContext(ee),{dispatch:f}=g.useContext(se);J(),Q();const[G,V]=d.useState(""),[te,B]=d.useState({}),[le,L]=d.useState(0),[w,j]=d.useState(!1),[H,h]=d.useState(!1),[N,v]=d.useState(""),{register:m,handleSubmit:k,setError:x,setValue:n,formState:{errors:l}}=W({resolver:X(U)}),K=[{key:"0",value:"Inactive"},{key:"2",value:"Suspend"},{key:"1",value:"Active"}],E=async s=>{j(!0);try{if(G!==s.email){const a=await i.updateEmailByAdmin(s.email,c);if(!a.error)q(f,"Email Updated",1e3);else if(a.validation){const r=Object.keys(a.validation);for(let o=0;o<r.length;o++){const u=r[o];x(u,{type:"manual",message:a.validation[u]})}}}if(s.password.length>0){const a=await i.updatePasswordByAdmin(s.password,c);if(!a.error)f({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(a.validation){const r=Object.keys(a.validation);for(let o=0;o<r.length;o++){const u=r[o];x(u,{type:"manual",message:a.validation[u]})}}}i.setTable("user");const t=await i.callRestAPI({id:c,email:s.email,status:s.status,photo:N,first_name:s.first_name,last_name:s.last_name,phone:s.phone},"PUT");if(!t.error)q(f,"Update",4e3),O(!M),b(!1);else if(t.validation){const a=Object.keys(t.validation);for(let r=0;r<a.length;r++){const o=a[r];x(o,{type:"manual",message:t.validation[o]})}}}catch(t){console.log("Error",t),x("email",{type:"manual",message:t.message}),D(y,t.message)}j(!1)};g.useEffect(()=>{(async function(){try{i.setTable("user");const s=await i.callRestAPI({id:c},"GET");s.error||(n("first_name",s.model.first_name),n("last_name",s.model.last_name),n("phone",s.model.phone),n("email",s.model.email),n("role",s.model.role),n("status",s.model.status),n("cedula_number",s.model.cedula_number),V(s.model.email),L(s.model.id),B(s.model),v(s.model.photo))}catch(s){console.log("Error",s),D(y,s.message)}})()},[c]);const z=async s=>{try{h(!0);const t=new FormData;t.append("file",s.target.files[0]);const a=await i.uploadImage(t);console.log("result >>",a),v(a.url),h(!1)}catch(t){h(!1),console.log("error >>",t)}};return e.jsxs("div",{className:"mx-auto rounded",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[e.jsx("div",{className:"flex items-center gap-3",children:e.jsx("span",{className:"text-lg font-semibold",children:"Edit Info"})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 shadow-sm hover:bg-[#f4f4f4]",onClick:()=>b(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center rounded-md bg-[#4F46E5] px-3 py-2 text-white shadow-sm",onClick:async()=>{await k(E)()},disabled:w,children:w?"Saving...":"Save"})]})]}),e.jsxs("form",{className:" w-full p-4 text-left",onSubmit:k(E),children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"first_name",children:"First Name"}),e.jsx("input",{type:"text",...m("first_name"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(_=l.first_name)!=null&&_.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(S=l.first_name)==null?void 0:S.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"last_name",children:"Last Name"}),e.jsx("input",{type:"text",...m("last_name"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(C=l.last_name)!=null&&C.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(P=l.last_name)==null?void 0:P.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"phone",children:"Phone"}),e.jsx("input",{type:"text",...m("phone",{onChange:ae}),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(A=l.phone)!=null&&A.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(F=l.phone)==null?void 0:F.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"email",children:"Email"}),e.jsx("input",{type:"email",...m("email"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(I=l.email)!=null&&I.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(R=l.email)==null?void 0:R.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Status"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...m("status"),children:K.map(s=>e.jsx("option",{name:"status",value:s.key,children:s.value},s.key))})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"password",children:"Password"}),e.jsx("input",{type:"password",placeholder:"******************",autoComplete:"new-password",...m("password"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(T=l.password)!=null&&T.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:($=l.password)==null?void 0:$.message})]}),e.jsxs("div",{className:" mt-4 ",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Profile Pic"}),e.jsxs("div",{className:" flex flex-col flex-wrap gap-1 ",children:[e.jsx("img",{src:N,alt:"Profile",className:" h-[150px] w-[150px] border object-cover "}),H?e.jsx(e.Fragment,{children:e.jsx("div",{className:"font-['Poppins'] text-base font-medium leading-tight",children:"Loading..."})}):e.jsx("div",{className:"",children:e.jsxs("button",{className:" relative mt-3 inline-block overflow-hidden ",children:[e.jsx("div",{className:"font-['Poppins'] text-base font-medium text-[#4fa7f9]",children:"Change Image"}),e.jsx("input",{type:"file",onChange:z,className:" absolute left-0 top-[-150%] h-[250%] w-full cursor-pointer border-none outline-none ring-0 focus:border-none focus:outline-none focus:ring-0 "})]})})]})]})]})]})};export{Ee as default};
