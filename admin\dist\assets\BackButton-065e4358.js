import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{i as a}from"./vendor-4f06b3f4.js";import{a as r}from"./index-250f6b3d.js";import"./qr-scanner-cf010ec4.js";const x=({text:s="Back",link:e})=>t.jsx("div",{children:t.jsxs(a,{className:"flex items-center gap-1 py-1 px-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition duration-200",to:e||-1,children:[t.jsx(r,{className:"text-white"}),t.jsx("span",{className:"font-semibold",children:s})]})});export{x as default};
