import{j as r}from"./@react-google-maps/api-afbf18d5.js";import{A as M,M as j,R as w,C as v,b as g,u as k}from"./@vis.gl/react-google-maps-e1ad9da7.js";import{M as b}from"./@googlemaps/markerclusterer-2f9a3a53.js";import{r as t}from"./vendor-f36d475e.js";import{M as N,e as V}from"./index-cf5e6bc7.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./react-i18next-1e3e6bc5.js";const _=new N,J=({alertList:C,selectedItem:i,setselectedItem:p,setUserDistance:c,position:o,setPosition:d,cameraProps:a,setCameraProps:u})=>{t.useState({lat:39.7827,lng:-89.6501}),t.useState(15);const[n,f]=t.useState(null),h=t.useCallback(e=>u(e.detail));return t.useRef(null),console.log("activeMarker >> ",n),console.log("position >> ",o),r.jsx("div",{className:"relative h-full w-full ",children:o&&r.jsx(M,{apiKey:_._google_api_key,children:r.jsx("div",{className:"relative",style:{width:"100%",height:"100%"},children:r.jsxs(j,{renderingType:w.VECTOR,mapId:"18a845e702a72124",...a,onCameraChanged:h,colorScheme:v.LIGHT,options:{zoomControl:!1},style:{width:"100%",height:"100%"},children:[r.jsx(g,{position:o}),r.jsx(m,{points:C,setselectedItem:p,selectedItem:i,position:o,setUserDistance:c})]})})})})},m=({points:C,setselectedItem:i,selectedItem:p,position:c,setUserDistance:o})=>{const d=k(),[a,u]=t.useState({}),n=t.useRef(null);t.useEffect(()=>{d&&(n.current||(n.current=new b({map:d})))},[d]),t.useEffect(()=>{var e,s;console.log("mark",a),(e=n.current)==null||e.clearMarkers(),(s=n.current)==null||s.addMarkers(Object.values(a))},[a]);const f=(e,s)=>{e&&a[s]||!e&&!a[s]||u(l=>{if(e)return{...l,[s]:e};{const x={...l};return delete x[s],x}})},h=(e,s)=>{console.log("item =>",s),i(s);const l=V(Number(s==null?void 0:s.latitude),Number(s==null?void 0:s.longtitude),Number(c.lat),Number(c.lng));console.log("distach calc",l),o(l)};return r.jsx(r.Fragment,{children:C.map((e,s)=>r.jsx(g,{position:{lat:Number(e!=null&&e.latitude?e==null?void 0:e.latitude:e==null?void 0:e.location_latitude),lng:Number(e!=null&&e.longtitude?e==null?void 0:e.longtitude:e==null?void 0:e.location_longitude)},onClick:l=>h(l,e),ref:l=>f(l,e.id),children:r.jsxs("div",{className:"flex flex-col items-center justify-center",children:[r.jsxs("svg",{width:"30",height:"44",viewBox:"0 0 35 44",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[r.jsx("path",{d:"M17.385 0C7.79886 0 0 7.14846 0 15.9351C0 26.8395 15.5579 42.8479 16.2203 43.524C16.8424 44.1592 17.9287 44.1581 18.5497 43.524C19.2121 42.8479 34.77 26.8395 34.77 15.9351C34.7698 7.14846 26.971 0 17.385 0ZM17.385 23.9524C12.562 23.9524 8.63826 20.3559 8.63826 15.9351C8.63826 11.5143 12.5621 7.91778 17.385 7.91778C22.2079 7.91778 26.1316 11.5143 26.1316 15.9352C26.1316 20.356 22.2079 23.9524 17.385 23.9524Z",fill:"#56CCF2"}),r.jsxs("g",{clipPath:"url(#clip0_8144_7816)",children:[r.jsx("rect",{x:"10.873",y:"9.10352",width:"13.9795",height:"13.6552",rx:"6.82759",fill:"white"}),r.jsx("path",{d:"M13.203 15.3622V13.6554H17.4493C17.0008 12.176 17.5716 11.0324 17.6648 10.8276C12.6031 10.6228 12.0381 11.9598 12.0381 13.0864V18.4916C12.0381 19.0321 12.2594 19.5214 12.6206 19.8798V20.7674C12.6206 21.2397 13.0108 21.6209 13.4943 21.6209C13.9777 21.6209 14.368 21.2397 14.368 20.7674V20.4829H19.0278V20.7674C19.0278 21.234 19.4181 21.6209 19.9015 21.6209C20.3792 21.6209 20.7752 21.2397 20.7752 20.7674V19.8798C21.1364 19.5214 21.3577 19.0321 21.3577 18.4916V16.5002C20.2452 16.5002 19.2433 16.0678 18.5094 15.3622H13.203ZM14.6592 18.776C14.1758 18.776 13.7855 18.3948 13.7855 17.9226C13.7855 17.4504 14.1758 17.0691 14.6592 17.0691C15.1427 17.0691 15.5329 17.4504 15.5329 17.9226C15.5329 18.3948 15.1427 18.776 14.6592 18.776ZM19.6103 17.9226C19.6103 18.3948 19.22 18.776 18.7366 18.776C18.2531 18.776 17.8629 18.3948 17.8629 17.9226C17.8629 17.4504 18.2531 17.0691 18.7366 17.0691C19.22 17.0691 19.6103 17.4504 19.6103 17.9226Z",fill:"#56CCF2"}),r.jsx("path",{d:"M21.3577 9.67236C19.7501 9.67236 18.4453 10.9468 18.4453 12.5172C18.4453 14.0875 19.7501 15.362 21.3577 15.362C22.9653 15.362 24.2701 14.0875 24.2701 12.5172C24.2701 10.9468 22.9653 9.67236 21.3577 9.67236ZM21.6489 12.8017C21.6489 12.961 21.5208 13.0862 21.3577 13.0862C21.1946 13.0862 21.0665 12.961 21.0665 12.8017V11.0948C21.0665 10.9355 21.1946 10.8103 21.3577 10.8103C21.5208 10.8103 21.6489 10.9355 21.6489 11.0948V12.8017ZM21.6489 13.9396C21.6489 14.0989 21.5208 14.2241 21.3577 14.2241C21.1946 14.2241 21.0665 14.0989 21.0665 13.9396C21.0665 13.7803 21.1946 13.6551 21.3577 13.6551C21.5208 13.6551 21.6489 13.7803 21.6489 13.9396Z",fill:"#56CCF2"})]}),r.jsx("defs",{children:r.jsx("clipPath",{id:"clip0_8144_7816",children:r.jsx("rect",{x:"10.873",y:"9.10352",width:"13.9795",height:"13.6552",rx:"6.82759",fill:"white"})})})]}),r.jsx("h1",{className:"mt-2 w-max rounded bg-[#ffffffcd] px-2 text-sm ",children:e.service_name})]})},s))})};export{J as default};
