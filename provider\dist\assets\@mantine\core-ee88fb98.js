import{r as y,R as s}from"../vendor-f36d475e.js";import{s as tr}from"../@emotion/serialize-460cad7f.js";import{i as nr,g as ar}from"../@emotion/utils-47271980.js";import{c as or}from"../@emotion/cache-9a5b99cd.js";function ae(e){var r,t,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e))for(r=0;r<e.length;r++)e[r]&&(t=ae(e[r]))&&(n&&(n+=" "),n+=t);else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function ir(){for(var e=0,r,t,n="";e<arguments.length;)(r=arguments[e++])&&(t=ae(r))&&(n&&(n+=" "),n+=t);return n}const fr={dark:["#C1C2C5","#A6A7AB","#909296","#5c5f66","#373A40","#2C2E33","#25262b","#1A1B1E","#141517","#101113"],gray:["#f8f9fa","#f1f3f5","#e9ecef","#dee2e6","#ced4da","#adb5bd","#868e96","#495057","#343a40","#212529"],red:["#fff5f5","#ffe3e3","#ffc9c9","#ffa8a8","#ff8787","#ff6b6b","#fa5252","#f03e3e","#e03131","#c92a2a"],pink:["#fff0f6","#ffdeeb","#fcc2d7","#faa2c1","#f783ac","#f06595","#e64980","#d6336c","#c2255c","#a61e4d"],grape:["#f8f0fc","#f3d9fa","#eebefa","#e599f7","#da77f2","#cc5de8","#be4bdb","#ae3ec9","#9c36b5","#862e9c"],violet:["#f3f0ff","#e5dbff","#d0bfff","#b197fc","#9775fa","#845ef7","#7950f2","#7048e8","#6741d9","#5f3dc4"],indigo:["#edf2ff","#dbe4ff","#bac8ff","#91a7ff","#748ffc","#5c7cfa","#4c6ef5","#4263eb","#3b5bdb","#364fc7"],blue:["#e7f5ff","#d0ebff","#a5d8ff","#74c0fc","#4dabf7","#339af0","#228be6","#1c7ed6","#1971c2","#1864ab"],cyan:["#e3fafc","#c5f6fa","#99e9f2","#66d9e8","#3bc9db","#22b8cf","#15aabf","#1098ad","#0c8599","#0b7285"],teal:["#e6fcf5","#c3fae8","#96f2d7","#63e6be","#38d9a9","#20c997","#12b886","#0ca678","#099268","#087f5b"],green:["#ebfbee","#d3f9d8","#b2f2bb","#8ce99a","#69db7c","#51cf66","#40c057","#37b24d","#2f9e44","#2b8a3e"],lime:["#f4fce3","#e9fac8","#d8f5a2","#c0eb75","#a9e34b","#94d82d","#82c91e","#74b816","#66a80f","#5c940d"],yellow:["#fff9db","#fff3bf","#ffec99","#ffe066","#ffd43b","#fcc419","#fab005","#f59f00","#f08c00","#e67700"],orange:["#fff4e6","#ffe8cc","#ffd8a8","#ffc078","#ffa94d","#ff922b","#fd7e14","#f76707","#e8590c","#d9480f"]};function sr(e){return()=>({fontFamily:e.fontFamily||"sans-serif"})}var lr=Object.defineProperty,N=Object.getOwnPropertySymbols,cr=Object.prototype.hasOwnProperty,pr=Object.prototype.propertyIsEnumerable,z=(e,r,t)=>r in e?lr(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,M=(e,r)=>{for(var t in r||(r={}))cr.call(r,t)&&z(e,t,r[t]);if(N)for(var t of N(r))pr.call(r,t)&&z(e,t,r[t]);return e};function dr(e){return r=>({WebkitTapHighlightColor:"transparent",[r||"&:focus"]:M({},e.focusRing==="always"||e.focusRing==="auto"?e.focusRingStyles.styles(e):e.focusRingStyles.resetStyles(e)),[r?r.replace(":focus",":focus:not(:focus-visible)"):"&:focus:not(:focus-visible)"]:M({},e.focusRing==="auto"||e.focusRing==="never"?e.focusRingStyles.resetStyles(e):null)})}function b(e){return r=>typeof e.primaryShade=="number"?e.primaryShade:e.primaryShade[r||e.colorScheme]}function x(e){const r=b(e);return(t,n,o=!0,a=!0)=>{if(typeof t=="string"&&t.includes(".")){const[f,c]=t.split("."),p=parseInt(c,10);if(f in e.colors&&p>=0&&p<10)return e.colors[f][typeof n=="number"&&!a?n:p]}const i=typeof n=="number"?n:r();return t in e.colors?e.colors[t][i]:o?e.colors[e.primaryColor][i]:t}}function oe(e){let r="";for(let t=1;t<e.length-1;t+=1)r+=`${e[t]} ${t/(e.length-1)*100}%, `;return`${e[0]} 0%, ${r}${e[e.length-1]} 100%`}function ur(e,...r){return`linear-gradient(${e}deg, ${oe(r)})`}function gr(...e){return`radial-gradient(circle, ${oe(e)})`}function ie(e){const r=x(e),t=b(e);return n=>{const o={from:(n==null?void 0:n.from)||e.defaultGradient.from,to:(n==null?void 0:n.to)||e.defaultGradient.to,deg:(n==null?void 0:n.deg)||e.defaultGradient.deg};return`linear-gradient(${o.deg}deg, ${r(o.from,t(),!1)} 0%, ${r(o.to,t(),!1)} 100%)`}}function fe(e){return r=>{if(typeof r=="number")return`${r/16}${e}`;if(typeof r=="string"){const t=r.replace("px","");if(!Number.isNaN(Number(t)))return`${Number(t)/16}${e}`}return r}}const d=fe("rem"),k=fe("em");function g({size:e,sizes:r,units:t}){return e in r?r[e]:typeof e=="number"?t==="em"?k(e):d(e):e||r.md}function _(e){return typeof e=="number"?e:typeof e=="string"&&e.includes("rem")?Number(e.replace("rem",""))*16:typeof e=="string"&&e.includes("em")?Number(e.replace("em",""))*16:Number(e)}function mr(e){return r=>`@media (min-width: ${k(_(g({size:r,sizes:e.breakpoints})))})`}function yr(e){return r=>`@media (max-width: ${k(_(g({size:r,sizes:e.breakpoints}))-1)})`}function br(e){return/^#?([0-9A-F]{3}){1,2}$/i.test(e)}function vr(e){let r=e.replace("#","");if(r.length===3){const i=r.split("");r=[i[0],i[0],i[1],i[1],i[2],i[2]].join("")}const t=parseInt(r,16),n=t>>16&255,o=t>>8&255,a=t&255;return{r:n,g:o,b:a,a:1}}function _r(e){const[r,t,n,o]=e.replace(/[^0-9,.]/g,"").split(",").map(Number);return{r,g:t,b:n,a:o||1}}function R(e){return br(e)?vr(e):e.startsWith("rgb")?_r(e):{r:0,g:0,b:0,a:1}}function m(e,r){if(typeof e!="string"||r>1||r<0)return"rgba(0, 0, 0, 1)";if(e.startsWith("var(--"))return e;const{r:t,g:n,b:o}=R(e);return`rgba(${t}, ${n}, ${o}, ${r})`}function hr(e=0){return{position:"absolute",top:d(e),right:d(e),left:d(e),bottom:d(e)}}function Sr(e,r){if(typeof e=="string"&&e.startsWith("var(--"))return e;const{r:t,g:n,b:o,a}=R(e),i=1-r,f=c=>Math.round(c*i);return`rgba(${f(t)}, ${f(n)}, ${f(o)}, ${a})`}function Or(e,r){if(typeof e=="string"&&e.startsWith("var(--"))return e;const{r:t,g:n,b:o,a}=R(e),i=f=>Math.round(f+(255-f)*r);return`rgba(${i(t)}, ${i(n)}, ${i(o)}, ${a})`}function wr(e){return r=>{if(typeof r=="number")return d(r);const t=typeof e.defaultRadius=="number"?e.defaultRadius:e.radius[e.defaultRadius]||e.defaultRadius;return e.radius[r]||r||t}}function $r(e,r){if(typeof e=="string"&&e.includes(".")){const[t,n]=e.split("."),o=parseInt(n,10);if(t in r.colors&&o>=0&&o<10)return{isSplittedColor:!0,key:t,shade:o}}return{isSplittedColor:!1}}function Er(e){const r=x(e),t=b(e),n=ie(e);return({variant:o,color:a,gradient:i,primaryFallback:f})=>{const c=$r(a,e);switch(o){case"light":return{border:"transparent",background:m(r(a,e.colorScheme==="dark"?8:0,f,!1),e.colorScheme==="dark"?.2:1),color:a==="dark"?e.colorScheme==="dark"?e.colors.dark[0]:e.colors.dark[9]:r(a,e.colorScheme==="dark"?2:t("light")),hover:m(r(a,e.colorScheme==="dark"?7:1,f,!1),e.colorScheme==="dark"?.25:.65)};case"subtle":return{border:"transparent",background:"transparent",color:a==="dark"?e.colorScheme==="dark"?e.colors.dark[0]:e.colors.dark[9]:r(a,e.colorScheme==="dark"?2:t("light")),hover:m(r(a,e.colorScheme==="dark"?8:0,f,!1),e.colorScheme==="dark"?.2:1)};case"outline":return{border:r(a,e.colorScheme==="dark"?5:t("light")),background:"transparent",color:r(a,e.colorScheme==="dark"?5:t("light")),hover:e.colorScheme==="dark"?m(r(a,5,f,!1),.05):m(r(a,0,f,!1),.35)};case"default":return{border:e.colorScheme==="dark"?e.colors.dark[4]:e.colors.gray[4],background:e.colorScheme==="dark"?e.colors.dark[6]:e.white,color:e.colorScheme==="dark"?e.white:e.black,hover:e.colorScheme==="dark"?e.colors.dark[5]:e.colors.gray[0]};case"white":return{border:"transparent",background:e.white,color:r(a,t()),hover:null};case"transparent":return{border:"transparent",color:a==="dark"?e.colorScheme==="dark"?e.colors.dark[0]:e.colors.dark[9]:r(a,e.colorScheme==="dark"?2:t("light")),background:"transparent",hover:null};case"gradient":return{background:n(i),color:e.white,border:"transparent",hover:null};default:{const p=t(),u=c.isSplittedColor?c.shade:p,v=c.isSplittedColor?c.key:a;return{border:"transparent",background:r(v,u,f),color:e.white,hover:r(v,u===9?8:u+1)}}}}}function Pr(e){return r=>{const t=b(e)(r);return e.colors[e.primaryColor][t]}}function Cr(e){return{"@media (hover: hover)":{"&:hover":e},"@media (hover: none)":{"&:active":e}}}function xr(e){return()=>({userSelect:"none",color:e.colorScheme==="dark"?e.colors.dark[3]:e.colors.gray[5]})}function kr(e){return()=>e.colorScheme==="dark"?e.colors.dark[2]:e.colors.gray[6]}const l={fontStyles:sr,themeColor:x,focusStyles:dr,linearGradient:ur,radialGradient:gr,smallerThan:yr,largerThan:mr,rgba:m,cover:hr,darken:Sr,lighten:Or,radius:wr,variant:Er,primaryShade:b,hover:Cr,gradient:ie,primaryColor:Pr,placeholderStyles:xr,dimmed:kr};var Rr=Object.defineProperty,jr=Object.defineProperties,Nr=Object.getOwnPropertyDescriptors,I=Object.getOwnPropertySymbols,zr=Object.prototype.hasOwnProperty,Mr=Object.prototype.propertyIsEnumerable,T=(e,r,t)=>r in e?Rr(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,Ir=(e,r)=>{for(var t in r||(r={}))zr.call(r,t)&&T(e,t,r[t]);if(I)for(var t of I(r))Mr.call(r,t)&&T(e,t,r[t]);return e},Tr=(e,r)=>jr(e,Nr(r));function Ar(e){return Tr(Ir({},e),{fn:{fontStyles:l.fontStyles(e),themeColor:l.themeColor(e),focusStyles:l.focusStyles(e),largerThan:l.largerThan(e),smallerThan:l.smallerThan(e),radialGradient:l.radialGradient,linearGradient:l.linearGradient,gradient:l.gradient(e),rgba:l.rgba,cover:l.cover,lighten:l.lighten,darken:l.darken,primaryShade:l.primaryShade(e),radius:l.radius(e),variant:l.variant(e),hover:l.hover,primaryColor:l.primaryColor(e),placeholderStyles:l.placeholderStyles(e),dimmed:l.dimmed(e)}})}const Vr={dir:"ltr",primaryShade:{light:6,dark:8},focusRing:"auto",loader:"oval",colorScheme:"light",white:"#fff",black:"#000",defaultRadius:"sm",transitionTimingFunction:"ease",colors:fr,lineHeight:1.55,fontFamily:"-apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji",fontFamilyMonospace:"ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace",primaryColor:"blue",respectReducedMotion:!0,cursorType:"default",defaultGradient:{from:"indigo",to:"cyan",deg:45},shadows:{xs:"0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.05), 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.1)",sm:"0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 0 0.625rem 0.9375rem -0.3125rem, rgba(0, 0, 0, 0.04) 0 0.4375rem 0.4375rem -0.3125rem",md:"0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 0 1.25rem 1.5625rem -0.3125rem, rgba(0, 0, 0, 0.04) 0 0.625rem 0.625rem -0.3125rem",lg:"0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 0 1.75rem 1.4375rem -0.4375rem, rgba(0, 0, 0, 0.04) 0 0.75rem 0.75rem -0.4375rem",xl:"0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 0 2.25rem 1.75rem -0.4375rem, rgba(0, 0, 0, 0.04) 0 1.0625rem 1.0625rem -0.4375rem"},fontSizes:{xs:"0.75rem",sm:"0.875rem",md:"1rem",lg:"1.125rem",xl:"1.25rem"},radius:{xs:"0.125rem",sm:"0.25rem",md:"0.5rem",lg:"1rem",xl:"2rem"},spacing:{xs:"0.625rem",sm:"0.75rem",md:"1rem",lg:"1.25rem",xl:"1.5rem"},breakpoints:{xs:"36em",sm:"48em",md:"62em",lg:"75em",xl:"88em"},headings:{fontFamily:"-apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji",fontWeight:700,sizes:{h1:{fontSize:"2.125rem",lineHeight:1.3,fontWeight:void 0},h2:{fontSize:"1.625rem",lineHeight:1.35,fontWeight:void 0},h3:{fontSize:"1.375rem",lineHeight:1.4,fontWeight:void 0},h4:{fontSize:"1.125rem",lineHeight:1.45,fontWeight:void 0},h5:{fontSize:"1rem",lineHeight:1.5,fontWeight:void 0},h6:{fontSize:"0.875rem",lineHeight:1.5,fontWeight:void 0}}},other:{},components:{},activeStyles:{transform:"translateY(0.0625rem)"},datesLocale:"en",globalStyles:void 0,focusRingStyles:{styles:e=>({outlineOffset:"0.125rem",outline:`0.125rem solid ${e.colors[e.primaryColor][e.colorScheme==="dark"?7:5]}`}),resetStyles:()=>({outline:"none"}),inputStyles:e=>({outline:"none",borderColor:e.colors[e.primaryColor][typeof e.primaryShade=="object"?e.primaryShade[e.colorScheme]:e.primaryShade]})}},se=Ar(Vr);function le(e){return Object.keys(e).reduce((r,t)=>(e[t]!==void 0&&(r[t]=e[t]),r),{})}var Hr=Object.defineProperty,A=Object.getOwnPropertySymbols,Gr=Object.prototype.hasOwnProperty,Lr=Object.prototype.propertyIsEnumerable,V=(e,r,t)=>r in e?Hr(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,E=(e,r)=>{for(var t in r||(r={}))Gr.call(r,t)&&V(e,t,r[t]);if(A)for(var t of A(r))Lr.call(r,t)&&V(e,t,r[t]);return e};const ce=y.createContext({theme:se});function j(){var e;return((e=y.useContext(ce))==null?void 0:e.theme)||se}function Fr(){var e;return(e=y.useContext(ce))==null?void 0:e.emotionCache}function Wr(e,r,t){var n;const o=j(),a=(n=o.components[e])==null?void 0:n.defaultProps,i=typeof a=="function"?a(o):a;return E(E(E({},r),i),le(t))}function Dr(e,r){const t=y.useRef();return(!t.current||r.length!==t.current.prevDeps.length||t.current.prevDeps.map((n,o)=>n===r[o]).indexOf(!1)>=0)&&(t.current={v:e(),prevDeps:[...r]}),t.current.v}const Ur=or({key:"mantine",prepend:!0});function Br(){return Fr()||Ur}var Xr=Object.defineProperty,H=Object.getOwnPropertySymbols,Yr=Object.prototype.hasOwnProperty,qr=Object.prototype.propertyIsEnumerable,G=(e,r,t)=>r in e?Xr(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,Jr=(e,r)=>{for(var t in r||(r={}))Yr.call(r,t)&&G(e,t,r[t]);if(H)for(var t of H(r))qr.call(r,t)&&G(e,t,r[t]);return e};const P="ref";function Qr(e){let r;if(e.length!==1)return{args:e,ref:r};const[t]=e;if(!(t instanceof Object))return{args:e,ref:r};if(!(P in t))return{args:e,ref:r};r=t[P];const n=Jr({},t);return delete n[P],{args:[n],ref:r}}const{cssFactory:Zr}=(()=>{function e(t,n,o){const a=[],i=ar(t,a,o);return a.length<2?o:i+n(a)}function r(t){const{cache:n}=t,o=(...i)=>{const{ref:f,args:c}=Qr(i),p=tr(c,n.registered);return nr(n,p,!1),`${n.key}-${p.name}${f===void 0?"":` ${f}`}`};return{css:o,cx:(...i)=>e(n.registered,o,ir(i))}}return{cssFactory:r}})();function Kr(){const e=Br();return Dr(()=>Zr({cache:e}),[e])}var L=Object.getOwnPropertySymbols,et=Object.prototype.hasOwnProperty,rt=Object.prototype.propertyIsEnumerable,tt=(e,r)=>{var t={};for(var n in e)et.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(e!=null&&L)for(var n of L(e))r.indexOf(n)<0&&rt.call(e,n)&&(t[n]=e[n]);return t};function nt(e){const r=e,{m:t,mx:n,my:o,mt:a,mb:i,ml:f,mr:c,p,px:u,py:v,pt:Oe,pb:we,pl:$e,pr:Ee,bg:Pe,c:Ce,opacity:xe,ff:ke,fz:Re,fw:je,lts:Ne,ta:ze,lh:Me,fs:Ie,tt:Te,td:Ae,w:Ve,miw:He,maw:Ge,h:Le,mih:Fe,mah:We,bgsz:De,bgp:Ue,bgr:Be,bga:Xe,pos:Ye,top:qe,left:Je,bottom:Qe,right:Ze,inset:Ke,display:er}=r,rr=tt(r,["m","mx","my","mt","mb","ml","mr","p","px","py","pt","pb","pl","pr","bg","c","opacity","ff","fz","fw","lts","ta","lh","fs","tt","td","w","miw","maw","h","mih","mah","bgsz","bgp","bgr","bga","pos","top","left","bottom","right","inset","display"]);return{systemStyles:le({m:t,mx:n,my:o,mt:a,mb:i,ml:f,mr:c,p,px:u,py:v,pt:Oe,pb:we,pl:$e,pr:Ee,bg:Pe,c:Ce,opacity:xe,ff:ke,fz:Re,fw:je,lts:Ne,ta:ze,lh:Me,fs:Ie,tt:Te,td:Ae,w:Ve,miw:He,maw:Ge,h:Le,mih:Fe,mah:We,bgsz:De,bgp:Ue,bgr:Be,bga:Xe,pos:Ye,top:qe,left:Je,bottom:Qe,right:Ze,inset:Ke,display:er}),rest:rr}}function at(e,r){const t=Object.keys(e).filter(n=>n!=="base").sort((n,o)=>_(g({size:n,sizes:r.breakpoints}))-_(g({size:o,sizes:r.breakpoints})));return"base"in e?["base",...t]:t}function ot({value:e,theme:r,getValue:t,property:n}){if(e==null)return;if(typeof e=="object")return at(e,r).reduce((i,f)=>{if(f==="base"&&e.base!==void 0){const p=t(e.base,r);return Array.isArray(n)?(n.forEach(u=>{i[u]=p}),i):(i[n]=p,i)}const c=t(e[f],r);return Array.isArray(n)?(i[r.fn.largerThan(f)]={},n.forEach(p=>{i[r.fn.largerThan(f)][p]=c}),i):(i[r.fn.largerThan(f)]={[n]:c},i)},{});const o=t(e,r);return Array.isArray(n)?n.reduce((a,i)=>(a[i]=o,a),{}):{[n]:o}}function it(e,r){return e==="dimmed"?r.colorScheme==="dark"?r.colors.dark[2]:r.colors.gray[6]:r.fn.variant({variant:"filled",color:e,primaryFallback:!1}).background}function ft(e){return d(e)}function st(e){return e}function lt(e,r){return g({size:e,sizes:r.fontSizes})}const ct=["-xs","-sm","-md","-lg","-xl"];function pt(e,r){return ct.includes(e)?`calc(${g({size:e.replace("-",""),sizes:r.spacing})} * -1)`:g({size:e,sizes:r.spacing})}const dt={identity:st,color:it,size:ft,fontSize:lt,spacing:pt},ut={m:{type:"spacing",property:"margin"},mt:{type:"spacing",property:"marginTop"},mb:{type:"spacing",property:"marginBottom"},ml:{type:"spacing",property:"marginLeft"},mr:{type:"spacing",property:"marginRight"},mx:{type:"spacing",property:["marginRight","marginLeft"]},my:{type:"spacing",property:["marginTop","marginBottom"]},p:{type:"spacing",property:"padding"},pt:{type:"spacing",property:"paddingTop"},pb:{type:"spacing",property:"paddingBottom"},pl:{type:"spacing",property:"paddingLeft"},pr:{type:"spacing",property:"paddingRight"},px:{type:"spacing",property:["paddingRight","paddingLeft"]},py:{type:"spacing",property:["paddingTop","paddingBottom"]},bg:{type:"color",property:"background"},c:{type:"color",property:"color"},opacity:{type:"identity",property:"opacity"},ff:{type:"identity",property:"fontFamily"},fz:{type:"fontSize",property:"fontSize"},fw:{type:"identity",property:"fontWeight"},lts:{type:"size",property:"letterSpacing"},ta:{type:"identity",property:"textAlign"},lh:{type:"identity",property:"lineHeight"},fs:{type:"identity",property:"fontStyle"},tt:{type:"identity",property:"textTransform"},td:{type:"identity",property:"textDecoration"},w:{type:"spacing",property:"width"},miw:{type:"spacing",property:"minWidth"},maw:{type:"spacing",property:"maxWidth"},h:{type:"spacing",property:"height"},mih:{type:"spacing",property:"minHeight"},mah:{type:"spacing",property:"maxHeight"},bgsz:{type:"size",property:"backgroundSize"},bgp:{type:"identity",property:"backgroundPosition"},bgr:{type:"identity",property:"backgroundRepeat"},bga:{type:"identity",property:"backgroundAttachment"},pos:{type:"identity",property:"position"},top:{type:"identity",property:"top"},left:{type:"size",property:"left"},bottom:{type:"size",property:"bottom"},right:{type:"size",property:"right"},inset:{type:"size",property:"inset"},display:{type:"identity",property:"display"}};var gt=Object.defineProperty,F=Object.getOwnPropertySymbols,mt=Object.prototype.hasOwnProperty,yt=Object.prototype.propertyIsEnumerable,W=(e,r,t)=>r in e?gt(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,D=(e,r)=>{for(var t in r||(r={}))mt.call(r,t)&&W(e,t,r[t]);if(F)for(var t of F(r))yt.call(r,t)&&W(e,t,r[t]);return e};function U(e,r,t=ut){return Object.keys(t).reduce((o,a)=>(a in e&&e[a]!==void 0&&o.push(ot({value:e[a],getValue:dt[t[a].type],property:t[a].property,theme:r})),o),[]).reduce((o,a)=>(Object.keys(a).forEach(i=>{typeof a[i]=="object"&&a[i]!==null&&i in o?o[i]=D(D({},o[i]),a[i]):o[i]=a[i]}),o),{})}function B(e,r){return typeof e=="function"?e(r):e}function bt(e,r,t){const n=j(),{css:o,cx:a}=Kr();return Array.isArray(e)?a(t,o(U(r,n)),e.map(i=>o(B(i,n)))):a(t,o(B(e,n)),o(U(r,n)))}var vt=Object.defineProperty,h=Object.getOwnPropertySymbols,pe=Object.prototype.hasOwnProperty,de=Object.prototype.propertyIsEnumerable,X=(e,r,t)=>r in e?vt(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,_t=(e,r)=>{for(var t in r||(r={}))pe.call(r,t)&&X(e,t,r[t]);if(h)for(var t of h(r))de.call(r,t)&&X(e,t,r[t]);return e},ht=(e,r)=>{var t={};for(var n in e)pe.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(e!=null&&h)for(var n of h(e))r.indexOf(n)<0&&de.call(e,n)&&(t[n]=e[n]);return t};const ue=y.forwardRef((e,r)=>{var t=e,{className:n,component:o,style:a,sx:i}=t,f=ht(t,["className","component","style","sx"]);const{systemStyles:c,rest:p}=nt(f),u=o||"div";return s.createElement(u,_t({ref:r,className:bt(i,c,n),style:a},p))});ue.displayName="@mantine/core/Box";const St=ue;var Ot=Object.defineProperty,S=Object.getOwnPropertySymbols,ge=Object.prototype.hasOwnProperty,me=Object.prototype.propertyIsEnumerable,Y=(e,r,t)=>r in e?Ot(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,q=(e,r)=>{for(var t in r||(r={}))ge.call(r,t)&&Y(e,t,r[t]);if(S)for(var t of S(r))me.call(r,t)&&Y(e,t,r[t]);return e},J=(e,r)=>{var t={};for(var n in e)ge.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(e!=null&&S)for(var n of S(e))r.indexOf(n)<0&&me.call(e,n)&&(t[n]=e[n]);return t};function wt(e){var r=e,{size:t,color:n}=r,o=J(r,["size","color"]);const a=o,{style:i}=a,f=J(a,["style"]);return s.createElement("svg",q({viewBox:"0 0 135 140",xmlns:"http://www.w3.org/2000/svg",fill:n,style:q({width:t},i)},f),s.createElement("rect",{y:"10",width:"15",height:"120",rx:"6"},s.createElement("animate",{attributeName:"height",begin:"0.5s",dur:"1s",values:"120;110;100;90;80;70;60;50;40;140;120",calcMode:"linear",repeatCount:"indefinite"}),s.createElement("animate",{attributeName:"y",begin:"0.5s",dur:"1s",values:"10;15;20;25;30;35;40;45;50;0;10",calcMode:"linear",repeatCount:"indefinite"})),s.createElement("rect",{x:"30",y:"10",width:"15",height:"120",rx:"6"},s.createElement("animate",{attributeName:"height",begin:"0.25s",dur:"1s",values:"120;110;100;90;80;70;60;50;40;140;120",calcMode:"linear",repeatCount:"indefinite"}),s.createElement("animate",{attributeName:"y",begin:"0.25s",dur:"1s",values:"10;15;20;25;30;35;40;45;50;0;10",calcMode:"linear",repeatCount:"indefinite"})),s.createElement("rect",{x:"60",width:"15",height:"140",rx:"6"},s.createElement("animate",{attributeName:"height",begin:"0s",dur:"1s",values:"120;110;100;90;80;70;60;50;40;140;120",calcMode:"linear",repeatCount:"indefinite"}),s.createElement("animate",{attributeName:"y",begin:"0s",dur:"1s",values:"10;15;20;25;30;35;40;45;50;0;10",calcMode:"linear",repeatCount:"indefinite"})),s.createElement("rect",{x:"90",y:"10",width:"15",height:"120",rx:"6"},s.createElement("animate",{attributeName:"height",begin:"0.25s",dur:"1s",values:"120;110;100;90;80;70;60;50;40;140;120",calcMode:"linear",repeatCount:"indefinite"}),s.createElement("animate",{attributeName:"y",begin:"0.25s",dur:"1s",values:"10;15;20;25;30;35;40;45;50;0;10",calcMode:"linear",repeatCount:"indefinite"})),s.createElement("rect",{x:"120",y:"10",width:"15",height:"120",rx:"6"},s.createElement("animate",{attributeName:"height",begin:"0.5s",dur:"1s",values:"120;110;100;90;80;70;60;50;40;140;120",calcMode:"linear",repeatCount:"indefinite"}),s.createElement("animate",{attributeName:"y",begin:"0.5s",dur:"1s",values:"10;15;20;25;30;35;40;45;50;0;10",calcMode:"linear",repeatCount:"indefinite"})))}var $t=Object.defineProperty,O=Object.getOwnPropertySymbols,ye=Object.prototype.hasOwnProperty,be=Object.prototype.propertyIsEnumerable,Q=(e,r,t)=>r in e?$t(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,Z=(e,r)=>{for(var t in r||(r={}))ye.call(r,t)&&Q(e,t,r[t]);if(O)for(var t of O(r))be.call(r,t)&&Q(e,t,r[t]);return e},K=(e,r)=>{var t={};for(var n in e)ye.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(e!=null&&O)for(var n of O(e))r.indexOf(n)<0&&be.call(e,n)&&(t[n]=e[n]);return t};function Et(e){var r=e,{size:t,color:n}=r,o=K(r,["size","color"]);const a=o,{style:i}=a,f=K(a,["style"]);return s.createElement("svg",Z({viewBox:"0 0 38 38",xmlns:"http://www.w3.org/2000/svg",stroke:n,style:Z({width:t,height:t},i)},f),s.createElement("g",{fill:"none",fillRule:"evenodd"},s.createElement("g",{transform:"translate(2.5 2.5)",strokeWidth:"5"},s.createElement("circle",{strokeOpacity:".5",cx:"16",cy:"16",r:"16"}),s.createElement("path",{d:"M32 16c0-9.94-8.06-16-16-16"},s.createElement("animateTransform",{attributeName:"transform",type:"rotate",from:"0 16 16",to:"360 16 16",dur:"1s",repeatCount:"indefinite"})))))}var Pt=Object.defineProperty,w=Object.getOwnPropertySymbols,ve=Object.prototype.hasOwnProperty,_e=Object.prototype.propertyIsEnumerable,ee=(e,r,t)=>r in e?Pt(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,re=(e,r)=>{for(var t in r||(r={}))ve.call(r,t)&&ee(e,t,r[t]);if(w)for(var t of w(r))_e.call(r,t)&&ee(e,t,r[t]);return e},te=(e,r)=>{var t={};for(var n in e)ve.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(e!=null&&w)for(var n of w(e))r.indexOf(n)<0&&_e.call(e,n)&&(t[n]=e[n]);return t};function Ct(e){var r=e,{size:t,color:n}=r,o=te(r,["size","color"]);const a=o,{style:i}=a,f=te(a,["style"]);return s.createElement("svg",re({viewBox:"0 0 120 30",xmlns:"http://www.w3.org/2000/svg",fill:n,style:re({width:t},i)},f),s.createElement("circle",{cx:"15",cy:"15",r:"15"},s.createElement("animate",{attributeName:"r",from:"15",to:"15",begin:"0s",dur:"0.8s",values:"15;9;15",calcMode:"linear",repeatCount:"indefinite"}),s.createElement("animate",{attributeName:"fill-opacity",from:"1",to:"1",begin:"0s",dur:"0.8s",values:"1;.5;1",calcMode:"linear",repeatCount:"indefinite"})),s.createElement("circle",{cx:"60",cy:"15",r:"9",fillOpacity:"0.3"},s.createElement("animate",{attributeName:"r",from:"9",to:"9",begin:"0s",dur:"0.8s",values:"9;15;9",calcMode:"linear",repeatCount:"indefinite"}),s.createElement("animate",{attributeName:"fill-opacity",from:"0.5",to:"0.5",begin:"0s",dur:"0.8s",values:".5;1;.5",calcMode:"linear",repeatCount:"indefinite"})),s.createElement("circle",{cx:"105",cy:"15",r:"15"},s.createElement("animate",{attributeName:"r",from:"15",to:"15",begin:"0s",dur:"0.8s",values:"15;9;15",calcMode:"linear",repeatCount:"indefinite"}),s.createElement("animate",{attributeName:"fill-opacity",from:"1",to:"1",begin:"0s",dur:"0.8s",values:"1;.5;1",calcMode:"linear",repeatCount:"indefinite"})))}var xt=Object.defineProperty,$=Object.getOwnPropertySymbols,he=Object.prototype.hasOwnProperty,Se=Object.prototype.propertyIsEnumerable,ne=(e,r,t)=>r in e?xt(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,kt=(e,r)=>{for(var t in r||(r={}))he.call(r,t)&&ne(e,t,r[t]);if($)for(var t of $(r))Se.call(r,t)&&ne(e,t,r[t]);return e},Rt=(e,r)=>{var t={};for(var n in e)he.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(e!=null&&$)for(var n of $(e))r.indexOf(n)<0&&Se.call(e,n)&&(t[n]=e[n]);return t};const C={bars:wt,oval:Et,dots:Ct},jt={xs:d(18),sm:d(22),md:d(36),lg:d(44),xl:d(58)},Nt={size:"md"};function zt(e){const r=Wr("Loader",Nt,e),{size:t,color:n,variant:o}=r,a=Rt(r,["size","color","variant"]),i=j(),f=o in C?o:i.loader;return s.createElement(St,kt({role:"presentation",component:C[f]||C.bars,size:g({size:t,sizes:jt}),color:i.fn.variant({variant:"filled",primaryFallback:!1,color:n||i.primaryColor}).background},a))}zt.displayName="@mantine/core/Loader";export{zt as L};
