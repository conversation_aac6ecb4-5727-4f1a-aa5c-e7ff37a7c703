import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{r as s,d as R}from"./vendor-f36d475e.js";import{A as E,M as z,a as K}from"./@vis.gl/react-google-maps-e1ad9da7.js";import{a as O}from"./index-895fa99b.js";import{M as B}from"./index-cf5e6bc7.js";import{S as F}from"./index-65bc3378.js";import"./@googlemaps/markerclusterer-2f9a3a53.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./react-i18next-1e3e6bc5.js";const u=(i,t)=>{const r=i.find(l=>l.types.includes(t));return r?r.long_name:null},G=new B,ct=({handleChoose:i})=>{const[t,r]=s.useState(),[l,g]=s.useState(!0),[f,Z]=s.useState(14),[x,h]=s.useState(""),[C,S]=s.useState(""),[v,P]=s.useState(""),[_,k]=s.useState(""),c=s.useRef(null),a=s.useRef(null);R();const L=o=>{c.current=o,a.current=new window.google.maps.Marker({position:t,map:o,draggable:!0}),a.current.addListener("dragend",n=>{const d={lat:n.latLng.lat(),lng:n.latLng.lng()};r(d)})},b=o=>{const n={lat:o.detail.latLng.lat,lng:o.detail.latLng.lng};r(n),a.current&&a.current.setPosition(n),M(n)},M=async o=>{const{lat:n,lng:d}=o;new window.google.maps.Geocoder().geocode({location:{lat:n,lng:d}},(m,N)=>{if(N==="OK"&&m[0]){h(m[0].formatted_address);const p=m[0],y=u(p.address_components,"locality"),w=u(p.address_components,"administrative_area_level_1"),j=u(p.address_components,"country");S(y),P(w),k(j),console.log("City:",y),console.log("State:",w),console.log("Country:",j)}else h("No address found")})},A=()=>{g(!0),navigator.geolocation?navigator.geolocation.getCurrentPosition(o=>{const n={lat:o.coords.latitude,lng:o.coords.longitude};r(n)},o=>{console.log(o.message)}):console.error("Geolocation is not supported by this browser."),g(!1)};return s.useEffect(()=>{A()},[]),s.useEffect(()=>{c.current&&(c.current.setCenter(t),a.current&&a.current.setPosition(t))},[t]),!t&&l?e.jsx(e.Fragment,{children:e.jsx(F,{})}):e.jsx("div",{className:"h-full w-full",children:e.jsx(E,{apiKey:G._google_api_key,children:e.jsxs("div",{className:"relative",style:{width:"100%",height:"100%"},children:[e.jsx(z,{defaultZoom:f,defaultCenter:t,mapOptions:{center:t,zoom:f},options:{zoomControl:!1},style:{width:"100%",height:"100%"},onLoad:L,onClick:b,children:e.jsx(K,{position:t})}),e.jsxs("div",{className:"absolute bottom-0 left-0 z-[99999999999999] w-full rounded-xl bg-white px-[17px] py-[17px]",children:[e.jsx("div",{className:"text-center font-['Poppins'] text-[22px] font-semibold leading-normal text-[#56ccf2]",children:x}),e.jsx("div",{children:e.jsx(O,{onClick:()=>i(t.lat,t.lng,x,C,v,_),children:"Choose this address"})})]})]})})})};export{ct as default};
