import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as c,b as U,r as s,h as q}from"./vendor-4f06b3f4.js";import{u as K}from"./react-hook-form-f3d72793.js";import{o as V}from"./yup-2324a46a.js";import{c as z,a as i}from"./yup-17027d7a.js";import{M as J,A as Q,G as X,t as Y,s as Z}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as r}from"./MkdInput-ff3aa862.js";import{I as ee}from"./InteractiveButton-8f7d74ee.js";import{S as te}from"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";let x=new J;const Oe=p=>{var j,S,_,E,v,I;const{dispatch:D}=c.useContext(Q),k=z({first_name:i(),last_name:i(),company_id:i(),website:i(),email:i(),phone:i(),description:i(),notes:i(),data:i(),status:i()}).required(),{dispatch:h}=c.useContext(X),[y,ae]=c.useState({}),[N,b]=c.useState(!1),[F,g]=c.useState(!1),C=U(),[se,P]=s.useState(""),[oe,A]=s.useState(""),[le,R]=s.useState(0),[ie,T]=s.useState(""),[ne,L]=s.useState(""),[re,$]=s.useState(""),[me,M]=s.useState(""),[de,G]=s.useState(""),[ce,O]=s.useState(""),[pe,W]=s.useState(""),{register:o,handleSubmit:B,setError:w,setValue:l,formState:{errors:a}}=K({resolver:V(k)}),m=q();s.useEffect(function(){(async function(){try{g(!0),x.setTable("contact");const e=await x.callRestAPI({id:p.activeId?p.activeId:Number(m==null?void 0:m.id)},"GET");e.error||(l("first_name",e.model.first_name),l("last_name",e.model.last_name),l("company_id",e.model.company_id),l("website",e.model.website),l("email",e.model.email),l("phone",e.model.phone),l("description",e.model.description),l("notes",e.model.notes),l("data",e.model.data),l("status",e.model.status),P(e.model.first_name),A(e.model.last_name),R(e.model.company_id),T(e.model.website),L(e.model.email),$(e.model.phone),M(e.model.description),G(e.model.notes),O(e.model.data),W(e.model.status),g(!1))}catch(e){g(!1),console.log("error",e),Y(D,e.message)}})()},[]);const H=async e=>{b(!0);try{x.setTable("contact");for(let u in y){let d=new FormData;d.append("file",y[u].file);let f=await x.uploadImage(d);e[u]=f.url}const n=await x.callRestAPI({id:p.activeId?p.activeId:Number(m==null?void 0:m.id),first_name:e.first_name,last_name:e.last_name,company_id:e.company_id,website:e.website,email:e.email,phone:e.phone,description:e.description,notes:e.notes,data:e.data,status:e.status},"PUT");if(!n.error)Z(h,"Updated"),C("/admin/contact"),h({type:"REFRESH_DATA",payload:{refreshData:!0}}),p.setSidebar(!1);else if(n.validation){const u=Object.keys(n.validation);for(let d=0;d<u.length;d++){const f=u[d];w(f,{type:"manual",message:n.validation[f]})}}b(!1)}catch(n){b(!1),console.log("Error",n),w("first_name",{type:"manual",message:n.message})}};return c.useEffect(()=>{h({type:"SETPATH",payload:{path:"contact"}})},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Contact"}),F?t.jsx(te,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:B(H),children:[t.jsx(r,{type:"text",page:"edit",name:"first_name",errors:a,label:"First Name",placeholder:"First Name",register:o,className:""}),t.jsx(r,{type:"text",page:"edit",name:"last_name",errors:a,label:"Last Name",placeholder:"Last Name",register:o,className:""}),t.jsx(r,{type:"number",page:"edit",name:"company_id",errors:a,label:"Company Id",placeholder:"Company Id",register:o,className:""}),t.jsx(r,{type:"text",page:"edit",name:"website",errors:a,label:"Website",placeholder:"Website",register:o,className:""}),t.jsx(r,{type:"text",page:"edit",name:"email",errors:a,label:"Email",placeholder:"Email",register:o,className:""}),t.jsx(r,{type:"text",page:"edit",name:"phone",errors:a,label:"Phone",placeholder:"Phone",register:o,className:""}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"description",children:"Description"}),t.jsx("textarea",{placeholder:"Description",...o("description"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(j=a.description)!=null&&j.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(S=a.description)==null?void 0:S.message})]}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"notes",children:"Notes"}),t.jsx("textarea",{placeholder:"Notes",...o("notes"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(_=a.notes)!=null&&_.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(E=a.notes)==null?void 0:E.message})]}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"data",children:"Data"}),t.jsx("textarea",{placeholder:"Data",...o("data"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(v=a.data)!=null&&v.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(I=a.data)==null?void 0:I.message})]}),t.jsx(r,{type:"number",page:"edit",name:"status",errors:a,label:"Status",placeholder:"Status",register:o,className:""}),t.jsx(ee,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:N,disable:N,children:"Submit"})]})]})};export{Oe as default};
