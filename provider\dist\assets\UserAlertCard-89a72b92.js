import{j as C}from"./@react-google-maps/api-afbf18d5.js";import{L as n}from"./vendor-f36d475e.js";import{f as l}from"./index-cf5e6bc7.js";import{u as o}from"./react-i18next-1e3e6bc5.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";const r={paint:C.jsx("svg",{width:"28",height:"28",viewBox:"0 0 28 28",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:C.jsx("path",{d:"M6.125 1.75C5.89294 1.75 5.67038 1.84219 5.50628 2.00628C5.34219 2.17038 5.25 2.39294 5.25 2.625V15.75C5.25 16.6783 5.61875 17.5685 6.27513 18.2249C6.9315 18.8813 7.82174 19.25 8.75 19.25H10.5V22.75C10.5 23.6783 10.8687 24.5685 11.5251 25.2249C12.1815 25.8813 13.0717 26.25 14 26.25C14.9283 26.25 15.8185 25.8813 16.4749 25.2249C17.1313 24.5685 17.5 23.6783 17.5 22.75V19.25H19.25C20.1783 19.25 21.0685 18.8813 21.7249 18.2249C22.3813 17.5685 22.75 16.6783 22.75 15.75V2.625C22.75 2.39294 22.6578 2.17038 22.4937 2.00628C22.3296 1.84219 22.1071 1.75 21.875 1.75H6.125ZM7 12.25V3.5H14V6.125C14 6.35706 14.0922 6.57962 14.2563 6.74372C14.4204 6.90781 14.6429 7 14.875 7C15.1071 7 15.3296 6.90781 15.4937 6.74372C15.6578 6.57962 15.75 6.35706 15.75 6.125V3.5H17.5V7.875C17.5 8.10706 17.5922 8.32962 17.7563 8.49372C17.9204 8.65781 18.1429 8.75 18.375 8.75C18.6071 8.75 18.8296 8.65781 18.9937 8.49372C19.1578 8.32962 19.25 8.10706 19.25 7.875V3.5H21V12.25H7ZM7 14H21V15.75C21 16.2141 20.8156 16.6592 20.4874 16.9874C20.1592 17.3156 19.7141 17.5 19.25 17.5H16.625C16.3929 17.5 16.1704 17.5922 16.0063 17.7563C15.8422 17.9204 15.75 18.1429 15.75 18.375V22.75C15.75 23.2141 15.5656 23.6592 15.2374 23.9874C14.9092 24.3156 14.4641 24.5 14 24.5C13.5359 24.5 13.0908 24.3156 12.7626 23.9874C12.4344 23.6592 12.25 23.2141 12.25 22.75V18.375C12.25 18.1429 12.1578 17.9204 11.9937 17.7563C11.8296 17.5922 11.6071 17.5 11.375 17.5H8.75C8.28587 17.5 7.84075 17.3156 7.51256 16.9874C7.18437 16.6592 7 16.2141 7 15.75V14Z",fill:"#56CCF2"})}),car:C.jsx("svg",{width:"28",height:"28",viewBox:"0 0 28 28",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:C.jsx("path",{d:"M24.4533 19.1917C24.465 19.0167 24.5 18.8417 24.5 18.6667V19.2501L24.4533 19.1917ZM12.8333 18.6667C12.8333 19.4951 13.0083 20.2884 13.3233 21.0001H7V22.1667C7 22.8084 6.475 23.3334 5.83333 23.3334H4.66667C4.025 23.3334 3.5 22.8084 3.5 22.1667V12.8334L5.92667 5.83341C6.16 5.15675 6.81333 4.66675 7.58333 4.66675H20.4167C21.1867 4.66675 21.84 5.15675 22.0733 5.83341L24.5 12.8334V18.6667C24.5 15.4467 21.8867 12.8334 18.6667 12.8334C15.4467 12.8334 12.8333 15.4467 12.8333 18.6667ZM9.33333 15.7501C9.33333 14.7817 8.55167 14.0001 7.58333 14.0001C6.615 14.0001 5.83333 14.7817 5.83333 15.7501C5.83333 16.7184 6.615 17.5001 7.58333 17.5001C8.55167 17.5001 9.33333 16.7184 9.33333 15.7501ZM22.1667 11.6667L20.4167 6.41675H7.58333L5.83333 11.6667H22.1667ZM26.6817 24.7217L21.8867 19.9267C22.365 18.7134 22.0967 17.2901 21.0933 16.2984C20.0433 15.2367 18.4683 15.0267 17.1967 15.6101L19.46 17.8734L17.885 19.4601L15.5633 17.1851C14.9333 18.4567 15.225 20.0317 16.2517 21.0934C16.715 21.5661 17.3064 21.893 17.9531 22.034C18.5998 22.1749 19.2737 22.1237 19.8917 21.8867L24.6867 26.6701C24.8967 26.8917 25.2117 26.8917 25.4217 26.6701L26.635 25.4684C26.8917 25.2584 26.8917 24.8851 26.6817 24.7217Z",fill:"#56CCF2"})}),flat_tire:C.jsxs("svg",{width:"28",height:"28",viewBox:"0 0 28 28",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[C.jsx("path",{d:"M13.9997 25.6666C20.443 25.6666 25.6663 20.4432 25.6663 13.9999C25.6663 7.5566 20.443 2.33325 13.9997 2.33325C7.55635 2.33325 2.33301 7.5566 2.33301 13.9999C2.33301 20.4432 7.55635 25.6666 13.9997 25.6666Z",stroke:"#56CCF2","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),C.jsx("path",{d:"M13.9993 16.3332C15.288 16.3332 16.3327 15.2885 16.3327 13.9998C16.3327 12.7112 15.288 11.6665 13.9993 11.6665C12.7107 11.6665 11.666 12.7112 11.666 13.9998C11.666 15.2885 12.7107 16.3332 13.9993 16.3332Z",stroke:"#56CCF2","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),C.jsx("path",{d:"M14 21C17.866 21 21 17.866 21 14C21 10.134 17.866 7 14 7C10.134 7 7 10.134 7 14C7 17.866 10.134 21 14 21Z",stroke:"#56CCF2","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),C.jsx("path",{d:"M13.9996 16.3332V20.9999M11.7829 14.7232L7.34961 16.1582M12.6229 12.1099L9.88128 8.34155M16.2163 14.7232L20.6496 16.1582M15.3763 12.1099L18.1179 8.34155",stroke:"#56CCF2","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})]}),babysitter:C.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[C.jsx("path",{d:"M12 14C15.3137 14 18 11.3137 18 8C18 4.68629 15.3137 2 12 2C8.68629 2 6 4.68629 6 8C6 11.3137 8.68629 14 12 14Z",stroke:"#56CCF2","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}),C.jsx("path",{d:"M16.8741 12C17.613 13.086 18.0056 14.3704 18.0001 15.684C18.0001 16.502 17.8521 17.283 17.5841 18M17.5841 18C16.7061 20.342 14.5371 22 12.0001 22C8.68606 22 6.00006 19.172 6.00006 15.684C5.99452 14.3704 6.3871 13.086 7.12606 12M17.5841 18C14.8511 16.8 12.0561 14.833 11.0001 14M12.0001 2C11.0061 2 10.2001 2.784 10.2001 3.75C10.2001 4.716 11.0061 5.5 12.0001 5.5C12.4611 5.5 12.8821 5.332 13.2001 5.054M10.0001 8H10.0081M14.0001 8H14.0081",stroke:"#56CCF2","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]}),cleaning:C.jsx("svg",{width:"19",height:"26",viewBox:"0 0 19 26",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:C.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.158 1.04844C16.2705 0.773264 16.4838 0.551412 16.7544 0.428188C17.0249 0.304964 17.3323 0.289664 17.6137 0.385412C17.8951 0.481161 18.1294 0.680735 18.2687 0.943379C18.4079 1.20602 18.4417 1.51192 18.363 1.79861L18.3187 1.92694L15.2737 9.41694L16.0402 9.62111C17.2652 9.94894 18.2849 10.9441 18.4785 12.3021C18.6442 13.4688 18.7994 15.2421 18.5859 17.0691C18.3735 18.8798 17.7809 20.8934 16.3144 22.3728C15.7754 22.9164 15.0019 23.0028 14.4792 23.0121C13.883 23.0214 13.1959 22.9304 12.49 22.7904C11.0714 22.5081 9.37036 21.9854 7.76036 21.4301L7.27736 21.2609L6.32886 20.9179L5.41886 20.5784L4.56253 20.2483L3.42153 19.7956L2.46253 19.4048L1.50936 19.0023C1.32232 18.9233 1.15914 18.7968 1.03596 18.6354C0.912771 18.474 0.833844 18.2832 0.806978 18.0819C0.780112 17.8807 0.806237 17.6759 0.882771 17.4878C0.959304 17.2998 1.0836 17.1349 1.24336 17.0096C2.84986 15.7496 4.35836 14.3006 5.63119 12.9624L6.24836 12.3044L6.54003 11.9848L7.09069 11.3699L7.34736 11.0759L8.04036 10.2651L8.43353 9.79144C8.84522 9.28636 9.38572 8.9019 9.99789 8.67867C10.6101 8.45544 11.2712 8.40175 11.9114 8.52327L12.1435 8.57694L13.0034 8.80677L16.158 1.04844ZM8.25036 13.5726C6.97303 14.9862 5.61799 16.3276 4.19153 17.5906C4.86275 17.862 5.53669 18.1265 6.21336 18.3839C6.83541 17.9406 7.39499 17.4155 7.87703 16.8229C8.08148 16.5799 8.27158 16.3251 8.44636 16.0599L8.61203 15.7893C8.76556 15.5248 9.01681 15.3311 9.3117 15.25C9.60658 15.169 9.92151 15.2069 10.1887 15.3557C10.4559 15.5045 10.654 15.7523 10.7403 16.0457C10.8266 16.3391 10.7942 16.6547 10.6502 16.9244C10.3772 17.4144 10.0354 17.8741 9.67953 18.3058C9.39122 18.6564 9.08269 18.9898 8.75553 19.3044C9.29453 19.4888 9.83353 19.6638 10.3562 19.8248C10.7914 19.5249 11.3012 18.9089 11.7632 18.2089C11.9491 17.9289 12.1249 17.6412 12.2905 17.3456L12.5285 16.8976C12.6658 16.6202 12.9076 16.4087 13.2007 16.3096C13.4939 16.2105 13.8145 16.2319 14.0919 16.3691C14.3693 16.5063 14.5808 16.7481 14.6799 17.0413C14.779 17.3345 14.7576 17.655 14.6204 17.9324C14.3532 18.4714 14.0417 18.9929 13.7104 19.4946C13.4805 19.844 13.2336 20.182 12.9707 20.5073L13.4619 20.6029C13.8842 20.6811 14.3299 20.7359 14.7324 20.6531C15.626 19.7023 16.0892 18.3221 16.2677 16.7973C16.3089 16.4426 16.3354 16.0911 16.347 15.7426L8.25036 13.5726ZM11.5392 10.8309C11.3027 10.772 11.0543 10.7822 10.8235 10.8603C10.5926 10.9385 10.3891 11.0814 10.2372 11.2719L9.95369 11.6138L16.2514 13.3008L16.1965 12.8434L16.1685 12.6311C16.1254 12.3359 15.9165 12.0583 15.5724 11.9194L15.437 11.8751L11.5392 10.8309ZM2.49286 7.00194L2.56753 7.16527C2.88567 7.82338 3.38671 8.37586 4.01069 8.75661L4.12736 8.82427C4.13948 8.83122 4.14955 8.84124 4.15656 8.85333C4.16356 8.86541 4.16725 8.87914 4.16725 8.89311C4.16725 8.90707 4.16356 8.9208 4.15656 8.93288C4.14955 8.94497 4.13948 8.95499 4.12736 8.96194L4.01069 9.02961C3.38681 9.41115 2.88615 9.96446 2.56869 10.6233L2.49403 10.7854C2.48756 10.8002 2.47694 10.8127 2.46347 10.8215C2.45001 10.8303 2.43427 10.8349 2.41819 10.8349C2.40211 10.8349 2.38638 10.8303 2.37291 10.8215C2.35945 10.8127 2.34883 10.8002 2.34236 10.7854L2.26769 10.6221C1.94955 9.964 1.44851 9.41151 0.824526 9.03077L0.707859 8.96311C0.695738 8.95616 0.685667 8.94614 0.678662 8.93405C0.671658 8.92196 0.667969 8.90824 0.667969 8.89427C0.667969 8.8803 0.671658 8.86658 0.678662 8.85449C0.685667 8.84241 0.695738 8.83239 0.707859 8.82544L0.824526 8.75777C1.44798 8.37606 1.94822 7.82276 2.26536 7.16411L2.34003 7.00194C2.3465 6.98722 2.35711 6.9747 2.37058 6.9659C2.38404 6.95711 2.39978 6.95243 2.41586 6.95243C2.43194 6.95243 2.44767 6.95711 2.46114 6.9659C2.47461 6.9747 2.48639 6.98722 2.49286 7.00194ZM5.79686 1.33311C5.84353 1.22811 5.99053 1.22811 6.03719 1.33311L6.15619 1.59211C6.66354 2.63933 7.46136 3.51856 8.45453 4.12494L8.63769 4.23344C8.65739 4.24433 8.6738 4.26031 8.68523 4.2797C8.69666 4.29908 8.70269 4.32118 8.70269 4.34369C8.70269 4.3662 8.69666 4.38829 8.68523 4.40768C8.6738 4.42707 8.65739 4.44304 8.63769 4.45394L8.45336 4.56127C7.46002 5.16797 6.66219 6.04762 6.15503 7.09527L6.03836 7.35427C6.02808 7.37774 6.01118 7.3977 5.98974 7.41171C5.96829 7.42573 5.94323 7.4332 5.91761 7.4332C5.89199 7.4332 5.86693 7.42573 5.84548 7.41171C5.82404 7.3977 5.80714 7.37774 5.79686 7.35427L5.67786 7.09527C5.1707 6.04762 4.37286 5.16797 3.37953 4.56127L3.19519 4.45394C3.1755 4.44304 3.15908 4.42707 3.14765 4.40768C3.13622 4.38829 3.13019 4.3662 3.13019 4.34369C3.13019 4.32118 3.13622 4.29908 3.14765 4.2797C3.15908 4.26031 3.1755 4.24433 3.19519 4.23344L3.38069 4.12494C4.37386 3.51856 5.17168 2.63933 5.67903 1.59211L5.79569 1.33311H5.79686Z",fill:"#56CCF2"})}),run_errand:C.jsx("svg",{width:"28",height:"28",viewBox:"0 0 28 28",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:C.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M18.0264 10.154C17.6596 9.92824 17.3625 9.60535 17.1679 9.22112C16.9733 8.83688 16.8889 8.40628 16.9239 7.977L14.583 7.56575L14.7848 6.41659L17.3153 6.86109C17.529 6.54454 17.8171 6.28529 18.1544 6.10608C18.4917 5.92687 18.8678 5.83319 19.2497 5.83325H21.2243C21.4226 5.83325 21.583 5.99367 21.583 6.192V10.1412C21.583 10.3395 21.422 10.4999 21.2243 10.4999H20.9997V15.3124C21.1261 15.2763 21.2546 15.2465 21.3853 15.2232C22.2585 15.0693 23.158 15.2262 23.9275 15.6668C24.6969 16.1074 25.2876 16.8037 25.5969 17.6347C25.7795 18.1247 25.5853 18.5721 25.2504 18.8387C25.2792 19.6131 25.0268 20.3718 24.5398 20.9746C24.0528 21.5775 23.364 21.9837 22.6008 22.1182C21.8376 22.2527 21.0515 22.1065 20.3877 21.7066C19.7239 21.3066 19.2272 20.68 18.9895 19.9423C18.6739 19.8362 18.3998 19.5982 18.2959 19.2499H11.6541C11.5825 20.048 11.2149 20.7904 10.6235 21.3311C10.0321 21.8717 9.25987 22.1716 8.45859 22.1716C7.65732 22.1716 6.88504 21.8717 6.29368 21.3311C5.70232 20.7904 5.33469 20.048 5.26309 19.2499H2.60717C2.57216 19.2509 2.5373 19.245 2.50456 19.2326C2.47183 19.2201 2.44188 19.2013 2.41642 19.1772C2.39096 19.1532 2.37049 19.1243 2.35617 19.0924C2.34186 19.0604 2.33399 19.0259 2.33301 18.9909C2.33301 16.9283 3.51542 15.1275 5.27301 14.1633C5.25753 14.1102 5.24967 14.0552 5.24967 13.9999V12.2499C5.24967 11.7858 5.43405 11.3407 5.76224 11.0125C6.09043 10.6843 6.53555 10.4999 6.99967 10.4999H12.2497C12.7138 10.4999 13.1589 10.6843 13.4871 11.0125C13.8153 11.3407 13.9997 11.7858 13.9997 12.2499V13.9999C13.9997 14.1546 13.9382 14.303 13.8288 14.4124C13.7194 14.5218 13.5711 14.5833 13.4163 14.5833V15.1666H17.0563L18.0264 10.154ZM18.083 8.16659C18.083 7.85717 18.2059 7.56042 18.4247 7.34163C18.6435 7.12284 18.9403 6.99992 19.2497 6.99992H20.4163V9.33325H19.2497C18.9403 9.33325 18.6435 9.21034 18.4247 8.99154C18.2059 8.77275 18.083 8.476 18.083 8.16659ZM16.83 16.3333H13.4163V18.0833H16.4917L16.83 16.3333ZM17.6799 18.0833H18.3543C18.5651 17.1936 19.0902 16.4101 19.833 15.8771V10.4999H19.1476L17.6799 18.0833ZM20.2174 19.8718C20.4142 20.2652 20.7329 20.5846 21.1258 20.7823C21.5188 20.9801 21.9652 21.0457 22.3984 20.9693C22.8317 20.8929 23.2287 20.6787 23.5304 20.3585C23.832 20.0383 24.0222 19.6292 24.0727 19.1922L20.2174 19.8718ZM6.41634 12.2499C6.41634 12.0952 6.4778 11.9468 6.5872 11.8374C6.69659 11.728 6.84496 11.6666 6.99967 11.6666H12.2497C12.4044 11.6666 12.5528 11.728 12.6622 11.8374C12.7716 11.9468 12.833 12.0952 12.833 12.2499V13.4166H6.41634V12.2499ZM3.60117 18.0833C4.04451 16.1203 5.89893 14.5833 8.21651 14.5833H12.2497V18.0646C12.2304 18.077 12.2079 18.0835 12.1849 18.0833H3.60117ZM10.2774 19.2633C10.3509 19.2657 10.417 19.2682 10.4758 19.2709C10.4014 19.7527 10.157 20.1919 9.78696 20.5093C9.41689 20.8266 8.94549 21.001 8.45801 21.001C7.97052 21.001 7.49913 20.8266 7.12905 20.5093C6.75898 20.1919 6.51466 19.7527 6.44026 19.2709C6.49898 19.2678 6.56509 19.2653 6.63859 19.2633C7.06792 19.2499 7.64717 19.2499 8.45801 19.2499C9.26884 19.2499 9.84809 19.2499 10.278 19.2633M23.55 16.8075C23.1657 16.5383 22.7162 16.3771 22.2485 16.3406C21.7807 16.304 21.3117 16.3936 20.8902 16.5998C20.4688 16.8061 20.1103 17.1215 19.8522 17.5133C19.594 17.9051 19.4456 18.359 19.4223 18.8276L24.4635 17.9386C24.2717 17.4829 23.955 17.0912 23.55 16.8075Z",fill:"#56CCF2"})}),my_alert:C.jsxs("svg",{width:"28",height:"28",viewBox:"0 0 28 28",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[C.jsx("path",{d:"M7.58301 19.8333C8.54951 19.8333 9.33301 19.0498 9.33301 18.0833C9.33301 17.1168 8.54951 16.3333 7.58301 16.3333C6.61651 16.3333 5.83301 17.1168 5.83301 18.0833C5.83301 19.0498 6.61651 19.8333 7.58301 19.8333Z",fill:"#56CCF2"}),C.jsx("path",{d:"M15.75 19.8333C16.7165 19.8333 17.5 19.0498 17.5 18.0833C17.5 17.1168 16.7165 16.3333 15.75 16.3333C14.7835 16.3333 14 17.1168 14 18.0833C14 19.0498 14.7835 19.8333 15.75 19.8333Z",fill:"#56CCF2"}),C.jsx("path",{d:"M4.66634 12.8332V9.33324H13.1713C12.9497 8.59824 12.833 7.81657 12.833 6.99991H5.16801C6.13634 6.17157 8.64467 5.72824 12.9263 5.85657C13.043 5.03991 13.2763 4.25824 13.6147 3.53491C3.46467 3.11491 2.33301 5.85657 2.33301 8.16657V19.2499C2.33301 20.3582 2.77634 21.3616 3.49967 22.0966V24.4999C3.49967 25.1416 4.02467 25.6666 4.66634 25.6666H5.83301C6.47467 25.6666 6.99967 25.1416 6.99967 24.4999V23.3332H16.333V24.4999C16.333 25.1416 16.858 25.6666 17.4997 25.6666H18.6663C19.308 25.6666 19.833 25.1416 19.833 24.4999V22.0966C20.5563 21.3616 20.9997 20.3582 20.9997 19.2499V15.1666C18.7713 15.1666 16.7647 14.2799 15.2947 12.8332H4.66634ZM18.6663 18.6666C18.6663 19.9499 17.6163 20.9999 16.333 20.9999H6.99967C5.71634 20.9999 4.66634 19.9499 4.66634 18.6666V15.1666H18.6663V18.6666Z",fill:"#56CCF2"}),C.jsx("path",{d:"M21.0003 1.16675C17.7803 1.16675 15.167 3.78008 15.167 7.00008C15.167 10.2201 17.7803 12.8334 21.0003 12.8334C24.2203 12.8334 26.8337 10.2201 26.8337 7.00008C26.8337 3.78008 24.2203 1.16675 21.0003 1.16675ZM21.5837 10.5001H20.417V9.33341H21.5837V10.5001ZM21.5837 8.16675H20.417V3.50008H21.5837V8.16675Z",fill:"#56CCF2"})]})},Z=({taskData:t})=>{const{i18n:i}=o();return C.jsx("div",{className:" flex h-full w-full flex-col gap-4  px-5 py-5 ",children:t.length===0?C.jsxs("div",{className:"mt-5 w-full text-center font-['Poppins'] text-2xl font-medium text-[#b4b4b4]",children:["Oops looks like ",C.jsx("br",{})," there is no alert."]}):t.map((e,s)=>C.jsxs(n,{to:`/user/my-alert-view/${e.id}`,className:"inline-flex items-center justify-start gap-[5px]",children:[C.jsx("div",{className:"flex h-11 w-11 flex-shrink-0 items-center justify-center rounded-full bg-[#50a8f9]/10",children:r.cleaning}),C.jsxs("div",{className:"inline-flex w-full flex-col items-start justify-center gap-[2px] pb-2",children:[C.jsxs("div",{className:"inline-flex items-center justify-start gap-1",children:[C.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:e==null?void 0:e.service_name}),C.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:"→"}),C.jsxs("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:[e==null?void 0:e.service_alert_distance," Meters"]})]}),e.status=="active"?C.jsxs("div",{className:"inline-flex items-center justify-start gap-1",children:[C.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:e==null?void 0:e.operating_city}),C.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:"•"}),C.jsx("div",{className:"text-right font-['Poppins'] text-xs font-medium text-[#8080a3]",children:l(e.create_at,i.language)})]}):C.jsxs("div",{className:"inline-flex items-center justify-start gap-1",children:[C.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:"Calle Las Marias"}),C.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:"•"}),C.jsx("div",{className:"text-right font-['Poppins'] text-xs font-medium text-[#fd5d5d]",children:"Inactive"})]}),C.jsxs("div",{className:"flex items-center justify-start gap-1",children:[C.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#56ccf2]",children:"Message :"}),C.jsx("div",{className:" w-1.5 font-['Poppins'] text-xs font-medium text-[#8080a3]",children:"•"}),C.jsx("div",{className:" font-['Poppins'] text-xs font-medium text-[#8080a3]",children:e.message})]})]})]},s))})};export{Z as default};
