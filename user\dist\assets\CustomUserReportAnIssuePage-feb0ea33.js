import{j as e}from"./@react-google-maps/api-ee55a349.js";import{r as t,i as o,R as l,L as a}from"./vendor-b16525a8.js";import"./index-9ef1c042.js";import{M as n,A as m,G as p}from"./index-09a1718e.js";import{B as x}from"./index-d54cffea.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./react-i18next-4a61273e.js";new n;const c=[{title:"Chiripero was a different peroson",id:1},{title:"Chiripero was rude",id:2},{title:"Issue with the service",id:2},{title:"Other issue",id:2}],E=()=>{t.useContext(m),t.useContext(p);const{id:s}=o();return l.useEffect(()=>{},[]),e.jsxs("div",{className:"p-5",children:[e.jsxs("div",{className:"relative flex w-full items-center justify-center ",children:[e.jsx("div",{className:" absolute left-0 top-0 ",children:e.jsx(x,{})}),e.jsxs("div",{className:"",children:[e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:"Report an issue"}),e.jsx("div",{className:"text-center font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:"Select issue"})]})]}),e.jsx("div",{className:" mt-[60px] ",children:e.jsx("div",{className:"flex flex-col gap-[10px] ",children:c.map((i,r)=>e.jsxs(a,{to:`/user/report-issue-submit/${s}?title=${i.title}`,className:"relative inline-flex h-12 w-full items-center justify-between gap-2 rounded-2xl bg-[#8181a4]/10 px-4 font-['Poppins'] text-sm font-medium text-black",children:["Chiripero was a different peroson",e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M14.6481 9.07267C15.1173 9.58478 15.1173 10.4151 14.6481 10.9272L9.55094 16.4908C9.08176 17.003 8.32107 17.003 7.85189 16.4908C7.3827 15.9787 7.38271 15.1484 7.85189 14.6363L12.9491 9.07267C13.4182 8.56055 14.1789 8.56055 14.6481 9.07267Z",fill:"#8181A4"}),e.jsx("path",{d:"M7.85189 3.50903C8.32107 2.99691 9.08176 2.99691 9.55094 3.50903L14.6481 9.07267C15.1173 9.58478 15.1173 10.4151 14.6481 10.9272C14.1789 11.4393 13.4182 11.4393 12.9491 10.9272L7.85189 5.36357C7.38271 4.85146 7.38271 4.02115 7.85189 3.50903Z",fill:"#8181A4"})]})]},r))})})]})};export{E as default};
