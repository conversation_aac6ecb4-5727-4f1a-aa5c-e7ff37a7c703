import{g as i}from"../vendor-4f06b3f4.js";function p(e){const t=e.lastIndexOf(".");return t===-1||t===e.length-1?{name:e,extension:void 0}:{name:e.slice(0,t),extension:e.slice(t+1)}}var B=function(t){if(typeof t!="number"||Number.isNaN(t))throw new TypeError(`Expected a number, got ${typeof t}`);const r=t<0,o=["B","KB","MB","GB","TB","PB","EB","ZB","YB"];if(r&&(t=-t),t<1)return`${(r?"-":"")+t} B`;const n=Math.min(Math.floor(Math.log(t)/Math.log(1024)),o.length-1);t=Number(t/1024**n);const s=o[n];return t>=10||t%1===0?`${(r?"-":"")+t.toFixed(0)} ${s}`:`${(r?"-":"")+t.toFixed(1)} ${s}`};const c=i(B);export{p as g,c as p};
