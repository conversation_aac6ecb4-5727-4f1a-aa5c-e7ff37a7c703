import{j as s}from"./@react-google-maps/api-ee55a349.js";import"./vendor-b16525a8.js";import{C as c}from"./index-32785bdc.js";import{I as p}from"./InteractiveButton-767677a2.js";import"./qr-scanner-cf010ec4.js";import"./MoonLoader-49322f56.js";const k=({open:u,closeModalFunction:i,actionHandler:x,message:n,title:a,messageClasses:b="font-normal text-base",titleClasses:h="text-center font-bold text-lg",acceptText:e="YES",rejectText:l="NO",loading:r=!1,isInfo:o=!1})=>{var d,m,t,f;return s.jsx("aside",{className:`fixed inset-0 m-auto flex items-center justify-center backdrop-blur-sm transition-all ${u?"scale-100":"scale-0"}`,children:s.jsxs("section",{className:"flex w-auto min-w-[27rem]  flex-col gap-3 rounded-[.5rem] bg-white py-6",children:[s.jsxs("div",{className:"flex justify-between px-6",children:[s.jsx("div",{children:a?s.jsx("div",{className:` ${h} `,children:a}):null}),s.jsx("button",{disabled:r,onClick:i,children:s.jsx(c,{className:"w-4 h-4"})})]}),n?s.jsxs("div",{children:[s.jsx("div",{className:`px-6 text-[#525252] ${b} `,children:n}),!o&&s.jsx("div",{className:"px-6 text-[#525252] font-normal pt-3 pb-1",children:"This action cannot be undone."})]}):null,s.jsxs("div",{className:"flex justify-between px-6 font-medium leading-[1.5rem] text-[base]",children:[s.jsx("button",{disabled:r,className:"flex h-[2.75rem] items-center justify-center rounded-[.5rem] border border-[#d8dae5] text-[#667085] w-full mr-2",onClick:i,children:((d=l==null?void 0:l.charAt(0))==null?void 0:d.toUpperCase())+((m=l==null?void 0:l.slice(1))==null?void 0:m.toLowerCase())}),s.jsxs(p,{disabled:r,loading:r,className:`flex items-center justify-center gap-x-5 rounded-[.5rem]  ${o?"bg-primaryBlue":"bg-[#E11D48]"} px-6 text-white w-full ml-2`,onClick:x,children:["Yes, ",((t=e==null?void 0:e.charAt(0))==null?void 0:t.toUpperCase())+((f=e==null?void 0:e.slice(1))==null?void 0:f.toLowerCase())]})]})]})})};export{k as default};
