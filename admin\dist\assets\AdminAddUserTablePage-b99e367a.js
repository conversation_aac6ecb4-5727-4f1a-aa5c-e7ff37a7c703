import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as n,b as P}from"./vendor-4f06b3f4.js";import{u as E}from"./react-hook-form-f3d72793.js";import{o as F}from"./yup-2324a46a.js";import{c as R,a as s}from"./yup-17027d7a.js";import{G as L,A as T,M as q,s as I,t as O}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as o}from"./MkdInput-ff3aa862.js";import{I as D}from"./InteractiveButton-8f7d74ee.js";import"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";const he=({setSidebar:w})=>{var b,f,y,_,N,j;const{dispatch:d}=n.useContext(L),k=R({oauth:s(),role:s(),first_name:s(),last_name:s(),email:s(),password:s(),type:s(),verify:s(),phone:s(),photo:s(),operating_city:s(),refer:s(),stripe_uid:s(),paypal_uid:s(),listing_balance:s(),confirmation_code:s(),two_factor_authentication:s(),cedula_number:s(),cedula_image_link:s(),qr_link:s(),status:s()}).required(),{dispatch:S}=n.useContext(T),[h,U]=n.useState({}),[x,c]=n.useState(!1),v=P(),{register:l,handleSubmit:C,setError:g,setValue:$,formState:{errors:a}}=E({resolver:F(k)});n.useState([]);const A=async t=>{let u=new q;c(!0);try{for(let m in h){let i=new FormData;i.append("file",h[m].file);let p=await u.uploadImage(i);t[m]=p.url}u.setTable("user");const r=await u.callRestAPI({oauth:t.oauth,role:t.role,first_name:t.first_name,last_name:t.last_name,email:t.email,password:t.password,type:t.type,verify:t.verify,phone:t.phone,photo:t.photo,operating_city:t.operating_city,refer:t.refer,stripe_uid:t.stripe_uid,paypal_uid:t.paypal_uid,listing_balance:t.listing_balance,confirmation_code:t.confirmation_code,two_factor_authentication:t.two_factor_authentication,cedula_number:t.cedula_number,cedula_image_link:t.cedula_image_link,qr_link:t.qr_link,status:t.status},"POST");if(!r.error)I(d,"Added"),v("/admin/user"),w(!1),d({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(r.validation){const m=Object.keys(r.validation);for(let i=0;i<m.length;i++){const p=m[i];g(p,{type:"manual",message:r.validation[p]})}}c(!1)}catch(r){c(!1),console.log("Error",r),g("oauth",{type:"manual",message:r.message}),O(S,r.message)}};return n.useEffect(()=>{d({type:"SETPATH",payload:{path:"user"}})},[]),e.jsxs("div",{className:" mx-auto rounded  p-5 shadow-md",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add User"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:C(A),children:[e.jsx(o,{type:"text",page:"add",name:"oauth",errors:a,label:"Oauth",placeholder:"Oauth",register:l,className:""}),e.jsx(o,{type:"text",page:"add",name:"role",errors:a,label:"Role",placeholder:"Role",register:l,className:""}),e.jsx(o,{type:"text",page:"add",name:"first_name",errors:a,label:"First Name",placeholder:"First Name",register:l,className:""}),e.jsx(o,{type:"text",page:"add",name:"last_name",errors:a,label:"Last Name",placeholder:"Last Name",register:l,className:""}),e.jsx(o,{type:"text",page:"add",name:"email",errors:a,label:"Email",placeholder:"Email",register:l,className:""}),e.jsx(o,{type:"text",page:"add",name:"password",errors:a,label:"Password",placeholder:"Password",register:l,className:""}),e.jsx(o,{type:"number",page:"add",name:"type",errors:a,label:"Type",placeholder:"Type",register:l,className:""}),e.jsx(o,{type:"number",page:"add",name:"verify",errors:a,label:"Verify",placeholder:"Verify",register:l,className:""}),e.jsx(o,{type:"text",page:"add",name:"phone",errors:a,label:"Phone",placeholder:"Phone",register:l,className:""}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"photo",children:"Photo"}),e.jsx("textarea",{placeholder:"Photo",...l("photo"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(b=a.photo)!=null&&b.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-xs italic text-red-500",children:(f=a.photo)==null?void 0:f.message})]}),e.jsx(o,{type:"text",page:"add",name:"operating_city",errors:a,label:"Operating City",placeholder:"Operating City",register:l,className:""}),e.jsx(o,{type:"text",page:"add",name:"refer",errors:a,label:"Refer",placeholder:"Refer",register:l,className:""}),e.jsx(o,{type:"text",page:"add",name:"stripe_uid",errors:a,label:"Stripe Uid",placeholder:"Stripe Uid",register:l,className:""}),e.jsx(o,{type:"text",page:"add",name:"paypal_uid",errors:a,label:"Paypal Uid",placeholder:"Paypal Uid",register:l,className:""}),e.jsx(o,{type:"number",page:"add",name:"listing_balance",errors:a,label:"Listing Balance",placeholder:"Listing Balance",register:l,className:""}),e.jsx(o,{type:"text",page:"add",name:"confirmation_code",errors:a,label:"Confirmation Code",placeholder:"Confirmation Code",register:l,className:""}),e.jsx(o,{type:"number",page:"add",name:"two_factor_authentication",errors:a,label:"Two Factor Authentication",placeholder:"Two Factor Authentication",register:l,className:""}),e.jsx(o,{type:"text",page:"add",name:"cedula_number",errors:a,label:"Cedula Number",placeholder:"Cedula Number",register:l,className:""}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"cedula_image_link",children:"Cedula Image Link"}),e.jsx("textarea",{placeholder:"Cedula Image Link",...l("cedula_image_link"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(y=a.cedula_image_link)!=null&&y.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-xs italic text-red-500",children:(_=a.cedula_image_link)==null?void 0:_.message})]}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"qr_link",children:"Qr Link"}),e.jsx("textarea",{placeholder:"Qr Link",...l("qr_link"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(N=a.qr_link)!=null&&N.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-xs italic text-red-500",children:(j=a.qr_link)==null?void 0:j.message})]}),e.jsx(o,{type:"number",page:"add",name:"status",errors:a,label:"Status",placeholder:"Status",register:l,className:""}),e.jsx(D,{type:"submit",loading:x,disabled:x,className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",children:"Submit"})]})]})};export{he as default};
