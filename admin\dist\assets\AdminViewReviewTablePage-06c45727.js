import{j as s}from"./@react-google-maps/api-ac2f9d6f.js";import{R as i,h as x}from"./vendor-4f06b3f4.js";import"./yup-17027d7a.js";import{M as f,G as l,t as h}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{S as p}from"./index-2d8231e7.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";let c=new f;const D=()=>{const{dispatch:m}=i.useContext(l),{dispatch:d}=i.useContext(l),[e,n]=i.useState({}),[o,t]=i.useState(!0),r=x();return i.useEffect(function(){(async function(){try{t(!0),c.setTable("review");const a=await c.callRestAPI({id:Number(r==null?void 0:r.id),join:""},"GET");a.error||(n(a.model),t(!1))}catch(a){t(!1),console.log("error",a),h(d,a.message)}})()},[]),i.useEffect(()=>{m({type:"SHOW_BACKBUTTON",payload:{showBackButton:!0}})},[]),s.jsx("div",{className:" mx-auto rounded  p-5 shadow-md",children:o?s.jsx(p,{}):s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Rating"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.rating})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Description"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.description})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Client Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.client_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Provider Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.provider_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Job Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.job_id})]})})]})})};export{D as default};
