import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as s,b as x,r as g}from"./vendor-4f06b3f4.js";import{M as b,A as w,G as E,p as A,q as j}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{L as o}from"./index-6416aa2c.js";import{M as p}from"./index-d97c616d.js";import{M as D}from"./index-68c82eaa.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";new b;const M=[{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Address",accessor:"address",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"business name",accessor:"business_name",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"phone",accessor:"phone",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"website",accessor:"website",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"image",accessor:"image",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"hours",accessor:"hours",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"description",accessor:"description",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],O=()=>{s.useContext(w);const{dispatch:m}=s.useContext(E);x();const[r,a]=s.useState(!1),[d,t]=s.useState(!1),[f,h]=s.useState(),u=g.useRef(null),[v,S]=s.useState([]),l=(i,n,c=[])=>{switch(i){case"add":a(n);break;case"edit":t(n),S(c),h(c[0]);break}};return s.useEffect(()=>{m({type:"SETPATH",payload:{path:"nearby_location"}})},[]),e.jsxs(e.Fragment,{children:[e.jsx(e.Fragment,{children:e.jsx("div",{className:"overflow-x-auto  rounded bg-white p-5 shadow",children:e.jsx(o,{children:e.jsx(D,{columns:M,tableRole:"admin",table:"nearby_location",actionId:"id",actions:{view:{show:!0,action:null,multiple:!1},edit:{show:!0,multiple:!1,action:i=>l("edit",!0,i)},delete:{show:!0,action:null,multiple:!1},select:{show:!0,action:null,multiple:!1},add:{show:!0,action:()=>l("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!1,action:null,multiple:!0}},actionPostion:"ontable",refreshRef:u})})})}),r&&e.jsx(o,{children:e.jsx(p,{isModalActive:r,closeModalFn:()=>a(!1),children:e.jsx(A,{setSidebar:a})})}),d&&e.jsx(o,{children:e.jsx(p,{isModalActive:d,closeModalFn:()=>t(!1),children:e.jsx(j,{activeId:f,setSidebar:t})})})]})};export{O as default};
