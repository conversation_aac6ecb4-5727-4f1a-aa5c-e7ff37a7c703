import{j as i}from"./@react-google-maps/api-afbf18d5.js";import{t as r}from"./i18next-7389dd8c.js";import"./vendor-f36d475e.js";import{f as s}from"./index-cf5e6bc7.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-1e3e6bc5.js";const y=({taskData:t})=>i.jsx("div",{className:" flex h-full w-full flex-col gap-4 px-5 py-5 ",children:t.length===0?i.jsx("div",{className:"mt-5 w-full text-center font-['Poppins'] text-2xl font-medium text-[#b4b4b4]",dangerouslySetInnerHTML:{__html:r("provider.earnings.no_earnings")}}):t.map((e,n)=>i.jsxs("div",{className:"inline-flex items-start justify-start gap-[5px]",children:[i.jsx("div",{className:"flex h-11 w-11 flex-shrink-0 items-center justify-center overflow-hidden rounded-full bg-[#50a8f9]/10",children:i.jsx("img",{src:e==null?void 0:e.service_logo,alt:"",className:"h-full w-full object-cover"})}),i.jsxs("div",{className:"inline-flex w-[283px] flex-col items-start justify-center pb-2",children:[i.jsxs("div",{className:"inline-flex items-center justify-start gap-1",children:[i.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:e==null?void 0:e.provider_task_title}),i.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:"→"}),i.jsxs("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:["$",e==null?void 0:e.provider_task_amount]})]}),i.jsxs("div",{className:"inline-flex items-center justify-start gap-1",children:[i.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:e==null?void 0:e.provider_operating_city}),i.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:"•"}),i.jsx("div",{className:"text-right font-['Poppins'] text-xs font-medium text-[#8080a3]",children:s(e==null?void 0:e.create_at)})]}),i.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:r("provider.earnings.paid")})]})]},n))});export{y as default};
