import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{h as g,r as c,R as r,d as N,L as l}from"./vendor-f36d475e.js";import"./index-5deedf4a.js";import{M as b,A as k,G as y}from"./index-cf5e6bc7.js";import{B as P}from"./index-895fa99b.js";import{u as L}from"./user-a875fff3.js";import{a as _}from"./index-04e38e92.js";import{S as M}from"./index-65bc3378.js";import{h as Z}from"./moment-a9aaa855.js";import{u as H}from"./react-i18next-1e3e6bc5.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-2487dba8.js";import"./@react-pdf-viewer/core-9d395990.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";const o=new b,se=()=>{g();const{state:d,dispatch:V}=c.useContext(k);c.useContext(y);const[s,A]=r.useState(d.userDetails),[x,m]=r.useState([]),[p,h]=r.useState([]),[f,v]=r.useState([]),[j,a]=r.useState(!1),{t:i}=H(),n=N(),C=async()=>{try{const t=await o.callRawAPI("/v3/api/custom/chumpchange/provider/products",{},"GET");t.error||m(t.data)}catch(t){console.log(t)}},u=async()=>{try{const t=await o.callRawAPI("/v3/api/custom/chumpchange/provider/skills",{},"GET");t.error||h(t.data)}catch(t){console.log(t)}},w=async()=>{try{const t=await o.callRawAPI("/v3/api/custom/chumpchange/provider/payment-methods",{},"GET");t.error||v(t.data)}catch(t){console.log(t)}};return r.useEffect(()=>{(async()=>(a(!0),await C(),await u(),await w(),a(!1)))()},[]),e.jsxs("div",{className:"p-5",children:[e.jsxs("div",{className:"relative flex w-full items-center justify-between ",children:[e.jsx("div",{className:" ",children:e.jsx(P,{link:"/provider/dashboard"})}),e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:i("provider.profile.title")}),e.jsx("div",{className:" ",children:e.jsx(l,{to:"/provider/setting",className:"relative flex h-10 w-10 items-center justify-center rounded-[32px] bg-white/60 backdrop-blur-[20px]",children:e.jsxs("svg",{width:"22",height:"22",viewBox:"0 0 22 22",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M2 11.7924V10.2075C2 9.27096 2.76462 8.49654 3.70915 8.49654C5.33733 8.49654 6.003 7.34386 5.18441 5.93003C4.71664 5.11955 4.9955 4.06593 5.81409 3.59766L7.37031 2.70613C8.08096 2.28288 8.9985 2.53503 9.42129 3.24645L9.52024 3.41755C10.3298 4.83138 11.6612 4.83138 12.4798 3.41755L12.5787 3.24645C13.0015 2.53503 13.919 2.28288 14.6297 2.70613L16.1859 3.59766C17.0045 4.06593 17.2834 5.11955 16.8156 5.93003C15.997 7.34386 16.6627 8.49654 18.2909 8.49654C19.2264 8.49654 20 9.26196 20 10.2075V11.7924C20 12.729 19.2354 13.5035 18.2909 13.5035C16.6627 13.5035 15.997 14.6561 16.8156 16.07C17.2834 16.8895 17.0045 17.9341 16.1859 18.4023L14.6297 19.2939C13.919 19.7171 13.0015 19.465 12.5787 18.7535L12.4798 18.5824C11.6702 17.1686 10.3388 17.1686 9.52024 18.5824L9.42129 18.7535C8.9985 19.465 8.08096 19.7171 7.37031 19.2939L5.81409 18.4023C4.9955 17.9341 4.71664 16.8804 5.18441 16.07C6.003 14.6561 5.33733 13.5035 3.70915 13.5035C2.76462 13.5035 2 12.729 2 11.7924Z",stroke:"#8181A4",strokeWidth:"2",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M11 14C12.6569 14 14 12.6569 14 11C14 9.3431 12.6569 8 11 8C9.3431 8 8 9.3431 8 11C8 12.6569 9.3431 14 11 14Z",stroke:"#8181A4",strokeWidth:"2",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"})]})})})]}),e.jsx(_,{user:L,userData:s}),e.jsxs("div",{className:" mt-9 ",children:[e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:i("provider.profile.phone")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:s!=null&&s.phone?s==null?void 0:s.phone:"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:i("provider.profile.email")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:e.jsx("a",{href:`mailto:${s==null?void 0:s.email}`,className:" text-[#4fa7f9] underline ",children:s!=null&&s.email?s==null?void 0:s.email:"N/A"})})]}),e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:i("provider.profile.status")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:e.jsxs("button",{className:" text-right ",children:[i("provider.profile.active_on")," ",s!=null&&s.create_at?Z(s==null?void 0:s.create_at).format("MMMM D, YYYY"):"N/A"]})})]}),e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:i("provider.profile.balance")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:e.jsxs(l,{to:"/provider/recharge-balance",className:" text-[#4fa7f9] underline ",children:[i("provider.profile.dop")," ",s!=null&&s.listing_balance?s==null?void 0:s.listing_balance:"0"]})})]})]}),j?e.jsx("div",{className:" flex h-full w-full items-center justify-center",children:e.jsx(M,{})}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:" mt-[50px] ",children:e.jsxs("div",{className:"",children:[e.jsx("div",{className:"font-['Poppins'] text-lg font-medium text-black",children:i("provider.profile.help_with")}),e.jsx("div",{className:"scrollbar-hide mt-5 flex w-full overflow-x-auto ",children:e.jsx("div",{className:"flex gap-[3px]",children:e.jsxs(e.Fragment,{children:[p.map(t=>e.jsxs("div",{onClick:()=>{n(`/provider/edit-skill/${t.id}`,{state:{...t}})},className:"flex h-[127px] w-[120px] cursor-pointer flex-col items-center justify-center rounded-[20px] bg-[#56ccf2]",children:[e.jsx("div",{className:" flex h-12 w-12 items-center justify-center overflow-hidden rounded-full bg-white backdrop-blur-[20px]",children:e.jsx("img",{src:t==null?void 0:t.service_logo,className:"h-full w-full object-cover ",alt:""})}),e.jsx("div",{className:" mt-2 text-center font-['Poppins'] text-xs font-medium text-black",children:t==null?void 0:t.service_name}),e.jsx("div",{className:" inline-flex items-center justify-start gap-1",children:e.jsxs("div",{className:"text-center font-['Poppins'] text-xs font-medium text-[#8080a3]",children:["$",t.price]})})]})),e.jsxs(l,{to:"/provider/add-skill",className:"flex h-[127px] w-[120px] flex-col items-center justify-center rounded-[20px] bg-[#e0e0e0]",children:[e.jsxs("svg",{width:"48",height:"48",viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"47",height:"47",rx:"23.5",fill:"#56CCF2"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"47",height:"47",rx:"23.5",stroke:"white"}),e.jsx("path",{d:"M22.1667 16.3333C22.1667 15.597 22.7636 15 23.5 15C24.2364 15 24.8333 15.597 24.8333 16.3333V29.6667C24.8333 30.403 24.2364 31 23.5 31C22.7636 31 22.1667 30.403 22.1667 29.6667V16.3333Z",fill:"white"}),e.jsx("path",{d:"M16.8333 24.3333C16.097 24.3333 15.5 23.7364 15.5 23C15.5 22.2636 16.097 21.6667 16.8333 21.6667H30.1667C30.903 21.6667 31.5 22.2636 31.5 23C31.5 23.7364 30.903 24.3333 30.1667 24.3333H16.8333Z",fill:"white"})]}),e.jsx("div",{className:"mt-2 text-center font-['Poppins'] text-xs font-medium text-black",children:i("provider.profile.tap_here")}),e.jsx("div",{className:"text-center font-['Poppins'] text-xs font-medium text-black",children:i("provider.profile.add_new")})]})]})})})]})}),e.jsx("div",{className:" mt-[50px] ",children:e.jsxs("div",{className:"",children:[e.jsx("div",{className:"font-['Poppins'] text-lg font-medium text-black",children:i("provider.profile.ambulantly")}),e.jsx("div",{className:"scrollbar-hide mt-5 flex w-full overflow-x-auto  ",children:e.jsx("div",{className:" flex gap-[3px] ",children:e.jsxs(e.Fragment,{children:[x.map(t=>e.jsxs("div",{onClick:()=>{n(`/provider/edit-license_product/${t.id}`,{state:{...t}})},className:"flex h-[127px] w-[120px] cursor-pointer flex-col items-center justify-center rounded-[20px] bg-[#56ccf2]",children:[e.jsx("div",{className:" flex h-12 w-12 items-center justify-center overflow-hidden rounded-full bg-white backdrop-blur-[20px]",children:e.jsx("img",{src:t==null?void 0:t.product_logo,className:"h-full w-full object-cover ",alt:""})}),e.jsx("div",{className:" mt-2 text-center font-['Poppins'] text-xs font-medium text-black",children:t==null?void 0:t.product_type_name}),e.jsx("div",{className:" inline-flex items-center justify-start gap-1",children:e.jsxs("div",{className:"text-center font-['Poppins'] text-xs font-medium text-[#8080a3]",children:["$",(t==null?void 0:t.amount)||0]})})]})),e.jsxs(l,{to:"/provider/add-license_product",className:"flex h-[127px] w-[120px] flex-col items-center justify-center rounded-[20px] bg-[#e0e0e0]",children:[e.jsxs("svg",{width:"48",height:"48",viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"47",height:"47",rx:"23.5",fill:"#56CCF2"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"47",height:"47",rx:"23.5",stroke:"white"}),e.jsx("path",{d:"M22.1667 16.3333C22.1667 15.597 22.7636 15 23.5 15C24.2364 15 24.8333 15.597 24.8333 16.3333V29.6667C24.8333 30.403 24.2364 31 23.5 31C22.7636 31 22.1667 30.403 22.1667 29.6667V16.3333Z",fill:"white"}),e.jsx("path",{d:"M16.8333 24.3333C16.097 24.3333 15.5 23.7364 15.5 23C15.5 22.2636 16.097 21.6667 16.8333 21.6667H30.1667C30.903 21.6667 31.5 22.2636 31.5 23C31.5 23.7364 30.903 24.3333 30.1667 24.3333H16.8333Z",fill:"white"})]}),e.jsx("div",{className:"mt-2 text-center font-['Poppins'] text-xs font-medium text-black",children:i("provider.profile.tap_here")}),e.jsx("div",{className:"text-center font-['Poppins'] text-xs font-medium text-black",children:i("provider.profile.add_new")})]})]})})})]})}),e.jsxs("div",{className:" mt-[55px] ",children:[e.jsx("div",{className:"font-['Poppins'] text-lg font-medium text-black",children:i("provider.profile.payments")}),e.jsxs(e.Fragment,{children:[f.map(t=>e.jsxs("div",{onClick:()=>{n(`/provider/edit-payment/${t.id}`,{state:{...t}})},className:"mt-[11px] flex w-full items-center gap-4 rounded-[20px] bg-[#56ccf2] p-[6px]",children:[e.jsx("div",{className:" flex h-12 w-12 items-center justify-center rounded-[32px] bg-white backdrop-blur-[20px] ",children:e.jsx("svg",{width:"32",height:"31",viewBox:"0 0 32 31",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M24.0381 0.884277L17.5385 4.42274L6.76922 10.3083L0.269531 13.8849L3.07692 18.9618V15C3.07692 14.5103 3.27142 14.0408 3.61764 13.6945C3.96386 13.3483 4.43344 13.1538 4.92307 13.1538H5.92369L5.96061 13L20.2314 5.19197L21.3083 5.49966C21.3378 5.59812 21.3698 5.71259 21.4228 5.80735C21.8461 6.58274 22.8012 6.88551 23.5766 6.46212C23.6701 6.41043 23.7305 6.29843 23.808 6.23074L24.9231 6.57659L27.3846 11.0775L27.0769 12.1926C26.9797 12.2221 26.864 12.2172 26.7692 12.2689C26.4086 12.4658 26.1661 12.7957 26.0381 13.1538H30.7692L27.5766 7.34582L24.0381 0.884277ZM22.6929 8.96182C22.3324 9.03893 22.0094 9.23772 21.7781 9.5248C21.5469 9.81188 21.4214 10.1698 21.4228 10.5384C21.4228 11.4197 22.1181 12.1544 23.0006 12.1544C23.8818 12.1544 24.6154 11.4197 24.6154 10.5384C24.6154 9.6572 23.8818 8.96182 22.9994 8.96182C22.8898 8.96182 22.7975 8.93966 22.6929 8.96182ZM16.6929 10.0387C15.7529 10.0111 14.8228 10.2369 14 10.6923C12.928 11.2781 12.1255 12.1741 11.6923 13.1538H20.6154C20.5437 12.777 20.4137 12.4137 20.2301 12.0769C19.5335 10.8043 18.1834 10.083 16.6929 10.0387ZM4.92307 15V31H32V15H4.92307ZM10.3458 16.9618H26.5772L27.3846 17.7692C27.3637 17.8714 27.3071 17.9686 27.3071 18.0769C27.3071 18.9594 28.0406 19.6929 28.9231 19.6929C29.0314 19.6929 29.1286 19.635 29.2308 19.6154L30.0381 20.4227V25.5772L29.2308 26.3846C29.1286 26.3637 29.0314 26.307 28.9231 26.307C28.0406 26.307 27.3071 27.0406 27.3071 27.923C27.3071 28.0314 27.3637 28.1286 27.3846 28.2307L26.5772 29.0381H10.3458L9.53845 28.2307C9.55938 28.1286 9.61599 28.0314 9.61599 27.923C9.61599 27.0406 8.88245 26.307 7.99999 26.307C7.89169 26.307 7.79445 26.3649 7.6923 26.3846L6.88492 25.5772V20.4227L7.6923 19.6154C7.79445 19.6363 7.89169 19.6929 7.99999 19.6929C8.88245 19.6929 9.61599 18.9594 9.61599 18.0769C9.61599 17.9686 9.55938 17.8714 9.53845 17.7692L10.3458 16.9618ZM18.4615 18.808C15.7932 18.808 13.6148 20.6787 13.6148 23C13.6148 25.3212 15.7932 27.192 18.4615 27.192C21.1298 27.192 23.3083 25.3212 23.3083 23C23.3083 20.6787 21.1298 18.808 18.4615 18.808ZM10.4615 21.384C9.5803 21.384 8.84553 22.1187 8.84553 23C8.84553 23.8812 9.5803 24.616 10.4615 24.616C11.3428 24.616 12.0775 23.8812 12.0775 23C12.0775 22.1187 11.3428 21.384 10.4615 21.384ZM26.4615 21.384C25.5791 21.384 24.8455 22.1175 24.8455 23C24.8455 23.8824 25.5791 24.616 26.4615 24.616C27.344 24.616 28.0775 23.8824 28.0775 23C28.0775 22.1175 27.344 21.384 26.4615 21.384Z",fill:"#56CCF2",stroke:"#56CCF2"})})}),e.jsxs("div",{className:"flex flex-col gap-[6px] py-3 ",children:[e.jsxs("div",{className:" font-['Poppins'] text-xs font-medium capitalize text-black ",children:[t.method_type=="credit_card"?"Deposit":t.method_type," ",t.account_type?`- ${t.account_type}`:""]}),t.account_number?e.jsxs("div",{className:" font-['Poppins'] text-xs font-medium text-[#8080a3] ",children:["Acc.# ",t.account_number]}):"",t.cedula_rnc?e.jsxs("div",{className:" font-['Poppins'] text-xs font-medium text-[#8080a3] ",children:["RNC/Cedula# ",t.cedula_rnc]}):""]})]})),e.jsxs(l,{to:"/provider/add-payment",className:"mb-10 mt-[11px] flex h-16 w-full items-center gap-4 rounded-[20px] bg-[#e0e0e0] p-[6px]",children:[e.jsx("div",{className:"relative flex h-12 w-12 items-center justify-center rounded-3xl border border-white bg-[#56ccf2]",children:e.jsxs("svg",{width:"17",height:"16",viewBox:"0 0 17 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M7.16667 1.33333C7.16667 0.596953 7.76362 0 8.5 0C9.23638 0 9.83333 0.596954 9.83333 1.33333V14.6667C9.83333 15.403 9.23638 16 8.5 16C7.76362 16 7.16667 15.403 7.16667 14.6667V1.33333Z",fill:"white"}),e.jsx("path",{d:"M1.83333 9.33333C1.09695 9.33333 0.5 8.73638 0.5 8C0.5 7.26362 1.09695 6.66667 1.83333 6.66667H15.1667C15.903 6.66667 16.5 7.26362 16.5 8C16.5 8.73638 15.903 9.33333 15.1667 9.33333H1.83333Z",fill:"white"})]})}),e.jsxs("div",{className:"",children:[e.jsx("div",{className:" font-['Poppins'] text-xs font-medium text-black",children:i("provider.profile.add_new_one")}),e.jsx("div",{className:" font-['Poppins'] text-xs font-medium text-[#8080a3]",children:i("provider.profile.payment_option")})]})]})]})]})]})]})};export{se as default};
