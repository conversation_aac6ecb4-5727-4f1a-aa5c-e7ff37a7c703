import{j as s}from"./@react-google-maps/api-ac2f9d6f.js";import{R as d,b as E}from"./vendor-4f06b3f4.js";import{u as k}from"./react-hook-form-f3d72793.js";import{o as D}from"./yup-2324a46a.js";import{c as P,a as m,e as U}from"./yup-17027d7a.js";import{G as B,A as C,M as y,s as F,t as M}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{M as l}from"./MkdInput-ff3aa862.js";import{I as _}from"./InteractiveButton-8f7d74ee.js";import{S as H}from"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./MoonLoader-16bed42a.js";const ce=({setSidebar:w})=>{var x;const{dispatch:u}=d.useContext(B),S=P({address:m().required("Address is required"),business_name:m().required("Business name is required"),phone:m().required("Phone is required"),website:m().url("Must be a valid URL"),image:U().required("Image is required"),hours:m(),status:m(),description:m(),type_id:m().required("Type is required")}).required(),{dispatch:j}=d.useContext(C),[o,N]=d.useState({}),[b,h]=d.useState(!1),[v,A]=d.useState([]),[L,g]=d.useState(!0);E();const{register:r,handleSubmit:T,setError:f,formState:{errors:i}}=k({resolver:D(S)}),R=(e,t)=>{let a=o;a[e]={file:t.files[0],tempURL:URL.createObjectURL(t.files[0])},N({...a})},I=async e=>{let t=new y;h(!0);try{for(let n in o){let p=new FormData;p.append("file",o[n].file);let c=await t.uploadImage(p);e[n]=c.url}t.setTable("nearby_location");const a=await t.callRestAPI({address:e.address,business_name:e.business_name,phone:e.phone,website:e.website,image:e.image,hours:e.hours,status:e.status,description:e.description,type_id:e.type_id},"POST");if(!a.error)F(u,"Added"),w(!1),u({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(a.validation){const n=Object.keys(a.validation);for(let p=0;p<n.length;p++){const c=n[p];f(c,{type:"manual",message:a.validation[c]})}}h(!1)}catch(a){h(!1),console.log("Error",a),f("address",{type:"manual",message:a.message}),M(j,a.message)}},q=async()=>{var e;try{g(!0);let t=new y;t.setTable("nearby_type");const a=await t.callRestAPI({},"GETALL");A((e=a==null?void 0:a.list)==null?void 0:e.map(n=>({value:n.id,name:n.name}))),g(!1)}catch(t){g(!1),console.log("error",t)}};return d.useEffect(()=>{u({type:"SETPATH",payload:{path:"nearby_location"}}),q()},[]),s.jsxs("div",{className:"mx-auto rounded p-5 shadow-md",children:[s.jsx("h4",{className:"text-2xl font-medium",children:"Add Location"}),L?s.jsx(H,{}):s.jsxs("form",{className:"w-full max-w-lg",onSubmit:T(I),children:[s.jsx(l,{type:"text",page:"add",name:"address",errors:i,label:"Address",placeholder:"Address",register:r,className:""}),s.jsx(l,{type:"text",page:"add",name:"business_name",errors:i,label:"Business Name",placeholder:"Business Name",register:r,className:""}),s.jsx(l,{type:"text",page:"add",name:"phone",errors:i,label:"Phone",placeholder:"Phone",register:r,className:""}),s.jsx(l,{type:"text",page:"add",name:"website",errors:i,label:"Website",placeholder:"Website",register:r,className:""}),o!=null&&o.image?s.jsx("div",{className:" py-4 ",children:s.jsx("img",{src:(x=o==null?void 0:o.image)==null?void 0:x.tempURL,alt:"",className:" h-[100px] w-auto "})}):"",s.jsx(l,{type:"file",page:"add",name:"image",errors:i,label:"Image",placeholder:"Upload Image",register:r,onChange:e=>R("image",e.target),className:""}),s.jsx(l,{type:"text",page:"add",name:"hours",errors:i,label:"Hours",placeholder:"Hours",register:r,className:""}),s.jsx(l,{type:"dropdown",page:"edit",name:"status",errors:i,label:"Status",placeholder:"Status",register:r,className:"",options:[{value:"inactive",name:"Inactive"},{value:"active",name:"Active"}]}),s.jsx(l,{type:"textarea",page:"add",name:"description",errors:i,label:"Description",placeholder:"Description",register:r,className:"",rows:"5"}),s.jsx(l,{type:"dropdown",page:"add",name:"type_id",errors:i,label:"Type",placeholder:"Select Type",register:r,options:v,className:""}),s.jsx(_,{type:"submit",loading:b,disabled:b,className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",children:"Submit"})]})]})};export{ce as default};
