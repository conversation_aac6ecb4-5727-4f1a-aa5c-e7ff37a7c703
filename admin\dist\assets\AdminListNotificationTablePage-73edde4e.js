import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as s,b as m,r as f}from"./vendor-4f06b3f4.js";import{M as u,A as h,G as S}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{L as x}from"./index-6416aa2c.js";import"./index-d97c616d.js";import{M as g}from"./index-68c82eaa.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";new u;const w=[{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"User Id",accessor:"user_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Title",accessor:"title",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Message",accessor:"message",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Update At",accessor:"update_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],V=()=>{s.useContext(h);const{dispatch:r}=s.useContext(S);m();const[b,l]=s.useState(!1),[E,d]=s.useState(!1),[A,c]=s.useState(),n=f.useRef(null),[D,p]=s.useState([]),a=(t,i,o=[])=>{switch(t){case"add":l(i);break;case"edit":d(i),p(o),c(o[0]);break}};return s.useEffect(()=>{r({type:"SETPATH",payload:{path:"notification"}})},[]),e.jsx(e.Fragment,{children:e.jsx(e.Fragment,{children:e.jsx("div",{className:"overflow-x-auto  rounded bg-white p-5 shadow",children:e.jsx(x,{children:e.jsx(g,{columns:w,tableRole:"admin",table:"notification",actionId:"id",actions:{view:{show:!1,action:null,multiple:!1},edit:{show:!1,multiple:!1,action:t=>a("edit",!0,t)},delete:{show:!1,action:null,multiple:!1},select:{show:!1,action:null,multiple:!1},add:{show:!1,action:()=>a("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!0,action:null,multiple:!0}},actionPostion:"ontable",refreshRef:n})})})})})};export{V as default};
