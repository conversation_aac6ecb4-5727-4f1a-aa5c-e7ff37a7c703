import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{r as _,u as j,h as v,d as P}from"./vendor-f36d475e.js";import{u as b}from"./react-hook-form-ff037c98.js";import{o as y}from"./yup-afe5cf51.js";import{c as S,a as l,b as N}from"./yup-2f6e2476.js";import{A as L,a as q}from"./index-55e4d382.js";import{P as d}from"./index-ad319f83.js";import{B,a as k}from"./index-895fa99b.js";import{M as A}from"./index-cf5e6bc7.js";import{u as E}from"./react-i18next-1e3e6bc5.js";import"./@hookform/resolvers-eb417cd0.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";let I=new A;const re=()=>{var p,c;const[r,o]=_.useState(!1),i=j();new URLSearchParams(i.search).get("phone");const{t}=E(),{code:u}=v(),f=S({password:l().min(8,t("auth_pages.reset.password_long")).required(),confirm_password:l().oneOf([N("password"),null],t("auth_pages.reset.password_must")).required()}).required(),{register:m,handleSubmit:h,setError:g,formState:{errors:s}}=b({resolver:y(f)}),n=P(),x=async w=>{try{o(!0);const a=await I.callRawAPI("/v3/api/custom/chumpchange/provider/password-reset/verify-code",{phone:i.state.phone,confirmationCode:u,password:w.password},"POST");localStorage.getItem("token")?n("/provider/home"):n("/provider/login"),o(!1)}catch(a){o(!1),console.log("Error",a),g("phone",{type:"manual",message:a==null?void 0:a.message})}};return e.jsx(L,{children:e.jsxs("form",{onSubmit:h(x),children:[e.jsx(B,{}),e.jsx(q,{className:"-mt-6 leading-none ",children:t("auth_pages.reset.title")}),e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx("div",{className:" flex h-[70px] flex-col items-center",children:s!=null&&s.password||s!=null&&s.confirm_password?e.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:e.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:((p=s==null?void 0:s.password)==null?void 0:p.message)||((c=s==null?void 0:s.confirm_password)==null?void 0:c.message)})}):e.jsx("div",{className:"font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:t("auth_pages.reset.sub_title_password")})}),e.jsx(d,{name:"password",placeholder:t("auth_pages.reset.password"),errors:s,register:m}),e.jsx(d,{name:"confirm_password",placeholder:t("auth_pages.reset.confirm_password"),errors:s,register:m}),e.jsx("div",{className:"flex justify-center",children:e.jsx("div",{className:"text-center font-['Poppins'] text-sm font-medium leading-snug text-[#8080a3]",dangerouslySetInnerHTML:{__html:t("auth_pages.reset.password_des")}})}),e.jsx("div",{className:" mt-10 ",children:e.jsx(k,{loading:r,disabled:r,type:"submit",children:t("auth_pages.reset.set_pass")})})]})]})})};export{re as default};
