import{j as t}from"./@react-google-maps/api-ee55a349.js";import{r as m,R as i,i as O,d as K}from"./vendor-b16525a8.js";import"./index-9ef1c042.js";import{M as Z,A as J,G as Q,f as X,o as Y,r as M,t as v}from"./index-09a1718e.js";import{a as k,R as f,B as ee}from"./index-d54cffea.js";import{C as te}from"./index-49471902.js";import{M as d}from"./index-243c4859.js";import{G as R}from"./react-icons-488361f7.js";import{R as se}from"./react-rating-71fc9949.js";import{S as L}from"./index-5a645c18.js";import{t as s}from"./i18next-7389dd8c.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";function ae(o){return R({tag:"svg",attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"}}]})(o)}function re(o){return R({tag:"svg",attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M528.1 171.5L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6zM388.6 312.3l23.7 138.4L288 385.4l-124.3 65.3 23.7-138.4-100.6-98 139-20.2 62.2-126 62.2 126 139 20.2-100.6 98z"}}]})(o)}const u=new Z,qe=()=>{var w,q;const{state:o,dispatch:h}=m.useContext(J);m.useContext(Q);const[e,A]=m.useState(null),[B,j]=i.useState(!0),[I,l]=i.useState(!1),[ie,g]=i.useState(!1),[H,D]=i.useState(!1),[E,y]=i.useState(!1),[T,b]=i.useState(!1),[z,x]=i.useState(!1),[G,p]=i.useState(!1),[r,V]=i.useState(null),[P,W]=m.useState(2),[N,U]=m.useState(""),n=O(),C=K(),S=async()=>{try{j(!0),u.setTable("task");const a=await u.callRestAPI({id:Number(n==null?void 0:n.id),join:"user,location,service"},"GET");if(!a.error){A(a.model),b(a.model.provider_status=="completed"&&a.model.status!="completed"),p(a.model.provider_status=="in-progress"&&(a.model.status=="created"||a.model.status=="accepted"));const c=await u.callRawAPI(`/v3/api/custom/chumpchange/user/${Number(a.model.provider_id)}`,{},"GET");V(c.data),j(!1)}}catch(a){j(!1),console.log("error",a),v(h,a.message)}},_=async a=>{try{l(!0),g(!0),x(!1),p(!1),(await u.callRawAPI("/v3/api/custom/chumpchange/user/task/respond",{task_id:Number(n==null?void 0:n.id),status:a},"POST")).error||await S(),l(!1),g(!1)}catch(c){g(!1),l(!1),v(h,c.message)}},$=async()=>{try{l(!0),b(!1),(await u.callRawAPI("/v3/api/custom/chumpchange/user/rating/create",{client_id:localStorage.getItem("user"),provider_id:e.provider_id,job_id:Number(n==null?void 0:n.id),rating:P,description:N},"POST")).error||await _("completed"),l(!1)}catch(a){l(!1),v(h,a.message)}};i.useEffect(function(){(async function(){await S()})()},[]);const F=()=>{const a=new URLSearchParams({price:e==null?void 0:e.offer,startDate:e==null?void 0:e.start_datetime,endDate:e==null?void 0:e.start_datetime,location:e==null?void 0:e.location_id,description:e==null?void 0:e.description,skill:e==null?void 0:e.service_id,images:e==null?void 0:e.image}).toString();C(`/user/helping-hand-request?${a}`)};return t.jsxs("div",{className:"p-5",children:[E&&t.jsx(d,{closeModal:()=>y(!1),children:t.jsx("div",{className:" mt-10 flex h-max w-full flex-col items-center justify-center gap-8 pb-6 ",children:t.jsx("img",{className:" h-auto w-full max-w-[450px] object-cover ",src:H})})}),I&&t.jsx(d,{showCloseButton:!1,children:t.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[t.jsxs("div",{className:"text-lg font-semibold",children:[s("loading.creating"),"..."]}),t.jsx("div",{className:"mt-12",children:t.jsx(L,{})})]})}),T&&t.jsx(d,{closeModal:()=>b(!1),children:t.jsxs("div",{className:" mt-10 flex w-full flex-col items-center justify-center ",children:[t.jsxs("div",{className:"w-[230px] text-center font-['Poppins'] text-2xl font-medium leading-[31.20px] text-black",children:[s("user.view_request.how_helpful_was"),t.jsx("br",{}),r==null?void 0:r.first_name," ",r==null?void 0:r.last_name,t.jsx("br",{}),s("user.view_request.on_this_task")]}),t.jsx("div",{className:"mt-5",children:t.jsx(se,{fractions:1,initialRating:P,onChange:a=>{console.log(`New rating: ${a}`),W(a)},emptySymbol:t.jsx(re,{size:30,color:"#ddd"}),fullSymbol:t.jsx(ae,{size:30,color:"#56ccf2"})})}),t.jsx("div",{className:"mt-5 w-full px-5",children:t.jsxs("div",{className:"relative  h-[70px] w-full ",children:[t.jsx("div",{className:" absolute left-5 top-3 z-[1] h-5 w-5 ",children:t.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[t.jsx("path",{d:"M7.08335 15.8332H6.66669C3.33335 15.8332 1.66669 14.9998 1.66669 10.8332V6.6665C1.66669 3.33317 3.33335 1.6665 6.66669 1.6665H13.3334C16.6667 1.6665 18.3334 3.33317 18.3334 6.6665V10.8332C18.3334 14.1665 16.6667 15.8332 13.3334 15.8332H12.9167C12.6584 15.8332 12.4084 15.9582 12.25 16.1665L11 17.8332C10.45 18.5665 9.55002 18.5665 9.00002 17.8332L7.75002 16.1665C7.61669 15.9832 7.30835 15.8332 7.08335 15.8332Z",stroke:"#8181A4",strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),t.jsx("path",{d:"M13.3304 9.16667H13.3378",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),t.jsx("path",{d:"M9.99626 9.16667H10.0037",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),t.jsx("path",{d:"M6.66209 9.16667H6.66957",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),t.jsx("textarea",{type:"text",placeholder:s("user.view_request.performance"),value:N,onChange:a=>U(a.target.value),className:" absolute left-0 top-0 h-full w-full rounded-2xl border-none bg-[#f7f7f7] p-3 pl-[50px] font-['Poppins'] text-xs font-medium text-[#111] outline-none focus:border-none focus:outline-none focus:ring-0 "})]})}),t.jsx("div",{className:"mt-10 w-full px-5 pb-6 ",children:t.jsx(k,{onClick:$,disabled:!N,children:s("buttons.submit")})})]})}),G&&t.jsx(d,{closeModal:()=>p(!1),children:t.jsxs("div",{className:" mt-10 flex w-full flex-col items-center justify-center ",children:[t.jsxs("div",{className:"w-[230px] text-center font-['Poppins'] text-2xl font-medium leading-[31.20px] text-black",children:[s("user.view_request.has")," ",r==null?void 0:r.first_name,t.jsx("br",{}),s("user.view_request.started")]}),t.jsxs("div",{className:"mt-[80px] flex w-full gap-2 px-5 pb-6 ",children:[t.jsx("button",{onClick:()=>p(!1),className:"w-max-[163px] relative h-12 w-full rounded-2xl bg-[#8181a4]/20 text-center font-['Poppins'] text-sm font-medium text-[#8080a3]",children:s("user.view_request.no_yet")}),t.jsx(k,{onClick:()=>_("in-progress"),className:"!w-max-[163px] w-full",children:s("user.view_request.yes_confirm")})]})]})}),z&&t.jsx(d,{closeModal:()=>x(!1),children:t.jsxs("div",{className:" mt-10 flex w-full flex-col items-center justify-center ",children:[t.jsx("div",{className:"w-[230px] text-center font-['Poppins'] text-2xl font-medium leading-[31.20px] text-black",dangerouslySetInnerHTML:{__html:s("user.view_request.sure")}}),t.jsx("div",{className:"mt-[80px] w-[163px] px-5 pb-6 ",children:t.jsx(f,{onClick:()=>_("cancelled"),className:"",children:s("buttons.cancel")})})]})}),B?t.jsx("div",{className:" flex h-screen w-full items-center justify-center ",children:t.jsx(L,{})}):t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:"relative flex w-full items-center justify-center ",children:[t.jsx("div",{className:" absolute left-0 top-0 ",children:t.jsx(ee,{link:"/user/my-requests"})}),t.jsxs("div",{className:"",children:[t.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:s("user.view_request.title")}),t.jsxs("div",{className:"text-center font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:[s("user.view_request.active"),":"," ",e!=null&&e.provider_status?e==null?void 0:e.provider_status:s("user.view_request.on_offer")]})]})]}),t.jsx("div",{className:"mt-[30px] font-['Poppins'] text-lg font-medium text-[#8080a3]",children:e!=null&&e.provider_status?s("user.view_request.your_helper")+" :":t.jsx("div",{dangerouslySetInnerHTML:{__html:s("user.view_request.awaiting")}})}),(e==null?void 0:e.provider_status)&&r&&t.jsx(te,{userData:r,className:"mt-5"}),t.jsxs("div",{className:" mt-9 ",children:[t.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[t.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:s("user.view_request.need_help")}),t.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:e!=null&&e.title?e==null?void 0:e.title:"N/A"})]}),t.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[t.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:s("user.view_request.date_time")}),t.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:X(e==null?void 0:e.start_datetime)})]}),t.jsxs("div",{className:"flex items-center justify-between gap-4 border-t border-[#8181a4]/20 py-3 ",children:[t.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:s("user.view_request.location")}),t.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:t.jsxs("button",{onClick:()=>{Y(Number(e==null?void 0:e.latitude),Number(e==null?void 0:e.longtitude))},className:" text-right text-[#4fa7f9] underline ",children:[" ",e!=null&&e.address?M(e==null?void 0:e.address):"N/A"]})})]}),t.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[t.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:s("user.view_request.pay_offered")}),t.jsxs("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:["DOP ",e!=null&&e.offer?e==null?void 0:e.offer:"N/A"]})]}),t.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[t.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:s("user.view_request.my_number")}),t.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:o.userDetails.phone})]}),t.jsxs("div",{className:"flex justify-between border-t border-[#8181a4]/20 py-3 ",children:[t.jsxs("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:[s("user.view_request.description"),":"]}),t.jsx("div",{className:"w-[165px] text-right font-['Poppins'] text-sm font-light leading-[16.80px] text-black",children:e!=null&&e.description?e==null?void 0:e.description:"N/A"})]}),((w=e==null?void 0:e.image)==null?void 0:w.split(",").length)>0&&t.jsxs("div",{className:" mt-7 border-t border-[#8181a4]/20 py-3 pt-8 ",children:[t.jsx("div",{className:"font-['Poppins'] text-lg font-medium text-black",children:s("user.view_request.images")}),t.jsx("div",{className:"scrollbar-hide flex w-full overflow-x-auto",children:t.jsx("div",{className:" mt-5 flex gap-2",children:(q=e==null?void 0:e.image)==null?void 0:q.split(",").map((a,c)=>t.jsx("img",{onClick:()=>{y(!0),D(a)},className:"h-[127px] w-[120px] rounded-[20px] object-cover ",src:a},c))})})]})]}),t.jsxs("div",{className:"mt-10 flex flex-col gap-3",children:[(!(e!=null&&e.provider_status)||(e==null?void 0:e.provider_status)=="accepted")&&t.jsx(f,{onClick:()=>x(!0),className:"uppercase opacity-[0.85]",children:s("buttons.cancel")}),((e==null?void 0:e.status)=="in-progress"||(e==null?void 0:e.provider_status)=="in-progress")&&t.jsx(f,{onClick:()=>x(!0),className:" !bg-[#e0e0e0] uppercase text-white opacity-[0.85] disabled:!opacity-100 ",disabled:!0,children:s("buttons.cancel")}),(!(e!=null&&e.status)||(e==null?void 0:e.status)=="created")&&(e==null?void 0:e.provider_status)=="completed"&&t.jsx(f,{onClick:()=>C(`/user/report-issue/${e.id}`),className:"opacity-[0.85]",children:s("user.view_request.report_issue")}),((e==null?void 0:e.status)=="completed"||(e==null?void 0:e.provider_status)=="completed")&&t.jsx(k,{onClick:F,className:"opacity-[0.85]",children:s("user.view_request.reorder")})]})]})]})};export{qe as default};
