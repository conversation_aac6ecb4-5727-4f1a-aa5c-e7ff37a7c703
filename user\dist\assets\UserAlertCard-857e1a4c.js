import{j as e}from"./@react-google-maps/api-ee55a349.js";import{t as s}from"./i18next-7389dd8c.js";import{L as r}from"./vendor-b16525a8.js";import{f as l}from"./index-09a1718e.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";const P=({taskData:i})=>e.jsx("div",{className:" flex h-full max-h-[calc(100vh-250px)] w-full flex-col gap-4 overflow-y-auto px-5 py-5 ",children:i.length===0?e.jsx("div",{className:"mt-5 w-full text-center font-['Poppins'] text-2xl font-medium text-[#b4b4b4]",dangerouslySetInnerHTML:{__html:s("user.my_alert.no_alert")}}):i.map((t,m)=>e.jsxs(r,{to:`/user/my-alert-view/${t.id}`,className:"inline-flex items-center justify-start gap-[5px]",children:[e.jsx("div",{className:"flex h-11 w-12 flex-shrink-0 items-center justify-center overflow-hidden rounded-full bg-[#50a8f9]/10",children:e.jsx("img",{src:t==null?void 0:t.service_logo,alt:"",className:" h-full w-full object-cover "})}),e.jsxs("div",{className:"flex w-full flex-col items-start justify-center gap-[2px] pb-2",children:[e.jsxs("div",{className:"flex items-center justify-start gap-1 flex-wrap ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:t==null?void 0:t.service_name}),e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:"→"}),e.jsxs("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:[t==null?void 0:t.service_alert_distance," ",s("user.my_alert.meters"),":"]})]}),t.status=="active"?e.jsxs("div",{className:"flex items-center justify-start gap-1",children:[e.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:t==null?void 0:t.operating_city}),e.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:"•"}),e.jsx("div",{className:"text-right font-['Poppins'] text-xs font-medium text-[#8080a3]",children:l(t.update_at)})]}):e.jsxs("div",{className:"inline-flex items-center justify-start gap-1",children:[e.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:s("user.my_alert.marias")}),e.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:"•"}),e.jsx("div",{className:"text-right font-['Poppins'] text-xs font-medium text-[#fd5d5d]",children:s("user.my_alert.inactive")})]}),e.jsxs("div",{className:"flex items-center justify-start gap-1",children:[e.jsxs("div",{className:"font-['Poppins'] text-xs font-medium text-[#56ccf2]",children:[s("user.my_alert.message"),":"]}),e.jsx("div",{className:" w-1.5 font-['Poppins'] text-xs font-medium text-[#8080a3]",children:"•"}),e.jsx("div",{className:" font-['Poppins'] text-xs font-medium text-[#8080a3]",children:t.message})]})]})]},m))});export{P as default};
