import{_ as s}from"./audio-a0565fb1.js";import{t as p}from"./dashboard-f719adde.js";import{U as l}from"./core-10860ef6.js";const o={strings:{chooseFiles:"Choose files"}},h={version:"3.1.2"},a={pretty:!0,inputName:"files[]"};let u=class extends l{constructor(t,e){super(t,{...a,...e}),this.id=this.opts.id||"FileInput",this.title="File Input",this.type="acquirer",this.defaultLocale=o,this.i18nInit(),this.render=this.render.bind(this),this.handleInputChange=this.handleInputChange.bind(this),this.handleClick=this.handleClick.bind(this)}addFiles(t){const e=t.map(i=>({source:this.id,name:i.name,type:i.type,data:i}));try{this.uppy.addFiles(e)}catch(i){this.uppy.log(i)}}handleInputChange(t){this.uppy.log("[FileInput] Something selected through input...");const e=p(t.target.files);this.addFiles(e),t.target.value=null}handleClick(){this.input.click()}render(){const t={width:"0.1px",height:"0.1px",opacity:0,overflow:"hidden",position:"absolute",zIndex:-1},{restrictions:e}=this.uppy.opts,i=e.allowedFileTypes?e.allowedFileTypes.join(","):void 0;return s("div",{className:"uppy-FileInput-container"},s("input",{className:"uppy-FileInput-input",style:this.opts.pretty?t:void 0,type:"file",name:this.opts.inputName,onChange:this.handleInputChange,multiple:e.maxNumberOfFiles!==1,accept:i,ref:n=>{this.input=n}}),this.opts.pretty&&s("button",{className:"uppy-FileInput-btn",type:"button",onClick:this.handleClick},this.i18n("chooseFiles")))}install(){const{target:t}=this.opts;t&&this.mount(t,this)}uninstall(){this.unmount()}};u.VERSION=h.version;export{u as F};
