import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{R as a,u as D,d as I,r,h as $}from"./vendor-f36d475e.js";import{u as G}from"./react-hook-form-ff037c98.js";import{o as U}from"./yup-afe5cf51.js";import{c as V,a as n}from"./yup-2f6e2476.js";import{M as q,A as F,G as H,n as K,t as O,s as b}from"./index-cf5e6bc7.js";import"./react-quill-3f3a006b.js";/* empty css                   */import"./InteractiveButton-303096ac.js";import"./index-bec80226.js";import{B as z,a as J,R as Q}from"./index-895fa99b.js";import W from"./DropDownSelection-d1fecb01.js";import{M as k}from"./index-bf8d79cc.js";import{u as X}from"./react-i18next-1e3e6bc5.js";import{L as Y}from"./@mantine/core-ee88fb98.js";import"./@hookform/resolvers-eb417cd0.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./@craftjs/core-01ce56b9.js";import"./MoonLoader-4d8718ee.js";import"./@emotion/serialize-460cad7f.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-47271980.js";let h=new q;const Ie=Z=>{const{dispatch:y}=a.useContext(F),N=V({skill_id:n(),price:n(),avaibility_start:n(),avaibility_end:n(),description:n(),status:n()}).required(),{dispatch:p}=a.useContext(H);a.useState({});const[_,l]=a.useState(!1),[w,m]=a.useState(!1),[u,E]=a.useState([]),[ee,f]=a.useState(!1),c=D().state;console.log("skill >>",c);const P=I(),[C,x]=r.useState({name:"Select"});r.useState(0),r.useState(""),r.useState(""),r.useState(""),r.useState(""),r.useState("");const{register:R,handleSubmit:L,setError:g,setValue:v,formState:{errors:se}}=G({resolver:U(N)}),{t:o}=X(),j=$();console.log("skill >> ",c),r.useEffect(()=>{v("price",c.price),v("service_id",c.service_id),x(u.find(s=>s.id==c.service_id))},[c,u]);const B=async()=>{var s;try{f(!0);const t=await h.callRawAPI("/v3/api/custom/chumpchange/users/services",{},"GET");if(console.log("services",t),!t.error){let d=[{name:"Select"}];(s=t==null?void 0:t.data)==null||s.map(i=>{d.push({name:i==null?void 0:i.name,price:i==null?void 0:i.price,id:i.id})}),E(d),f(!1)}}catch(t){f(!1),console.log("error",t),O(y,t.message)}};a.useEffect(()=>{B()},[]);const M=async s=>{l(!0);try{const t=await h.callRawAPI(`/v3/api/custom/chumpchange/provider/skills/edit/${j.id}`,{service_id:s.service_id,price:s.price},"PUT");if(!t.error)b(p,o("toast.updated"));else if(t.validation){const d=Object.keys(t.validation);for(let i=0;i<d.length;i++){const S=d[i];g(S,{type:"manual",message:t.validation[S]})}}l(!1)}catch(t){l(!1),console.log("Error",t),g("product_id",{type:"manual",message:t.message})}},T=s=>{x(s),v("service_id",s.id)};a.useEffect(()=>{p({type:"SETPATH",payload:{path:"skill"}})},[]);const A=async()=>{m(!1);try{l(!0),(await h.callRawAPI(`/v3/api/custom/chumpchange/provider/skills/delete/${j.id}`,{},"DELETE")).error||(b(p,o("toast.removed")),P("/provider/profile")),l(!1)}catch(s){console.log("Error",s),l(!1)}};return e.jsxs("div",{className:"p-5",children:[_&&e.jsx(k,{showCloseButton:!1,children:e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsxs("div",{className:"text-lg font-semibold",children:[o("loading.uploading"),"..."]}),e.jsx("div",{className:"mt-12",children:e.jsx(Y,{})})]})}),w&&e.jsx(k,{closeModal:()=>m(!1),children:e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsx("div",{className:"text-lg font-semibold",children:o("provider.add_skill.sure")}),e.jsxs("div",{className:"mt-12 flex w-full justify-evenly gap-5 px-5",children:[e.jsx("button",{onClick:A,className:"text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:o("buttons.remove")}),e.jsxs("button",{onClick:()=>m(!1),className:"relative flex h-12 w-[163px] items-center justify-center rounded-2xl bg-[#8181a4]/20 text-center font-['Poppins'] text-sm font-medium text-[#8080a3]",children:[" ",o("buttons.cancel")]})]})]})}),e.jsxs("div",{className:"relative flex w-full items-center justify-center ",children:[e.jsx("div",{className:" absolute left-0 top-0 ",children:e.jsx(z,{})}),e.jsx("div",{className:"mt-3 text-center font-['Poppins'] text-lg font-medium text-black",children:o("provider.add_skill.e_title")})]}),e.jsxs("form",{className:" w-full ",onSubmit:L(M),children:[e.jsx("div",{className:"mt-[50px] font-['Poppins'] text-[22px] font-medium text-black",children:o("provider.add_skill.help_with")}),e.jsxs("div",{className:"mt-[27px] w-full ",children:[e.jsx("div",{className:"mb-1 px-2 font-['Poppins'] text-sm font-medium text-[#8080a3] ",children:o("provider.add_skill.service")}),e.jsx(W,{services:u,onServiceSelect:T,selectedService:C,setSelectedService:x})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("div",{className:" px-2 font-['Poppins'] text-[22px] font-bold tracking-wide text-black",children:o("provider.add_skill.rate")}),e.jsx("input",{type:"text",placeholder:o("provider.add_skill.p_rate"),...R("price",{onChange:s=>K(s,10)}),className:"relative mt-3 h-12 w-full rounded-2xl border-none bg-white text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "})]}),e.jsxs("div",{className:"mt-[35px]",children:[e.jsx(J,{type:"submit",children:o("buttons.save")}),e.jsx(Q,{className:"mt-[17px]",onClick:()=>m(!0),children:o("buttons.remove")})]})]})]})};export{Ie as default};
