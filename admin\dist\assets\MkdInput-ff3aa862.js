import{j as o}from"./@react-google-maps/api-ac2f9d6f.js";import"./vendor-4f06b3f4.js";import{S as N}from"./index-06b5b6dd.js";const P=({type:s="text",page:b,cols:m="30",rows:v="50",name:e,label:S,errors:n,register:d,className:r,placeholder:l,options:u=[],mapping:c=null,disabled:a=!1,onChange:k})=>{var x,h,p,g,f,w,j,$;return o.jsx(o.Fragment,{children:o.jsxs("div",{className:`mb-4 ${b==="list"?"w-full pl-2 pr-2 md:w-1/2":""}`,children:[o.jsx("label",{className:"mb-2 block cursor-pointer text-sm font-bold text-gray-700",htmlFor:e,children:N(S,{casetype:"capitalize",separator:"space"})}),s==="textarea"?o.jsx("textarea",{className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${r} ${(x=n[e])!=null&&x.message?"border-red-500":""}`,disabled:a,id:e,cols:m,name:e,placeholder:l,rows:v,...d(e)}):s==="radio"||s==="checkbox"||s==="color"?o.jsx("input",{disabled:a,type:s,id:e,name:e,placeholder:l,...d(e),className:`focus:shadow-outline cursor-pointer appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${r} ${(h=n[e])!=null&&h.message?"border-red-500":""} ${s==="color"?"min-h-[3.125rem] min-w-[6.25rem]":""}`}):s==="dropdown"?o.jsxs("select",{type:s,id:e,disabled:a,placeholder:l,...d(e),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${r} ${(p=n[e])!=null&&p.message?"border-red-500":""}`,children:[o.jsx("option",{children:"Select"}),u.map((t,i)=>o.jsx("option",{value:t.value,children:t.name},i+1))]}):s==="select"?o.jsxs("select",{type:s,id:e,disabled:a,placeholder:l,...d(e),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${r} ${(g=n[e])!=null&&g.message?"border-red-500":""}`,children:[o.jsx("option",{value:"",children:"Select"}),u.map((t,i)=>o.jsx("option",{value:t,children:t},i+1))]}):s==="mapping"?o.jsx(o.Fragment,{children:c?o.jsxs("select",{type:s,id:e,disabled:a,placeholder:l,...d(e),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${r} ${(f=n[e])!=null&&f.message?"border-red-500":""}`,children:[o.jsx("option",{children:"Select"}),u.map((t,i)=>o.jsx("option",{value:t,children:c[t]},i+1))]}):"Please Pass the mapping e.g {key:value}"}):s==="file"?o.jsx("input",{type:s,id:e,disabled:a,placeholder:l,...d(e,{onChange:k}),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${r} ${(w=n[e])!=null&&w.message?"border-red-500":""}`}):o.jsx("input",{type:s,id:e,disabled:a,placeholder:l,...d(e),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${r} ${(j=n[e])!=null&&j.message?"border-red-500":""}`}),o.jsx("p",{className:"text-field-error italic text-red-500",children:($=n[e])==null?void 0:$.message})]})})};export{P as M};
