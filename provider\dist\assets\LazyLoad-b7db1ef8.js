import{j as l}from"./@react-google-maps/api-afbf18d5.js";import{r as m,R as i}from"./vendor-f36d475e.js";import{S as d}from"./index-bec80226.js";import"./qr-scanner-cf010ec4.js";const x=({children:a,counts:p=[1],count:n=1,circle:c=!1})=>{var s,r,e,t;const o=i.Children.toArray(a).filter(Boolean),f=(r=(s=o.filter(Boolean)[0])==null?void 0:s.props)!=null&&r.className?(t=(e=o[0])==null?void 0:e.props)==null?void 0:t.className:"";return l.jsx(m.Suspense,{fallback:l.jsx(d,{counts:p,count:n,className:f,circle:c}),children:a})},N=m.memo(x);export{N as default};
