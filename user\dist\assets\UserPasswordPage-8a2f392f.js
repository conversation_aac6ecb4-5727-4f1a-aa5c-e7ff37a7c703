import{j as s}from"./@react-google-maps/api-ee55a349.js";import{r as P,R as d,i as v,d as N}from"./vendor-b16525a8.js";import{u as y}from"./react-hook-form-b6ed2679.js";import{o as S}from"./yup-3990215a.js";import{c as A,a as l,b as B}from"./yup-f828ae80.js";import{A as L,a as C}from"./index-dd254604.js";import{P as c}from"./index-4ee87ce5.js";import{B as G,a as I}from"./index-d54cffea.js";import{M as R,A as T,G as k,s as q}from"./index-09a1718e.js";import{t as a}from"./i18next-7389dd8c.js";import"./@hookform/resolvers-3e831b4a.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";let E=new R;const ms=()=>{var n,m;const[i,o]=P.useState(!1),{state:M,dispatch:u}=d.useContext(T),{dispatch:f}=d.useContext(k),{id:g}=v(),x=N(),h=A({password:l().min(8,a("auth_pages.sign_up.password_long")).required(),confirm_password:l().oneOf([B("password"),null],a("auth_pages.sign_up.password_must")).required()}).required(),{register:r,handleSubmit:_,setError:w,formState:{errors:t}}=y({resolver:S(h)}),j=async p=>{try{o(!0);const e=await E.callRawAPI("/v3/api/custom/chumpchange/user/signup/step3",{password:p.password,confirmPassword:p.confirm_password,email:g},"POST"),b={user_id:e.data[0].id,...e.data[0]};console.log("result >> ",e),e.error||(u({type:"LOGIN",payload:b}),x("/user/onboarding")),o(!1)}catch(e){o(!1),console.log("error >> ",e),q(f,e==null?void 0:e.message,4e3,"error"),w("password",{type:"manual",message:e==null?void 0:e.message})}};return s.jsx(L,{children:s.jsxs("form",{onSubmit:_(j),children:[s.jsx(G,{}),s.jsx(C,{title:"Sign up",className:" -mt-6 "}),s.jsxs("div",{className:"flex flex-col gap-4",children:[s.jsx("div",{className:" flex h-[70px] flex-col items-center",children:t!=null&&t.password||t!=null&&t.confirm_password?s.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:s.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:((n=t==null?void 0:t.password)==null?void 0:n.message)||((m=t==null?void 0:t.confirm_password)==null?void 0:m.message)})}):s.jsx("div",{className:"font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:a("auth_pages.sign_up.sub_title_password")})}),s.jsx(c,{name:"password",placeholder:a("auth_pages.sign_up.password"),errors:t,register:r}),s.jsx(c,{name:"confirm_password",placeholder:a("auth_pages.sign_up.confirm_password"),errors:t,register:r}),s.jsx("div",{className:"flex justify-center",children:s.jsx("div",{className:"text-center font-['Poppins'] text-sm font-medium leading-snug text-[#8080a3]",dangerouslySetInnerHTML:{__html:a("auth_pages.sign_up.password_des")}})}),s.jsx("div",{className:" mt-10 ",children:s.jsx(I,{loading:i,disabled:i,type:"submit",children:a("auth_pages.sign_up.sign_in")})})]})]})})};export{ms as default};
