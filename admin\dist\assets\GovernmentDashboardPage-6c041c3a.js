import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as a}from"./vendor-4f06b3f4.js";import{M as c,A as d,G as x}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{S as f}from"./react-loading-skeleton-e20104c1.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";let h=new c;const T=()=>{a.useContext(d);const{dispatch:r}=a.useContext(x),[i,o]=a.useState(!0),[l,n]=a.useState({}),m=async()=>{try{o(!0);const e=await h.callRawAPI("/v3/api/custom/chumpchange/government/dashboard",{},"GET");n(e.data),o(!1)}catch(e){o(!1),console.log("error >> ",e)}};return a.useEffect(()=>{r({type:"SETPATH",payload:{path:"government"}}),m()},[]),t.jsxs("div",{className:"px-10 py-10",children:[t.jsx("h1",{className:"font-['Poppins'] text-[32px] font-medium leading-normal text-black",children:"Chiriperos Dashboard"}),i?t.jsx("div",{className:"mt-8 grid grid-cols-4 gap-8",children:Array.from({length:7}).map((e,s)=>t.jsx(f,{className:"h-[154px] w-full "},s))}):t.jsx("div",{className:"mt-8 grid grid-cols-4 gap-8",children:Object.entries(l).map(([e,s])=>t.jsxs("div",{className:" inline-flex h-[154px] w-full flex-col items-center justify-center gap-6 rounded-lg border border-[#e4e6eb] bg-white p-4 shadow",children:[t.jsx("div",{className:"font-['Inter'] text-3xl font-semibold leading-[38px] text-[#0f1728]",children:s.total}),t.jsx("div",{className:"font-['Poppins'] text-base font-medium leading-normal text-[#0f1728]",children:e.replace(/([A-Z])/g," $1").replace(/^./,p=>p.toUpperCase())})]},e))})]})};export{T as default};
