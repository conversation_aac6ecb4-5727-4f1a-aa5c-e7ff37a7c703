import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as n,b as je,r as be}from"./vendor-4f06b3f4.js";import{M as ve,G as Ne,A as ye,d as we,a as Ce,R as Se,t as U}from"./index-06b5b6dd.js";import{o as ke}from"./yup-2324a46a.js";import{u as Pe}from"./react-hook-form-f3d72793.js";import{c as _e,a as m}from"./yup-17027d7a.js";import{P as Fe}from"./index-19801678.js";import"./AddButton.module-98aac587.js";import{S as Le}from"./index-2d8231e7.js";import{B as Me,R as Ae,A as Ee}from"./index.esm-1a4cea12.js";import{M as q}from"./index-d97c616d.js";import{M as De,D as Ie}from"./index-f6c8bd1f.js";import{L as Te}from"./index-6416aa2c.js";import{h as Oe}from"./moment-a9aaa855.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@hookform/resolvers-1aa18522.js";import"./react-icons-e5379072.js";let g=new ve;const Y=[{header:"Created",accessor:"create_at"},{header:"ID",accessor:"id"},{header:"First Name",accessor:"first_name"},{header:"Last Name",accessor:"last_name"},{header:"Cedula#",accessor:"cedula_number"},{header:"Phone#",accessor:"phone"},{header:"Docs?",accessor:"docs"},{header:"Status",accessor:"status",mapping:["Inactive","Active","Suspend"]},{header:"Action",accessor:""}],Re=[{header:"Created",accessor:"create_at"},{header:"User ID",accessor:"id"},{header:"First Name",accessor:"first_name"},{header:"Last Name",accessor:"last_name"},{header:"Cedula#",accessor:"cedula_number"},{header:"Phone#",accessor:"phone"},{header:"Status",accessor:"status",mapping:["Inactive","Active","Suspend"]}],p=["create_at","update_at"],He=["Inactive","Active","Suspend"],fs=()=>{const{dispatch:t}=n.useContext(Ne),{dispatch:u}=n.useContext(ye),[x,_]=n.useState([]),[d,F]=n.useState(10),[W,G]=n.useState(0),[Z,K]=n.useState(0),[f,Q]=n.useState(0),[X,ee]=n.useState(!1),[se,te]=n.useState(!1),[L,M]=n.useState(!1),[A,E]=n.useState(!1),[o,j]=n.useState([]),[ae,b]=n.useState([]),[D,ne]=n.useState("eq"),[v,N]=n.useState(!0),[I,re]=n.useState(!0),[T,y]=n.useState(!1),[ie,O]=n.useState(!1),[le,h]=n.useState(!1),[R,H]=n.useState(null),[oe,w]=n.useState(!1),[de,ce]=n.useState();je();const C=n.useRef(null),xe=_e({id:m(),email:m(),role:m(),status:m()}),{register:Be,handleSubmit:pe,formState:{errors:Je}}=Pe({resolver:ke(xe)});function ue(){c(f-1,d)}function he(){c(f+1,d)}const V=(s,r,a)=>{const i=r===(p.includes(s)?"cs":"eq")&&isNaN(a)?`"${a}"`:a,l=`${s},${p.includes(s)?"cs":r},${i}`.toLowerCase();b(S=>[...S.filter(k=>!k.includes(s)),l])},B=()=>{c(0,d,{},ae)},me=s=>{c(0,d,{},s)};async function c(s,r,a={},i=[]){N(!0),console.log("filters >> ",i);try{g.setTable("user");const l=await g.callRestAPI({payload:{...a},page:s,limit:r},"PAGINATE");l&&N(!1);const{list:S,total:$,limit:k,num_pages:z,page:P}=l;_(S),F(k),G(z),Q(P),K($),ee(P>1),te(P+1<=z)}catch(l){N(!1),console.log("ERROR",l),U(u,l.message)}}n.useEffect(()=>{t({type:"SETPATH",payload:{path:"providers"}});const r=setTimeout(async()=>{await c(1,d)},700);return()=>{clearTimeout(r)}},[I]);const J=s=>{C.current&&!C.current.contains(s.target)&&M(!1)};n.useEffect(()=>(document.addEventListener("mousedown",J),()=>{document.removeEventListener("mousedown",J)}),[]);const ge=()=>{j([]),b([]),c(1,d)},fe=async s=>{try{w(!0),_(r=>r.filter(a=>a.id!==s)),g.setTable("user"),await g.callRestAPI({id:s},"DELETE"),w(!1),h(!1)}catch(r){w(!1),h(!1),console.log("ERROR",r),U(u,r.message)}};return e.jsxs("div",{className:"px-8",children:[e.jsxs("div",{className:"mt-4 inline-flex h-[154px] w-[261px] flex-col items-center justify-center gap-6 rounded-lg border border-[#e4e6eb] bg-white p-4 shadow",children:[e.jsx("div",{className:" font-['Inter'] text-3xl font-semibold leading-[38px] text-[#0f1728]",children:Z}),e.jsx("div",{className:" font-['Poppins'] text-base font-medium leading-normal text-[#0f1728]",children:"Total Provider"})]}),e.jsxs("div",{className:"mt-4 rounded-lg border border-[#eaecf0] ",children:[e.jsxs("div",{className:"flex items-center justify-between px-4 py-4 ",children:[e.jsx("form",{className:"relative rounded bg-white",onSubmit:pe(B),children:e.jsx("div",{className:"flex items-center gap-4 text-nowrap text-gray-700",children:e.jsxs("div",{className:"relative",ref:C,children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-1",onClick:()=>M(!L),children:[e.jsx(Me,{}),e.jsx("span",{children:"Filters"}),o.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:o.length})]}),L&&e.jsx("div",{className:"top-fill filter-form-holder absolute left-0 z-10 mt-4 w-[500px] min-w-[90%] rounded-md border border-gray-200 bg-white shadow-lg",children:e.jsxs("div",{className:"p-4",children:[o==null?void 0:o.map((s,r)=>e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:" mb-3 w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",children:s}),e.jsxs("select",{className:"mb-3 w-[40%] rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:a=>{ne(a.target.value)},children:[p.includes(s)?"":e.jsx("option",{value:"eq",selected:!0,children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),p.includes(s)?"":e.jsxs(e.Fragment,{children:[e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]})]}),console.log("optoin",p.includes(s),s),s=="status"?e.jsxs("select",{name:"",id:"",className:" mb-3 w-1/3 rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:a=>V(s,D,a.target.value),children:[e.jsx("option",{value:"",children:"Select"}),e.jsx("option",{value:"0",children:"Inactive"}),e.jsx("option",{value:"1",children:"Active"}),e.jsx("option",{value:"2",children:"Suspend"})]}):e.jsx("input",{type:p.includes(s)?"date":"text",placeholder:"Enter value...",className:" mb-3 w-1/3 rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:a=>V(s,D,a.target.value)}),e.jsx("div",{className:"mt-[-10px] w-1/12",children:e.jsx(Ae,{className:" cursor-pointer text-xl",onClick:()=>{j(a=>a.filter(i=>i!==s)),b(a=>{const i=a.filter(l=>!l.includes(s));return me(i),i})}})})]},r)),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out ",onClick:()=>{E(!A)},children:[e.jsx(Ee,{}),"Add filter"]}),A&&e.jsx("div",{className:"absolute top-11 z-10 bg-white px-5 py-3 text-gray-600 shadow-md",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:Re.map(s=>e.jsx("li",{className:`${o.includes(s.accessor)?"cursor-not-allowed text-gray-400":"cursor-pointer"}`,onClick:()=>{o.includes(s.accessor)||j(r=>[...r,s.accessor]),E(!1)},children:s.header},s.header))})}),o.length>0&&e.jsx("div",{onClick:ge,className:"inline-block cursor-pointer  rounded px-6  py-2.5 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out",children:"Clear all filter"})]}),e.jsx("button",{type:"button",onClick:B,className:"mt-4 inline-block cursor-pointer rounded bg-blue-500 px-6 py-2.5 font-medium leading-tight text-white transition duration-150 ease-in-out",children:"Apply Filters"})]})})]})})}),e.jsxs("button",{onClick:()=>{we(x)},className:"rounded-lgflex inline-flex h-10  items-center justify-center gap-2 rounded-lg border border-[#cfd4dc] bg-white px-4 py-2.5 font-['Poppins'] text-sm font-medium leading-tight text-[#344053] shadow",children:[e.jsx("svg",{width:"20",height:"18",viewBox:"0 0 20 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M13.3335 12.3334L10.0002 9.00003M10.0002 9.00003L6.66688 12.3334M10.0002 9.00003V16.5M16.9919 14.325C17.8047 13.8819 18.4467 13.1808 18.8168 12.3322C19.1868 11.4837 19.2637 10.5361 19.0354 9.63894C18.807 8.74182 18.2865 7.94629 17.5558 7.3779C16.8251 6.80951 15.9259 6.50064 15.0002 6.50003H13.9502C13.698 5.5244 13.2278 4.61864 12.5752 3.85085C11.9225 3.08307 11.1042 2.47324 10.182 2.0672C9.25967 1.66116 8.25734 1.46949 7.25031 1.5066C6.24328 1.5437 5.25777 1.80861 4.36786 2.28142C3.47795 2.75422 2.7068 3.42261 2.1124 4.23635C1.51799 5.05008 1.11579 5.98797 0.936028 6.97952C0.756269 7.97107 0.803632 8.99047 1.07456 9.96108C1.34548 10.9317 1.83291 11.8282 2.50021 12.5834",stroke:"#344054",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),"Export as CSV"]})]}),v?e.jsx(Le,{}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"overflow-x-auto border-b border-gray-200 ",children:[e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:Y.map((s,r)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[s.header,e.jsx("span",{children:s.isSorted?s.isSortedDesc?" ▼":" ▲":""})]},r))})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:x.map((s,r)=>e.jsx("tr",{children:Y.map((a,i)=>a.accessor==""?e.jsxs("td",{className:"whitespace-nowrap px-6 py-4",children:[e.jsx("button",{className:"inline-flex h-10 items-center justify-center gap-2 rounded-lg p-2.5 text-[#4F46E5] ",onClick:()=>{ce(s.id),y(!0)},children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M14.1665 2.5C14.3854 2.28112 14.6452 2.1075 14.9312 1.98905C15.2171 1.8706 15.5236 1.80963 15.8332 1.80963C16.1427 1.80963 16.4492 1.8706 16.7352 1.98905C17.0211 2.1075 17.281 2.28112 17.4998 2.5C17.7187 2.71886 17.8923 2.97869 18.0108 3.26466C18.1292 3.55063 18.1902 3.85713 18.1902 4.16665C18.1902 4.47618 18.1292 4.78268 18.0108 5.06865C17.8923 5.35461 17.7187 5.61445 17.4998 5.83332L6.24984 17.0833L1.6665 18.3333L2.9165 13.75L14.1665 2.5Z",stroke:"#667085",strokeWidth:"1.66667",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("button",{className:"ml-2 inline-flex h-10 items-center justify-center gap-2 rounded-lg p-2.5 text-red-500 ",onClick:()=>{H(s.id),h(!0)},children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M2.5 5H4.17M4.17 5H17.5M4.17 5V16.67C4.17 17.11 4.34 17.53 4.65 17.85C4.97 18.16 5.39 18.33 5.83 18.33H14.17C14.61 18.33 15.03 18.16 15.35 17.85C15.66 17.53 15.83 17.11 15.83 16.67V5H4.17ZM6.67 5V3.33C6.67 2.89 6.84 2.47 7.15 2.15C7.47 1.84 7.89 1.67 8.33 1.67H11.67C12.11 1.67 12.53 1.84 12.85 2.15C13.16 2.47 13.33 2.89 13.33 3.33V5M8.33 9.17V14.17M11.67 9.17V14.17",stroke:"#667085",strokeWidth:"1.67",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx(Ve,{item:s})]},i):a.mapping&&a.accessor==="status"?e.jsx("td",{className:" whitespace-nowrap px-6 py-5 text-sm",children:s[a.accessor]===1?e.jsx("span",{className:"rounded-md bg-[#D1FAE5] px-3 py-1 text-[#065F46]",children:a.mapping[s[a.accessor]]}):e.jsx("span",{className:"rounded-md bg-[#F4F4F4] px-3 py-1 text-[#393939]",children:a.mapping[s[a.accessor]]})},i):a.accessor==="docs"?e.jsx("td",{className:"whitespace-nowrap px-6 py-5 text-sm",children:s!=null&&s.cedula_image_link?JSON.parse(s==null?void 0:s.cedula_image_link).front&&JSON.parse(s==null?void 0:s.cedula_image_link).back?2:JSON.parse(s==null?void 0:s.cedula_image_link).front||JSON.parse(s==null?void 0:s.cedula_image_link).back?1:0:0},i):a.mapping?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:a.mapping[s[a.accessor]]},i):a.accessor=="create_at"||a.accessor=="update_at"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s[a.accessor]?Oe(s[a.accessor]).format("DD/MM/YYYY"):"N/A"},i):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s[a.accessor]},i))},r))})]}),v&&e.jsx(e.Fragment,{children:e.jsx("p",{className:" px-10 py-3 text-xl capitalize ",children:"Loading..."})}),!v&&x.length===0&&e.jsx(e.Fragment,{children:e.jsx("p",{className:" px-10 py-3 text-xl capitalize ",children:"You Don't have any User"})})]})})]}),e.jsx(Fe,{currentPage:f,pageCount:W,pageSize:d,canPreviousPage:X,canNextPage:se,updatePageSize:s=>{F(s),c(1,s)},previousPage:ue,nextPage:he}),e.jsx(Te,{children:e.jsx(De,{open:le,actionHandler:()=>{fe(R)},closeModalFunction:()=>{H(null),h(!1)},title:"Delete Provider ",message:`You are about to delete Provider ${R}, note that this action is irreversible`,acceptText:"DELETE",rejectText:"CANCEL",loading:oe})}),e.jsx(q,{isModalActive:ie,closeModalFn:()=>O(!1),children:e.jsx(Ce,{setSidebar:O})}),T&&e.jsx(q,{isModalActive:T,closeModalFn:()=>y(!1),children:e.jsx(Se,{activeId:de,setSidebar:y,setIsUpdate:re,isUpdate:I})})]})},Ve=({item:t})=>{const[u,x]=be.useState(!1);return console.log(t),e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:()=>x(!0),className:"inline-flex h-10 items-center justify-center gap-2 rounded-lg bg-[#42cbee]/5 p-2.5",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5.833 14.167L14.167 5.833M14.167 5.833H5.833M14.167 5.833V14.167",stroke:"#56CCF2",strokeWidth:"1.67",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx(Ie,{isModal:u,closeModal:()=>x(!1),title:"Provider details",children:e.jsxs("div",{className:"flex w-full flex-col items-center justify-start gap-8 self-stretch",children:[e.jsx("img",{className:"h-[173px] w-[173px] rounded-[100px]",src:t!=null&&t.photo?t==null?void 0:t.photo:"https://via.placeholder.com/173x173"}),e.jsxs("div",{className:"flex w-full flex-col gap-2 rounded-xl border border-[#50a8f9]/10 p-6",children:[e.jsxs("div",{className:"flex w-full items-center justify-between gap-4 ",children:[e.jsx("div",{className:" font-['Poppins'] text-base font-normal leading-normal text-[#8080a3]",children:"User ID"}),e.jsx("div",{className:" text-right font-['Poppins'] text-base font-semibold leading-normal text-[#585555]",children:t==null?void 0:t.id})]}),e.jsxs("div",{className:"flex w-full items-center justify-between gap-4",children:[e.jsx("div",{className:" font-['Poppins'] text-base font-normal leading-normal text-[#8080a3]",children:"Full Name"}),e.jsx("div",{className:" text-right font-['Poppins'] text-base font-semibold leading-normal text-[#585555]",children:t==null?void 0:t.first_name})]}),e.jsxs("div",{className:"flex w-full items-center justify-between gap-4",children:[e.jsx("div",{className:" font-['Poppins'] text-base font-normal leading-normal text-[#8080a3]",children:"Phone#"}),e.jsx("div",{className:" text-right font-['Poppins'] text-base font-semibold leading-normal text-[#585555]",children:t==null?void 0:t.phone})]}),e.jsxs("div",{className:"flex w-full items-center justify-between gap-4",children:[e.jsx("div",{className:" font-['Poppins'] text-base font-normal leading-normal text-[#8080a3]",children:"Email"}),e.jsx("div",{className:" text-right font-['Poppins'] text-base font-semibold leading-normal text-[#585555]",children:t==null?void 0:t.email})]}),e.jsxs("div",{className:"flex w-full items-center justify-between gap-4",children:[e.jsx("div",{className:" font-['Poppins'] text-base font-normal leading-normal text-[#8080a3]",children:"Status"}),e.jsx("div",{className:" text-right font-['Poppins'] text-base font-semibold leading-normal text-[#585555]",children:He[t==null?void 0:t.status]})]}),e.jsxs("div",{className:"flex w-full items-center justify-between gap-4",children:[e.jsx("div",{className:" font-['Poppins'] text-base font-normal leading-normal text-[#8080a3]",children:"Work Type"}),e.jsx("div",{className:" text-right font-['Poppins'] text-base font-semibold leading-normal text-[#585555]",children:" Helper"})]}),e.jsxs("div",{className:"flex w-full items-center justify-between gap-4",children:[e.jsx("div",{className:" font-['Poppins'] text-base font-normal leading-normal text-[#8080a3]",children:"Docs?"}),e.jsx("div",{className:" text-right font-['Poppins'] text-base font-semibold leading-normal text-[#585555]",children:t!=null&&t.cedula_image_link?JSON.parse(t==null?void 0:t.cedula_image_link).front&&JSON.parse(t==null?void 0:t.cedula_image_link).back?2:JSON.parse(t==null?void 0:t.cedula_image_link).front||JSON.parse(t==null?void 0:t.cedula_image_link).back?1:0:0})]}),e.jsxs("div",{className:"flex w-full items-center justify-between gap-4",children:[e.jsx("div",{className:" font-['Poppins'] text-base font-normal leading-normal text-[#8080a3]",children:"Date created"}),e.jsx("div",{className:" text-right font-['Poppins'] text-base font-semibold leading-normal text-[#585555]",children:t==null?void 0:t.create_at})]})]})]})})]})};export{fs as default};
