import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{R as u,r as p,u as y,d as b}from"./vendor-f36d475e.js";import{u as A}from"./react-hook-form-ff037c98.js";import{o as M}from"./yup-afe5cf51.js";import{c as S,a as i}from"./yup-2f6e2476.js";import{M as N,A as B,G as W,h as P,s as x}from"./index-cf5e6bc7.js";import"./InteractiveButton-303096ac.js";import{A as V,a as Z}from"./index-55e4d382.js";import{A as E}from"./index-ad319f83.js";import{B as q,a as H}from"./index-895fa99b.js";import{u as R}from"./react-i18next-1e3e6bc5.js";import"./@hookform/resolvers-eb417cd0.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./MoonLoader-4d8718ee.js";const F=[{name:"first_name",type:"text",placeholder:"First Name",p:"p_first_name",icon:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M10.1331 9.05835C10.0498 9.05002 9.9498 9.05002 9.85814 9.05835C7.8748 8.99169 6.2998 7.36669 6.2998 5.36669C6.2998 3.32502 7.9498 1.66669 9.9998 1.66669C12.0415 1.66669 13.6998 3.32502 13.6998 5.36669C13.6915 7.36669 12.1165 8.99169 10.1331 9.05835Z",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M5.9666 12.1333C3.94993 13.4833 3.94993 15.6833 5.9666 17.025C8.25827 18.5583 12.0166 18.5583 14.3083 17.025C16.3249 15.675 16.3249 13.475 14.3083 12.1333C12.0249 10.6083 8.2666 10.6083 5.9666 12.1333Z",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})},{name:"last_name",type:"text",placeholder:"Last Name",p:"p_last_name",icon:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M10.1331 9.05835C10.0498 9.05002 9.9498 9.05002 9.85814 9.05835C7.8748 8.99169 6.2998 7.36669 6.2998 5.36669C6.2998 3.32502 7.9498 1.66669 9.9998 1.66669C12.0415 1.66669 13.6998 3.32502 13.6998 5.36669C13.6915 7.36669 12.1165 8.99169 10.1331 9.05835Z",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M5.9666 12.1333C3.94993 13.4833 3.94993 15.6833 5.9666 17.025C8.25827 18.5583 12.0166 18.5583 14.3083 17.025C16.3249 15.675 16.3249 13.475 14.3083 12.1333C12.0249 10.6083 8.2666 10.6083 5.9666 12.1333Z",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})},{name:"phone",type:"phone",placeholder:"Phone",p:"p_phone",icon:e.jsxs("svg",{width:"20",height:"22",viewBox:"0 0 20 22",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M18 6V16C18 20 17 21 13 21H7C3 21 2 20 2 16V6C2 2 3 1 7 1H13C17 1 18 2 18 6Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M12 4.5H8",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M10.0002 18.1C10.8562 18.1 11.5502 17.406 11.5502 16.55C11.5502 15.694 10.8562 15 10.0002 15C9.14415 15 8.4502 15.694 8.4502 16.55C8.4502 17.406 9.14415 18.1 10.0002 18.1Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),length:13},{name:"email",type:"email",placeholder:"Email",p:"p_email",icon:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M14.167 17.0834H5.83366C3.33366 17.0834 1.66699 15.8334 1.66699 12.9167V7.08335C1.66699 4.16669 3.33366 2.91669 5.83366 2.91669H14.167C16.667 2.91669 18.3337 4.16669 18.3337 7.08335V12.9167C18.3337 15.8334 16.667 17.0834 14.167 17.0834Z",stroke:"#8181A4",strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M14.1663 7.5L11.558 9.58333C10.6997 10.2667 9.29134 10.2667 8.433 9.58333L5.83301 7.5",stroke:"#8181A4",strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"})]})}];let T=new N;const ue=()=>{const g=S({first_name:i().required(),last_name:i().required(),phone:i().required(),email:i().email().required()}).required();u.useContext(B);const{dispatch:l}=u.useContext(W),[m,n]=p.useState(!1),[f,k]=p.useState(!1);y();const{t:o}=R(),C=b(),{register:j,handleSubmit:w,setError:v,formState:{errors:L,isValid:d}}=A({resolver:M(g),mode:"onChange"});p.useEffect(()=>{k(d)},[d]);const _=async t=>{try{n(!0);const s={firstName:t.first_name,lastName:t.last_name,phone:t.phone,email:t.email},r=await T.callRawAPI("/v3/api/custom/chumpchange/provider/signup/step1",s,"POST");if(!r.error)P(),x(l,"Succesfully Registered",4e3,"success"),C(`/provider/sign-up-otp/${t.phone}`);else if(r.validation){const h=Object.keys(r.validation);for(let a=0;a<h.length;a++){const c=h[a];v(c,{type:"manual",message:r.validation[c]})}}n(!1)}catch(s){n(!1),console.log("Error",s),x(l,s==null?void 0:s.message,4e3,"error")}};return e.jsxs(V,{children:[e.jsx(q,{}),e.jsxs("form",{onSubmit:w(_),children:[e.jsx(Z,{title:o("auth_pages.sign_up.title"),className:" -mt-6 "}),e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx("div",{className:" flex h-[70px] flex-col items-center justify-start",children:e.jsx("div",{className:"font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:o("auth_pages.sign_up.sub_title_info")})}),F.map((t,s)=>e.jsx(E,{type:t.type,name:t.name,placeholder:o(`auth_pages.sign_up.${t.p}`),errors:L,register:j,icon:t.icon,length:t==null?void 0:t.length},s)),e.jsx("div",{className:" mt-5 ",children:e.jsx(H,{loading:m,disabled:!f||m,type:"submit",children:o("auth_pages.sign_up.next")})})]})]})]})};export{ue as default};
