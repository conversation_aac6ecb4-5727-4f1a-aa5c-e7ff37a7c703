import{j as e}from"./@react-google-maps/api-ee55a349.js";import{B as S,a as $}from"./index-d54cffea.js";import{r as i,R as q,u as T,d as U,i as G}from"./vendor-b16525a8.js";import{u as Z}from"./react-hook-form-b6ed2679.js";import{o as O}from"./yup-3990215a.js";import{c as z,a as P}from"./yup-f828ae80.js";import{M as K,A as W,G as J,s as Q}from"./index-09a1718e.js";import{S as X}from"./index-5a645c18.js";import{P as Y}from"./index-d152a0de.js";import{t as r}from"./i18next-7389dd8c.js";import"./qr-scanner-cf010ec4.js";import"./@hookform/resolvers-3e831b4a.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";const v=new K,Se=()=>{i.useContext(W);const{state:ee,dispatch:L}=i.useContext(J),[V,u]=i.useState(0),[A,x]=i.useState(0),[l,p]=i.useState(!1);i.useState();const[B,f]=i.useState(),[w,h]=i.useState(),[E,g]=q.useState(!1),N=T(),F=U(),{id:b}=G(),M=z({address:P().required(),label:P().required()}).required(),{register:j,handleSubmit:R,setError:te,setValue:a,formState:{errors:se,isValid:ae}}=Z({resolver:O(M),mode:"onChange"}),H=async()=>{var s,o,n,d,c,m,y,k,C,_;try{f(!0);const t=await v.callRawAPI(`/v3/api/custom/chumpchange/user/address/${b}`,{},"GET");a("address",((s=t==null?void 0:t.data)==null?void 0:s.description)||""),a("label",((o=t==null?void 0:t.data)==null?void 0:o.name)||""),a("city",((n=t==null?void 0:t.data)==null?void 0:n.city)||""),a("state",((d=t==null?void 0:t.data)==null?void 0:d.state)||""),a("country",((c=t==null?void 0:t.data)==null?void 0:c.country)||""),a("lat",((m=t==null?void 0:t.data)==null?void 0:m.latitude)||""),a("lng",((y=t==null?void 0:t.data)==null?void 0:y.longitude)||""),u(Number((k=t==null?void 0:t.data)==null?void 0:k.latitude)||0),x(Number((C=t==null?void 0:t.data)==null?void 0:C.longitude)||0),(_=t==null?void 0:t.data)!=null&&_.is_default&&p(!0),f(!1)}catch{f(!1)}};i.useEffect(()=>{(async()=>await H())()},[]),i.useEffect(()=>{const s=new URLSearchParams(N.search);a("address",s.get("address")||""),a("city",s.get("city")||""),a("state",s.get("state")||""),a("country",s.get("country")||""),a("lat",s.get("lat")||""),a("lng",s.get("lng")||""),u(Number(s.get("lat"))||0),x(Number(s.get("lng"))||0)},[N.search]);const D=async s=>{try{h(!0);const o=await await v.callRawAPI(`/v3/api/custom/chumpchange/user/address/edit/${b}`,{name:s.label,description:s.address,city:s.city,latitude:String(s.lat),longtitude:String(s.lng),state:s.state,country:s.country},"POST");if(l){const n=await await v.callRawAPI(`/v3/api/custom/chumpchange/user/address/set-default/${b}`,{is_default:!0},"POST")}Q(L,"Updated"),F("/user/address"),h(!1)}catch(o){h(!1),console.log("error >> ",o)}},I=(s,o,n,d,c,m)=>{console.log(s,o,d),u(s||""),x(o||""),a("address",n||""),a("city",d||""),a("state",c||""),a("country",m||""),a("lat",s||""),a("lng",o||""),g(!1)};return console.log(V,A),e.jsxs("div",{className:"p-5",children:[E&&e.jsxs("div",{className:"fixed left-0 top-0 z-[9999999] min-h-screen w-full bg-[#F2F2F7] ",children:[e.jsx("div",{className:"p-5",children:e.jsxs("div",{className:"relative flex w-full items-center justify-between ",children:[e.jsx(S,{onClick:()=>g(!1)}),e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:"Pick Location"}),e.jsx("div",{className:""})]})}),e.jsx("div",{className:" h-[calc(100vh-88px)] ",children:e.jsx(Y,{handleChoose:I})})]}),e.jsxs("div",{className:"relative flex w-full items-center justify-between ",children:[e.jsx("div",{className:" ",children:e.jsx(S,{link:"/user/address"})}),e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:r("user.add_address.e_title")}),e.jsx("div",{className:""})]}),B?e.jsx("div",{className:"flex h-full w-full items-center justify-center pt-10 ",children:e.jsx(X,{})}):e.jsxs("form",{onSubmit:R(D),className:" mt-[55px] flex flex-col gap-3 ",children:[e.jsxs("div",{className:"",children:[e.jsx("div",{className:"px-2 font-['Poppins'] text-base font-medium tracking-tight text-black",children:r("user.add_address.e_label")}),e.jsx("input",{type:"text",placeholder:r("user.add_address.label"),...j("label"),className:"relative mt-1 h-12 w-full rounded-2xl border-none bg-white text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "})]}),e.jsxs("div",{className:"",children:[e.jsx("div",{className:"px-2 font-['Poppins'] text-base font-medium tracking-tight text-black",children:r("user.add_address.e_address")}),e.jsx("input",{type:"text",placeholder:r("user.add_address.enter_address"),...j("address"),className:"relative mt-1 h-12 w-full rounded-2xl border-none bg-white text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "})]}),e.jsxs("div",{className:"",children:[e.jsx("div",{className:"px-2 font-['Poppins'] text-base font-medium tracking-tight text-black",children:r("user.add_address.e_city")}),e.jsx("input",{type:"text",placeholder:r("user.add_address.enter_city"),...j("city"),className:"relative mt-1 h-12 w-full rounded-2xl border-none bg-white text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "})]}),e.jsx("button",{onClick:()=>g(!0),type:"button",className:"",children:e.jsxs("div",{className:"flex h-[55px] w-full items-center gap-3 rounded-xl border border-[#e1e2e3] bg-[#f7f7f7] p-3 ",children:[e.jsx("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M16 10.1818C12.8 10.1818 10.1818 12.8 10.1818 16C10.1818 19.2 12.8 21.8182 16 21.8182C19.2 21.8182 21.8182 19.2 21.8182 16C21.8182 12.8 19.2 10.1818 16 10.1818ZM28.9455 14.5455C28.2182 8.43636 23.4182 3.63636 17.4545 3.05455V0H14.5455V3.05455C8.43636 3.63636 3.63636 8.43636 3.05455 14.5455H0V17.4545H3.05455C3.78182 23.5636 8.58182 28.3636 14.5455 28.9455V32H17.4545V28.9455C23.5636 28.2182 28.3636 23.4182 28.9455 17.4545H32V14.5455H28.9455ZM16 26.1818C10.3273 26.1818 5.81818 21.6727 5.81818 16C5.81818 10.3273 10.3273 5.81818 16 5.81818C21.6727 5.81818 26.1818 10.3273 26.1818 16C26.1818 21.6727 21.6727 26.1818 16 26.1818Z",fill:"#56CCF2"})}),e.jsx("div",{className:"font-['Poppins'] text-base font-light tracking-tight text-black",children:r("user.add_address.choose_on_map")})]})}),e.jsxs("div",{className:" mt-3 flex gap-4 px-1 ",children:[e.jsx("div",{className:"flex h-6 w-6 cursor-pointer items-center justify-center rounded-sm border border-[#e1e2e3]",onClick:()=>p(!l),children:l&&e.jsx("svg",{width:"16",height:"15",viewBox:"0 0 16 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M2 8.1875L7.25 13L14 2",stroke:"#56CCF2",strokeWidth:"3",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("div",{onClick:()=>p(!l),className:"h-6 w-[130px] cursor-pointer font-['Poppins'] text-base font-light tracking-tight text-black ",children:r("user.add_address.e_default")})]}),e.jsx("div",{className:"mt-10",children:e.jsx($,{loading:w,disabled:w,type:"submit",children:r("user.add_address.e_update")})})]})]})};export{Se as default};
