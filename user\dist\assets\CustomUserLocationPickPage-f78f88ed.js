import{j as s}from"./@react-google-maps/api-ee55a349.js";import{B as l}from"./index-d54cffea.js";import{a as n}from"./index-dd254604.js";import{P as d}from"./index-d152a0de.js";import{d as x}from"./vendor-b16525a8.js";import"./qr-scanner-cf010ec4.js";const g=()=>{const t=x(),a=(e,o,i,r,m,c)=>{t(`/user/address/pick?lat=${e}&lng=${o}&address=${i}&city=${r}&state=${m}$country=${c}`)};return s.jsxs("div",{children:[s.jsxs("div",{className:"mt-5 px-5",children:[s.jsx(l,{}),s.jsx("div",{className:" ",children:s.jsx(n,{title:"Sign up",className:" -mt-6 "})}),s.jsx("div",{className:""})]}),s.jsxs("div",{className:" relative h-[calc(100vh-88px)] ",children:[s.jsx("div",{className:" absolute left-1/2 top-2 z-[9999999999] -translate-x-1/2 rounded-xl bg-[#fff] px-2 py-1 font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:"Pinch on map to zoom"}),s.jsx(d,{handleChoose:a})]})]})};export{g as default};
