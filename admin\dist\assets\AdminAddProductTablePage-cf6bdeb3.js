import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as r,b as R}from"./vendor-4f06b3f4.js";import{u as A}from"./react-hook-form-f3d72793.js";import{o as E}from"./yup-2324a46a.js";import{c as D,a as f}from"./yup-17027d7a.js";import{G as L,A as I,M as k,s as O,t as P}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as c}from"./MkdInput-ff3aa862.js";import{I as T}from"./InteractiveButton-8f7d74ee.js";import"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";const ne=({setSidebar:h})=>{const{dispatch:d}=r.useContext(L),b=D({name:f().required(),description:f(),status:f()}).required(),{dispatch:j}=r.useContext(I),[i,y]=r.useState({}),[g,u]=r.useState(!1),S=R(),{register:m,handleSubmit:v,setError:x,setValue:C,formState:{errors:n}}=A({resolver:E(b)});r.useState([]);const N=(a,o)=>{let e=i;e[a]={file:o.files[0],tempURL:URL.createObjectURL(o.files[0])},y({...e})},w=async a=>{let o=new k;u(!0);try{for(let l in i){let s=new FormData;s.append("file",i[l].file);let p=await o.uploadImage(s);a[l]=p.url}o.setTable("product");const e=await o.callRestAPI({name:a.name,description:a.description,status:a.status,logo:String(a.logo)},"POST");if(!e.error)O(d,"Added"),S("/admin/product"),h(!1),d({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(e.validation){const l=Object.keys(e.validation);for(let s=0;s<l.length;s++){const p=l[s];x(p,{type:"manual",message:e.validation[p]})}}u(!1)}catch(e){u(!1),console.log("Error",e),x("name",{type:"manual",message:e.message}),P(j,e.message)}};return r.useEffect(()=>{d({type:"SETPATH",payload:{path:"product"}})},[]),t.jsxs("div",{className:" mx-auto rounded  p-5 shadow-md",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Add Product"}),t.jsxs("form",{className:" w-full max-w-lg",onSubmit:v(w),children:[t.jsx(c,{type:"text",page:"add",name:"name",errors:n,label:"Name",placeholder:"Name",register:m,className:""}),t.jsx(c,{type:"text",page:"add",name:"description",errors:n,label:"Description",placeholder:"Description",register:m,className:""}),t.jsx(c,{type:"select",page:"add",name:"status",errors:n,label:"Status",placeholder:"Status",register:m,className:"",options:["active","inactive"]}),t.jsx("div",{className:"",children:i.logo?t.jsx("img",{src:i.logo.tempURL,alt:"",className:"h-[100px] w-auto object-cover "}):""}),t.jsx(c,{type:"file",page:"add",name:"logo",errors:n,label:"Logo",placeholder:"Logo",register:m,className:"",onChange:a=>N("logo",a.target)}),t.jsx(T,{type:"submit",loading:g,disabled:g,className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",children:"Submit"})]})]})};export{ne as default};
