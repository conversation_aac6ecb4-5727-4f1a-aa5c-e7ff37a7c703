import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as s,b as S,r as b}from"./vendor-4f06b3f4.js";import{M as w,A as j,G as A,v,w as E}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{L as o}from"./index-6416aa2c.js";import{M as p}from"./index-d97c616d.js";import{M}from"./index-68c82eaa.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";new w;const D=[{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Update At",accessor:"update_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Name",accessor:"name",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Description",accessor:"description",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Logo",accessor:"logo",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},notfilter:!0},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],O=()=>{s.useContext(j),s.useContext(A),S();const[m,t]=s.useState(!1),[r,a]=s.useState(!1),[f,u]=s.useState(),[l,x]=s.useState({}),h=b.useRef(null),[I,g]=s.useState([]),d=(i,n,c=[])=>{switch(i){case"add":t(n);break;case"edit":a(n),g(c),u(c[0]);break}};return console.log("result >> ",l.total),e.jsxs(e.Fragment,{children:[e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"flex flex-wrap gap-4 bg-white px-14 shadow ",children:e.jsxs("div",{className:"mt-4 inline-flex h-[154px] w-[261px] flex-col items-center justify-center gap-6 rounded-lg border border-[#e4e6eb] bg-white p-4 shadow",children:[e.jsx("div",{className:" font-['Inter'] text-3xl font-semibold leading-[38px] text-[#0f1728]",children:l.total}),e.jsx("div",{className:" font-['Poppins'] text-base font-medium leading-normal text-[#0f1728]",children:"Total products"})]})}),e.jsx("div",{className:"overflow-x-auto  rounded bg-white p-5 shadow",children:e.jsx(o,{children:e.jsx(M,{setResult:x,columns:D,tableRole:"admin",table:"product",actionId:"id",actions:{view:{show:!0,action:null,multiple:!1},edit:{show:!0,multiple:!1,action:i=>d("edit",!0,i)},delete:{show:!0,action:null,multiple:!1},select:{show:!0,action:null,multiple:!1},add:{show:!0,action:()=>d("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!0,action:null,multiple:!0}},actionPostion:"ontable",refreshRef:h})})})]}),e.jsx(o,{children:e.jsx(p,{isModalActive:m,closeModalFn:()=>t(!1),children:e.jsx(v,{setSidebar:t})})}),r&&e.jsx(o,{children:e.jsx(p,{isModalActive:r,closeModalFn:()=>a(!1),children:e.jsx(E,{activeId:f,setSidebar:a})})})]})};export{O as default};
