import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as a,b as v}from"./vendor-4f06b3f4.js";import{u as E}from"./react-hook-form-f3d72793.js";import{o as L}from"./yup-2324a46a.js";import{c as k,a as n}from"./yup-17027d7a.js";import{G as R,A as D,M as I,s as T,t as F}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as u}from"./MkdInput-ff3aa862.js";import{I as C}from"./InteractiveButton-8f7d74ee.js";import"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";const me=({setSidebar:y})=>{var h,b;const{dispatch:m}=a.useContext(R),j=k({address:n(),latitude:n(),longitude:n(),geolocation:n()}).required(),{dispatch:w}=a.useContext(D),[g,G]=a.useState({}),[f,c]=a.useState(!1),S=v(),{register:l,handleSubmit:A,setError:x,setValue:M,formState:{errors:s}}=E({resolver:L(j)});a.useState([]);const N=async r=>{let p=new I;c(!0);try{for(let i in g){let o=new FormData;o.append("file",g[i].file);let d=await p.uploadImage(o);r[i]=d.url}p.setTable("location");const t=await p.callRestAPI({address:r.address,latitude:r.latitude,longitude:r.longitude,geolocation:r.geolocation},"POST");if(!t.error)T(m,"Added"),S("/admin/location"),y(!1),m({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(t.validation){const i=Object.keys(t.validation);for(let o=0;o<i.length;o++){const d=i[o];x(d,{type:"manual",message:t.validation[d]})}}c(!1)}catch(t){c(!1),console.log("Error",t),x("address",{type:"manual",message:t.message}),F(w,t.message)}};return a.useEffect(()=>{m({type:"SETPATH",payload:{path:"location"}})},[]),e.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Location"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:A(N),children:[e.jsx(u,{type:"text",page:"add",name:"address",errors:s,label:"Address",placeholder:"Address",register:l,className:""}),e.jsx(u,{page:"add",name:"latitude",errors:s,label:"Latitude",placeholder:"Latitude",register:l,className:""}),e.jsx(u,{page:"add",name:"longitude",errors:s,label:"Longitude",placeholder:"Longitude",register:l,className:""}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"geolocation",children:"Geolocation"}),e.jsx("textarea",{placeholder:"Geolocation",...l("geolocation"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(h=s.geolocation)!=null&&h.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(b=s.geolocation)==null?void 0:b.message})]}),e.jsx(C,{type:"submit",loading:f,disabled:f,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{me as default};
