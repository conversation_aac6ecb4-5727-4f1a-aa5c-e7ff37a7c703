import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as t,h as p}from"./vendor-4f06b3f4.js";import"./yup-17027d7a.js";import{M as x,G as i,t as f}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{S as u}from"./index-2d8231e7.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";let m=new x;const G=()=>{const{dispatch:l}=t.useContext(i),{dispatch:c}=t.useContext(i),[s,n]=t.useState({}),[d,o]=t.useState(!0),a=p();return t.useEffect(function(){(async function(){try{o(!0),m.setTable("provider_service");const r=await m.callRestAPI({id:Number(a==null?void 0:a.id),join:""},"GET");r.error||(n(r.model),o(!1))}catch(r){o(!1),console.log("error",r),f(c,r.message)}})()},[]),t.useEffect(()=>{l({type:"SHOW_BACKBUTTON",payload:{showBackButton:!0}})},[]),e.jsx("div",{className:" mx-auto rounded  p-5 shadow-md",children:d?e.jsx(u,{}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"mb-4 mt-4",children:e.jsxs("div",{className:"mb-4 flex",children:[e.jsx("div",{className:"flex-1",children:"Name"}),e.jsx("div",{className:"flex-1",children:s==null?void 0:s.name})]})}),e.jsx("div",{className:"mb-4 mt-4",children:e.jsxs("div",{className:"mb-4 flex",children:[e.jsx("div",{className:"flex-1",children:"Logo"}),e.jsx("div",{className:"flex-1",children:s==null?void 0:s.logo})]})})]})})};export{G as default};
