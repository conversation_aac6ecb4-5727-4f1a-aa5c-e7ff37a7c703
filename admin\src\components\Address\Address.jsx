import { RedButton } from "Components/Buttons";
import { MobileModal } from "Components/Modal";
import React, { useState } from "react";
import { Link } from "react-router-dom";

const Address = ({ item, handleRemove }) => {
  const [isModal, setIsModal] = useState();

  return (
    <>
      <div className="relative bg-white">
        <div className="  w-ful flex items-center justify-between bg-[#f7f7f7] px-6 py-2">
          <div className=" font-['Poppins'] text-2xl font-medium text-black">
            {item?.name ? item?.name : "N/A"}
          </div>
          <div className=" flex gap-6 ">
            <Link
              to={`/user/edit-address/${item.id}`}
              className=" bg-transparent  text-right font-['Poppins'] text-[17px] font-semibold leading-snug text-[#4ecbf0]"
            >
              Edit
            </Link>
            <button
              onClick={() => setIsModal(true)}
              className=" bg-transparent  text-right font-['Poppins'] text-[17px] font-semibold leading-snug text-[#fd5d5d]"
            >
              Remove
            </button>
          </div>
        </div>
        <div className=" flex items-center justify-between px-6 py-3 ">
          <div className=" font-['Poppins'] text-lg font-normal text-black">
            Address
          </div>
          <div className=" text-right font-['Poppins'] text-base font-medium text-[#bcbbc1]">
            {item?.description ? item?.description : "N/A"}
          </div>
        </div>
        <div className=" flex items-center justify-between px-6 py-3 ">
          <div className=" font-['Poppins'] text-lg font-normal text-black">
            City
          </div>
          <div className=" text-right font-['Poppins'] text-base font-medium text-[#bcbbc1]">
            {item?.city ? item?.city : "N/A"}
          </div>
        </div>
      </div>
      {isModal && (
        <MobileModal closeModal={() => setIsModal(false)}>
          <div className=" mt-5 flex h-max flex-col items-center justify-center gap-8 ">
            <div className="text-center font-['Poppins'] text-2xl font-medium leading-[31.20px] text-black">
              Confirmed
            </div>
            <div className=" text-center font-['Poppins'] text-2xl font-medium leading-[31.20px] text-black">
              Yes, Remove this Address
            </div>
            <RedButton
              onClick={() => {
                setIsModal(false);
                handleRemove(item.id);
              }}
              className=" !w-[163px]"
            >
              Remove
            </RedButton>
          </div>
        </MobileModal>
      )}
    </>
  );
};

export default Address;
