import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as a,r as g,b as ce}from"./vendor-4f06b3f4.js";import{M as de,G as le,A as pe,s as ue,t as me,g as d}from"./index-06b5b6dd.js";import{o as xe}from"./yup-2324a46a.js";import{u as he}from"./react-hook-form-f3d72793.js";import{c as ge,a as l}from"./yup-17027d7a.js";import{P as fe}from"./index-19801678.js";import{A as je}from"./AddButton-39426f55.js";import{B as ye,R as we,A as be,a as ve,b as Se}from"./index.esm-1a4cea12.js";import{S as Ce}from"./index-2d8231e7.js";import{M as $}from"./index-d97c616d.js";import Ne from"./EditAdminStripePricePage-7b23799a.js";import Pe from"./AddAdminStripePricePage-0fd3c82c.js";import"./react-confirm-alert-525c3702.js";import"./moment-a9aaa855.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@hookform/resolvers-1aa18522.js";import"./AddButton.module-98aac587.js";import"./react-icons-e5379072.js";let ke=new de;const N=[{header:"Stripe Id",accessor:"stripe_id"},{header:"Product",accessor:"product_name"},{header:"Nickname",accessor:"name"},{header:"Type",accessor:"type",mapping:{one_time:"One Time",recurring:"Recurring",lifetime:"Lifetime"}},{header:"Price",accessor:"amount"},{header:"Trial",accessor:"trial_days"},{header:"Status",accessor:"status",mapping:{0:"Inactive",1:"Active"}},{header:"Action",accessor:""}],nt=()=>{const{dispatch:P}=a.useContext(le),{dispatch:B}=a.useContext(pe);a.useState("");const[q,z]=a.useState([]),[o,k]=a.useState(10),[A,H]=a.useState(0),[Ae,V]=a.useState(0),[p,G]=a.useState(0),[K,Z]=a.useState(!1),[J,Q]=a.useState(!1),[E,F]=a.useState(!1),[_,T]=a.useState(!1),[n,f]=a.useState([]),[j,y]=a.useState([]),[U,W]=a.useState(""),[D,X]=a.useState("eq"),[Y,R]=a.useState(!1),[w,b]=g.useState(!1),[L,v]=g.useState(!1),[M,ee]=g.useState();ce();const S=a.useRef(null),te=ge({stripe_id:l(),name:l(),status:l(),product_name:l(),amount:l(),type:l()}),{register:Ee,handleSubmit:se,formState:{errors:Fe}}=he({resolver:xe(te)});function ae(t){navigator.clipboard.writeText(t),ue(P,"Copied to clipboard")}const O=(t,r,s)=>{const i=r==="eq"&&isNaN(s)?`"${s}"`:s,u=`${t},${r},${i}`;y(m=>[...m.filter(h=>!h.includes(t)),u]),W(s)};function re(t){(async function(){k(t),await c(1,t)})()}function ie(){(async function(){await c(p-1>1?p-1:1,o)})()}function ne(){(async function(){await c(p+1<=A?p+1:1,o)})()}async function c(t,r,s){R(!0);try{const i=await ke.getStripePrices({page:t,limit:r},`filter=${s.toString()}`),{list:u,total:m,limit:x,num_pages:h,page:C}=i;z(u),k(+x),H(+h),G(+C),V(+m),Z(+C>1),Q(+C+1<=+h)}catch(i){console.log("ERROR",i),me(B,i.message)}R(!1)}const oe=t=>{const r=d(t.stripe_id),s=d(t.product_name),i=d(t.name),u=d(t.amount),m=d(t.type),x=d(t.status);c(1,o,{stripe_id:r,product_name:s,name:i,amount:u,type:m,status:x})};a.useEffect(()=>{P({type:"SETPATH",payload:{path:"prices"}});const r=setTimeout(async()=>{await c(1,o,j)},700);return()=>{clearTimeout(r)}},[U,j,D]),g.useEffect(()=>{w||c(1,o,j)},[w,M,L]);const I=t=>{S.current&&!S.current.contains(t.target)&&F(!1)};return a.useEffect(()=>(document.addEventListener("mousedown",I),()=>{document.removeEventListener("mousedown",I)}),[]),e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center justify-between py-3",children:[e.jsx("form",{className:"relative rounded bg-white",onSubmit:se(oe),children:e.jsxs("div",{className:"flex items-center gap-4 text-gray-700 text-nowrap",children:[e.jsxs("div",{className:"relative",ref:S,children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-1",onClick:()=>F(!E),children:[e.jsx(ye,{}),e.jsx("span",{children:"Filters"}),n.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:n.length})]}),E&&e.jsx("div",{className:"absolute z-10 mt-4 w-[500px] min-w-[90%] top-fill left-0 filter-form-holder bg-white border border-gray-200 rounded-md shadow-lg",children:e.jsxs("div",{className:"p-4",children:[n==null?void 0:n.map((t,r)=>e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:" mb-3 w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",children:t}),e.jsxs("select",{className:"mb-3 w-[40%] rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:s=>{X(s.target.value)},children:[e.jsx("option",{value:"eq",selected:!0,children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),e.jsx("input",{type:"text",placeholder:"Enter value...",className:" mb-3 w-1/3 rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:s=>O(t,D,s.target.value)}),e.jsx("div",{className:"w-1/12 mt-[-10px]",children:e.jsx(we,{className:" cursor-pointer text-xl",onClick:()=>{f(s=>s.filter(i=>i!==t)),y(s=>s.filter(i=>!i.includes(t)))}})})]},r)),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out ",onClick:()=>{T(!_)},children:[e.jsx(be,{}),"Add filter"]}),_&&e.jsx("div",{className:"absolute top-11 z-10 bg-white px-5 py-3 text-gray-600 shadow-md",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:N.slice(0,-1).map(t=>e.jsx("li",{className:`${n.includes(t.header)?"cursor-not-allowed text-gray-400":"cursor-pointer"}`,onClick:()=>{n.includes(t.header)||f(r=>[...r,t.header]),T(!1)},children:t.header},t.header))})}),n.length>0&&e.jsx("div",{onClick:()=>{f([]),y([])},className:"inline-block cursor-pointer  rounded px-6  py-2.5 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out",children:"Clear all filter"})]})]})})]}),e.jsxs("div",{className:" flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-2 py-1 focus-within:border-gray-400",children:[e.jsx(ve,{className:"text-xl text-gray-200"}),e.jsx("input",{type:"text",placeholder:"search...",className:"border-none p-0 placeholder:text-left  focus:outline-none",style:{boxShadow:"0 0 transparent"},onInput:t=>{var r;return O("name","cs",(r=t.target)==null?void 0:r.value)}}),e.jsx(Se,{className:"text-lg text-gray-200"})]})]})}),e.jsx(je,{onClick:()=>v(!0)})]}),Y?e.jsx(Ce,{}):e.jsx("div",{className:"overflow-x-auto border-b border-gray-200 shadow ",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200 rounded-lg",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:N.map((t,r)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},r))})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:q.map((t,r)=>e.jsx("tr",{children:N.map((s,i)=>s.accessor==""?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsxs("button",{className:"mx-1 inline-block cursor-pointer p-1 text-sm font-medium text-[#4F46E5] transition duration-150 ease-in-out hover:underline",onClick:()=>{ee(t.id),b(!0)},children:[" ","Edit"]})},i):s.accessor==="stripe_id"?e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"w-full flex items-center justify-between",children:[e.jsxs("span",{children:[t[s.accessor]," "]}),e.jsx("span",{className:"cursor-pointer",onClick:()=>ae(t[s.accessor]),children:e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:[e.jsx("g",{clipPath:"url(#clip0_948_1315)",children:e.jsx("path",{d:"M6.66663 6.6665V4.33317C6.66663 3.39975 6.66663 2.93304 6.84828 2.57652C7.00807 2.26292 7.26304 2.00795 7.57664 1.84816C7.93316 1.6665 8.39987 1.6665 9.33329 1.6665H15.6666C16.6 1.6665 17.0668 1.6665 17.4233 1.84816C17.7369 2.00795 17.9918 2.26292 18.1516 2.57652C18.3333 2.93304 18.3333 3.39975 18.3333 4.33317V10.6665C18.3333 11.5999 18.3333 12.0666 18.1516 12.4232C17.9918 12.7368 17.7369 12.9917 17.4233 13.1515C17.0668 13.3332 16.6 13.3332 15.6666 13.3332H13.3333M4.33329 18.3332H10.6666C11.6 18.3332 12.0668 18.3332 12.4233 18.1515C12.7369 17.9917 12.9918 17.7368 13.1516 17.4232C13.3333 17.0666 13.3333 16.5999 13.3333 15.6665V9.33317C13.3333 8.39975 13.3333 7.93304 13.1516 7.57652C12.9918 7.26292 12.7369 7.00795 12.4233 6.84816C12.0668 6.6665 11.6 6.6665 10.6666 6.6665H4.33329C3.39987 6.6665 2.93316 6.6665 2.57664 6.84816C2.26304 7.00795 2.00807 7.26292 1.84828 7.57652C1.66663 7.93304 1.66663 8.39975 1.66663 9.33317V15.6665C1.66663 16.5999 1.66663 17.0666 1.84828 17.4232C2.00807 17.7368 2.26304 17.9917 2.57664 18.1515C2.93316 18.3332 3.39987 18.3332 4.33329 18.3332Z",stroke:"#8D8D8D","stroke-width":"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("defs",{children:e.jsx("clipPath",{id:"clip0_948_1315",children:e.jsx("rect",{width:"20",height:"20",fill:"white"})})})]})})]})},i):s.mapping&&s.accessor==="status"?e.jsx("td",{className:"px-6 py-5 whitespace-nowrap inline-block text-sm",children:t[s.accessor]===1?e.jsx("span",{className:"rounded-md bg-[#D1FAE5] px-3 py-1 text-[#065F46]",children:s.mapping[t[s.accessor]]}):e.jsx("span",{className:"rounded-md bg-[#F4F4F4] px-3 py-1 text-[#393939]",children:s.mapping[t[s.accessor]]})},i):s.mapping?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s.mapping[t[s.accessor]]},i):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[s.accessor]},i))},r))})]})}),e.jsx(fe,{currentPage:p,pageCount:A,pageSize:o,canPreviousPage:K,canNextPage:J,updatePageSize:re,previousPage:ie,nextPage:ne}),e.jsx($,{isModalActive:w,closeModalFn:()=>b(!1),children:e.jsx(Ne,{activeId:M,setSidebar:b})}),e.jsx($,{isModalActive:L,closeModalFn:()=>v(!1),children:e.jsx(Pe,{setSidebar:v})})]})};export{nt as default};
