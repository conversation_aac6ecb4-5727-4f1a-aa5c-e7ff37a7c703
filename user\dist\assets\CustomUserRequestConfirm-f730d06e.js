import{j as e}from"./@react-google-maps/api-ee55a349.js";import{r as N,d as R,u as A,R as b}from"./vendor-b16525a8.js";import{u as B}from"./user-a875fff3.js";import{B as L,a as E,R as M}from"./index-d54cffea.js";import{M as U,A as T,f as I,o as O,r as $,s as y}from"./index-09a1718e.js";import{P as G}from"./index-49471902.js";import{S as K}from"./index-5a645c18.js";import{M as W}from"./index-243c4859.js";import{t as s}from"./i18next-7389dd8c.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";let x=new U;const xe=()=>{var v;const{state:u,dispatch:p}=N.useContext(T),f=R(),P=A(),[w,n]=N.useState(!1),[k,_]=b.useState(null),t=new URLSearchParams(P.search),i=t.get("skill"),h=t.get("time"),g=t.get("endDate");t.get("location"),t.get("location_id");const o=t.get("address"),c=t.get("price"),j=t.get("description"),q=t.get("service_id"),l=t.get("lat"),m=t.get("lng"),C=t.get("city"),S=t.get("state"),d=(v=t.get("images"))==null?void 0:v.split(","),D=async()=>{try{n(!0),await await x.callRawAPI("/v3/api/custom/chumpchange/user/address/create",{description:o,latitude:String(l),longtitude:String(m)},"POST"),(await x.callRawAPI("/v3/api/custom/chumpchange/user/task/create",{title:i,service_id:q,offer:c,description:j,image:t.get("images"),start_datetime:`${g}T${h}`,latitude:l,longtitude:m,state:S,city:C,address:o,status:"created"},"POST")).error||(y(p,"Listing Created"),f("/user/my-requests")),n(!1)}catch(a){n(!1),console.log("Error",a),y(p,(a==null?void 0:a.message)||"Error Creating Listing",4e3,"error")}};return b.useEffect(()=>{(async()=>{const r=await x.callRawAPI("/v3/api/custom/chumpchange/provider/data",{},"GET");console.log("result >>",r),r.error||_(r.data)})()},[]),e.jsxs("div",{className:"p-5",children:[w&&e.jsx(W,{showCloseButton:!1,children:e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsxs("div",{className:"text-lg font-semibold",children:[s("loading.creating"),"..."]}),e.jsx("div",{className:"mt-12",children:e.jsx(K,{})})]})}),e.jsxs("div",{className:"relative flex w-full items-center justify-center ",children:[e.jsx("div",{className:" absolute left-0 top-0 ",children:e.jsx(L,{link:`/user/helping-hand-request?${t.toString()}`})}),e.jsxs("div",{className:"",children:[e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:s("user.con_request.title")}),e.jsx("div",{className:"text-center font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:i||"N/A"})]})]}),e.jsx(G,{user:B,userData:k}),e.jsxs("div",{className:" mt-9 ",children:[e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:s("user.con_request.help")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:i||"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:s("user.con_request.date_time")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:I(g+":"+h)})]}),e.jsxs("div",{className:"flex items-center gap-3 justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:s("user.con_request.location")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:e.jsx("button",{onClick:()=>{O(Number(l),Number(m))},className:" text-right text-[#4fa7f9] underline ",children:o?$(o):""})})]}),e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:s("user.con_request.pay_offered")}),e.jsxs("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:["DOP ",c]})]}),e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:s("user.con_request.my_number")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:u.userDetails.phone?u.userDetails.phone:"N/A"})]}),e.jsxs("div",{className:"flex justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsxs("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:[s("user.con_request.description"),":"]}),e.jsx("div",{className:"w-[165px] text-right font-['Poppins'] text-sm font-light leading-[16.80px] text-black",children:j})]}),e.jsxs("div",{className:" mt-7 border-t border-[#8181a4]/20 py-3 pt-8 ",children:[e.jsx("div",{className:"font-['Poppins'] text-lg font-medium text-black",children:s("user.con_request.images")}),e.jsx("div",{className:"scrollbar-hide flex w-full overflow-x-auto",children:e.jsx("div",{className:" mt-5 flex gap-2",children:d==null?void 0:d.map((a,r)=>e.jsx("img",{className:"h-[127px] w-[120px] rounded-[20px] object-cover ",src:a},r))})})]})]}),e.jsx("div",{className:"my-5 font-['Poppins'] text-xs font-medium tracking-tight text-black",children:s("user.con_request.pay_chiripero",{price:c})}),e.jsxs("div",{className:"mt-10 flex flex-col gap-3",children:[e.jsx(E,{onClick:D,className:"opacity-[0.85]",children:s("user.con_request.create_btn")}),e.jsx(M,{onClick:()=>f("/user/home"),className:"uppercase opacity-[0.85]",children:s("buttons.cancel")})]})]})};export{xe as default};
