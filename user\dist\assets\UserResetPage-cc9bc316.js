import{j as e}from"./@react-google-maps/api-ee55a349.js";import{r as j,u as b,i as v,d as P}from"./vendor-b16525a8.js";import{u as y}from"./react-hook-form-b6ed2679.js";import{o as S}from"./yup-3990215a.js";import{c as N,a as c,b as L}from"./yup-f828ae80.js";import{A as q,a as B}from"./index-dd254604.js";import{P as l}from"./index-4ee87ce5.js";import{B as k,a as A}from"./index-d54cffea.js";import{M as E}from"./index-09a1718e.js";import{t}from"./i18next-7389dd8c.js";import"./@hookform/resolvers-3e831b4a.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";let I=new E;const oe=()=>{var n,p;const[r,o]=j.useState(!1),d=b(),u=new URLSearchParams(d.search).get("phone"),{code:f}=v(),h=N({password:c().min(8,t("auth_pages.reset.password_long")).required(),confirm_password:c().oneOf([L("password"),null],t("auth_pages.reset.password_must")).required()}).required(),{register:i,handleSubmit:g,setError:x,formState:{errors:s}}=y({resolver:S(h)}),m=P(),w=async _=>{try{o(!0);const a=await I.callRawAPI("/v3/api/custom/chumpchange/user/password-reset/verify-code",{email:u,confirmationCode:f,password:_.password},"POST");localStorage.getItem("token")?m("/user/dashboard"):m("/user/login"),o(!1)}catch(a){o(!1),console.log("Error",a),x("phone",{type:"manual",message:a==null?void 0:a.message})}};return e.jsx(q,{children:e.jsxs("form",{onSubmit:g(w),children:[e.jsx(k,{}),e.jsx(B,{className:"-mt-6 leading-none ",children:t("auth_pages.reset.title")}),e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx("div",{className:" flex h-[70px] flex-col items-center",children:s!=null&&s.password||s!=null&&s.confirm_password?e.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:e.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:((n=s==null?void 0:s.password)==null?void 0:n.message)||((p=s==null?void 0:s.confirm_password)==null?void 0:p.message)})}):e.jsx("div",{className:"font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:t("auth_pages.reset.sub_title_password")})}),e.jsx(l,{name:"password",placeholder:t("auth_pages.reset.password"),errors:s,register:i}),e.jsx(l,{name:"confirm_password",placeholder:t("auth_pages.reset.confirm_password"),errors:s,register:i}),e.jsx("div",{className:"flex justify-center",children:e.jsx("div",{className:"text-center font-['Poppins'] text-sm font-medium leading-snug text-[#8080a3]",dangerouslySetInnerHTML:{__html:t("auth_pages.reset.password_des")}})}),e.jsx("div",{className:" mt-10 ",children:e.jsx(A,{loading:r,disabled:r,type:"submit",children:t("auth_pages.reset.set_pass")})})]})]})})};export{oe as default};
