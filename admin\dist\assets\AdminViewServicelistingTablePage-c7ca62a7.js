import{j as s}from"./@react-google-maps/api-ac2f9d6f.js";import{R as a,h as o}from"./vendor-4f06b3f4.js";import"./yup-17027d7a.js";import{M as h,G as r,t as f}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{S as j}from"./index-2d8231e7.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";let c=new h;const I=()=>{const{dispatch:m}=a.useContext(r),{dispatch:d}=a.useContext(r),[e,n]=a.useState({}),[x,l]=a.useState(!0),t=o();return a.useEffect(function(){(async function(){try{l(!0),c.setTable("servicelisting");const i=await c.callRestAPI({id:Number(t==null?void 0:t.id),join:""},"GET");i.error||(n(i.model),l(!1))}catch(i){l(!1),console.log("error",i),f(d,i.message)}})()},[]),a.useEffect(()=>{m({type:"SHOW_BACKBUTTON",payload:{showBackButton:!0}})},[]),s.jsx("div",{className:" mx-auto rounded  p-5 shadow-md",children:x?s.jsx(j,{}):s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"User Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.user_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Description"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.description})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Rate"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.rate})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Duration"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.duration})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"End Date"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.end_date})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Start Date"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.start_date})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Status"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.status})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Service Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.service_id})]})})]})})};export{I as default};
