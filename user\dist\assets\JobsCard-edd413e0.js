import{j as i}from"./@react-google-maps/api-ee55a349.js";import{t}from"./i18next-7389dd8c.js";import{L as l}from"./vendor-b16525a8.js";import{r as m,f as o}from"./index-09a1718e.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";const y=({taskData:s})=>i.jsx("div",{className:" flex h-full max-h-[calc(100vh-250px)] w-full flex-col gap-4 overflow-y-auto px-5 py-5 ",children:s.length===0?i.jsx("div",{className:"mt-5 w-full text-center font-['Poppins'] text-2xl font-medium text-[#b4b4b4]",dangerouslySetInnerHTML:{__html:t("user.my_reuqest.no_request")}}):s.map((e,r)=>i.jsxs(l,{to:`/${localStorage.getItem("role")}/view-job/${e.id}`,className:"flex w-full grid-cols-[20%,79%] gap-[1%] ",children:[i.jsx("div",{className:"flex h-11 w-11 flex-shrink-0 items-center justify-center overflow-hidden rounded-full bg-[#50a8f9]/10",children:i.jsx("img",{src:e==null?void 0:e.service_logo,alt:"",className:"h-11 w-11 object-cover"})}),i.jsxs("div",{className:"flex flex-col items-start justify-center pb-2",children:[i.jsxs("div",{className:"flex flex-wrap items-center justify-start gap-1",children:[i.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:e==null?void 0:e.service_name}),i.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:"→"}),i.jsxs("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:["$",e==null?void 0:e.offer]})]}),i.jsxs("div",{className:"flex flex-wrap items-center justify-start gap-1",children:[i.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:e!=null&&e.address?m(e==null?void 0:e.address):"N/A"}),i.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:"•"}),i.jsx("div",{className:"text-right font-['Poppins'] text-xs font-medium text-[#8080a3]",children:o(e==null?void 0:e.start_datetime)})]}),(e==null?void 0:e.status)==="completed"?i.jsxs("div",{className:" flex flex-wrap items-center justify-start gap-1",children:[i.jsxs("div",{className:" flex ",children:[i.jsx("div",{className:" font-['Poppins'] text-xs font-medium text-[#56ccf2]",children:t("user.my_reuqest.status")}),i.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:"•"})]}),i.jsx("div",{className:" text-right font-['Poppins'] text-xs font-medium capitalize text-[#8080a3] ",children:e!=null&&e.provider_status?e==null?void 0:e.provider_status:"On offer"})]}):i.jsxs("div",{className:" flex flex-wrap items-center justify-start gap-1",children:[i.jsxs("div",{className:" flex ",children:[i.jsx("div",{className:" font-['Poppins'] text-xs font-medium text-[#56ccf2]",children:t("user.my_reuqest.status")}),i.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:"•"})]}),i.jsx("div",{className:" text-right font-['Poppins'] text-xs font-medium capitalize text-[#8080a3] ",children:(e==null?void 0:e.provider_status)=="completed"?"Pending Completion":e!=null&&e.provider_status?e==null?void 0:e.provider_status:"On offer"})]}),(e==null?void 0:e.rating)&&i.jsxs("div",{className:" flex flex-wrap items-center justify-start gap-1",children:[i.jsx("div",{className:" font-['Poppins'] text-xs font-medium text-[#8080a3]",children:t("user.my_reuqest.rating")}),i.jsx("div",{className:" font-['Poppins'] text-xs font-medium text-[#8080a3]",children:"•"}),i.jsxs("div",{className:"flex items-center justify-start gap-1",children:[i.jsx("svg",{width:"15",height:"15",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{d:"M5.10938 10.1562L7.5 14.1406L9.89062 10.1562L14.6719 8.5625L11.75 5.375L12.5469 0.59375L7.5 2.45312L2.1875 0.859375L3.51562 5.10938L0.859375 8.5625L5.10938 10.1562Z",fill:"#56CCF2"})}),i.jsx("svg",{width:"15",height:"15",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{d:"M5.10938 10.1562L7.5 14.1406L9.89062 10.1562L14.6719 8.5625L11.75 5.375L12.5469 0.59375L7.5 2.45312L2.1875 0.859375L3.51562 5.10938L0.859375 8.5625L5.10938 10.1562Z",fill:"#56CCF2"})})]})]})]})]},r))});export{y as default};
