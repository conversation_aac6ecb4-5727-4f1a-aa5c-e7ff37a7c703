import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as s,b as f,r as m}from"./vendor-4f06b3f4.js";import{M as h,A as S,G as g}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{L as u}from"./index-6416aa2c.js";import"./index-d97c616d.js";import{M as x}from"./index-68c82eaa.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";new h;const E=[{header:"Oauth",accessor:"oauth",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Role",accessor:"role",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"First Name",accessor:"first_name",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Last Name",accessor:"last_name",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Email",accessor:"email",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Password",accessor:"password",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Type",accessor:"type",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Verify",accessor:"verify",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Phone",accessor:"phone",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Photo",accessor:"photo",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Operating City",accessor:"operating_city",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Refer",accessor:"refer",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Stripe Uid",accessor:"stripe_uid",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Paypal Uid",accessor:"paypal_uid",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Confirmation Code",accessor:"confirmation_code",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Two Factor Authentication",accessor:"two_factor_authentication",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Update At",accessor:"update_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],H=()=>{s.useContext(S);const{dispatch:r}=s.useContext(g);f();const[w,l]=s.useState(!1),[D,p]=s.useState(!1),[b,d]=s.useState(),c=m.useRef(null),[_,n]=s.useState([]),t=(a,i,o=[])=>{switch(a){case"add":l(i);break;case"edit":p(i),n(o),d(o[0]);break}};return s.useEffect(()=>{r({type:"SETPATH",payload:{path:"task"}})},[]),e.jsx(e.Fragment,{children:e.jsx(e.Fragment,{children:e.jsx("div",{className:"overflow-x-auto  rounded bg-white p-5 shadow",children:e.jsx(u,{children:e.jsx(x,{columns:E,tableRole:"government",table:"user",actionId:"id",actions:{view:{show:!1,action:null,multiple:!1},edit:{show:!1,multiple:!1,action:a=>t("edit",!0,a)},delete:{show:!1,action:null,multiple:!1},select:{show:!1,action:null,multiple:!1},add:{show:!1,action:()=>t("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!1,action:null,multiple:!0}},actionPostion:"ontable",refreshRef:c})})})})})};export{H as default};
