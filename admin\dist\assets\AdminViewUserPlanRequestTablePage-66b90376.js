import{j as s}from"./@react-google-maps/api-ac2f9d6f.js";import{R as t,h as N,r as b}from"./vendor-4f06b3f4.js";import"./yup-17027d7a.js";import{M as v,G as o,t as _}from"./index-06b5b6dd.js";import{h as S}from"./moment-a9aaa855.js";import{S as I}from"./index-2d8231e7.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";let r=new v;const P={0:"Inactive",1:"Active",2:"Suspend"},H=()=>{const{dispatch:c}=t.useContext(o),{dispatch:m}=t.useContext(o),[e,x]=t.useState({}),[h,n]=t.useState(!0);t.useState(!1);const[y,u]=t.useState(0),[i,j]=t.useState({}),[a,p]=t.useState({}),d=N();return b.useEffect(function(){(async function(){try{n(!0),r.setTable("user_plan");const l=await r.callRestAPI({id:Number(d==null?void 0:d.id)},"GET");if(!l.error){r.setTable("plan");const f=await r.callRestAPI({id:l.model.plan_id},"GET");r.setTable("user");const g=await r.callRestAPI({id:l.model.provider_id},"GET");j(g.model),p(f.model),x(l.model),u(l.model.provider_id),n(!1)}}catch(l){n(!1),console.log("error",l),_(m,l.message)}})()},[]),t.useEffect(()=>{c({type:"SHOW_BACKBUTTON",payload:{showBackButton:!0}})},[]),s.jsx("div",{className:" mx-auto rounded  p-5 shadow-md",children:h?s.jsx(I,{}):s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"mb-6 w-full rounded-lg border bg-gray-50 p-4 text-left shadow-md ",children:s.jsxs("ul",{className:"list-inside list-disc text-gray-700",children:[s.jsxs("li",{children:[s.jsx("strong",{children:"First Name:"})," ",i.first_name]}),s.jsxs("li",{children:[s.jsx("strong",{children:"Last Name:"})," ",i.last_name]}),s.jsxs("li",{children:[s.jsx("strong",{children:"Email:"})," ",i.email]}),s.jsxs("li",{children:[s.jsx("strong",{children:"Phone:"})," ",i.phone]}),s.jsxs("li",{children:[s.jsx("strong",{children:"City:"})," ",i.operating_city]}),s.jsxs("li",{children:[s.jsx("strong",{children:"Status:"})," ",P[i.status]]})]})}),s.jsxs("div",{className:"mb-6 w-full rounded-lg border bg-gray-50 p-4 text-left shadow-md ",children:[s.jsx("h5",{className:"text-lg font-semibold text-gray-800",children:"Plan Info"}),s.jsxs("ul",{className:"list-inside list-disc text-gray-700",children:[s.jsxs("li",{children:[s.jsx("strong",{children:"Name:"})," ",a.name]}),s.jsxs("li",{children:[s.jsx("strong",{children:"Amount:"})," $",a.amount/100]}),s.jsxs("li",{children:[s.jsx("strong",{children:"Duration:"})," ",a.duration," month(s)"]}),s.jsxs("li",{children:[s.jsx("strong",{children:"Description:"})," ",a.description]}),s.jsxs("li",{className:" capitalize ",children:[s.jsx("strong",{children:"Status:"})," ",a.status]})]})]}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Provider Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.provider_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Plan Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.plan_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Status"}),s.jsx("div",{className:"flex-1 capitalize ",children:e==null?void 0:e.status})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"End At"}),s.jsx("div",{className:"flex-1",children:e!=null&&e.end_at?S(e.end_at).format("MMMM Do YYYY"):"N/A"})]})})]})})};export{H as default};
