import{j as t}from"./@react-google-maps/api-ee55a349.js";import{r as s,i as g,R as n,L as w}from"./vendor-b16525a8.js";import"./index-9ef1c042.js";import{M as y,A as k,G as A}from"./index-09a1718e.js";import{U as N}from"./index-dd254604.js";import{U as m}from"./index-e25e12e5.js";import{S as D}from"./index-5a645c18.js";import{t as i}from"./i18next-7389dd8c.js";import{u as E}from"./user-a875fff3.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";const p=new y,X=()=>{var l;const{state:a,dispatch:S}=s.useContext(k),{state:P,dispatch:d}=s.useContext(A),[r,o]=s.useState("active"),[x,u]=s.useState([]),[f,h]=s.useState([]),[v,c]=s.useState(!1);g();const j=async()=>{try{const e=await p.callRawAPI("/v3/api/custom/chumpchange/user/alert/active",{},"GET");e.error||u(e.data)}catch(e){console.error("Error fetching active tasks:",e)}},b=async()=>{try{const e=await p.callRawAPI("/v3/api/custom/chumpchange/user/alert/inactive",{},"GET");e.error||h(e.data)}catch(e){return console.error("Error fetching completed tasks:",e),[]}};return n.useEffect(()=>{(async()=>(c(!0),await j(),await b(),c(!1)))()},[]),n.useEffect(()=>{d({type:"SETPATH",payload:{path:"alerts"}})},[]),t.jsxs(N,{className:" relative ",children:[t.jsxs("div",{className:" mt-2 flex items-center justify-between px-10 ",children:[t.jsx("div",{className:"font-['Poppins'] text-lg font-bold text-black",children:i("user.my_alert.title")}),t.jsx(w,{to:"/user/profile",className:" relative z-10 ",children:t.jsx("img",{className:"h-[45px] w-[45px] rounded-full",src:((l=a==null?void 0:a.userDetails)==null?void 0:l.photo)||E})})]}),t.jsx("div",{className:" mt-14 px-5 ",children:t.jsxs("div",{className:" grid h-12 w-full grid-cols-2 items-center rounded-2xl bg-[#8181a4]/20 px-1",children:[t.jsx("button",{className:`flex h-10 items-center justify-center rounded-2xl font-['Poppins'] text-sm font-medium ${r==="active"?"bg-black text-white":"text-black"}`,onClick:()=>o("active"),children:i("user.my_alert.active")}),t.jsx("button",{className:`flex h-10 items-center justify-center rounded-2xl font-['Poppins'] text-sm font-medium ${r==="inactive"?"bg-black text-white":"text-black"}`,onClick:()=>o("inactive"),children:i("user.my_alert.inactive")})]})}),t.jsxs("div",{className:" relative left-0 mt-6 flex w-full flex-1 flex-col items-center justify-start overflow-hidden rounded-tl-[32px] rounded-tr-[32px] bg-white",children:[t.jsx("div",{className:" flex h-7 w-full max-w-[335px] items-center justify-center border-b bg-white ",children:t.jsx("div",{className:" h-1 w-[50px] rounded bg-[#f2f2f7]"})}),v?t.jsx("div",{className:"flex h-full w-full items-center justify-center pt-10 ",children:t.jsx(D,{})}):r==="active"?t.jsx(m,{taskData:x}):t.jsx(m,{taskData:f})]})]})};export{X as default};
