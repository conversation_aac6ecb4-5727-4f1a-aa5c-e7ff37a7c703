import{j as e}from"./@react-google-maps/api-ee55a349.js";import{B as S,a as O}from"./index-d54cffea.js";import{r as n,u as Z,R as u,d as $}from"./vendor-b16525a8.js";import{u as z}from"./react-hook-form-b6ed2679.js";import{o as G}from"./yup-3990215a.js";import{c as T,a as C}from"./yup-f828ae80.js";import{M as U,G as J,s as Q}from"./index-09a1718e.js";import{P as W}from"./index-d152a0de.js";import{t as a}from"./i18next-7389dd8c.js";import{A as X}from"./@vis.gl/react-google-maps-934eb5c3.js";import"./qr-scanner-cf010ec4.js";import"./@hookform/resolvers-3e831b4a.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";let P=new U;const Pe=()=>{const{state:Y,dispatch:_}=n.useContext(J),[i,v]=n.useState(0),[c,b]=n.useState(0);Z();const[j,p]=n.useState(),[k,g]=u.useState(!1),[M,f]=n.useState(!1),[d,x]=n.useState([]),[A,y]=u.useState(""),[ee,V]=n.useState([]),F=$(),m=u.useRef(null);u.useEffect(()=>{if(window.google&&window.google.maps&&window.google.maps.places){const s=new window.google.maps.Map(document.createElement("div"));m.current=new window.google.maps.places.PlacesService(s)}},[]);const B=T({address:C().required(),label:C().required()}).required(),{register:h,handleSubmit:D,setError:se,setValue:r,formState:{errors:N,isValid:te}}=z({resolver:G(B),mode:"onChange"}),E=async s=>{try{if(!i&&!c){Q(_,a("user.add_address.select_address"),4e3,"error");return}p(!0);const o=await await P.callRawAPI("/v3/api/custom/chumpchange/user/address/create",{name:s.label,description:s.address,city:s.city,latitude:String(s.lat),longtitude:String(s.lng),state:s.state,country:s.country},"POST");F("/user/address"),p(!1)}catch(o){p(!1),console.log("error >> ",o)}},H=(s,o,t,l,w,K)=>{console.log(s,o),v(s||""),b(o||""),r("address",t||""),r("city",l||""),r("state",w||""),r("country",K||""),r("lat",s||""),r("lng",o||""),g(!1)},R=async s=>{const o=s.target.value;y(o),o?(q(o),f(!0)):(V([]),f(!1))},q=s=>{s.length>2?window.google&&new window.google.maps.places.AutocompleteService().getPlacePredictions({input:s},t=>x(t||[])):x([])},I=s=>new Promise((o,t)=>{m.current&&m.current.getDetails({placeId:s},(l,w)=>{w===window.google.maps.places.PlacesServiceStatus.OK&&l.geometry?(console.log("place >. ",l),o({city:l.name,address:l.formatted_address,lat:l.geometry.location.lat(),lng:l.geometry.location.lng()})):t(`Failed to fetch details for placeId: ${s}`)})}),L=async s=>{try{const o=new window.google.maps.Map(document.createElement("div"));m.current=new window.google.maps.places.PlacesService(o);const t=await I(s.place_id);console.log("details >> ",t),y(t==null?void 0:t.address),v(t.lat||""),b(t.lng||""),r("address",(t==null?void 0:t.address)||""),r("city",(t==null?void 0:t.city)||""),r("lat",(t==null?void 0:t.lat)||""),r("lng",(t==null?void 0:t.lng)||""),f(!1),x([])}catch(o){console.log("error >>",o)}};return console.log(i,c,N),e.jsxs("div",{className:"p-5",children:[k&&e.jsxs("div",{className:"fixed left-0 top-0 z-[9999999] min-h-screen w-full bg-[#F2F2F7] ",children:[e.jsx("div",{className:"p-5",children:e.jsxs("div",{className:"relative flex w-full items-center justify-between ",children:[e.jsx(S,{onClick:()=>g(!1)}),e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:a("user.add_address.pick_location")}),e.jsx("div",{className:""})]})}),e.jsx("div",{className:" h-[calc(100vh-88px)] ",children:e.jsx(W,{handleChoose:H,lat:i?Number(i):"",lng:c?Number(c):""})})]}),e.jsxs("div",{className:"relative flex w-full items-center justify-between ",children:[e.jsx("div",{className:" ",children:e.jsx(S,{link:"/user/address"})}),e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:a("user.add_address.title")}),e.jsx("div",{className:""})]}),Object.keys(N).length>0&&e.jsx("div",{className:" mt-5 flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:e.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:a("user.add_address.require")})}),e.jsxs("form",{onSubmit:D(E),className:" mt-[55px] flex flex-col gap-3 ",children:[e.jsx("div",{className:"",children:e.jsx("input",{type:"text",placeholder:a("user.add_address.label"),...h("label"),className:"relative mt-3 h-12 w-full rounded-2xl border-none bg-white text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "})}),e.jsxs("div",{className:" relative ",children:[e.jsx(X,{apiKey:P._google_api_key,libraries:["places"],children:e.jsx("input",{type:"text",...h("address",{onChange:R}),value:A,placeholder:a("user.create_request.address"),className:"relative mt-3 h-12 w-full rounded-2xl border-none bg-white text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "})}),M&&e.jsx("div",{className:"modal-content absolute top-[100%] z-[999999] max-h-[400px] w-full overflow-y-auto rounded-md border border-gray-300 bg-white",children:d.length>0?d==null?void 0:d.map((s,o)=>e.jsx("div",{className:"cursor-pointer p-2 hover:bg-gray-200",onClick:()=>L(s),children:s==null?void 0:s.description},o)):""})]}),e.jsx("div",{className:"",children:e.jsx("input",{type:"text",placeholder:a("user.add_address.enter_city"),...h("city"),className:"relative mt-3 h-12 w-full rounded-2xl border-none bg-white text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "})}),e.jsx("button",{type:"button",onClick:()=>g(!0),className:"",children:e.jsxs("div",{className:"flex h-[55px] w-full items-center gap-3 rounded-xl border border-[#e1e2e3] bg-[#f7f7f7] p-3 ",children:[e.jsx("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M16 10.1818C12.8 10.1818 10.1818 12.8 10.1818 16C10.1818 19.2 12.8 21.8182 16 21.8182C19.2 21.8182 21.8182 19.2 21.8182 16C21.8182 12.8 19.2 10.1818 16 10.1818ZM28.9455 14.5455C28.2182 8.43636 23.4182 3.63636 17.4545 3.05455V0H14.5455V3.05455C8.43636 3.63636 3.63636 8.43636 3.05455 14.5455H0V17.4545H3.05455C3.78182 23.5636 8.58182 28.3636 14.5455 28.9455V32H17.4545V28.9455C23.5636 28.2182 28.3636 23.4182 28.9455 17.4545H32V14.5455H28.9455ZM16 26.1818C10.3273 26.1818 5.81818 21.6727 5.81818 16C5.81818 10.3273 10.3273 5.81818 16 5.81818C21.6727 5.81818 26.1818 10.3273 26.1818 16C26.1818 21.6727 21.6727 26.1818 16 26.1818Z",fill:"#56CCF2"})}),e.jsx("div",{className:"font-['Poppins'] text-base font-light tracking-tight text-black",children:a("user.add_address.choose_on_map")})]})}),e.jsx("div",{className:"mt-10",children:e.jsx(O,{type:"submit",loading:j,disabled:j,children:a("user.add_address.add_btn")})})]})]})};export{Pe as default};
