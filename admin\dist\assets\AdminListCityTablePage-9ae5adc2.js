import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as t,b as x,r as b}from"./vendor-4f06b3f4.js";import{M as w,A as g,G as A,_ as E,$ as j}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{L as o}from"./index-6416aa2c.js";import{M as n}from"./index-d97c616d.js";import{M}from"./index-68c82eaa.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";new w;const v=[{header:"Name",accessor:"name",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Latitude",accessor:"lattitude",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Longitude",accessor:"longitude",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],J=()=>{t.useContext(g);const{dispatch:m}=t.useContext(A);x();const[p,s]=t.useState(!1),[l,a]=t.useState(!1),[u,f]=t.useState(),h=b.useRef(null),[y,S]=t.useState([]),d=(i,r,c=[])=>{switch(i){case"add":s(r);break;case"edit":a(r),S(c),f(c[0]);break}};return t.useEffect(()=>{m({type:"SETPATH",payload:{path:"city"}})},[]),e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"overflow-x-auto rounded bg-white p-5 shadow",children:e.jsx(o,{children:e.jsx(M,{columns:v,tableRole:"admin",table:"city",actionId:"id",actions:{view:{show:!0,action:null,multiple:!1},edit:{show:!0,multiple:!1,action:i=>d("edit",!0,i)},delete:{show:!0,action:null,multiple:!1},select:{show:!0,action:null,multiple:!1},add:{show:!0,action:()=>d("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!1,action:null,multiple:!0}},actionPostion:"ontable",refreshRef:h})})}),e.jsx(o,{children:e.jsx(n,{isModalActive:p,closeModalFn:()=>s(!1),children:e.jsx(E,{setSidebar:s})})}),l&&e.jsx(o,{children:e.jsx(n,{isModalActive:l,closeModalFn:()=>a(!1),children:e.jsx(j,{activeId:u,setSidebar:a})})})]})};export{J as default};
