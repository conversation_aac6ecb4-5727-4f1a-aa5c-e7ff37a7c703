import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as a,b as A,h as P,r as T}from"./vendor-4f06b3f4.js";import{u as R}from"./react-hook-form-f3d72793.js";import{o as k}from"./yup-2324a46a.js";import{c as C,a as D}from"./yup-17027d7a.js";import{M as q,A as G,G as L,t as M,s as F}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{M as O}from"./MkdInput-ff3aa862.js";import{I as U}from"./InteractiveButton-8f7d74ee.js";import{S as Z}from"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./MoonLoader-16bed42a.js";let r=new q;const $={0:"Inactive",1:"Active",2:"Suspend"},fe=l=>{const{dispatch:g}=a.useContext(G),j=C({status:D().oneOf(["created","rejected","approved"]).required()}).required(),{dispatch:p}=a.useContext(L),[h,c]=a.useState(!1),[v,m]=a.useState(!1),[B,b]=a.useState(0),[i,y]=a.useState({id:79,oauth:null,role:"provider",first_name:"yeaisn",last_name:"arasfty",email:"<EMAIL>",type:null,verify:1,admin_verify:0,phone:"+12085689506",photo:null,operating_city:"New York3",refer:null,stripe_uid:null,paypal_uid:null,listing_balance:6140,confirmation_code:null,two_factor_authentication:null,cedula_number:"520562",cedula_image_link:'{"front":false,"back":false}',qr_link:"[]",status:1,create_at:"2024-11-08",update_at:"2024-11-08T14:18:14.000Z"}),[n,_]=a.useState({id:3,name:"Premium Plan",amount:1999,duration:1,description:"This plan offers premium features and benefits.",status:"active",create_at:"2024-10-25T00:00:00.000Z",update_at:"2024-10-25T13:26:54.000Z"});A();const{register:S,handleSubmit:I,setError:f,setValue:w,formState:{errors:N}}=R({resolver:k(j)}),o=P();T.useEffect(function(){(async function(){try{m(!0),r.setTable("user_plan");const t=await r.callRestAPI({id:l.activeId?l.activeId:Number(o==null?void 0:o.id)},"GET");if(!t.error){r.setTable("plan");const s=await r.callRestAPI({id:t.model.plan_id},"GET");r.setTable("user");const d=await r.callRestAPI({id:t.model.provider_id},"GET");y(d.model),_(s.model),w("status",t.model.status),b(t.model.provider_id),m(!1)}}catch(t){m(!1),console.log("error",t),M(g,t.message)}})()},[]);const E=async t=>{c(!0);try{const s=await r.callRawAPI(`/v3/api/custom/chumpchange/admin/provider-plans/update/${l.activeId?l.activeId:Number(o==null?void 0:o.id)}`,{status:t.status},"POST");if(!s.error)F(p,"Updated"),p({type:"REFRESH_DATA",payload:{refreshData:!0}}),l.setSidebar(!1);else if(s.validation){const d=Object.keys(s.validation);for(let u=0;u<d.length;u++){const x=d[u];f(x,{type:"manual",message:s.validation[x]})}}c(!1)}catch(s){c(!1),console.log("Error",s),f("status",{type:"manual",message:s.message})}};return e.jsxs("div",{className:"mx-auto rounded p-5 shadow-md",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Edit Activation Requests"}),v?e.jsx(Z,{}):e.jsxs("form",{className:"mt-6 w-full max-w-lg ",onSubmit:I(E),children:[e.jsxs("div",{className:"mb-6 w-full rounded-lg border bg-gray-50 p-4 text-left shadow-md ",children:[e.jsx("h5",{className:"text-lg font-semibold text-gray-800",children:"Activation Requests"}),e.jsxs("ul",{className:"list-inside list-disc text-gray-700",children:[e.jsxs("li",{children:[e.jsx("strong",{children:"First Name:"})," ",i.first_name]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Last Name:"})," ",i.last_name]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Email:"})," ",i.email]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Phone:"})," ",i.phone]}),e.jsxs("li",{children:[e.jsx("strong",{children:"City:"})," ",i.operating_city]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Status:"})," ",$[i.status]]})]})]}),e.jsxs("div",{className:"mb-6 w-full rounded-lg border bg-gray-50 p-4 text-left shadow-md ",children:[e.jsx("h5",{className:"text-lg font-semibold text-gray-800",children:"Plan Info"}),e.jsxs("ul",{className:"list-inside list-disc text-gray-700",children:[e.jsxs("li",{children:[e.jsx("strong",{children:"Name:"})," ",n.name]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Amount:"})," ",n.amount]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Duration:"})," ",n.duration," month(s)"]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Description:"})," ",n.description]}),e.jsxs("li",{className:" capitalize ",children:[e.jsx("strong",{children:"Status:"})," ",n.status]})]})]}),e.jsx(O,{type:"dropdown",page:"edit",name:"status",errors:N,label:"Status",placeholder:"Select Status",register:S,options:[{value:"created",name:"Created"},{value:"rejected",name:"Rejected"},{value:"approved",name:"Approved"}],className:""}),e.jsx(U,{type:"submit",className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",loading:h,disable:h,children:"Submit"})]})]})};export{fe as default};
