import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{M as E,G as _}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{R as p,r as a}from"./vendor-4f06b3f4.js";import{u as k}from"./react-hook-form-f3d72793.js";import{o as M}from"./yup-2324a46a.js";import{c as P}from"./yup-17027d7a.js";import{D as A}from"./index-f6c8bd1f.js";import{I}from"./InteractiveButton-8f7d74ee.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@hookform/resolvers-1aa18522.js";import"./MoonLoader-16bed42a.js";let c=new E;const se=()=>{const{dispatch:u}=p.useContext(_),[r,x]=a.useState([]),[f,l]=a.useState(!0),[m,i]=a.useState(!1),[h,n]=a.useState(!1),[g,b]=a.useState({}),j=P().shape({}),{register:y,handleSubmit:S,reset:D,formState:{errors:o}}=k({resolver:M(j)}),d=async()=>{try{l(!0),c.setTable("setting");const e=await c.callRestAPI({},"PAGINATE"),{list:s}=e;x(s),l(!1)}catch(e){l(!1),console.log("error >> ",e)}},N=()=>{const e={};r.forEach(s=>{e[s.id]=s.setting_value}),b(e),n(!0)},v=async e=>{try{i(!0),console.log("formData >> ",e),await Promise.all(Object.keys(e).map(s=>c.callRestAPI({id:s,setting_value:e[s]},"PUT").catch(w=>{console.error(`Error updating setting ${s}:`,w)}))),n(!1),d(),i(!1)}catch(s){i(!1),console.log("Update error >> ",s)}};return p.useEffect(()=>{u({type:"SETPATH",payload:{path:"setting"}}),d()},[]),console.log("errors >> ",o),t.jsxs("div",{className:"mt-10 px-10",children:[f?t.jsx("p",{children:"Loading..."}):t.jsx("ul",{children:r.map(e=>t.jsxs("li",{className:" mb-1 flex gap-2  ",children:[t.jsxs("span",{className:"capitalize ",children:[e.setting_key.split("_").join(" "),":"," "]}),t.jsx("span",{children:e.setting_value})]},e.id))}),t.jsx("button",{onClick:N,className:"mt-10 rounded bg-blue-500 px-4 py-1 text-white",children:"Edit"}),t.jsx(A,{closeModal:()=>n(!1),isModal:h,title:"Edit Settings",children:t.jsxs("form",{onSubmit:S(v),className:"w-full ",children:[r.map(e=>t.jsxs("div",{className:"mt-4",children:[t.jsxs("label",{className:"mb-2 block cursor-pointer text-sm font-bold capitalize text-gray-700 ",children:[e.setting_key.split("_").join(" "),":"]}),t.jsx("input",{...y(e.id.toString()),defaultValue:g[e.id],className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${o[e.id]?"border-red-500":""}`}),o[e.id]&&t.jsx("span",{className:"text-red-500",children:o[e.id].message})]},e.id)),t.jsx("div",{className:"mt-4 flex justify-end",children:t.jsx(I,{loading:m,disabled:m,type:"submit",className:"rounded bg-blue-500 px-4 py-2 text-white disabled:opacity-50 ",children:"Save"})})]})})]})};export{se as default};
