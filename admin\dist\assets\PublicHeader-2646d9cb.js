import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as r,r as o,b as i}from"./vendor-4f06b3f4.js";import{A as m}from"./index-06b5b6dd.js";import"./react-confirm-alert-525c3702.js";import"./moment-a9aaa855.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";const C=()=>(r.useContext(m),o.useState(!1),i(),t.jsx("div",{}));export{C as PublicHeader,C as default};
