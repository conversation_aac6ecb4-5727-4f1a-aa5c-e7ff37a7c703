import{j as t}from"./@react-google-maps/api-ee55a349.js";import{r as c}from"./vendor-b16525a8.js";import{_ as m}from"./MoonLoader-49322f56.js";const b=({loading:e=!1,disabled:s,children:r,type:o="button",className:f,loaderclasses:a,onClick:n,color:i="#ffffff"})=>{const d={borderColor:"#ffffff"},l=c.useId();return t.jsx("button",{type:o,disabled:s,className:`flex h-12 w-full items-center justify-center gap-5 rounded-2xl bg-[#219653] font-['Poppins'] text-base font-semibold text-white disabled:opacity-25 ${f}`,onClick:n,children:t.jsxs(t.Fragment,{children:[t.jsx(m,{color:i,loading:e,cssOverride:d,size:20,className:a,"data-testid":l}),t.jsx("span",{children:r})]})})};export{b as default};
