import{j as o}from"./@react-google-maps/api-afbf18d5.js";import{t as p}from"./i18next-7389dd8c.js";import{r as s}from"./vendor-f36d475e.js";const c=({services:u,onServiceSelect:m,className:x,isIcon:d=!1,selectedService:n,setSelectedService:h})=>{const[a,l]=s.useState(!1),[w,g]=s.useState(!1),r=s.useRef(null),j=t=>{h(t),l(!1),m&&m(t)},f=t=>{r.current&&!r.current.contains(t.target)&&l(!1)};return s.useEffect(()=>(document.addEventListener("mousedown",f),()=>{document.removeEventListener("mousedown",f)}),[]),s.useEffect(()=>{if(a){const t=r.current.getBoundingClientRect(),i=window.innerHeight-t.bottom,b=t.top;g(i<200&&b>i)}},[a]),console.log(" selectedService >> ",n),o.jsxs("div",{id:"drop-down",className:`relative w-full rounded-2xl bg-white ${x}`,ref:r,children:[o.jsxs("div",{className:"flex h-12 cursor-pointer items-center justify-between gap-2 px-4",onClick:()=>l(!a),children:[o.jsx("div",{className:"flex flex-1 items-center justify-between font-['Poppins'] text-base font-normal text-black",children:o.jsxs("div",{className:"flex",children:[(n==null?void 0:n.icon)&&o.jsx("span",{className:" mr-3 ",children:n==null?void 0:n.icon}),p(`provider.setting.${n.tran}`)]})}),o.jsx("div",{className:"h-5 w-5",children:o.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:o.jsx("path",{d:"M16.6 7.5L10.7 13.4C10.3 13.8 9.7 13.8 9.3 13.4L3.4 7.5",stroke:"#8181A4",strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),a&&o.jsx("div",{className:`absolute z-[9999999] mt-1 w-full overflow-hidden rounded-2xl bg-white shadow-lg ${w?"bottom-full mb-1":"top-full mt-1"}`,children:u.map((t,i)=>o.jsx("div",{className:`flex h-12 cursor-pointer items-center justify-between px-4 hover:bg-gray-100 ${(n==null?void 0:n.name)==t.name?"bg-blue-100":""}`,onClick:()=>j(t),children:o.jsxs("div",{className:"flex font-['Poppins'] text-base font-normal text-black",children:[(n==null?void 0:n.icon)&&d&&o.jsx("span",{className:" mr-3 ",children:n==null?void 0:n.icon}),p(`provider.setting.${t.tran}`)]})},i))})]})};export{c as default};
