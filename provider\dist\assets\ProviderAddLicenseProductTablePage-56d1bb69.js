import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{R as i,d as T}from"./vendor-f36d475e.js";import{u as B}from"./react-hook-form-ff037c98.js";import{o as C}from"./yup-afe5cf51.js";import{c as M,a as h}from"./yup-2f6e2476.js";import{M as R,G as q,A as D,n as G,s as I,t as v}from"./index-cf5e6bc7.js";import"./react-quill-3f3a006b.js";/* empty css                   */import"./InteractiveButton-303096ac.js";import"./index-bec80226.js";import{B as O,a as V}from"./index-895fa99b.js";import $ from"./DropDownSelection-d1fecb01.js";import{M as z}from"./index-bf8d79cc.js";import{S as F}from"./index-65bc3378.js";import{u as H}from"./react-i18next-1e3e6bc5.js";import{L as K}from"./@mantine/core-ee88fb98.js";import"./@hookform/resolvers-eb417cd0.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./@craftjs/core-01ce56b9.js";import"./MoonLoader-4d8718ee.js";import"./@emotion/serialize-460cad7f.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-47271980.js";let d=new R;const Ee=({setSidebar:U})=>{const{dispatch:m}=i.useContext(q),j=M({product_id:h().required(),price:h().required()}).required(),{dispatch:p}=i.useContext(D),[g,l]=i.useState(!1),[b,S]=i.useState([]),[N,u]=i.useState({name:"Select"}),[_,n]=i.useState(!0);i.useState(!0);const{t:o}=H(),y=T(),{register:P,handleSubmit:w,setError:f,setValue:k,formState:{errors:a}}=B({resolver:C(j)}),L=async r=>{l(!0);try{const t=await d.callRawAPI("/v3/api/custom/chumpchange/provider/product/create",{ambulant_id:r.product_id,amount:Number(r.price)},"POST");if(!t.error)I(m,o("toast.added")),y("/provider/profile");else if(t.validation){const c=Object.keys(t.validation);for(let s=0;s<c.length;s++){const x=c[s];f(x,{type:"manual",message:t.validation[x]})}}l(!1)}catch(t){l(!1),console.log("Error",t),f("product_id",{type:"manual",message:t.message}),v(p,t.message)}},E=r=>{u(r),k("product_id",r.id)},A=async()=>{var r;try{n(!0),d.setTable("ambulant");const t=await d.callRestAPI({filter:['status,eq,"active"']},"GETALL");if(console.log("services",t),!t.error){let c=[{name:"Select"}];(r=t==null?void 0:t.list)==null||r.map(s=>{c.push({name:s==null?void 0:s.name,price:s==null?void 0:s.price,id:s.id})}),S(c),n(!1)}}catch(t){n(!1),console.log("error",t),v(p,t.message)}};return i.useEffect(()=>{m({type:"SETPATH",payload:{path:"license_product"}}),A()},[]),e.jsxs("div",{className:"p-5",children:[g&&e.jsx(z,{showCloseButton:!1,children:e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsxs("div",{className:"text-lg font-semibold",children:[o("loading.uploading"),"..."]}),e.jsx("div",{className:"mt-12",children:e.jsx(K,{})})]})}),e.jsxs("div",{className:"relative flex w-full items-center justify-center ",children:[e.jsx("div",{className:" absolute left-0 top-0 ",children:e.jsx(O,{})}),e.jsx("div",{className:"mt-3 text-center font-['Poppins'] text-lg font-medium text-black",children:o("provider.add_product.title")})]}),((a==null?void 0:a.product_id)||(a==null?void 0:a.price))&&e.jsx("div",{className:"mt-10 flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:e.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium capitalize text-[#f95050]",children:o("provider.add_product.missing")})}),_?e.jsx("div",{className:"flex min-h-screen w-full items-center justify-center",children:e.jsx(F,{})}):e.jsxs("form",{className:" w-full ",onSubmit:w(L),children:[e.jsx("div",{className:"mt-[50px] font-['Poppins'] text-[22px] font-medium text-black",children:o("provider.add_product.product_name")}),e.jsxs("div",{className:"mt-[27px] w-full ",children:[e.jsx("div",{className:"mb-1 px-2 font-['Poppins'] text-sm font-medium text-[#8080a3] ",children:o("provider.add_product.s_product")}),e.jsx($,{services:b,onServiceSelect:E,selectedService:N,setSelectedService:u})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("div",{className:" px-2 font-['Poppins'] text-[22px] font-bold tracking-wide text-black",children:o("provider.add_product.price")}),e.jsx("input",{type:"text",placeholder:o("provider.add_product.p_price"),...P("price",{onChange:r=>G(r,10)}),className:"relative mt-3 h-12 w-full rounded-2xl border-none bg-white text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "})]}),e.jsx("div",{className:"mt-[35px]",children:e.jsx(V,{type:"submit",children:o("buttons.save")})})]})]})};export{Ee as default};
