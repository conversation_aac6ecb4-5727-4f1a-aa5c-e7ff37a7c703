import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as g,b as Y,h as Z,r as c}from"./vendor-4f06b3f4.js";import{u as ee}from"./react-hook-form-f3d72793.js";import{o as se}from"./yup-2324a46a.js";import{c as ae,a as u}from"./yup-17027d7a.js";import{M as te,A as le,G as oe,t as q,T as J,s as B}from"./index-06b5b6dd.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./moment-a9aaa855.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";let d=new te;const Se=({activeId:x,setSidebar:h,isUpdate:G,setIsUpdate:U})=>{var S,C,P,F,A,O,$,D,I,T,R,V;const L=ae({first_name:u().required(),last_name:u().required(),email:u().email().required(),phone:u().required(),cedula_number:u(),password:u()}),{dispatch:j}=g.useContext(le),{dispatch:y}=g.useContext(oe);Y(),Z();const[M,K]=c.useState(""),[m,z]=c.useState({}),[re,H]=c.useState(0),[N,w]=c.useState(!1),[Q,b]=c.useState(!1),[v,_]=c.useState(""),{register:n,handleSubmit:k,setError:f,setValue:i,formState:{errors:l}}=ee({resolver:se(L)}),W=[{key:"0",value:"Inactive"},{key:"2",value:"Suspend"},{key:"1",value:"Active"}],E=async s=>{w(!0);try{if(M!==s.email){const a=await d.updateEmailByAdmin(s.email,x);if(!a.error)B(y,"Email Updated",1e3);else if(a.validation){const r=Object.keys(a.validation);for(let o=0;o<r.length;o++){const p=r[o];f(p,{type:"manual",message:a.validation[p]})}}}if(s.password.length>0){const a=await d.updatePasswordByAdmin(s.password,x);if(a.error){if(a.validation){const r=Object.keys(a.validation);for(let o=0;o<r.length;o++){const p=r[o];f(p,{type:"manual",message:a.validation[p]})}}}}d.setTable("user");const t=await d.callRestAPI({id:x,email:s.email,status:s.status,photo:v,first_name:s.first_name,last_name:s.last_name,phone:s.phone,cedula_number:s.cedula_number,admin_verify:s.admin_verify},"PUT");if(!t.error)B(y,"Updated",4e3),U(!G),h(!1);else if(t.validation){const a=Object.keys(t.validation);for(let r=0;r<a.length;r++){const o=a[r];f(o,{type:"manual",message:t.validation[o]})}}}catch(t){console.log("Error",t),f("email",{type:"manual",message:t.message}),q(j,t.message)}w(!1)};g.useEffect(()=>{(async function(){try{d.setTable("user");const s=await d.callRestAPI({id:x},"GET");s.error||(i("first_name",s.model.first_name),i("last_name",s.model.last_name),i("phone",s.model.phone),i("email",s.model.email),i("role",s.model.role),i("status",s.model.status),i("cedula_number",s.model.cedula_number),i("admin_verify",s.model.admin_verify),K(s.model.email),H(s.model.id),z(s.model),_(s.model.photo))}catch(s){console.log("Error",s),q(j,s.message)}})()},[x]);const X=async s=>{try{b(!0);const t=new FormData;t.append("file",s.target.files[0]);const a=await d.uploadImage(t);console.log("result >>",a),_(a.url),b(!1)}catch(t){b(!1),console.log("error >>",t)}};return e.jsxs("div",{className:"mx-auto rounded",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[e.jsx("div",{className:"flex items-center gap-3",children:e.jsx("span",{className:"text-lg font-semibold",children:"Edit Provider"})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 shadow-sm hover:bg-[#f4f4f4]",onClick:()=>h(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center rounded-md bg-[#4F46E5] px-3 py-2 text-white shadow-sm",onClick:async()=>{await k(E)(),h(!1)},disabled:N,children:N?"Saving...":"Save"})]})]}),e.jsxs("form",{className:" w-full p-4 text-left",onSubmit:k(E),children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"first_name",children:"First Name"}),e.jsx("input",{type:"text",...n("first_name"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(S=l.first_name)!=null&&S.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(C=l.first_name)==null?void 0:C.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"last_name",children:"Last Name"}),e.jsx("input",{type:"text",...n("last_name"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(P=l.last_name)!=null&&P.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(F=l.last_name)==null?void 0:F.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"phone",children:"Phone"}),e.jsx("input",{type:"text",...n("phone",{onChange:J}),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(A=l.phone)!=null&&A.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(O=l.phone)==null?void 0:O.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"email",children:"Email"}),e.jsx("input",{type:"email",...n("email"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${($=l.email)!=null&&$.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(D=l.email)==null?void 0:D.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"cedula_number",children:"Cedula#"}),e.jsx("input",{type:"text",...n("cedula_number",{onChange:J}),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(I=l.cedula_number)!=null&&I.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(T=l.cedula_number)==null?void 0:T.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Status"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...n("status"),children:W.map(s=>e.jsx("option",{name:"status",value:s.key,children:s.value},s.key))})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"password",children:"Password"}),e.jsx("input",{type:"password",placeholder:"******************",autoComplete:"new-password",...n("password"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(R=l.password)!=null&&R.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(V=l.password)==null?void 0:V.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Verification"}),e.jsxs("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...n("admin_verify"),children:[e.jsx("option",{value:"",children:"Select"}),e.jsx("option",{value:"1",children:"Verified"}),e.jsx("option",{value:"0",children:"Non-verified"})]})]}),e.jsxs("div",{className:"",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Document"}),e.jsx("div",{className:" flex flex-wrap gap-1 ",children:m!=null&&m.cedula_image_link?e.jsxs(e.Fragment,{children:[JSON.parse(m.cedula_image_link).front&&e.jsx("img",{src:JSON.parse(m.cedula_image_link).front,alt:"Front Document",className:"mb-2 h-auto w-[150px]"}),JSON.parse(m.cedula_image_link).back&&e.jsx("img",{src:JSON.parse(m.cedula_image_link).back,alt:"Back Document",className:"h-auto w-[150px]"})]}):e.jsx("p",{children:"No document available"})})]}),e.jsxs("div",{className:" mt-4 ",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Profile Pic"}),e.jsxs("div",{className:" flex flex-col flex-wrap gap-1 ",children:[e.jsx("img",{src:v,alt:"Profile",className:" h-[150px] w-[150px] border object-cover "}),Q?e.jsx(e.Fragment,{children:e.jsx("div",{className:"font-['Poppins'] text-base font-medium leading-tight",children:"Loading..."})}):e.jsx("div",{className:"",children:e.jsxs("button",{className:" relative mt-3 inline-block overflow-hidden ",children:[e.jsx("div",{className:"font-['Poppins'] text-base font-medium text-[#4fa7f9]",children:"Change Image"}),e.jsx("input",{type:"file",onChange:X,className:" absolute left-0 top-[-150%] h-[250%] w-full cursor-pointer border-none outline-none ring-0 focus:border-none focus:outline-none focus:ring-0 "})]})})]})]})]})]})};export{Se as default};
