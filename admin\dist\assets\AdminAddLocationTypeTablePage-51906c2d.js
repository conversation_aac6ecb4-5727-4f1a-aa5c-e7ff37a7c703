import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as o,b as v}from"./vendor-4f06b3f4.js";import{u as A}from"./react-hook-form-f3d72793.js";import{o as N}from"./yup-2324a46a.js";import{c as j,a as u}from"./yup-17027d7a.js";import{M as w,G as E,A as k,s as I,t as R}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{M as c}from"./MkdInput-ff3aa862.js";import{I as T}from"./InteractiveButton-8f7d74ee.js";import"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./MoonLoader-16bed42a.js";let f=new w;const te=({setSidebar:x})=>{const{dispatch:r}=o.useContext(E),h=j({status:u().required("Status is required"),name:u().required("Name is required")}).required(),{dispatch:b}=o.useContext(k),[i,a]=o.useState(!1);v();const{register:m,handleSubmit:y,setError:g,formState:{errors:n}}=A({resolver:N(h)}),S=async l=>{a(!0);try{f.setTable("nearby_type");const e=await f.callRestAPI({name:l.name,status:l.status},"POST");if(!e.error)I(r,"Added"),x(!1),r({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(e.validation){const d=Object.keys(e.validation);for(let s=0;s<d.length;s++){const p=d[s];g(p,{type:"manual",message:e.validation[p]})}}a(!1)}catch(e){a(!1),console.log("Error",e),R(b,e.message)}};return t.jsxs("div",{className:"mx-auto rounded p-5 shadow-md",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Add Location Type"}),t.jsxs("form",{className:"w-full max-w-lg",onSubmit:y(S),children:[t.jsx(c,{type:"text",page:"add",name:"name",errors:n,label:"Name",placeholder:"Name",register:m,className:""}),t.jsx(c,{type:"dropdown",page:"add",name:"status",errors:n,label:"Status",placeholder:"Select Status",register:m,options:[{value:"inactive",name:"Inactive"},{value:"active",name:"Active"}],className:""}),t.jsx(T,{type:"submit",loading:i,disabled:i,className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",children:"Submit"})]})]})};export{te as default};
