import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as s,b as T,r as n,h as C}from"./vendor-4f06b3f4.js";import{u as D}from"./react-hook-form-f3d72793.js";import{o as k}from"./yup-2324a46a.js";import{c as L,a as U}from"./yup-17027d7a.js";import{M,A as F,G,t as $,s as h}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as q}from"./MkdInput-ff3aa862.js";import{I as B}from"./InteractiveButton-8f7d74ee.js";import{S as H}from"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";let p=new M;const O={0:"Inactive",1:"Active",2:"Suspend"},ve=i=>{const{dispatch:x}=s.useContext(F),f=L({status:U().oneOf(["created","rejected","approved"]).required()}).required(),{dispatch:d}=s.useContext(G);s.useState({});const[g,l]=s.useState(!1),[j,m]=s.useState(!1),[r,S]=s.useState({}),[u,b]=s.useState({});T();const[K,v]=s.useState(0);n.useState(0);const[V,y]=n.useState(0),[z,I]=n.useState(0),[N,_]=n.useState(""),{register:w,handleSubmit:E,setError:A,setValue:c,formState:{errors:R}}=D({resolver:k(f)}),o=C();n.useEffect(function(){(async function(){try{m(!0),p.setTable("user_recharge");const t=await p.callRestAPI({id:i.activeId?i.activeId:Number(o==null?void 0:o.id),join:"user,recharge"},"GET");t.error||(S(t.model.user),b(t.model.recharge),c("user_id",t.model.user_id),c("recharge_id",t.model.recharge_id),c("amount",t.model.amount),c("status",t.model.status),v(t.model.user_id),y(t.model.recharge_id),I(t.model.amount),_(t.model.status),m(!1))}catch(t){m(!1),console.log("error",t),$(x,t.message)}})()},[]);const P=async t=>{if(l(!0),N=="approved"){h(d,"Already approved"),l(!1);return}try{const a=await p.callRawAPI(`/v3/api/custom/chumpchange/admin/provider-recharge/update/${i.activeId?i.activeId:Number(o==null?void 0:o.id)}`,{status:t.status},"POST");a!=null&&a.error||(h(d,"Updated"),d({type:"REFRESH_DATA",payload:{refreshData:!0}}),i.setSidebar(!1)),l(!1)}catch(a){l(!1),console.log("Error",a),h(d,a.message,4e3,"error"),A("user_id",{type:"manual",message:a.message})}};return s.useEffect(()=>{d({type:"SETPATH",payload:{path:"user_recharge"}})},[]),e.jsxs("div",{className:" mx-auto rounded   p-5 shadow-md",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Edit User Recharge"}),j?e.jsx(H,{}):e.jsxs("form",{className:" mt-6 w-full max-w-lg ",onSubmit:E(P),children:[e.jsxs("div",{className:"mb-6 w-full rounded-lg border bg-gray-50 p-4 text-left shadow-md ",children:[e.jsx("h5",{className:"text-lg font-semibold text-gray-800",children:"User info"}),e.jsxs("ul",{className:"list-inside list-disc text-gray-700",children:[e.jsxs("li",{children:[e.jsx("strong",{children:"ID:"})," ",r.id]}),e.jsxs("li",{children:[e.jsx("strong",{children:"First Name:"})," ",r.first_name]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Last Name:"})," ",r.last_name]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Email:"})," ",r.email]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Phone:"})," ",r.phone]}),e.jsxs("li",{children:[e.jsx("strong",{children:"City:"})," ",r.operating_city]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Status:"})," ",O[r.status]]})]})]}),e.jsxs("div",{className:"mb-6 w-full rounded-lg border bg-gray-50 p-4 text-left shadow-md ",children:[e.jsx("h5",{className:"text-lg font-semibold text-gray-800",children:"Recharge Info"}),e.jsxs("ul",{className:"list-inside list-disc text-gray-700",children:[e.jsxs("li",{children:[e.jsx("strong",{children:"ID:"})," ",u.id]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Name:"})," ",u.name]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Amount:"})," ",u.amount]})]})]}),e.jsx(q,{type:"dropdown",page:"edit",name:"status",errors:R,label:"Status",placeholder:"Select Status",register:w,options:[{value:"created",name:"Created"},{value:"rejected",name:"Rejected"},{value:"approved",name:"Approved"}],className:""}),e.jsx(B,{type:"submit",className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",loading:g,disable:g,children:"Submit"})]})]})};export{ve as default};
