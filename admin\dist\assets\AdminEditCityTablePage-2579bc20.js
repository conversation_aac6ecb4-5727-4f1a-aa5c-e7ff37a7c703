import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as o,b as w,h as L,r as q}from"./vendor-4f06b3f4.js";import{u as A}from"./react-hook-form-f3d72793.js";import{o as T}from"./yup-2324a46a.js";import{c as k,a as d}from"./yup-17027d7a.js";import{M as R,A as C,G as P,t as D,s as M}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{M as m}from"./MkdInput-ff3aa862.js";import{I as G}from"./InteractiveButton-8f7d74ee.js";import{S as B}from"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./MoonLoader-16bed42a.js";let u=new R;const de=i=>{const{dispatch:v}=o.useContext(C),S=k({name:d().required("Name is required"),latitude:d().required("Latitude is required"),longtitude:d().required("Longitude is required"),status:d().required("Status is required")}).required(),{dispatch:c}=o.useContext(P),[x,p]=o.useState(!1),[E,f]=o.useState(!1),N=w(),{register:r,handleSubmit:j,setError:h,setValue:l,formState:{errors:n}}=A({resolver:T(S)}),s=L();q.useEffect(function(){(async function(){try{f(!0),u.setTable("city");const e=await u.callRestAPI({id:i.activeId?i.activeId:Number(s==null?void 0:s.id)},"GET");e.error||(l("name",e.model.name),l("latitude",e.model.lattitude),l("longtitude",e.model.longitude),l("status",e.model.status),f(!1))}catch(e){f(!1),console.log("error",e),D(v,e.message)}})()},[]);const I=async e=>{p(!0);try{u.setTable("city");const a=await u.callRestAPI({id:i.activeId?i.activeId:Number(s==null?void 0:s.id),name:e.name,lattitude:e.latitude,longitude:e.longtitude,status:e.status},"PUT");if(!a.error)M(c,"Updated"),N("/admin/city"),c({type:"REFRESH_DATA",payload:{refreshData:!0}}),i.setSidebar(!1);else if(a.validation){const y=Object.keys(a.validation);for(let g=0;g<y.length;g++){const b=y[g];h(b,{type:"manual",message:a.validation[b]})}}p(!1)}catch(a){p(!1),console.log("Error",a),h("name",{type:"manual",message:a.message})}};return o.useEffect(()=>{c({type:"SETPATH",payload:{path:"city"}})},[]),t.jsxs("div",{className:"mx-auto rounded p-5 shadow-md",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit City"}),E?t.jsx(B,{}):t.jsxs("form",{className:"w-full max-w-lg",onSubmit:j(I),children:[t.jsx(m,{type:"text",page:"edit",name:"name",errors:n,label:"Name",placeholder:"Name",register:r,className:""}),t.jsx(m,{type:"text",page:"edit",name:"latitude",errors:n,label:"Latitude",placeholder:"Latitude",register:r,className:""}),t.jsx(m,{type:"text",page:"edit",name:"longtitude",errors:n,label:"Longitude",placeholder:"Longitude",register:r,className:""}),t.jsx(m,{type:"dropdown",page:"edit",name:"status",errors:n,label:"Status",placeholder:"Status",register:r,className:"",options:[{name:"Active",value:"active"},{name:"Inactive",value:"inactive"}]}),t.jsx(G,{type:"submit",className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",loading:x,disable:x,children:"Submit"})]})]})};export{de as default};
