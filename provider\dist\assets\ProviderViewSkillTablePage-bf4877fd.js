import{j as s}from"./@react-google-maps/api-afbf18d5.js";import{R as a,h as x}from"./vendor-f36d475e.js";import"./yup-2f6e2476.js";import{M as o,G as r,t as f}from"./index-cf5e6bc7.js";import{S as h}from"./index-bec80226.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./react-i18next-1e3e6bc5.js";let m=new o;const I=()=>{a.useContext(r);const{dispatch:c}=a.useContext(r),[e,d]=a.useState({}),[n,l]=a.useState(!0),t=x();return a.useEffect(function(){(async function(){try{l(!0),m.setTable("skill");const i=await m.callRestAPI({id:Number(t==null?void 0:t.id),join:""},"GET");i.error||(d(i.model),l(!1))}catch(i){l(!1),console.log("error",i),f(c,i.message)}})()},[]),s.jsx("div",{className:" shadow-md rounded  mx-auto p-5",children:n?s.jsx(h,{}):s.jsxs(s.Fragment,{children:[s.jsx("h4",{className:"text-2xl font-medium",children:"View Skill"}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Skill Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.skill_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Price"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.price})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Avaibility Start"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.avaibility_start})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Avaibility End"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.avaibility_end})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Description"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.description})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Status"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.status})]})})]})})};export{I as default};
