import{j as e}from"./@react-google-maps/api-ee55a349.js";import{B as j,a as v}from"./index-d54cffea.js";import{A as C,a as N}from"./index-dd254604.js";import{r as s,u as w,d as S}from"./vendor-b16525a8.js";import{u as V}from"./react-hook-form-b6ed2679.js";import{o as k}from"./yup-3990215a.js";import{c as y,a as P}from"./yup-f828ae80.js";import"./index-09a1718e.js";import{t as a}from"./i18next-7389dd8c.js";import"./qr-scanner-cf010ec4.js";import"./@hookform/resolvers-3e831b4a.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";const te=()=>{var l;const[i,L]=s.useState(!1),[d,m]=s.useState(!0),[_,c]=s.useState(0),[B,u]=s.useState(0),o=w(),p=S(),x=y({address:P().required()}).required(),{register:f,handleSubmit:h,setError:H,setValue:g,formState:{errors:t,isValid:n}}=V({resolver:k(x),mode:"onChange"});s.useEffect(()=>{const r=new URLSearchParams(o.search);g("address",r.get("address")||""),c(Number(r.get("lat"))||0),u(Number(r.get("lng"))||0)},[o.search]);const b=async r=>{p("/user/onboarding")};return s.useEffect(()=>{m(n)},[n]),console.log(t,!d),e.jsxs(C,{children:[e.jsx(j,{}),e.jsxs("form",{onSubmit:h(b),children:[e.jsx(N,{title:"Sign up",className:" -mt-6 "}),e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx("div",{className:" flex h-[70px] flex-col items-center justify-start",children:t!=null&&t.address?e.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:e.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:(l=t==null?void 0:t.address)==null?void 0:l.message})}):e.jsx("div",{className:"font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:a("user.pick_address.delivery")})}),e.jsx("input",{type:"text",placeholder:a("user.pick_address.enter_address"),...f("address"),className:"relative mt-3 h-12 w-full rounded-2xl border-none bg-white text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "}),e.jsx("div",{className:"",children:e.jsxs("div",{className:"flex h-[55px] w-full items-center gap-3 rounded-xl border border-[#e1e2e3] bg-[#f7f7f7] p-3 ",children:[e.jsx("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M16 10.1818C12.8 10.1818 10.1818 12.8 10.1818 16C10.1818 19.2 12.8 21.8182 16 21.8182C19.2 21.8182 21.8182 19.2 21.8182 16C21.8182 12.8 19.2 10.1818 16 10.1818ZM28.9455 14.5455C28.2182 8.43636 23.4182 3.63636 17.4545 3.05455V0H14.5455V3.05455C8.43636 3.63636 3.63636 8.43636 3.05455 14.5455H0V17.4545H3.05455C3.78182 23.5636 8.58182 28.3636 14.5455 28.9455V32H17.4545V28.9455C23.5636 28.2182 28.3636 23.4182 28.9455 17.4545H32V14.5455H28.9455ZM16 26.1818C10.3273 26.1818 5.81818 21.6727 5.81818 16C5.81818 10.3273 10.3273 5.81818 16 5.81818C21.6727 5.81818 26.1818 10.3273 26.1818 16C26.1818 21.6727 21.6727 26.1818 16 26.1818Z",fill:"#56CCF2"})}),e.jsx("div",{className:"font-['Poppins'] text-base font-light tracking-tight text-black",children:a("user.pick_address.select_location")})]})}),e.jsx("div",{className:" mt-[90px] ",children:e.jsx(v,{loading:i,disabled:i,type:"submit",children:a("user.pick_address.create_account")})})]})]})]})};export{te as default};
