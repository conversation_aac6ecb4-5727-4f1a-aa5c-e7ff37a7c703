import{j as t}from"./@react-google-maps/api-afbf18d5.js";import{B as k,a as C}from"./index-895fa99b.js";import{A as B,a as D}from"./index-55e4d382.js";import{M as L,G as O,s as m}from"./index-cf5e6bc7.js";import{r as i,R as T,u as A,h as M,d as E}from"./vendor-f36d475e.js";import{M as G}from"./index-bf8d79cc.js";import{S as K}from"./index-65bc3378.js";import{u as U}from"./react-i18next-1e3e6bc5.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";let x=new L;const ce=()=>{const[n,d]=i.useState(["","","",""]),a=i.useRef([]),[j,l]=i.useState(!1),[b,u]=i.useState(!1),[h,v]=i.useState(!1),{dispatch:f}=T.useContext(O),N=A();new URLSearchParams(N.search);const{t:r}=U(),{token:p}=M(),w=(s,e)=>{if(isNaN(s))return;const o=[...n];o[e]=s,d(o),s&&e<3?a.current[e+1].focus():!s&&e>0&&a.current[e-1].focus()},P=s=>{const e=s.clipboardData.getData("text").split("").filter(c=>!isNaN(c)),o=[...n];e.slice(0,4).forEach((c,g)=>{o[g]=c,a.current[g].value=c}),d(o),e.length>=4?a.current[3].focus():e.length===3?a.current[2].focus():e.length===2?a.current[1].focus():e.length===1&&a.current[0].focus()},_=(s,e)=>{s.key==="Backspace"&&!n[e]&&e>0&&a.current[e-1].focus()},y=E(),S=async s=>{try{u(!0),(await x.callRawAPI("/v3/api/custom/chumpchange/provider/signup/step2",{confirmationCode:n.join(""),phone:p},"POST")).error||y(`/provider/password/${p}`),u(!1)}catch(e){u(!1),console.log("error >> ",e),m(f,e==null?void 0:e.message,4e3,"error"),v(!0)}},R=async()=>{console.log("Resend OTP");try{l(!0);const s=await x.callRawAPI("/v3/api/custom/chumpchange/provider/resend-code",{phone:p},"POST");m(f,"Sent successfully"),l(!1)}catch(s){l(!1),console.log("error >> ",s),m(f,s.message,5e3,"error")}};return console.log("otp >> ",n.join("")),t.jsxs(B,{children:[j&&t.jsx(G,{showCloseButton:!1,children:t.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[t.jsxs("div",{className:"text-lg font-semibold",children:[r("loading.sending"),"..."]}),t.jsx("div",{className:"mt-12",children:t.jsx(K,{})})]})}),t.jsx(k,{}),t.jsx(D,{title:r("auth_pages.sign_up.title"),className:" -mt-6 "}),t.jsxs("div",{className:"flex flex-col gap-4",children:[t.jsx("div",{className:" flex h-[70px] flex-col items-center justify-start",children:h?t.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:t.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:r("auth_pages.sign_up.invalid_code")})}):t.jsx("div",{className:"font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:r("auth_pages.sign_up.sub_title_verify")})}),t.jsx("div",{className:"flex w-full items-center justify-between gap-2 ",children:n.map((s,e)=>t.jsx("input",{type:"text",maxLength:"1",value:s,onChange:o=>w(o.target.value,e),onPaste:P,onKeyDown:o=>_(o,e),ref:o=>a.current[e]=o,className:`h-[83px] w-[72px] rounded-2xl border-2  text-center font-['Poppins'] text-2xl  font-medium text-black outline-none focus:outline-none focus:ring-0 ${h?"border-[#ff0000]/5 bg-[#ff0000]/5 focus:border-[#ff0000]/5":"border-[#fff] bg-white focus:border-[#fff] "} `},e))}),t.jsxs("div",{className:" mt-0 flex gap-4 ",children:[t.jsx("p",{className:"text-center font-['Poppins'] text-sm font-medium leading-[18.20px] text-[#8080a3]",children:r("auth_pages.sign_up.get_code")}),t.jsx("p",{onClick:R,className:"text-center font-['Poppins'] text-sm font-medium leading-[18.20px] text-[#56ccf2]",children:r("auth_pages.sign_up.resend")})]}),t.jsx("div",{className:" mt-10 ",children:t.jsx(C,{loading:b,disabled:n.join("").length!==4,type:"submit",onClick:S,children:r("auth_pages.sign_up.next")})})]})]})};export{ce as default};
