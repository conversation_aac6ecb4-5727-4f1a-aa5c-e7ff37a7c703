import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as s,b as g,r as b}from"./vendor-4f06b3f4.js";import{M as w,A as v,G as j,a8 as E,a9 as D}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{L as r}from"./index-6416aa2c.js";import{M as n}from"./index-d97c616d.js";import{M as A}from"./index-68c82eaa.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";new w;const I=[{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Created",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"User Id",accessor:"user_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Provider Id",accessor:"provider_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Service",accessor:"service_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},notfilter:!0},{header:"Price Offered",accessor:"offer",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Job Date",accessor:"start_datetime",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Job location",accessor:"location_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},notfilter:!0},{header:"Image",accessor:"task_pic",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},notfilter:!0},{header:"Description",accessor:"description",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Provider Status",accessor:"provider_status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],H=()=>{s.useContext(v),s.useContext(j),g();const[p,a]=s.useState(!1),[o,t]=s.useState(!1),[m,f]=s.useState(),[x,u]=s.useState({}),h=b.useRef(null),[M,S]=s.useState([]),d=(i,l,c=[])=>{switch(i){case"add":a(l);break;case"edit":t(l),S(c),f(c[0]);break}};return e.jsxs(e.Fragment,{children:[e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"flex flex-wrap gap-4 bg-white px-14 shadow ",children:e.jsxs("div",{className:"mt-4 inline-flex h-[154px] w-[261px] flex-col items-center justify-center gap-6 rounded-lg border border-[#e4e6eb] bg-white p-4 shadow",children:[e.jsx("div",{className:" font-['Inter'] text-3xl font-semibold leading-[38px] text-[#0f1728]",children:x.total}),e.jsx("div",{className:" font-['Poppins'] text-base font-medium leading-normal text-[#0f1728]",children:"Total Job/task"})]})}),e.jsx("div",{className:"overflow-x-auto  rounded bg-white p-5 shadow",children:e.jsx(r,{children:e.jsx(A,{setResult:u,columns:I,tableRole:"admin",table:"task",actionId:"id",actions:{view:{show:!0,action:null,multiple:!1},edit:{show:!0,multiple:!1,action:i=>d("edit",!0,i)},delete:{show:!1,action:null,multiple:!1},select:{show:!0,action:null,multiple:!1},add:{show:!1,action:()=>d("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!0,action:null,multiple:!0}},actionPostion:"ontable",refreshRef:h,join:"service,location"})})})]}),e.jsx(r,{children:e.jsx(n,{isModalActive:p,closeModalFn:()=>a(!1),children:e.jsx(E,{setSidebar:a})})}),o&&e.jsx(r,{children:e.jsx(n,{isModalActive:o,closeModalFn:()=>t(!1),children:e.jsx(D,{activeId:m,setSidebar:t})})})]})};export{H as default};
