import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as s,b as k}from"./vendor-4f06b3f4.js";import{u as _}from"./react-hook-form-f3d72793.js";import{o as w}from"./yup-2324a46a.js";import{c as A,a as d}from"./yup-17027d7a.js";import{G as I,A as P,M as E,s as N,t as R}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as p}from"./MkdInput-ff3aa862.js";import{I as D}from"./InteractiveButton-8f7d74ee.js";import"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";const me=({setSidebar:b})=>{const{dispatch:n}=s.useContext(I),h=A({service_id:d(),provider_id:d(),price:d(),status:d()}).required(),{dispatch:g}=s.useContext(P),[f,T]=s.useState({}),[v,c]=s.useState(!1),S=k(),{register:o,handleSubmit:y,setError:x,setValue:C,formState:{errors:l}}=_({resolver:w(h)});s.useState([]);const j=async a=>{let u=new E;c(!0);try{for(let i in f){let r=new FormData;r.append("file",f[i].file);let m=await u.uploadImage(r);a[i]=m.url}u.setTable("provider_skill");const e=await u.callRestAPI({service_id:a.service_id,provider_id:a.provider_id,price:a.price,status:a.status},"POST");if(!e.error)N(n,"Added"),S("/admin/provider_skill"),b(!1),n({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(e.validation){const i=Object.keys(e.validation);for(let r=0;r<i.length;r++){const m=i[r];x(m,{type:"manual",message:e.validation[m]})}}c(!1)}catch(e){c(!1),console.log("Error",e),x("service_id",{type:"manual",message:e.message}),R(g,e.message)}};return s.useEffect(()=>{n({type:"SETPATH",payload:{path:"provider_skill"}})},[]),t.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Add Provider Skill"}),t.jsxs("form",{className:" w-full max-w-lg",onSubmit:y(j),children:[t.jsx(p,{type:"number",page:"add",name:"service_id",errors:l,label:"Service Id",placeholder:"Service Id",register:o,className:""}),t.jsx(p,{type:"number",page:"add",name:"provider_id",errors:l,label:"Provider Id",placeholder:"Provider Id",register:o,className:""}),t.jsx(p,{type:"text",page:"add",name:"price",errors:l,label:"Price",placeholder:"Price",register:o,className:""}),t.jsx(p,{type:"text",page:"add",name:"status",errors:l,label:"Status",placeholder:"Status",register:o,className:""}),t.jsx(D,{type:"submit",loading:v,disabled:v,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{me as default};
