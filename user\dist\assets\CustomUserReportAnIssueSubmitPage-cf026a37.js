import{j as e}from"./@react-google-maps/api-ee55a349.js";import{i as y,r as o,R as l,d as w}from"./vendor-b16525a8.js";import"./index-9ef1c042.js";import{M as P,A as C,G as L,f as A,t as f}from"./index-09a1718e.js";import{B as S,a as B}from"./index-d54cffea.js";import{C as R}from"./index-49471902.js";import{S as D}from"./index-5a645c18.js";import{M as H}from"./index-243c4859.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./react-i18next-4a61273e.js";const c=new P,ie=()=>{var x,u;const s=y(),{state:h,dispatch:d}=o.useContext(C);o.useContext(L);const[t,j]=o.useState(null),[b,r]=o.useState(null),[E,a]=l.useState(!0),[n,N]=l.useState(""),m=new URLSearchParams(window.location.search).get("title"),g=w(),k=async()=>{try{a(!0),c.setTable("task");const i=await c.callRestAPI({id:Number(s==null?void 0:s.id),join:"user,location,service"},"GET");i.error||(j(i.model),a(!1))}catch(i){a(!1),console.log("error",i),f(d,i.message)}},v=async i=>{try{a(!0),r(!0),(await c.callRawAPI("/v3/api/custom/chumpchange/user/issue/create",{task_id:Number(s==null?void 0:s.id),title:m,description:n,status:"active"},"POST")).error||g(`/user/view-job/${s==null?void 0:s.id}`),a(!1),r(!1)}catch(p){r(!1),a(!1),f(d,p.message)}};return l.useEffect(function(){(async function(){await k()})()},[]),e.jsxs("div",{className:"p-5",children:[b&&e.jsx(H,{showCloseButton:!1,children:e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsx("div",{className:"text-lg font-semibold",children:"Loading..."}),e.jsx("div",{className:"mt-12",children:e.jsx(D,{})})]})}),e.jsxs("div",{className:"relative flex w-full items-center justify-center ",children:[e.jsx("div",{className:" absolute left-0 top-0 ",children:e.jsx(S,{link:"/user/my-requests"})}),e.jsxs("div",{className:"",children:[e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:"My request"}),e.jsx("div",{className:"text-center font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:m})]})]}),e.jsx("div",{className:" mt-8 font-['Poppins'] text-sm font-medium text-black",children:"Request completed by:"}),e.jsx(R,{userData:h.userDetails,className:"!mt-4"}),e.jsxs("div",{className:" mt-9 ",children:[e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:"Helped with"}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:t!=null&&t.title?t==null?void 0:t.title:"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:"Date"}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:A(t==null?void 0:t.start_datetime)})]}),e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:"From location"}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:e.jsxs("button",{className:" text-[#4fa7f9] underline ",children:[" ",(x=t==null?void 0:t.location)!=null&&x.address?(u=t==null?void 0:t.location)==null?void 0:u.address:"N/A"]})})]}),e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:"Pay offered"}),e.jsxs("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:["DOP ",t!=null&&t.offer?t==null?void 0:t.offer:"N/A"]})]})]}),e.jsx("div",{className:"mt-10",children:e.jsxs("div",{className:"font-['Poppins'] text-sm font-light text-black",children:["We are sorry to hear things didn’t go",e.jsx("br",{}),"well, please tell us what went wrong:"]})}),e.jsxs("div",{className:"mt-[29px]",children:[e.jsx("div",{className:" mb-[7px] font-['Poppins'] text-base font-medium tracking-tight text-black",children:"Describe the Issue"}),e.jsxs("div",{className:"relative h-[104px] w-full overflow-hidden rounded-2xl bg-white",children:[e.jsx("span",{className:"absolute left-[14px] top-[14px] z-[99] h-5 w-5 ",children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M7.08366 15.8332H6.66699C3.33366 15.8332 1.66699 14.9998 1.66699 10.8332V6.6665C1.66699 3.33317 3.33366 1.6665 6.66699 1.6665H13.3337C16.667 1.6665 18.3337 3.33317 18.3337 6.6665V10.8332C18.3337 14.1665 16.667 15.8332 13.3337 15.8332H12.917C12.6587 15.8332 12.4087 15.9582 12.2503 16.1665L11.0003 17.8332C10.4503 18.5665 9.55033 18.5665 9.00033 17.8332L7.75033 16.1665C7.61699 15.9832 7.30866 15.8332 7.08366 15.8332Z",stroke:"#8181A4",strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M13.3301 9.16667H13.3375",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M9.99607 9.16667H10.0036",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M6.66209 9.16667H6.66957",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),e.jsx("textarea",{type:"text",name:"description",placeholder:"Be as detailed as possible",value:n,onChange:i=>N(i.target.value),className:" absolute left-0 top-0 h-full w-full border-none bg-white  pl-[40px] pt-[14px] text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "})]})]}),e.jsx("div",{className:"mt-10 flex flex-col gap-3",children:e.jsx(B,{onClick:v,disabled:!n,children:"SUBMIT"})})]})};export{ie as default};
