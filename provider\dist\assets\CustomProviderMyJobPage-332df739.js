import{j as o}from"./@react-google-maps/api-afbf18d5.js";import{h as r,r as t,R as m}from"./vendor-f36d475e.js";import"./index-5deedf4a.js";import{M as p,A as s,G as a}from"./index-cf5e6bc7.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./react-i18next-1e3e6bc5.js";new p;const G=()=>(r(),t.useContext(s),t.useContext(a),m.useEffect(()=>{},[]),o.jsx("div",{className:" mx-auto rounded  p-5 shadow-md"}));export{G as default};
