import{j as e}from"./@react-google-maps/api-ee55a349.js";import{R as o,d as l}from"./vendor-b16525a8.js";import{L as i}from"./index-5a645c18.js";import{B as r,a as c}from"./index-d54cffea.js";import{t}from"./i18next-7389dd8c.js";import"./qr-scanner-cf010ec4.js";const g=()=>{const[s,n]=o.useState(!0),a=l();return console.log(s),o.useEffect(()=>{setTimeout(()=>{n(!1)},5e3)},[]),e.jsx(e.Fragment,{children:s?e.jsx(i,{}):e.jsxs("div",{className:"p-5",children:[e.jsxs("div",{className:"relative flex w-full items-center justify-between ",children:[e.jsx("div",{className:" ",children:e.jsx(r,{})}),e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:t("not_found.title")}),e.jsx("div",{className:" "})]}),e.jsxs("div",{className:"flex h-screen w-full flex-col items-center justify-center bg-gradient-to-br from-[#FCF3F9] to-[#EAF8FB] text-gray-700",children:[e.jsx("h1",{className:"mb-4 text-2xl",children:t("not_found.sub_title")}),e.jsx("p",{className:"mb-8 text-xl",children:t("not_found.decs")}),e.jsx(c,{onClick:()=>a("/user/login"),children:t("not_found.btn")})]})]})})};export{g as default};
