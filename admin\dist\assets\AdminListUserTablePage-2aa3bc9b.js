import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as s,b,r as w}from"./vendor-4f06b3f4.js";import{M as E,A as j,G as v,a as A,E as D}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{L as o}from"./index-6416aa2c.js";import{M as c}from"./index-d97c616d.js";import{M}from"./index-68c82eaa.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";new E;const I=[{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"First Name",accessor:"first_name",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Last Name",accessor:"last_name",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Email",accessor:"email",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Phone",accessor:"phone",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Photo",accessor:"photo",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},notfilter:!0},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!0,mappings:{0:"Inactive",1:"Active",2:"Suspend"}},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Update At",accessor:"update_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],Q=()=>{s.useContext(j);const{dispatch:p}=s.useContext(v);b();const[m,t]=s.useState(!1),[r,a]=s.useState(!1),[f,u]=s.useState(),h=w.useRef(null),[x,S]=s.useState({}),[N,g]=s.useState([]),l=(i,d,n=[])=>{switch(i){case"add":t(d);break;case"edit":a(d),g(n),u(n[0]);break}};return s.useEffect(()=>{p({type:"SETPATH",payload:{path:"user"}})},[]),e.jsxs(e.Fragment,{children:[e.jsxs(e.Fragment,{children:[e.jsx("div",{className:" px-14 ",children:e.jsxs("div",{className:"mt-4 inline-flex h-[154px] w-[261px] flex-col items-center justify-center gap-6 rounded-lg border border-[#e4e6eb] bg-white p-4 shadow",children:[e.jsx("div",{className:" font-['Inter'] text-3xl font-semibold leading-[38px] text-[#0f1728]",children:x.total}),e.jsx("div",{className:" font-['Poppins'] text-base font-medium leading-normal text-[#0f1728]",children:"Total Govt."})]})}),e.jsx("div",{className:"overflow-x-auto  rounded bg-white p-5 shadow",children:e.jsx(o,{children:e.jsx(M,{setResult:S,columns:I,tableRole:"admin",table:"user",actionId:"id",actions:{view:{show:!0,action:null,multiple:!1},edit:{show:!0,multiple:!1,action:i=>l("edit",!0,i)},delete:{show:!0,action:null,multiple:!1},select:{show:!0,action:null,multiple:!1},add:{show:!0,action:()=>l("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!1,action:null,multiple:!0}},actionPostion:"ontable",refreshRef:h,defaultFilter:['role,eq,"government"']})})})]}),e.jsx(o,{children:e.jsx(c,{isModalActive:m,closeModalFn:()=>t(!1),children:e.jsx(A,{setSidebar:t})})}),r&&e.jsx(o,{children:e.jsx(c,{isModalActive:r,closeModalFn:()=>a(!1),children:e.jsx(D,{activeId:f,setSidebar:a})})})]})};export{Q as default};
