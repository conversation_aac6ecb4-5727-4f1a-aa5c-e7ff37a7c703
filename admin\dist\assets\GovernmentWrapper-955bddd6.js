import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{r}from"./vendor-4f06b3f4.js";import{_ as l}from"./qr-scanner-cf010ec4.js";import{T as o}from"./index-8a937bee.js";import"./index-250f6b3d.js";const a=r.lazy(()=>l(()=>import("./GovernmentHeader-59b389c1.js"),["assets/GovernmentHeader-59b389c1.js","assets/@react-google-maps/api-ac2f9d6f.js","assets/vendor-4f06b3f4.js","assets/index.esm-e652e625.js","assets/react-icons-e5379072.js","assets/index-06b5b6dd.js","assets/react-confirm-alert-525c3702.js","assets/moment-a9aaa855.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-d39d893a.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-d923fcf0.js","assets/@fortawesome/react-fontawesome-6b681b2b.js","assets/@fortawesome/fontawesome-svg-core-4fa3e289.js","assets/@fortawesome/free-solid-svg-icons-0a9c4907.js","assets/@fortawesome/free-regular-svg-icons-0a88e957.js","assets/@fortawesome/free-brands-svg-icons-fae0dcac.js","assets/i18next-ec2f391c.js","assets/react-i18next-c78f8e57.js","assets/index-6c569bc9.css","assets/index.esm-8e8a99ba.js","assets/admin-ad25220a.js"])),t=({children:s})=>e.jsx("div",{id:"government_wrapper",className:"flex w-full max-w-full flex-col bg-white",children:e.jsxs("div",{className:"flex min-h-screen w-full max-w-full ",children:[e.jsx(a,{}),e.jsxs("div",{className:"mb-20 w-full overflow-hidden",children:[e.jsx(o,{}),e.jsx(r.Suspense,{fallback:e.jsx("div",{className:"flex h-screen w-full items-center justify-center"}),children:e.jsx("div",{className:"w-full overflow-y-auto overflow-x-hidden",children:s})})]})]})}),d=r.memo(t);export{d as default};
