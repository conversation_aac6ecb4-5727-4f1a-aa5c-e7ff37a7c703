import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as s,b as R}from"./vendor-4f06b3f4.js";import{u as _}from"./react-hook-form-f3d72793.js";import{o as w}from"./yup-2324a46a.js";import{c as I,a as n}from"./yup-17027d7a.js";import{G as E,A as N,M as v,s as k,t as D}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as d}from"./MkdInput-ff3aa862.js";import{I as T}from"./InteractiveButton-8f7d74ee.js";import"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";const le=({setSidebar:b})=>{const{dispatch:u}=s.useContext(E),x=I({user_id:n(),recharge_id:n(),amount:n(),status:n()}).required(),{dispatch:y}=s.useContext(N),[h,C]=s.useState({}),[f,p]=s.useState(!1),S=R(),{register:i,handleSubmit:j,setError:g,setValue:F,formState:{errors:m}}=_({resolver:w(x)});s.useState([]);const A=async r=>{let c=new v;p(!0);try{for(let o in h){let a=new FormData;a.append("file",h[o].file);let l=await c.uploadImage(a);r[o]=l.url}c.setTable("user_recharge");const e=await c.callRestAPI({user_id:r.user_id,recharge_id:r.recharge_id,amount:r.amount,status:r.status},"POST");if(!e.error)k(u,"Added"),S("/admin/user_recharge"),b(!1),u({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(e.validation){const o=Object.keys(e.validation);for(let a=0;a<o.length;a++){const l=o[a];g(l,{type:"manual",message:e.validation[l]})}}p(!1)}catch(e){p(!1),console.log("Error",e),g("user_id",{type:"manual",message:e.message}),D(y,e.message)}};return s.useEffect(()=>{u({type:"SETPATH",payload:{path:"user_recharge"}})},[]),t.jsxs("div",{className:" mx-auto rounded  p-5 shadow-md",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Add User Recharge"}),t.jsxs("form",{className:" w-full max-w-lg",onSubmit:j(A),children:[t.jsx(d,{type:"number",page:"add",name:"user_id",errors:m,label:"User Id",placeholder:"User Id",register:i,className:""}),t.jsx(d,{type:"number",page:"add",name:"recharge_id",errors:m,label:"Recharge Id",placeholder:"Recharge Id",register:i,className:""}),t.jsx(d,{type:"number",page:"add",name:"amount",errors:m,label:"Amount",placeholder:"Amount",register:i,className:""}),t.jsx(d,{type:"text",page:"add",name:"status",errors:m,label:"Status",placeholder:"Status",register:i,className:""}),t.jsx(T,{type:"submit",loading:f,disabled:f,className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",children:"Submit"})]})]})};export{le as default};
