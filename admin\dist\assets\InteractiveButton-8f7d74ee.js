import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{r as e}from"./vendor-4f06b3f4.js";import{_ as x}from"./MoonLoader-16bed42a.js";const j=e.memo(({loading:s=!1,disabled:r,children:o,type:f="button",className:a,loaderclasses:n,onClick:i,color:m="#ffffff"})=>{const c={borderColor:"#ffffff"},d=e.useId();return t.jsx("button",{type:f,disabled:r,className:`flex items-center justify-center gap-5 ${a}`,onClick:i,children:t.jsxs(t.Fragment,{children:[t.jsx(x,{color:m,loading:s,cssOverride:c,size:20,className:n,"data-testid":d}),t.jsx("span",{children:o})]})})});export{j as I};
