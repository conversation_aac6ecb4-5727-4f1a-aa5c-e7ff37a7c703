import React, { useState } from "react";
import classes from "./AddButton.module.css";

const AddButton = ({
  onClick,
  children = "Add New",
  showPlus = true,
  className,
  showChildren = true,
}) => {
  const [animate, setAnimate] = useState(false);

  const onClickHandle = () => {
    if (onClick) {
      onClick();
    }
    setAnimate(true);
  };

  return (
    <button
      onAnimationEnd={() => setAnimate(false)}
      onClick={onClickHandle}
      className={`${animate && "animate-wiggle"} ${
        classes.button
      } relative flex h-10 w-fit min-w-fit items-center justify-center overflow-hidden rounded-md border border-primaryBlue bg-indigo-600 px-[.6125rem] py-[.5625rem] text-sm font-medium leading-none text-white shadow-md shadow-indigo-600  ${className}`}
    >
      {showPlus ? "+" : null} {showChildren ? children : null}
    </button>
  );
};

export default AddButton;
