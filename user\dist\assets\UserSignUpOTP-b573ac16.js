import{j as t}from"./@react-google-maps/api-ee55a349.js";import{B as k,a as C}from"./index-d54cffea.js";import{A as B,a as D}from"./index-dd254604.js";import{S as L}from"./index-5a645c18.js";import{M as O}from"./index-243c4859.js";import{M as T,G as A,s as p}from"./index-09a1718e.js";import{t as r}from"./i18next-7389dd8c.js";import{r as i,R as M,u as E,i as U,d as G}from"./vendor-b16525a8.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";let x=new T;const re=()=>{const[n,d]=i.useState(["","","",""]),o=i.useRef([]),[j,l]=i.useState(!1),[g,b]=i.useState(!1),[N,u]=i.useState(!1),{dispatch:f}=M.useContext(A),w=E();new URLSearchParams(w.search);const{token:m}=U(),P=(s,e)=>{if(isNaN(s))return;const a=[...n];a[e]=s,d(a),s&&e<3?o.current[e+1].focus():!s&&e>0&&o.current[e-1].focus()},v=s=>{const e=s.clipboardData.getData("text").split("").filter(c=>!isNaN(c)),a=[...n];e.slice(0,4).forEach((c,h)=>{a[h]=c,o.current[h].value=c}),d(a),e.length>=4?o.current[3].focus():e.length===3?o.current[2].focus():e.length===2?o.current[1].focus():e.length===1&&o.current[0].focus()},y=(s,e)=>{s.key==="Backspace"&&!n[e]&&e>0&&o.current[e-1].focus()},_=G(),S=async s=>{try{l(!0),(await x.callRawAPI("/v3/api/custom/chumpchange/user/signup/step2",{confirmationCode:n.join(""),email:m},"POST")).error||_(`/user/password/${m}`),l(!1)}catch(e){l(!1),console.log("error >> ",e),p(f,e==null?void 0:e.message,4e3,"error"),b(!0)}},R=async()=>{console.log("Resend OTP");try{u(!0);const s=await x.callRawAPI("/v3/api/custom/chumpchange/user/resend-code",{email:m},"POST");p(f,"Sent successfully"),u(!1)}catch(s){u(!1),console.log("error >> ",s),p(f,s.message,5e3,"error")}};return console.log("otp >> ",n.join("")),t.jsxs(B,{children:[N&&t.jsx(O,{showCloseButton:!1,children:t.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[t.jsxs("div",{className:"text-lg font-semibold",children:[r("loading.sending"),"..."]}),t.jsx("div",{className:"mt-12",children:t.jsx(L,{})})]})}),t.jsx(k,{}),t.jsx(D,{title:r("auth_pages.sign_up.title"),className:" -mt-6 "}),t.jsxs("div",{className:"flex flex-col gap-4",children:[t.jsx("div",{className:" flex h-[70px] flex-col items-center justify-start",children:g?t.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:t.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:r("auth_pages.sign_up.invalid_code")})}):t.jsx("div",{className:"font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:r("auth_pages.sign_up.sub_title_verify")})}),t.jsx("div",{className:"flex w-full items-center justify-between gap-2 ",children:n.map((s,e)=>t.jsx("input",{type:"text",maxLength:"1",value:s,onChange:a=>P(a.target.value,e),onPaste:v,onKeyDown:a=>y(a,e),ref:a=>o.current[e]=a,className:`h-[83px] w-[72px] rounded-2xl border-2  text-center font-['Poppins'] text-2xl  font-medium text-black outline-none focus:outline-none focus:ring-0 ${g?"border-[#ff0000]/5 bg-[#ff0000]/5 focus:border-[#ff0000]/5":"border-[#fff] bg-white focus:border-[#fff] "} `},e))}),t.jsxs("div",{className:" mt-0 flex gap-4 ",children:[t.jsx("p",{className:"text-center font-['Poppins'] text-sm font-medium leading-[18.20px] text-[#8080a3]",children:r("auth_pages.sign_up.get_code")}),t.jsx("p",{onClick:R,className:"text-center font-['Poppins'] text-sm font-medium leading-[18.20px] text-[#56ccf2]",children:r("auth_pages.sign_up.resend")})]}),t.jsx("div",{className:" mt-10 ",children:t.jsx(C,{loading:j,disabled:n.join("").length!==4,type:"submit",onClick:S,children:r("auth_pages.sign_up.next")})})]})]})};export{re as default};
