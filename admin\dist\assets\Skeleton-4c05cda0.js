import{j as o}from"./@react-google-maps/api-ac2f9d6f.js";import"./vendor-4f06b3f4.js";import{S as f}from"./react-loading-skeleton-e20104c1.js";const p=({className:l="",count:t=5,counts:r=[2,1,3,1,1],circle:m=!1})=>o.jsx("div",{className:`flex h-fit max-h-screen min-h-fit w-full flex-col gap-5 overflow-hidden p-4 ${l} `,children:Array.from({length:t}).map((a,e)=>o.jsx(f,{count:r[e]??1,height:r[e]&&r[e]>1||e+1===t?25:80,circle:m,style:{marginBottom:"0.6rem"}},`${a}${e}`))});export{p as default};
