import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{r as l}from"./vendor-f36d475e.js";const f=({children:n,closeModal:i,showCloseButton:r=!0,modal:a})=>{const s=l.useRef(null);l.useEffect(()=>{const t=s.current;t&&(t.style.transform="translateY(100%)",t.style.transition="transform 0.3s ease-out",setTimeout(()=>{t.style.transform="translateY(0)"},10))},[a]);const o=()=>{if(r){const t=s.current;t&&(t.style.transform="translateY(100%)",setTimeout(()=>{i()},300))}};return e.jsxs("div",{className:"fixed left-0 top-0 z-[9999999999] h-full w-full",children:[e.jsx("div",{className:"h-full w-full",onClick:o,style:{fill:"rgba(0, 0, 0, 0.10)",backdropFilter:"blur(2.5px)"}}),e.jsxs("div",{id:"modal-content",ref:s,className:"absolute bottom-0 left-0 max-h-[calc(100vh-50px)] min-h-[333px] w-full overflow-y-auto overflow-x-hidden rounded-tl-[32px] rounded-tr-[32px] bg-white",children:[e.jsx("div",{className:"px-10",children:e.jsx("div",{className:"flex h-7 items-center justify-center bg-white",children:e.jsx("div",{className:"h-1 w-[50px] rounded bg-[#f2f2f7]"})})}),r&&e.jsx("div",{onClick:o,className:"absolute right-5 top-5 flex h-8 w-8 cursor-pointer items-center justify-center rounded-[100px] bg-[#8181a4]/20",children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M5.0002 15L15.0002 5",stroke:"#8181A4","stroke-width":"2.5","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M15 15L5 5",stroke:"#8181A4","stroke-width":"2.5","stroke-linecap":"round","stroke-linejoin":"round"})]})}),n]})]})};export{f as default};
