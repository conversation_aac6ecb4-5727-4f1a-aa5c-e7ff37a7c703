import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as i,b as R}from"./vendor-4f06b3f4.js";import{u as A}from"./react-hook-form-f3d72793.js";import{o as E}from"./yup-2324a46a.js";import{c as L,a as g}from"./yup-17027d7a.js";import{G as I,A as k,M as D,s as O,t as T}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as h}from"./MkdInput-ff3aa862.js";import{I as C}from"./InteractiveButton-8f7d74ee.js";import"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";const ne=({setSidebar:x})=>{const{dispatch:n}=i.useContext(I),b=L({name:g(),logo:g()}).required(),{dispatch:j}=i.useContext(k),[a,v]=i.useState({}),[c,p]=i.useState(!1),S=R(),{register:d,handleSubmit:y,setError:u,setValue:F,formState:{errors:f}}=A({resolver:E(b)});i.useState([]);const w=(o,s)=>{let e=a;e[o]={file:s.files[0],tempURL:URL.createObjectURL(s.files[0])},v({...e})},N=async o=>{let s=new D;p(!0);try{for(let l in a){let r=new FormData;r.append("file",a[l].file);let m=await s.uploadImage(r);o[l]=m.url}s.setTable("service");const e=await s.callRestAPI({name:o.name,logo:o.logo},"POST");if(!e.error)O(n,"Added"),S("/admin/service"),x(!1),n({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(e.validation){const l=Object.keys(e.validation);for(let r=0;r<l.length;r++){const m=l[r];u(m,{type:"manual",message:e.validation[m]})}}p(!1)}catch(e){p(!1),console.log("Error",e),u("name",{type:"manual",message:e.message}),T(j,e.message)}};return i.useEffect(()=>{n({type:"SETPATH",payload:{path:"service"}})},[]),console.log(a),t.jsxs("div",{className:" mx-auto rounded  p-5 shadow-md",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Add Service"}),t.jsxs("form",{className:" w-full max-w-lg",onSubmit:y(N),children:[t.jsx(h,{type:"text",page:"add",name:"name",errors:f,label:"Name",placeholder:"Name",register:d,className:""}),t.jsx("div",{className:"",children:a.logo?t.jsx("img",{src:a.logo.tempURL,alt:"",className:"h-[100px] w-auto object-cover "}):""}),t.jsx(h,{type:"file",page:"add",name:"logo",errors:f,label:"Logo",placeholder:"Logo",register:d,className:"",onChange:o=>w("logo",o.target)}),t.jsx(C,{type:"submit",loading:c,disabled:c,className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",children:"Submit"})]})]})};export{ne as default};
