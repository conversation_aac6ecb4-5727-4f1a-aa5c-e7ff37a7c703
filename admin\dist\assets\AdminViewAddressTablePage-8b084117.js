import{j as s}from"./@react-google-maps/api-ac2f9d6f.js";import{R as a,h as o}from"./vendor-4f06b3f4.js";import"./yup-17027d7a.js";import{M as h,G as t,t as f}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{S as j}from"./index-2d8231e7.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";let m=new h;const G=()=>{const{dispatch:r}=a.useContext(t),{dispatch:d}=a.useContext(t),[e,x]=a.useState({}),[n,i]=a.useState(!0),c=o();return a.useEffect(function(){(async function(){try{i(!0),m.setTable("address");const l=await m.callRestAPI({id:Number(c==null?void 0:c.id),join:""},"GET");l.error||(x(l.model),i(!1))}catch(l){i(!1),console.log("error",l),f(d,l.message)}})()},[]),a.useEffect(()=>{r({type:"SHOW_BACKBUTTON",payload:{showBackButton:!0}})},[]),s.jsx("div",{className:" mx-auto rounded  p-5 shadow-md",children:n?s.jsx(j,{}):s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Name"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.name})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"User Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.user_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Description"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.description})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"City"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.city})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Latitude"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.latitude})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Longtitude"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.longtitude})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"State"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.state})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Country"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.country})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Is Default"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.is_default})]})})]})})};export{G as default};
