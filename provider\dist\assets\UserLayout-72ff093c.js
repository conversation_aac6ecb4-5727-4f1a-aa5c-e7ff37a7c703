import{j as t}from"./@react-google-maps/api-afbf18d5.js";import{R as o,L as r}from"./vendor-f36d475e.js";import{G as C}from"./index-cf5e6bc7.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./react-i18next-1e3e6bc5.js";const n=[{icon:t.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M9.02 2.84016L3.63 7.04016C2.73 7.74016 2 9.23016 2 10.3602V17.7702C2 20.0902 3.89 21.9902 6.21 21.9902H17.79C20.11 21.9902 22 20.0902 22 17.7802V10.5002C22 9.29016 21.19 7.74016 20.2 7.05016L14.02 2.72016C12.62 1.74016 10.37 1.79016 9.02 2.84016Z",stroke:"#A3A9AE",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),iconActive:t.jsx("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M12.027 3.78672L4.84033 9.38672C3.64033 10.3201 2.66699 12.3067 2.66699 13.8134V23.6934C2.66699 26.7867 5.18699 29.3201 8.28033 29.3201H23.7203C26.8137 29.3201 29.3337 26.7867 29.3337 23.7067V14.0001C29.3337 12.3867 28.2537 10.3201 26.9337 9.40005L18.6937 3.62672C16.827 2.32005 13.827 2.38672 12.027 3.78672Z",stroke:"#56CCF2",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),title:"Home",link:"/user/dashboard",value:"home"},{icon:t.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M7.01408 16.003H11.0141M7.01408 11.005H15.0141M10.0141 22H11.0141M7.51008 22C6.34008 21.975 5.54008 21.9 4.76408 21.675C3.71408 21.4 2.94908 20.475 2.72408 19.176C2.43908 17.952 2.53908 14.929 2.51408 11.33C2.51608 8.53904 2.38908 5.85804 2.83908 4.33404C3.11408 3.83404 3.33908 2.01004 7.06408 2.03404C7.68908 2.01004 14.3141 1.96004 15.3641 2.06004C18.8641 2.13504 19.2141 3.93404 19.4141 5.63304C19.5411 6.88304 19.5141 8.88104 19.5141 11.005M7.01408 2.01004C7.31408 3.63404 7.28908 4.68404 8.38908 4.93304C8.86408 5.00804 9.94908 5.00604 11.1141 5.00804C12.1541 5.01004 13.2141 5.02004 13.6891 4.90804C14.8641 4.63404 14.7391 3.18404 15.0391 2.01004M18.2791 14.379C16.9041 15.779 14.2561 18.277 14.2561 18.452C14.0431 18.749 13.8561 19.352 13.7321 20.202C13.5751 20.989 13.3871 21.675 13.6071 21.875C13.8271 22.075 14.6541 21.907 15.5301 21.725C16.2301 21.65 16.8801 21.4 17.2041 21.151C17.6791 20.731 20.9021 17.477 21.2761 17.053C21.5511 16.678 21.5761 15.978 21.3361 15.553C21.2021 15.253 20.3521 14.453 20.0771 14.229C19.8006 14.0585 19.4767 13.981 19.1529 14.008C18.8291 14.035 18.5235 14.1651 18.2791 14.379Z",stroke:"#A3A9AE",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),iconActive:t.jsx("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M9.35211 21.3373H14.6854M9.35211 14.6733H20.0188M13.3521 29.3333H14.6854M10.0134 29.3333C8.45344 29.3 7.38678 29.2 6.35211 28.9C4.95211 28.5333 3.93211 27.3 3.63211 25.568C3.25211 23.936 3.38544 19.9053 3.35211 15.1066C3.35478 11.3853 3.18544 7.81064 3.78544 5.77864C4.15211 5.11197 4.45211 2.67997 9.41878 2.71197C10.2521 2.67997 19.0854 2.6133 20.4854 2.74664C25.1521 2.84664 25.6188 5.2453 25.8854 7.51064C26.0548 9.1773 26.0188 11.8413 26.0188 14.6733M9.35211 2.67997C9.75211 4.8453 9.71878 6.2453 11.1854 6.5773C11.8188 6.6773 13.2654 6.67464 14.8188 6.6773C16.2054 6.67997 17.6188 6.6933 18.2521 6.54397C19.8188 6.17864 19.6521 4.2453 20.0521 2.67997M24.3721 19.172C22.5388 21.0386 19.0081 24.3693 19.0081 24.6026C18.7241 24.9986 18.4748 25.8026 18.3094 26.936C18.1001 27.9853 17.8494 28.9 18.1428 29.1666C18.4361 29.4333 19.5388 29.2093 20.7068 28.9666C21.6401 28.8666 22.5068 28.5333 22.9388 28.2013C23.5721 27.6413 27.8694 23.3026 28.3681 22.7373C28.7348 22.2373 28.7681 21.304 28.4481 20.7373C28.2694 20.3373 27.1361 19.2706 26.7694 18.972C26.4007 18.7446 25.9689 18.6413 25.5372 18.6773C25.1055 18.7133 24.6981 18.8867 24.3721 19.172Z",stroke:"#56CCF2",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),title:"My requests",link:"/user/my-requests",value:"requests"},{icon:t.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[t.jsx("path",{d:"M7 15.5C7 15.7761 6.77614 16 6.5 16C6.22386 16 6 15.7761 6 15.5C6 15.2239 6.22386 15 6.5 15C6.77614 15 7 15.2239 7 15.5Z",fill:"#A3A9AE",stroke:"#A3A9AE",strokeWidth:"2"}),t.jsx("path",{d:"M14 15.5C14 15.7761 13.7761 16 13.5 16C13.2239 16 13 15.7761 13 15.5C13 15.2239 13.2239 15 13.5 15C13.7761 15 14 15.2239 14 15.5Z",fill:"#A3A9AE",stroke:"#A3A9AE",strokeWidth:"2"}),t.jsx("path",{d:"M4 11V7.99999H11.29C11.1 7.36999 11 6.69999 11 5.99999H4.43C5.26 5.28999 7.41 4.90999 11.08 5.01999C11.18 4.31999 11.38 3.64999 11.67 3.02999C2.97 2.66999 2 5.01999 2 6.99999V16.5C2 17.45 2.38 18.31 3 18.94V21C3 21.55 3.45 22 4 22H5C5.55 22 6 21.55 6 21V20H14V21C14 21.55 14.45 22 15 22H16C16.55 22 17 21.55 17 21V18.94C17.62 18.31 18 17.45 18 16.5V13C16.09 13 14.37 12.24 13.11 11H4ZM16 16C16 17.1 15.1 18 14 18H6C4.9 18 4 17.1 4 16V13H16V16Z",fill:"#A3A9AE"}),t.jsx("path",{d:"M18 1C15.24 1 13 3.24 13 6C13 8.76 15.24 11 18 11C20.76 11 23 8.76 23 6C23 3.24 20.76 1 18 1ZM18.5 9H17.5V8H18.5V9ZM18.5 7H17.5V3H18.5V7Z",fill:"#A3A9AE"})]}),iconActive:t.jsxs("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[t.jsx("path",{d:"M9.66669 20.6665C9.66669 21.2188 9.21897 21.6665 8.66669 21.6665C8.1144 21.6665 7.66669 21.2188 7.66669 20.6665C7.66669 20.1142 8.1144 19.6665 8.66669 19.6665C9.21897 19.6665 9.66669 20.1142 9.66669 20.6665Z",fill:"#56CCF2",stroke:"#56CCF2",strokeWidth:"2"}),t.jsx("path",{d:"M19 20.6665C19 21.2188 18.5523 21.6665 18 21.6665C17.4477 21.6665 17 21.2188 17 20.6665C17 20.1142 17.4477 19.6665 18 19.6665C18.5523 19.6665 19 20.1142 19 20.6665Z",fill:"#56CCF2",stroke:"#56CCF2",strokeWidth:"2"}),t.jsx("path",{d:"M5.33335 14.6665V10.6665H15.0534C14.8 9.82649 14.6667 8.93316 14.6667 7.99982H5.90669C7.01335 7.05316 9.88002 6.54649 14.7734 6.69316C14.9067 5.75982 15.1734 4.86649 15.56 4.03982C3.96002 3.55982 2.66669 6.69316 2.66669 9.33316V21.9998C2.66669 23.2665 3.17335 24.4132 4.00002 25.2532V27.9998C4.00002 28.7332 4.60002 29.3332 5.33335 29.3332H6.66669C7.40002 29.3332 8.00002 28.7332 8.00002 27.9998V26.6665H18.6667V27.9998C18.6667 28.7332 19.2667 29.3332 20 29.3332H21.3334C22.0667 29.3332 22.6667 28.7332 22.6667 27.9998V25.2532C23.4934 24.4132 24 23.2665 24 21.9998V17.3332C21.4534 17.3332 19.16 16.3198 17.48 14.6665H5.33335ZM21.3334 21.3332C21.3334 22.7998 20.1334 23.9998 18.6667 23.9998H8.00002C6.53335 23.9998 5.33335 22.7998 5.33335 21.3332V17.3332H21.3334V21.3332Z",fill:"#56CCF2"}),t.jsx("path",{d:"M24 1.3335C20.32 1.3335 17.3333 4.32016 17.3333 8.00016C17.3333 11.6802 20.32 14.6668 24 14.6668C27.68 14.6668 30.6666 11.6802 30.6666 8.00016C30.6666 4.32016 27.68 1.3335 24 1.3335ZM24.6666 12.0002H23.3333V10.6668H24.6666V12.0002ZM24.6666 9.3335H23.3333V4.00016H24.6666V9.3335Z",fill:"#56CCF2"})]}),title:"My alerts",link:"/user/my-alerts",value:"alerts"},{icon:t.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[t.jsxs("mask",{id:"mask0_7217_8751",maskUnits:"userSpaceOnUse",x:"2",y:"0",width:"20",height:"24",children:[t.jsx("path",{d:"M13.87 3.19994C13.56 3.10994 13.24 3.03994 12.91 2.99994C11.95 2.87994 11.03 2.94994 10.17 3.19994C10.46 2.45994 11.18 1.93994 12.02 1.93994C12.86 1.93994 13.58 2.45994 13.87 3.19994Z",stroke:"#9D87D5",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),t.jsx("path",{d:"M15 20C15 21.65 13.67 22.06 12.02 22.06C11.0482 22.06 9 21.1554 9 20",stroke:"#9D87D5",strokeWidth:"3",strokeLinecap:"round",strokeLinejoin:"round"}),t.jsx("path",{d:"M12.0196 2.91016C8.7096 2.91016 6.0196 5.60016 6.0196 8.91016V11.8002C6.0196 12.4102 5.7596 13.3402 5.4496 13.8602L4.2996 15.7702C3.5896 16.9502 4.0796 18.2602 5.3796 18.7002C9.6896 20.1402 14.3396 20.1402 18.6496 18.7002C19.8596 18.3002 20.3896 16.8702 19.7296 15.7702L18.5796 13.8602C18.2796 13.3402 18.0196 12.4102 18.0196 11.8002V8.91016C18.0196 5.61016 15.3196 2.91016 12.0196 2.91016Z",stroke:"#9D87D5",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),t.jsx("g",{mask:"url(#mask0_7217_8751)",children:t.jsx("rect",{width:"24",height:"24",fill:"#A3A9AE"})})]}),iconActive:t.jsxs("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[t.jsxs("mask",{id:"mask0_7366_7832",maskUnits:"userSpaceOnUse",x:"4",y:"1",width:"24",height:"30",children:[t.jsx("path",{d:"M18.4934 4.26667C18.0801 4.14667 17.6534 4.05334 17.2134 4C15.9334 3.84 14.7067 3.93334 13.5601 4.26667C13.9467 3.28 14.9067 2.58667 16.0267 2.58667C17.1467 2.58667 18.1067 3.28 18.4934 4.26667Z",stroke:"#9D87D5",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),t.jsx("path",{d:"M20 26.6667C20 28.8667 18.2267 29.4134 16.0267 29.4134C14.7309 29.4134 12 28.2073 12 26.6667",stroke:"#9D87D5",strokeWidth:"3",strokeLinecap:"round",strokeLinejoin:"round"}),t.jsx("path",{d:"M16.0261 3.88013C11.6127 3.88013 8.02605 7.46679 8.02605 11.8801V15.7335C8.02605 16.5468 7.67938 17.7868 7.26605 18.4801L5.73272 21.0268C4.78605 22.6001 5.43938 24.3468 7.17272 24.9335C12.9194 26.8535 19.1194 26.8535 24.866 24.9335C26.4794 24.4001 27.186 22.4935 26.306 21.0268L24.7727 18.4801C24.3727 17.7868 24.0261 16.5468 24.0261 15.7335V11.8801C24.0261 7.48013 20.4261 3.88013 16.0261 3.88013Z",stroke:"#9D87D5",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),t.jsx("g",{mask:"url(#mask0_7366_7832)",children:t.jsx("rect",{width:"32",height:"32",fill:"#56CCF2"})})]}),title:"Notifications",link:"/user/notifications",value:"notifications"}],l=()=>{const{state:{isOpen:i,path:s},dispatch:h}=o.useContext(C);return t.jsxs("div",{className:" fixed bottom-0 left-0 flex h-[84px] w-full flex-col items-center justify-center bg-white",children:[t.jsx("div",{className:"w-full px-5 ",children:t.jsx("div",{className:"h-px w-[full] bg-[#f2f2f7]"})}),t.jsx("div",{className:" grid w-full grid-cols-4 pt-1 ",children:n.map(e=>t.jsxs(r,{to:e.link,className:"flex flex-col items-center justify-center gap-1 duration-200 ",children:[t.jsx("div",{className:"",children:s==e.value?e.iconActive:e.icon}),t.jsx("div",{className:`text-center font-['Poppins'] text-[10px] ${s==e.value?"font-semibold text-black":"font-medium text-[#8080a3]"}`,children:e.title})]}))})]})},L=({children:i,className:s=""})=>t.jsxs("div",{className:` mx-auto flex min-h-screen max-w-full flex-col justify-between pb-[84px] ${s}`,children:[i,t.jsx(l,{})]});export{L as default};
