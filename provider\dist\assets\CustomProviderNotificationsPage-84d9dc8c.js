import{j as t}from"./@react-google-maps/api-afbf18d5.js";import{h as D,r as p,d as M,R as S,L as F}from"./vendor-f36d475e.js";import"./index-5deedf4a.js";import{M as T,A as $,G as A,f as C}from"./index-cf5e6bc7.js";import{P as V}from"./index-55e4d382.js";import{h as W}from"./moment-a9aaa855.js";import{S as Z}from"./index-65bc3378.js";import{t as n}from"./i18next-7389dd8c.js";import E from"./MobileModal-83d8db3c.js";import H from"./BlueButton-99843563.js";import{u as R}from"./react-i18next-1e3e6bc5.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-2487dba8.js";import"./@react-pdf-viewer/core-9d395990.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./MoonLoader-4d8718ee.js";const B=new T,m=(s,o)=>{if(s!=null&&s.endsWith("nearby.")){const[l]=s.split("nearby.");return`${l}${n("provider.noti_page.nearby")}`}if(s!=null&&s.startsWith("User has updated")){const[l,...d]=(o==null?void 0:o.split(" "))||[],i=d.pop();return n("provider.noti_page.user_updated",{task_title:(l==null?void 0:l.trim())||"",status:n(`provider.noti_page.${(i==null?void 0:i.trim())||""}`)})}return s==="0"?n("provider.noti_page.service_no_longer"):s==="1"?n("provider.noti_page.alert_received"):n(`provider.noti_page.${s}`,{defaultValue:s})},c=s=>{if(!s)return"N/A";if(s.toLowerCase()==="alert")return n("provider.noti_page.Alert");if(s.toLowerCase().startsWith("activation"))return n(`provider.noti_page.${s}`);const o=s.split(" ");if(o.length<=1)return s;const l=o[o.length-1],d=o.slice(0,-1).join(" "),i=n(`provider.noti_page.${l.toLowerCase()}`,{defaultValue:l.toLowerCase()});return`${d} ${i}`},mt=()=>{D();const{state:s,dispatch:o}=p.useContext($),{state:l,dispatch:d}=p.useContext(A),[i,g]=p.useState([]),[L,h]=p.useState(!1),[f,u]=p.useState(!1),[e,w]=p.useState(null),{i18n:j}=R(),_=M(),y=async()=>{try{h(!0);const r=await B.callRawAPI("/v3/api/custom/chumpchange/users/notification",{},"GET");r.error||g(r.data)}catch{}finally{h(!1)}};S.useEffect(()=>{d({type:"SETPATH",payload:{path:"notifications"}}),(async()=>await y())()},[]);const b=r=>{w(r),u(!0)},k=r=>{_("/provider/my-alert")},P=(r,v)=>{if(r!=null&&r.endsWith("nearby.")){const[N]=r.split("nearby.");return`${N}`}},x=e!=null&&e.metadata?(()=>JSON.parse(e.metadata))():{},a=e!=null&&e.metadata?(()=>x!=null&&x.data?x.data:{})():{};return t.jsx(V,{className:"",children:t.jsxs("div",{className:"px-5 py-5",children:[t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"inline-flex h-14 flex-col items-start justify-start gap-1",children:[t.jsx("div",{className:"font-['Poppins'] text-[28px] font-semibold leading-7 text-black",children:n("provider.noti_page.title")}),t.jsx("div",{className:"font-['Poppins'] text-base font-medium text-[#8080a3]",children:n("provider.noti_page.sub_title")})]}),t.jsx(F,{to:"/provider/profile",className:" relative z-10 ",children:t.jsx("img",{className:"h-[45px] w-[45px] rounded-full object-cover",src:s.userDetails.photo||a})})]}),L?t.jsx("div",{className:"flex h-full w-full items-center justify-center pt-10 ",children:t.jsx(Z,{})}):!i||(i==null?void 0:i.length)===0?t.jsxs("div",{className:" mt-[100px] flex flex-col items-center justify-center ",children:[t.jsx("div",{className:"flex h-20 w-20 items-center justify-center rounded-full bg-[#57bffa]/5",children:t.jsx("svg",{width:"48",height:"48",viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M9.60946 18.1633C9.68177 16.2638 10.1562 14.4012 11.0015 12.6985L5.15026 6.8497C5.03868 6.73813 4.95018 6.60567 4.8898 6.4599C4.82942 6.31413 4.79834 6.15789 4.79834 6.0001C4.79834 5.84231 4.82942 5.68607 4.8898 5.5403C4.95018 5.39452 5.03868 5.26207 5.15026 5.1505C5.26183 5.03893 5.39428 4.95043 5.54006 4.89004C5.68583 4.82966 5.84207 4.79858 5.99986 4.79858C6.15764 4.79858 6.31388 4.82966 6.45966 4.89004C6.60543 4.95043 6.73788 5.03893 6.84946 5.1505L42.8495 41.1505C43.0748 41.3758 43.2014 41.6814 43.2014 42.0001C43.2014 42.3188 43.0748 42.6244 42.8495 42.8497C42.6241 43.075 42.3185 43.2016 41.9999 43.2016C41.6812 43.2016 41.3756 43.075 41.1503 42.8497L34.3127 36.0097H29.9999L29.9879 36.3961C29.8914 37.9187 29.2186 39.3474 28.1062 40.3915C26.9938 41.4356 25.5255 42.0168 23.9999 42.0168C22.4742 42.0168 21.0059 41.4356 19.8935 40.3915C18.7811 39.3474 18.1083 37.9187 18.0119 36.3961L17.9999 36.0097H9.59746C9.38678 36.01 9.17697 35.9826 8.97346 35.9281L8.67586 35.8273C8.19139 35.6231 7.78738 35.2654 7.52592 34.8093C7.26447 34.3531 7.16003 33.8238 7.22866 33.3025L7.27906 32.9953L7.38226 32.6881L9.59746 27.3625L9.59986 18.7033L9.60946 18.1633ZM31.9175 33.6145L12.8135 14.5105C12.3961 15.5369 12.1355 16.6202 12.0407 17.7241L12.0095 18.2329L11.9999 18.7249V27.6001L11.9087 28.0609L9.59746 33.6121L31.9175 33.6145ZM27.5975 36.0097H20.3975L20.4167 36.3481C20.4974 37.1791 20.8645 37.9563 21.4551 38.5464C22.0458 39.1366 22.8232 39.5032 23.6543 39.5833L23.9999 39.6001C24.8944 39.6001 25.7569 39.2671 26.4193 38.6659C27.0817 38.0647 27.4966 37.2385 27.5831 36.3481L27.5975 36.0097ZM36.0935 28.0609L38.1911 33.1009L40.2359 35.1457C40.6025 34.718 40.8035 34.173 40.8023 33.6097L40.7831 33.2977L40.7447 33.0889C40.7141 32.9518 40.6715 32.8177 40.6175 32.6881L38.3999 27.3553V18.7249L38.3879 18.1873L38.3567 17.6521C37.7807 10.4041 31.5527 4.8001 23.9951 4.8001C20.0591 4.8001 16.4879 6.3193 13.8839 8.7937L15.5807 10.4905C17.8632 8.36078 20.8733 7.18372 23.9951 7.2001C30.4679 7.2001 35.7383 12.0841 35.9903 18.2593L35.9999 18.7489V27.6001L36.0935 28.0609Z",fill:"#56CCF2"})})}),t.jsx("div",{className:" ",children:t.jsx("div",{className:"text-center font-['Poppins'] text-sm font-medium leading-[16.80px] text-[#434383]",dangerouslySetInnerHTML:{__html:n("provider.noti_page.no_noti")}})})]}):t.jsx("div",{className:" mt-7 flex flex-col items-start justify-start gap-4  ",children:i==null?void 0:i.map((r,v)=>t.jsxs("div",{className:"flex w-full cursor-pointer items-start justify-start gap-3 rounded p-2 hover:bg-gray-50",onClick:()=>b(r),children:[t.jsx("div",{className:"relative flex h-10 w-10 flex-none items-center justify-center rounded-[56px] bg-[#8181a4]/20",children:t.jsxs("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[t.jsxs("mask",{id:"mask0_7366_8891",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:"4",y:"1",width:"24",height:"30",children:[t.jsx("path",{d:"M18.4934 4.26667C18.0801 4.14667 17.6534 4.05334 17.2134 4C15.9334 3.84 14.7067 3.93334 13.5601 4.26667C13.9467 3.28 14.9067 2.58667 16.0267 2.58667C17.1467 2.58667 18.1067 3.28 18.4934 4.26667Z",stroke:"#9D87D5",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),t.jsx("path",{d:"M20 26.6667C20 28.8667 18.2267 29.4134 16.0267 29.4134C14.7309 29.4134 12 28.2073 12 26.6667",stroke:"#9D87D5",strokeWidth:"3",strokeLinecap:"round",strokeLinejoin:"round"}),t.jsx("path",{d:"M16.0263 3.88013C11.613 3.88013 8.0263 7.46679 8.0263 11.8801V15.7335C8.0263 16.5468 7.67963 17.7868 7.2663 18.4801L5.73296 21.0268C4.7863 22.6001 5.43963 24.3468 7.17296 24.9335C12.9196 26.8535 19.1196 26.8535 24.8663 24.9335C26.4796 24.4001 27.1863 22.4935 26.3063 21.0268L24.773 18.4801C24.373 17.7868 24.0263 16.5468 24.0263 15.7335V11.8801C24.0263 7.48013 20.4263 3.88013 16.0263 3.88013Z",stroke:"#9D87D5",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),t.jsx("g",{mask:"url(#mask0_7366_8891)",children:t.jsx("rect",{width:"32",height:"32",fill:"#56CCF2"})})]})}),t.jsxs("div",{className:"flex w-full flex-col flex-wrap items-start justify-center border-b pb-2",children:[t.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-black",children:m(r.message,r.title)}),t.jsx("div",{className:"flex w-full items-center justify-between  gap-1 ",children:t.jsxs("div",{className:"inline-flex w-fit items-center  gap-1",children:[t.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:"→"}),t.jsxs("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:[c(r.title),t.jsx("span",{className:"ml-1 font-['Poppins'] text-xs font-medium text-[#8080a3]",children:"•"}),t.jsx("span",{className:"ml-1 w-[120px] whitespace-nowrap  text-right font-['Poppins'] text-xs font-medium text-[#8080a3] ",children:W(r.update_at).fromNow()})]})]})})]})]},v))}),console.log({selectedNotification:e}),t.jsx("div",{className:f?"block":"hidden",children:t.jsx(E,{closeModal:()=>u(!1),modal:f,children:(e==null?void 0:e.message)=="1"||(e==null?void 0:e.message)=="0"?t.jsx(t.Fragment,{children:t.jsx("div",{className:"mt-5 flex h-max flex-col items-center justify-center gap-2 p-4",children:t.jsxs(t.Fragment,{children:[(e==null?void 0:e.message)=="1"?t.jsx(t.Fragment,{children:t.jsx("div",{className:"text-center font-['Poppins'] text-2xl font-medium capitalize leading-[31.20px] text-[#000000]",children:n("provider.my_alerts.confirmed")})}):(e==null?void 0:e.message)=="0"?t.jsx(t.Fragment,{children:t.jsx("div",{className:"text-center font-['Poppins'] text-2xl font-medium capitalize leading-[31.20px] text-[#FD5D5D]",children:n("provider.my_alerts.declined")})}):null,t.jsx("div",{className:`text-center font-['Poppins'] text-2xl font-medium leading-[31.20px] text-[#000000] ${(e==null?void 0:e.message)=="0"?"text-[#FD5D5D]":"text-[#000000]"}`,children:m(e==null?void 0:e.message,e==null?void 0:e.title)}),t.jsxs("div",{className:"text-center font-['Poppins'] text-sm text-[#8080a3]",children:[P(x==null?void 0:x.service_name)," ",c(e==null?void 0:e.title).toLowerCase()," ",e&&C(e.update_at,j.language)]}),t.jsxs("div",{className:"text-center font-['Poppins'] text-base text-[#8080a3]",children:[a==null?void 0:a.first_name," ",a==null?void 0:a.last_name]}),t.jsx(H,{onClick:()=>k(a==null?void 0:a.alert_id),className:"mt-10 max-w-[163px]",children:n("provider.my_alerts.see_alert")})]})})}):t.jsx(t.Fragment,{children:t.jsxs("div",{className:"mt-5 flex h-max flex-col items-center justify-center gap-2 p-4",children:[t.jsx("div",{className:"text-center font-['Poppins'] text-2xl font-medium leading-[31.20px] text-[#434383]",children:m(e==null?void 0:e.message,e==null?void 0:e.title)}),t.jsx("div",{className:"text-center font-['Poppins'] text-base text-[#8080a3]",children:c(e==null?void 0:e.title)}),t.jsx("div",{className:"text-center font-['Poppins'] text-sm text-[#8080a3]",children:e&&C(e.update_at,j.language)}),t.jsxs("div",{className:"text-center font-['Poppins'] text-base text-[#8080a3]",children:[a==null?void 0:a.first_name," ",a==null?void 0:a.last_name]})]})})})})]})})};export{mt as default};
