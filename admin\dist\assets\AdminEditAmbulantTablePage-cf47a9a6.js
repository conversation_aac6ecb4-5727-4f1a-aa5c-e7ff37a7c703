import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as l,r as c,b as F,h as M}from"./vendor-4f06b3f4.js";import{u as G}from"./react-hook-form-f3d72793.js";import{o as O}from"./yup-2324a46a.js";import{c as $,a as y}from"./yup-17027d7a.js";import{M as q,A as B,G as H,t as K,s as V}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as S}from"./MkdInput-ff3aa862.js";import{I as _}from"./InteractiveButton-8f7d74ee.js";import{S as z}from"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";let d=new q;const we=n=>{var N,w;const{dispatch:E}=l.useContext(B),I=$({name:y().required(),description:y(),status:y()}).required(),{dispatch:g}=l.useContext(H),[s,R]=l.useState({}),[j,x]=l.useState(!1),[A,h]=l.useState(!1),[b,D]=c.useState("");F();const[J,L]=c.useState(""),[Q,P]=c.useState(""),[W,T]=c.useState(""),{register:u,handleSubmit:k,setError:v,setValue:p,formState:{errors:m}}=G({resolver:O(I)}),r=M();c.useEffect(function(){(async function(){try{h(!0),d.setTable("ambulant");const e=await d.callRestAPI({id:n.activeId?n.activeId:Number(r==null?void 0:r.id)},"GET");e.error||(p("name",e.model.name),p("description",e.model.description),p("status",e.model.status),p("logo",e.model.logo),D(e.model.logo),L(e.model.name),P(e.model.description),T(e.model.status),h(!1))}catch(e){h(!1),console.log("error",e),K(E,e.message)}})()},[]);const U=(e,o)=>{let a=s;a[e]={file:o.files[0],tempURL:URL.createObjectURL(o.files[0])},R({...a})},C=async e=>{x(!0);try{d.setTable("ambulant");for(let a in s){let i=new FormData;i.append("file",s[a].file);let f=await d.uploadImage(i);e[a]=f.url}const o=await d.callRestAPI({id:n.activeId?n.activeId:Number(r==null?void 0:r.id),name:e.name,description:e.description,status:e.status,logo:e.logo},"PUT");if(!o.error)V(g,"Updated"),g({type:"REFRESH_DATA",payload:{refreshData:!0}}),n.setSidebar(!1);else if(o.validation){const a=Object.keys(o.validation);for(let i=0;i<a.length;i++){const f=a[i];v(f,{type:"manual",message:o.validation[f]})}}x(!1)}catch(o){x(!1),console.log("Error",o),v("name",{type:"manual",message:o.message})}};return l.useEffect(()=>{g({type:"SETPATH",payload:{path:"provider_ambulant_product"}})},[]),t.jsxs("div",{className:" mx-auto rounded   p-5 shadow-md",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Provider Ambulant Product"}),A?t.jsx(z,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:k(C),children:[t.jsx(S,{type:"text",page:"edit",name:"name",errors:m,label:"Name",placeholder:"Name",register:u,className:""}),t.jsx(S,{type:"text",page:"edit",name:"description",errors:m,label:"Description",placeholder:"Description",register:u,className:""}),t.jsx(S,{type:"select",page:"edit",name:"status",errors:m,label:"Status",placeholder:"Status",register:u,className:"",options:["active","inactive"]}),t.jsx("div",{className:"",children:s.logo||b?t.jsx("img",{src:s!=null&&s.logo?s.logo.tempURL:b,alt:"",className:"h-[100px] w-auto object-cover "}):""}),t.jsxs("div",{className:" mb-4 ",children:[t.jsx("input",{type:"file",id:b,...u("logo",{onChange:e=>U("logo",e.target)}),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none  ${(N=m.logo)!=null&&N.message?"border-red-500":""}`}),t.jsx("p",{className:"text-field-error italic text-red-500",children:(w=m.logo)==null?void 0:w.message})]}),t.jsx(_,{type:"submit",className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",loading:j,disable:j,children:"Submit"})]})]})};export{we as default};
