import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as l,b as _,r as d,h as C}from"./vendor-4f06b3f4.js";import{u as F}from"./react-hook-form-f3d72793.js";import{o as M}from"./yup-2324a46a.js";import{c as G,a as w}from"./yup-17027d7a.js";import{M as O,A as $,G as B,t as H,s as q}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as K}from"./MkdInput-ff3aa862.js";import{I as V}from"./InteractiveButton-8f7d74ee.js";import{S as z}from"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";let n=new O;const we=m=>{var y,j;const{dispatch:N}=l.useContext($),E=G({name:w(),logo:w()}).required(),{dispatch:u}=l.useContext(B),[a,I]=l.useState({}),[v,p]=l.useState(!1),[R,f]=l.useState(!1),A=_(),[J,L]=d.useState(""),[g,T]=d.useState(""),[Q,k]=d.useState(""),{register:b,handleSubmit:D,setError:S,setValue:x,formState:{errors:h}}=F({resolver:M(E)}),r=C();d.useEffect(function(){(async function(){try{f(!0),n.setTable("service");const e=await n.callRestAPI({id:m.activeId?m.activeId:Number(r==null?void 0:r.id)},"GET");e.error||(x("name",e.model.name),x("logo",e.model.logo),x("service_alert",e.model.service_alert),L(e.model.name),T(e.model.logo),k(e.model.service_alert),f(!1))}catch(e){f(!1),console.log("error",e),H(N,e.message)}})()},[]);const P=(e,o)=>{let s=a;s[e]={file:o.files[0],tempURL:URL.createObjectURL(o.files[0])},I({...s})},U=async e=>{p(!0);try{n.setTable("service");for(let s in a){let i=new FormData;i.append("file",a[s].file);let c=await n.uploadImage(i);e[s]=c.url}const o=await n.callRestAPI({id:m.activeId?m.activeId:Number(r==null?void 0:r.id),name:e.name,logo:e.logo,service_alert:e.service_alert},"PUT");if(!o.error)q(u,"Updated"),A("/admin/service"),u({type:"REFRESH_DATA",payload:{refreshData:!0}}),m.setSidebar(!1);else if(o.validation){const s=Object.keys(o.validation);for(let i=0;i<s.length;i++){const c=s[i];S(c,{type:"manual",message:o.validation[c]})}}p(!1)}catch(o){p(!1),console.log("Error",o),S("name",{type:"manual",message:o.message})}};return l.useEffect(()=>{u({type:"SETPATH",payload:{path:"service"}})},[]),t.jsxs("div",{className:" mx-auto rounded   p-5 shadow-md",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Service"}),R?t.jsx(z,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:D(U),children:[t.jsx(K,{type:"text",page:"edit",name:"name",errors:h,label:"Name",placeholder:"Name",register:b,className:""}),t.jsx("div",{className:"",children:a.logo||g?t.jsx("img",{src:a!=null&&a.logo?a.logo.tempURL:g,alt:"",className:"h-[100px] w-auto object-cover "}):""}),t.jsxs("div",{className:" mb-4 ",children:[t.jsx("input",{type:"file",id:g,...b("logo",{onChange:e=>P("logo",e.target)}),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none  ${(y=h.logo)!=null&&y.message?"border-red-500":""}`}),t.jsx("p",{className:"text-field-error italic text-red-500",children:(j=h.logo)==null?void 0:j.message})]}),t.jsx(V,{type:"submit",className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",loading:v,disable:v,children:"Submit"})]})]})};export{we as default};
