import{j as e}from"./@react-google-maps/api-ee55a349.js";import{i as b,r as s,R as x,L as l}from"./vendor-b16525a8.js";import"./index-9ef1c042.js";import{M as y,A as N,G as k}from"./index-09a1718e.js";import{U as _}from"./index-dd254604.js";import{u as B}from"./user-a875fff3.js";import{a as f}from"./index-e25e12e5.js";import{S as M}from"./index-5a645c18.js";import{t as r}from"./i18next-7389dd8c.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";const u=new y,X=()=>{var c,d;b();const{state:i,dispatch:F}=s.useContext(N),{state:H,dispatch:m}=s.useContext(k);s.useState([]);const[a,o]=s.useState("services"),[h,n]=s.useState(!1),[p,C]=s.useState([]),[j,g]=s.useState([]),v=async()=>{try{const t=await u.callRawAPI("/v3/api/custom/chumpchange/user/listing/service/active",{},"GET");t.error||C(t.data)}catch(t){console.error("Error fetching active services:",t)}},w=async()=>{try{const t=await u.callRawAPI("/v3/api/custom/chumpchange/user/listing/product/active",{},"GET");t.error||g(t.data)}catch(t){return console.error("Error fetching products:",t),[]}};return x.useEffect(()=>{(async()=>(n(!0),await v(),await w(),n(!1)))()},[]),x.useEffect(()=>{m({type:"SETPATH",payload:{path:"home"}})},[]),e.jsxs(_,{className:" relative ",children:[e.jsxs("div",{className:" relative h-full w-full px-3 py-4 ",children:[e.jsxs("div",{className:"mb-[31px] flex flex-col px-2 py-5",children:[e.jsx("div",{className:" flex w-full items-center justify-end gap-2 ",children:e.jsx(l,{to:"/user/profile",className:" relative z-10 ",children:e.jsx("img",{className:"h-[50px] w-[50px] rounded-full",src:((c=i==null?void 0:i.userDetails)==null?void 0:c.photo)||B})})}),e.jsxs("div",{className:"font-['Poppins'] text-3xl font-bold capitalize text-black ",children:["Hola ",(d=i==null?void 0:i.userDetails)==null?void 0:d.first_name,"!"]})]}),e.jsxs("svg",{className:"absolute right-0 top-0 ",width:"216",height:"336",viewBox:"0 0 216 336",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("g",{filter:"url(#filter0_f_6956_1462)",children:e.jsx("path",{d:"M244.642 213.42C239.945 245.036 244.414 225.866 217.471 235.334L184.308 226.203C170.469 214.856 140.907 189.848 133.376 180.593C125.845 171.337 108.053 146.927 100.098 135.879C121.861 137.315 174.008 141.627 208.497 147.392C251.608 154.597 227.544 111.441 220.686 98.9126C213.827 86.3842 204.838 90.8648 174.206 84.7494C143.574 78.634 173.079 73.0441 192.697 47.7829C212.314 22.5217 254.265 38.9644 273.483 35.485C292.702 32.0056 396.542 29.6422 410.728 39.4793C424.915 49.3164 429.209 16.1607 441.092 10.5358C452.975 4.91087 454.858 27.6442 465.409 37.2712C475.96 46.8982 267.131 47.6414 256.853 96.3949C246.575 145.148 322.57 121.033 340.748 122.084C358.926 123.134 348.874 117.635 369.576 116.865C390.278 116.095 371.958 143.725 374.454 164.363C376.95 185.001 363.618 170.295 339.577 183.455C315.536 196.616 330.424 202.107 325.941 211.603C321.457 221.098 302.8 188.624 281.15 173.191C259.499 157.758 264.543 172.6 249.324 172.13C234.104 171.66 249.338 181.804 244.642 213.42Z",fill:"#50A8F9","fill-opacity":"0.6"})}),e.jsx("defs",{children:e.jsxs("filter",{id:"filter0_f_6956_1462",x:"0.0976562",y:"-90.3467",width:"565.698",height:"425.681",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),e.jsx("feGaussianBlur",{stdDeviation:"50",result:"effect1_foregroundBlur_6956_1462"})]})})]}),e.jsxs("svg",{className:"absolute left-0 top-[50%]",width:"180",height:"401",viewBox:"0 0 180 401",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("g",{filter:"url(#filter0_f_6956_1463)",children:e.jsx("path",{d:"M-42.0347 119.41C-29.7579 89.899 -38.7737 107.396 -10.3346 104.796L19.5937 121.75C30.2424 136.134 52.8004 167.605 57.8427 178.42C62.8849 189.235 74.1761 217.251 79.1915 229.908C58.4385 223.2 8.9236 206.281 -23.1126 192.267C-63.1579 174.749 -50.3635 222.476 -46.7731 236.3C-43.1828 250.124 -33.3715 247.975 -5.16075 261.387C23.05 274.799 -6.92722 273.013 -32.1205 292.718C-57.3138 312.422 -93.9775 286.231 -113.464 284.911C-132.951 283.59 -234.222 260.519 -245.576 247.515C-256.93 234.511 -269.193 265.613 -282.09 268.165C-294.987 270.717 -291.26 248.213 -299.14 236.3C-307.02 224.388 -104.335 274.674 -82.4603 229.908C-60.5854 185.141 -140.168 189.964 -157.539 184.506C-174.91 179.047 -166.506 186.834 -186.769 182.525C-207.032 178.215 -182.518 155.896 -179.897 135.273C-177.277 114.651 -167.941 132.168 -141.414 125.279C-114.887 118.389 -127.982 109.427 -121.315 101.314C-114.648 93.2017 -104.488 129.249 -87.2631 149.503C-70.0381 169.757 -71.3042 154.132 -56.6602 158.306C-42.0162 162.479 -54.3115 148.921 -42.0347 119.41Z",fill:"#00B3FF","fill-opacity":"0.6"})}),e.jsx("defs",{children:e.jsxs("filter",{id:"filter0_f_6956_1463",x:"-399.364",y:"0.145264",width:"578.556",height:"399.889",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),e.jsx("feGaussianBlur",{stdDeviation:"50",result:"effect1_foregroundBlur_6956_1463"})]})})]}),e.jsxs("div",{className:" w-full rounded-[32px] border-4 border-white bg-white/50 px-5 py-5 pb-6 backdrop-blur-2xl",children:[e.jsx("div",{className:"text-center font-['Poppins'] text-xs font-semibold uppercase text-[#000030]/50",children:r("user.home.sub_title")}),e.jsxs("div",{className:"mt-4 grid w-full grid-cols-3 justify-items-center gap-x-1 gap-y-4 ",children:[e.jsx("div",{className:"",children:e.jsxs(l,{to:"/user/helping-hand-request",className:"flex w-full flex-col items-center justify-center gap-2 ",children:[e.jsx("div",{className:"flex h-14 w-14 items-center justify-center rounded-full border-2 border-[#50a8f9]/10 bg-[#50a8f9]/10",children:e.jsxs("svg",{width:"35",height:"35",viewBox:"0 0 39 36",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M31.75 21.25C34.3575 18.695 37 15.6325 37 11.625C37 9.07229 35.9859 6.62414 34.1809 4.8191C32.3759 3.01406 29.9277 2 27.375 2C24.295 2 22.125 2.875 19.5 5.5C16.875 2.875 14.705 2 11.625 2C9.07229 2 6.62413 3.01406 4.8191 4.8191C3.01406 6.62414 2 9.07229 2 11.625C2 15.65 4.625 18.7125 7.25 21.25L19.5 33.5L31.75 21.25Z",stroke:"#56CCF2","stroke-width":"4","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M19.4999 5.5L14.3199 10.68C13.9644 11.033 13.6822 11.4528 13.4896 11.9154C13.297 12.3779 13.1979 12.874 13.1979 13.375C13.1979 13.876 13.297 14.3721 13.4896 14.8346C13.6822 15.2972 13.9644 15.717 14.3199 16.07C15.7549 17.505 18.0474 17.5575 19.5699 16.1925L23.1924 12.8675C24.1004 12.0436 25.2826 11.5872 26.5087 11.5872C27.7348 11.5872 28.9169 12.0436 29.8249 12.8675L35.0049 17.5225M29.9999 23L26.4999 19.5M24.7499 28.25L21.2499 24.75",stroke:"#56CCF2","stroke-width":"4","stroke-linecap":"round","stroke-linejoin":"round"})]})}),e.jsx("div",{className:"text-center font-['Poppins'] text-xs font-medium text-[#000030]/50",children:r("user.home.helping_hand")})]})}),e.jsx("div",{className:"",children:e.jsxs(l,{to:"/user/create-alert",className:"flex w-full flex-col items-center justify-center gap-2 ",children:[e.jsx("div",{className:"flex h-14 w-14 items-center justify-center rounded-full border-2 border-[#50a8f9]/10 bg-[#50a8f9]/10",children:e.jsxs("svg",{width:"35",height:"35",viewBox:"0 0 35 35",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M9.47913 24.7917C10.6872 24.7917 11.6666 23.8123 11.6666 22.6042C11.6666 21.3961 10.6872 20.4167 9.47913 20.4167C8.271 20.4167 7.29163 21.3961 7.29163 22.6042C7.29163 23.8123 8.271 24.7917 9.47913 24.7917Z",fill:"#56CCF2"}),e.jsx("path",{d:"M19.6875 24.7917C20.8956 24.7917 21.875 23.8123 21.875 22.6042C21.875 21.3961 20.8956 20.4167 19.6875 20.4167C18.4794 20.4167 17.5 21.3961 17.5 22.6042C17.5 23.8123 18.4794 24.7917 19.6875 24.7917Z",fill:"#56CCF2"}),e.jsx("path",{d:"M5.83329 16.0417V11.6667H16.4645C16.1875 10.7479 16.0416 9.77084 16.0416 8.75H6.46038C7.67079 7.71459 10.8062 7.16042 16.1583 7.32084C16.3041 6.3 16.5958 5.32292 17.0187 4.41875C4.33121 3.89375 2.91663 7.32084 2.91663 10.2083V24.0625C2.91663 25.4479 3.47079 26.7021 4.37496 27.6208V30.625C4.37496 31.4271 5.03121 32.0833 5.83329 32.0833H7.29163C8.09371 32.0833 8.74996 31.4271 8.74996 30.625V29.1667H20.4166V30.625C20.4166 31.4271 21.0729 32.0833 21.875 32.0833H23.3333C24.1354 32.0833 24.7916 31.4271 24.7916 30.625V27.6208C25.6958 26.7021 26.25 25.4479 26.25 24.0625V18.9583C23.4645 18.9583 20.9562 17.85 19.1187 16.0417H5.83329ZM23.3333 23.3333C23.3333 24.9375 22.0208 26.25 20.4166 26.25H8.74996C7.14579 26.25 5.83329 24.9375 5.83329 23.3333V18.9583H23.3333V23.3333Z",fill:"#56CCF2"}),e.jsx("path",{d:"M26.25 1.45831C22.225 1.45831 18.9584 4.72498 18.9584 8.74998C18.9584 12.775 22.225 16.0416 26.25 16.0416C30.275 16.0416 33.5417 12.775 33.5417 8.74998C33.5417 4.72498 30.275 1.45831 26.25 1.45831ZM26.9792 13.125H25.5209V11.6666H26.9792V13.125ZM26.9792 10.2083H25.5209V4.37498H26.9792V10.2083Z",fill:"#56CCF2"})]})}),e.jsx("div",{className:"text-center font-['Poppins'] text-xs font-medium text-[#000030]/50",children:r("user.home.alert_me")})]})}),e.jsx("div",{className:"",children:e.jsxs(l,{to:"/user/search-nearby",className:"flex w-full flex-col items-center justify-center gap-2 ",children:[e.jsx("div",{className:"flex h-14 w-14 items-center justify-center rounded-full border-2 border-[#50a8f9]/10 bg-[#50a8f9]/10",children:e.jsx("svg",{width:"35",height:"35",viewBox:"0 0 35 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M17.3838 0C7.79831 0 0 7.79831 0 17.3837C0 29.2794 15.5568 46.7431 16.2191 47.4807C16.8413 48.1736 17.9274 48.1724 18.5484 47.4807C19.2108 46.7431 34.7676 29.2794 34.7676 17.3837C34.7674 7.79831 26.9692 0 17.3838 0ZM17.3838 26.1299C12.5611 26.1299 8.63766 22.2064 8.63766 17.3837C8.63766 12.561 12.5612 8.63756 17.3838 8.63756C22.2064 8.63756 26.1298 12.5611 26.1298 17.3838C26.1298 22.2065 22.2064 26.1299 17.3838 26.1299Z",fill:"#56CCF2"})})}),e.jsx("div",{className:"text-center font-['Poppins'] text-xs font-medium text-[#000030]/50",children:r("user.home.nearby")})]})})]})]}),e.jsxs("div",{className:" mt-[47px] ",children:[e.jsx("div",{className:"px-4 font-['Poppins'] text-2xl font-medium text-black ",children:r("user.home.offers_for_you")}),e.jsxs("div",{className:"relative mt-[12px] grid h-12 w-full grid-cols-2 items-center rounded-2xl bg-[#8181a4]/20 ",children:[e.jsx("div",{onClick:()=>o("services"),className:`flex h-10 cursor-pointer items-center justify-center rounded-2xl text-center font-['Poppins'] text-sm font-medium ${a==="services"?"bg-black text-white":" bg-transparent text-black"}`,children:r("user.home.services")}),e.jsx("div",{onClick:()=>o("products"),className:`flex h-10 cursor-pointer items-center justify-center rounded-2xl text-center font-['Poppins'] text-sm font-medium ${a==="products"?"bg-black text-white":"bg-transparent text-black"}`,children:r("user.home.products")})]})]})]}),e.jsxs("div",{className:" relative left-0 mt-6 flex w-full flex-1 flex-col items-center justify-start overflow-hidden rounded-tl-[32px] rounded-tr-[32px] bg-white",children:[e.jsx("div",{className:" flex h-7 w-full max-w-[335px] items-center justify-center border-b bg-white ",children:e.jsx("div",{className:" h-1 w-[50px] rounded bg-[#f2f2f7]"})}),h?e.jsx("div",{className:"flex h-full w-full items-center justify-center pt-10 ",children:e.jsx(M,{})}):a==="services"?e.jsx(f,{taskData:p}):e.jsx(f,{taskData:j})]})]})};export{X as default};
