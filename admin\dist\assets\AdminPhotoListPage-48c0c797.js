import{j as a}from"./@react-google-maps/api-ac2f9d6f.js";import{R as e,b as K}from"./vendor-4f06b3f4.js";import{M as w,A as J,G as y,t as Q,s as U}from"./index-06b5b6dd.js";import{o as V}from"./yup-2324a46a.js";import{c as W,a as m}from"./yup-17027d7a.js";import{u as X}from"./react-hook-form-f3d72793.js";import"./moment-a9aaa855.js";import{P as Y}from"./index-19801678.js";import{M as Z}from"./index-d97c616d.js";import tt from"./AddAdminPhotoPage-675a044a.js";import{A as et}from"./AddButton-39426f55.js";import{S as at}from"./index-2d8231e7.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@hookform/resolvers-1aa18522.js";import"./@uppy/xhr-upload-57e1d45c.js";import"./@uppy/aws-s3-e4097d1c.js";import"./@craftjs/core-4ea9888f.js";import"./@uppy/aws-s3-multipart-fe0b9775.js";import"./@uppy/react-ed4c83d4.js";import"./@uppy/core-10860ef6.js";import"./@uppy/compressor-af9f85a3.js";import"./@uppy/audio-a0565fb1.js";import"./@uppy/dashboard-f719adde.js";import"./@uppy/drag-drop-b912c52e.js";import"./@uppy/progress-bar-70880302.js";import"./@uppy/file-input-ee919754.js";import"./AddButton.module-98aac587.js";let c=new w;const P=[{header:"Photos",accessor:"url"},{header:"Create at",accessor:"create_at"},{header:"Action",accessor:""}],Kt=()=>{const{dispatch:b}=e.useContext(J),{dispatch:u}=e.useContext(y);e.useState("");const[v,A]=e.useState([]),[d,h]=e.useState(3),[f,N]=e.useState(0),[st,C]=e.useState(0),[o,E]=e.useState(0),[k,D]=e.useState(!1),[R,T]=e.useState(!1),[it,x]=e.useState(!1),[L,p]=e.useState(!1),[ot,M]=e.useState(!1);e.useState(!1),e.useState([]),e.useState([]),e.useState(""),e.useState("eq"),K(),e.useContext(y);const g=e.useRef(null),[F,_]=e.useState(!0),I=W({date:m(),id:m(),user_id:m()});X({resolver:V(I)});function z(t){(async function(){h(t),await r(0,t)})()}function G(){(async function(){await r(o-1>0?o-1:0,d)})()}function O(){(async function(){await r(o+1<=f?o+1:0,d)})()}async function r(t,n,s){try{c.setTable("photo");const i=await c.callRestAPI({payload:{...s},page:t,limit:n},"PAGINATE"),{list:B,total:q,limit:H,num_pages:j,page:l}=i;A(B),h(H),N(j),E(l),C(q),D(l>1),T(l+1<=j),_(!1)}catch(i){console.log("ERROR",i),Q(b,i.message)}}e.useEffect(()=>{u({type:"SETPATH",payload:{path:"photo"}}),async function(){x(!0),await r(0,50),x(!1)}()},[]);async function $(t){c.setTable("photo"),await c.callRestAPI({id:t},"DELETE"),U(u,"Deleted"),await r(0,50)}const S=t=>{g.current&&!g.current.contains(t.target)&&M(!1)};return e.useEffect(()=>(document.addEventListener("mousedown",S),()=>{document.removeEventListener("mousedown",S)}),[]),a.jsxs("div",{className:"px-8",children:[a.jsx("div",{className:"flex items-center justify-between py-3",children:a.jsx(et,{onClick:()=>p(!0)})}),a.jsx("div",{children:F?a.jsx(at,{}):a.jsx("div",{className:"shadow overflow-x-auto border-b border-gray-200 ",children:a.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[a.jsx("thead",{className:"bg-gray-50",children:a.jsx("tr",{children:P.map((t,n)=>a.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[t.header,a.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},n))})}),a.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:v.map((t,n)=>a.jsx("tr",{children:P.map((s,i)=>s.accessor==""?a.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:a.jsxs("button",{className:"text-xs text-red-400",onClick:()=>{$(t.id)},children:[" ","Delete"]})},i):s.mapping?a.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s.mapping[t[s.accessor]]},i):a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s.accessor=="url"?a.jsx("img",{width:200,height:200,src:t!=null&&t.url.includes("http")?t==null?void 0:t.url:`https://mkdlabs.com${t==null?void 0:t.url}`,alt:t!=null&&t.caption?t==null?void 0:t.caption:"broken image link"}):t[s.accessor]},i))},n))})]})})}),a.jsx(Y,{currentPage:o,pageCount:f,pageSize:d,canPreviousPage:k,canNextPage:R,updatePageSize:z,previousPage:G,nextPage:O}),a.jsx(Z,{isModalActive:L,closeModalFn:()=>p(!1),children:a.jsx(tt,{setSidebar:p})})]})};export{Kt as default};
