import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as m,b as M,r as a,h as G}from"./vendor-4f06b3f4.js";import{u as O}from"./react-hook-form-f3d72793.js";import{o as $}from"./yup-2324a46a.js";import{c as B,a as o}from"./yup-17027d7a.js";import{M as H,A as q,G as K,t as V,s as z}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as l}from"./MkdInput-ff3aa862.js";import{I as J}from"./InteractiveButton-8f7d74ee.js";import{S as Q}from"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";let f=new H;const Te=u=>{var y,j;const{dispatch:D}=m.useContext(q),N=B({user_id:o(),description:o(),rate:o(),duration:o(),end_date:o(),start_date:o(),status:o(),service_id:o()}).required(),{dispatch:g}=m.useContext(K),[S,W]=m.useState({}),[v,b]=m.useState(!1),[E,h]=m.useState(!1),I=M(),[X,w]=a.useState(0),[Y,R]=a.useState(""),[Z,k]=a.useState(0),[ee,A]=a.useState(0),[te,T]=a.useState(""),[se,P]=a.useState(""),[ae,F]=a.useState(""),[re,U]=a.useState(0),{register:r,handleSubmit:C,setError:_,setValue:i,formState:{errors:s}}=O({resolver:$(N)}),n=G();a.useEffect(function(){(async function(){try{h(!0),f.setTable("servicelisting");const e=await f.callRestAPI({id:u.activeId?u.activeId:Number(n==null?void 0:n.id)},"GET");e.error||(i("user_id",e.model.user_id),i("description",e.model.description),i("rate",e.model.rate),i("duration",e.model.duration),i("end_date",e.model.end_date),i("start_date",e.model.start_date),i("status",e.model.status),i("service_id",e.model.service_id),w(e.model.user_id),R(e.model.description),k(e.model.rate),A(e.model.duration),T(e.model.end_date),P(e.model.start_date),F(e.model.status),U(e.model.service_id),h(!1))}catch(e){h(!1),console.log("error",e),V(D,e.message)}})()},[]);const L=async e=>{b(!0);try{f.setTable("servicelisting");for(let p in S){let c=new FormData;c.append("file",S[p].file);let x=await f.uploadImage(c);e[p]=x.url}const d=await f.callRestAPI({id:u.activeId?u.activeId:Number(n==null?void 0:n.id),user_id:e.user_id,description:e.description,rate:e.rate,duration:e.duration,end_date:e.end_date,start_date:e.start_date,status:e.status,service_id:e.service_id},"PUT");if(!d.error)z(g,"Updated"),I("/admin/servicelisting"),g({type:"REFRESH_DATA",payload:{refreshData:!0}}),u.setSidebar(!1);else if(d.validation){const p=Object.keys(d.validation);for(let c=0;c<p.length;c++){const x=p[c];_(x,{type:"manual",message:d.validation[x]})}}b(!1)}catch(d){b(!1),console.log("Error",d),_("user_id",{type:"manual",message:d.message})}};return m.useEffect(()=>{g({type:"SETPATH",payload:{path:"servicelisting"}})},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Servicelisting"}),E?t.jsx(Q,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:C(L),children:[t.jsx(l,{type:"number",page:"edit",name:"user_id",errors:s,label:"User Id",placeholder:"User Id",register:r,className:""}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"description",children:"Description"}),t.jsx("textarea",{placeholder:"Description",...r("description"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(y=s.description)!=null&&y.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(j=s.description)==null?void 0:j.message})]}),t.jsx(l,{type:"number",page:"edit",name:"rate",errors:s,label:"Rate",placeholder:"Rate",register:r,className:""}),t.jsx(l,{type:"number",page:"edit",name:"duration",errors:s,label:"Duration",placeholder:"Duration",register:r,className:""}),t.jsx(l,{type:"date",page:"edit",name:"end_date",errors:s,label:"End Date",placeholder:"End Date",register:r,className:""}),t.jsx(l,{type:"date",page:"edit",name:"start_date",errors:s,label:"Start Date",placeholder:"Start Date",register:r,className:""}),t.jsx(l,{type:"text",page:"edit",name:"status",errors:s,label:"Status",placeholder:"Status",register:r,className:""}),t.jsx(l,{type:"number",page:"edit",name:"service_id",errors:s,label:"Service Id",placeholder:"Service Id",register:r,className:""}),t.jsx(J,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:v,disable:v,children:"Submit"})]})]})};export{Te as default};
