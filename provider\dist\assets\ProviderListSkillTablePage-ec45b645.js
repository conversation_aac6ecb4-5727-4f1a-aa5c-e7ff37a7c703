import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{R as s,d as h,r as x}from"./vendor-f36d475e.js";import{M as g,A as b,G as w,P as v,b as E}from"./index-cf5e6bc7.js";import{L as r}from"./index-5deedf4a.js";import{M as A,a as p}from"./index-6c24bd04.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./react-i18next-1e3e6bc5.js";new g;const j=[{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Update At",accessor:"update_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Skill Id",accessor:"skill_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Price",accessor:"price",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Avaibility Start",accessor:"avaibility_start",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Avaibility End",accessor:"avaibility_end",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Description",accessor:"description",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],H=()=>{s.useContext(b),s.useContext(w),h();const[n,a]=s.useState(!1),[o,t]=s.useState(!1),[m,f]=s.useState(),u=x.useRef(null),[D,S]=s.useState([]),l=(i,d,c=[])=>{switch(i){case"add":a(d);break;case"edit":t(d),S(c),f(c[0]);break}};return e.jsxs(e.Fragment,{children:[e.jsx(e.Fragment,{children:e.jsx("div",{className:"overflow-x-auto  rounded bg-white p-5 shadow",children:e.jsx(r,{children:e.jsx(A,{columns:j,tableRole:"provider",table:"skill",actionId:"id",actions:{view:{show:!0,action:null,multiple:!1},edit:{show:!0,multiple:!1,action:i=>l("edit",!0,i)},delete:{show:!0,action:null,multiple:!1},select:{show:!0,action:null,multiple:!1},add:{show:!0,action:()=>l("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!1,action:null,multiple:!0}},actionPostion:"ontable",refreshRef:u})})})}),e.jsx(r,{children:e.jsx(p,{isModalActive:n,closeModalFn:()=>a(!1),children:e.jsx(v,{setSidebar:a})})}),o&&e.jsx(r,{children:e.jsx(p,{isModalActive:o,closeModalFn:()=>t(!1),children:e.jsx(E,{activeId:m,setSidebar:t})})})]})};export{H as default};
