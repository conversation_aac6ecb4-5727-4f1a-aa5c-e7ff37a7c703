import{j as a}from"./@react-google-maps/api-ac2f9d6f.js";import{R as s,b as j}from"./vendor-4f06b3f4.js";import{u as w}from"./react-hook-form-f3d72793.js";import{o as E}from"./yup-2324a46a.js";import{c as N,a as h}from"./yup-17027d7a.js";import{G as D,A as R,M as k,s as I,t as T}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as x}from"./MkdInput-ff3aa862.js";import{I as C}from"./InteractiveButton-8f7d74ee.js";import"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";const le=({setSidebar:b})=>{const{dispatch:i}=s.useContext(D),g=N({name:h(),distance:h()}).required(),{dispatch:S}=s.useContext(R),[p,F]=s.useState({}),[d,m]=s.useState(!1),y=j(),{register:c,handleSubmit:v,setError:u,setValue:M,formState:{errors:f}}=w({resolver:E(g)});s.useState([]);const A=async l=>{let n=new k;m(!0);try{for(let o in p){let t=new FormData;t.append("file",p[o].file);let r=await n.uploadImage(t);l[o]=r.url}n.setTable("service_alerts");const e=await n.callRestAPI({name:l.name,distance:l.distance},"POST");if(!e.error)I(i,"Added"),y("/admin/service_alerts"),b(!1),i({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(e.validation){const o=Object.keys(e.validation);for(let t=0;t<o.length;t++){const r=o[t];u(r,{type:"manual",message:e.validation[r]})}}m(!1)}catch(e){m(!1),console.log("Error",e),u("name",{type:"manual",message:e.message}),T(S,e.message)}};return s.useEffect(()=>{i({type:"SETPATH",payload:{path:"service_alerts"}})},[]),a.jsxs("div",{className:" mx-auto rounded  p-5 shadow-md",children:[a.jsx("h4",{className:"text-2xl font-medium",children:"Add Service Alerts"}),a.jsxs("form",{className:" w-full max-w-lg",onSubmit:v(A),children:[a.jsx(x,{type:"text",page:"add",name:"name",errors:f,label:"Name",placeholder:"Name",register:c,className:""}),a.jsx(x,{type:"number",page:"add",name:"distance",errors:f,label:"Distance",placeholder:"Distance",register:c,className:""}),a.jsx(C,{type:"submit",loading:d,disabled:d,className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",children:"Submit"})]})]})};export{le as default};
