import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as n,b as F}from"./vendor-4f06b3f4.js";import{u as C}from"./react-hook-form-f3d72793.js";import{o as I}from"./yup-2324a46a.js";import{c as R,a as o}from"./yup-17027d7a.js";import{G as P,A as T,M as _,s as $,t as L}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as r}from"./MkdInput-ff3aa862.js";import{I as M}from"./InteractiveButton-8f7d74ee.js";import"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";const xe=({setSidebar:S})=>{var f,g,y,N,j,w;const{dispatch:c}=n.useContext(P),v=R({first_name:o(),last_name:o(),company_id:o(),website:o(),email:o(),phone:o(),description:o(),notes:o(),data:o(),status:o()}).required(),{dispatch:E}=n.useContext(T),[x,O]=n.useState({}),[h,p]=n.useState(!1),A=F(),{register:s,handleSubmit:D,setError:b,setValue:B,formState:{errors:t}}=C({resolver:I(v)});n.useState([]);const k=async a=>{let u=new _;p(!0);try{for(let m in x){let i=new FormData;i.append("file",x[m].file);let d=await u.uploadImage(i);a[m]=d.url}u.setTable("contact");const l=await u.callRestAPI({first_name:a.first_name,last_name:a.last_name,company_id:a.company_id,website:a.website,email:a.email,phone:a.phone,description:a.description,notes:a.notes,data:a.data,status:a.status},"POST");if(!l.error)$(c,"Added"),A("/admin/contact"),S(!1),c({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(l.validation){const m=Object.keys(l.validation);for(let i=0;i<m.length;i++){const d=m[i];b(d,{type:"manual",message:l.validation[d]})}}p(!1)}catch(l){p(!1),console.log("Error",l),b("first_name",{type:"manual",message:l.message}),L(E,l.message)}};return n.useEffect(()=>{c({type:"SETPATH",payload:{path:"contact"}})},[]),e.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Contact"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:D(k),children:[e.jsx(r,{type:"text",page:"add",name:"first_name",errors:t,label:"First Name",placeholder:"First Name",register:s,className:""}),e.jsx(r,{type:"text",page:"add",name:"last_name",errors:t,label:"Last Name",placeholder:"Last Name",register:s,className:""}),e.jsx(r,{type:"number",page:"add",name:"company_id",errors:t,label:"Company Id",placeholder:"Company Id",register:s,className:""}),e.jsx(r,{type:"text",page:"add",name:"website",errors:t,label:"Website",placeholder:"Website",register:s,className:""}),e.jsx(r,{type:"text",page:"add",name:"email",errors:t,label:"Email",placeholder:"Email",register:s,className:""}),e.jsx(r,{type:"text",page:"add",name:"phone",errors:t,label:"Phone",placeholder:"Phone",register:s,className:""}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"description",children:"Description"}),e.jsx("textarea",{placeholder:"Description",...s("description"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(f=t.description)!=null&&f.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(g=t.description)==null?void 0:g.message})]}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"notes",children:"Notes"}),e.jsx("textarea",{placeholder:"Notes",...s("notes"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(y=t.notes)!=null&&y.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(N=t.notes)==null?void 0:N.message})]}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"data",children:"Data"}),e.jsx("textarea",{placeholder:"Data",...s("data"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(j=t.data)!=null&&j.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(w=t.data)==null?void 0:w.message})]}),e.jsx(r,{type:"number",page:"add",name:"status",errors:t,label:"Status",placeholder:"Status",register:s,className:""}),e.jsx(M,{type:"submit",loading:h,disabled:h,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{xe as default};
