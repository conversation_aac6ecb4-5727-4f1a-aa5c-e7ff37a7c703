import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{r as n,d as k,R as C}from"./vendor-f36d475e.js";import{u as b}from"./react-hook-form-ff037c98.js";import{o as w}from"./yup-afe5cf51.js";import{c as y,a as _}from"./yup-2f6e2476.js";import{G as L,M as N,s as P}from"./index-cf5e6bc7.js";import"./InteractiveButton-303096ac.js";import{A as S,a as A}from"./index-55e4d382.js";import{B,a as E}from"./index-895fa99b.js";import{A as M}from"./index-ad319f83.js";import{u as R}from"./react-i18next-1e3e6bc5.js";import"./@hookform/resolvers-eb417cd0.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./MoonLoader-4d8718ee.js";const V=[{name:"phone",type:"phone",placeholder:"Phone",p:"p_phone",icon:e.jsxs("svg",{width:"20",height:"22",viewBox:"0 0 20 22",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M18 6V16C18 20 17 21 13 21H7C3 21 2 20 2 16V6C2 2 3 1 7 1H13C17 1 18 2 18 6Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M12 4.5H8",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M10.0002 18.1C10.8562 18.1 11.5502 17.406 11.5502 16.55C11.5502 15.694 10.8562 15 10.0002 15C9.14415 15 8.4502 15.694 8.4502 16.55C8.4502 17.406 9.14415 18.1 10.0002 18.1Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),length:12}],pe=()=>{var m;const[l,a]=n.useState(!1),[c,d]=n.useState(!1),{t:o}=R(),h=y({phone:_().required(o("auth_pages.reset.error_phone"))}).required(),u=k(),{register:x,handleSubmit:f,setError:g,formState:{errors:s,isValid:p}}=b({resolver:w(h),mode:"onChange"});n.useEffect(()=>{d(p)},[p]);const{dispatch:j}=C.useContext(L),v=async t=>{let i=new N;try{a(!0),(await i.callRawAPI("/v3/api/custom/chumpchange/provider/password-reset/send-code",{phone:t.phone},"POST")).error||(P(j,"Reset Code Sent"),u(`/provider/verify-code?phone=${t.phone}`,{state:{phone:t.phone}})),a(!1)}catch(r){a(!1),console.log("Error",r),g("phone",{type:"manual",message:r==null?void 0:r.message})}};return e.jsxs(S,{children:[e.jsx(B,{}),e.jsxs("form",{onSubmit:f(v),children:[e.jsx(A,{className:"mx-auto -mt-6 max-w-[286px] leading-none ",children:o("auth_pages.reset.title")}),e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx("div",{className:" flex h-[70px] flex-col items-center justify-start",children:s!=null&&s.phone?e.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:e.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:(m=s==null?void 0:s.phone)==null?void 0:m.message})}):e.jsx("div",{className:"font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:o("auth_pages.reset.sub_title")})}),V.map((t,i)=>e.jsx(M,{type:t.type,name:t.name,placeholder:o(`auth_pages.reset.${t.p}`),errors:s,register:x,icon:t.icon},i)),e.jsx("div",{className:"",children:e.jsx("div",{className:"font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:o("auth_pages.reset.will_send")})}),e.jsx("div",{className:" mt-5 ",children:e.jsx(E,{loading:l,disabled:!c,type:"submit",children:o("buttons.next")})})]})]})]})};export{pe as default};
