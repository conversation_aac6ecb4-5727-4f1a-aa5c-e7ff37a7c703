import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{R as i,d as D}from"./vendor-f36d475e.js";import{u as L}from"./react-hook-form-ff037c98.js";import{o as R}from"./yup-afe5cf51.js";import{c as z,a as r}from"./yup-2f6e2476.js";import{G as F,A as G,n as S,M as I,s as N,t as O}from"./index-cf5e6bc7.js";import"./react-quill-3f3a006b.js";/* empty css                   */import"./InteractiveButton-303096ac.js";import"./index-bec80226.js";import{B as V,a as $}from"./index-895fa99b.js";import w from"./DropDownSelection-d1fecb01.js";import{M as H}from"./index-bf8d79cc.js";import{u as K}from"./react-i18next-1e3e6bc5.js";import{L as U}from"./@mantine/core-ee88fb98.js";import"./@hookform/resolvers-eb417cd0.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./@craftjs/core-01ce56b9.js";import"./MoonLoader-4d8718ee.js";import"./@emotion/serialize-460cad7f.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-********.js";const J=[{name:"Cash",value:"cash"},{name:"Deposit",value:"credit_card"}],Q=[{name:"Savings"},{name:"Business"}],Te=({setSidebar:W})=>{var v,y,b,_;const{t:s}=K(),{dispatch:c}=i.useContext(F),k=z({method:r().required(s("provider.add_payment.r_method")),type:r().when("method",(a,o)=>a==="Banco Popular"?o.required(s("provider.add_payment.r_banco")):o),account_number:r().when("method",(a,o)=>a==="Banco Popular"?o.required(s("provider.add_payment.r_ac_number")):o),cedula_rnc:r().when("method",(a,o)=>a==="Banco Popular"?o.required(s("provider.add_payment.r_rnc")):o)}).required(),{dispatch:P}=i.useContext(G);i.useState({});const[B,d]=i.useState(!1),[l,p]=i.useState({}),[M,u]=i.useState({}),C=D(),{register:x,handleSubmit:T,setError:h,setValue:f,formState:{errors:t}}=L({resolver:R(k)}),E=async a=>{let o=new I;d(!0);try{const n=await o.callRawAPI("/v3/api/custom/chumpchange/provider/payment-method/create",{method_type:a.method,account_type:a.type?a.type:"",account_number:a.account_number,cedula_rnc:a.cedula_rnc},"POST");if(!n.error)N(c,s("toast.added")),C("/provider/profile");else if(n.validation){const g=Object.keys(n.validation);for(let m=0;m<g.length;m++){const j=g[m];h(j,{type:"manual",message:n.validation[j]})}}d(!1)}catch(n){d(!1),console.log("Error",n),h("skill_id",{type:"manual",message:n.message}),N(c,n.message,5e3,"error"),O(P,n.message)}},q=a=>{p(a),f("method",a.value)},A=a=>{u(a),f("type",a.name)};return i.useEffect(()=>{c({type:"SETPATH",payload:{path:"skill"}})},[]),console.log("selectedMethod >> ",l),e.jsxs("div",{className:"p-5",children:[B&&e.jsx(H,{showCloseButton:!1,children:e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsxs("div",{className:"text-lg font-semibold",children:[s("loading.adding"),"..."]}),e.jsx("div",{className:"mt-12",children:e.jsx(U,{})})]})}),e.jsxs("div",{className:"relative flex w-full items-center justify-center ",children:[e.jsx("div",{className:" absolute left-0 top-0 ",children:e.jsx(V,{})}),e.jsx("div",{className:"mt-3 text-center font-['Poppins'] text-lg font-medium text-black",children:s("provider.add_payment.title")})]}),((t==null?void 0:t.method)||(t==null?void 0:t.type)||(t==null?void 0:t.account_number)||(t==null?void 0:t.cedula_rnc))&&e.jsx("div",{className:"mt-10 flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:e.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium capitalize text-[#f95050] ",children:((v=t==null?void 0:t.method)==null?void 0:v.message)||((y=t==null?void 0:t.type)==null?void 0:y.message)||((b=t==null?void 0:t.account_number)==null?void 0:b.message)||((_=t==null?void 0:t.cedula_rnc)==null?void 0:_.message)})}),e.jsxs("form",{className:" w-full ",onSubmit:T(E),children:[e.jsx("div",{className:"mt-[50px] font-['Poppins'] text-[22px] font-medium text-black",children:s("provider.add_payment.method_accept")}),e.jsxs("div",{className:"mt-[27px] w-full ",children:[e.jsx("div",{className:"mb-1 px-2 font-['Poppins'] text-sm font-medium text-[#8080a3] ",children:s("provider.add_payment.s_method")}),e.jsx(w,{services:J,onServiceSelect:q,selectedService:l,setSelectedService:p})]}),l.value=="credit_card"?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mt-[27px] w-full ",children:[e.jsx("div",{className:"mb-1 px-2 font-['Poppins'] text-sm font-medium text-[#8080a3] ",children:s("provider.add_payment.a_type")}),e.jsx(w,{services:Q,onServiceSelect:A,selectedService:M,setSelectedService:u})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("div",{className:" px-2 font-['Poppins'] text-[22px] font-bold capitalize tracking-wide text-black",children:s("provider.add_payment.enter_account")}),e.jsx("input",{type:"text",placeholder:s("provider.add_payment.p_enter_account"),...x("account_number",{onChange:a=>S(a,20)}),className:"relative mt-3 h-12 w-full rounded-2xl border-none bg-white text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("div",{className:" px-2 font-['Poppins'] text-[22px] font-bold tracking-wide text-black",children:s("provider.add_payment.rnc")}),e.jsx("input",{type:"text",placeholder:s("provider.add_payment.p_rnc"),...x("cedula_rnc",{onChange:a=>S(a,10)}),className:"relative mt-3 h-12 w-full rounded-2xl border-none bg-white text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "})]})]}):null,e.jsx("div",{className:"mt-[35px]",children:e.jsx($,{type:"submit",children:s("buttons.save")})})]})]})};export{Te as default};
