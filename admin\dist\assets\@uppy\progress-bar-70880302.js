import{_ as r}from"./audio-a0565fb1.js";import{U as e}from"./core-10860ef6.js";const o={version:"3.1.1"},n={target:"body",fixed:!1,hideAfterFinish:!0};let a=class extends e{constructor(s,t){super(s,{...n,...t}),this.id=this.opts.id||"ProgressBar",this.title="Progress Bar",this.type="progressindicator",this.render=this.render.bind(this)}render(s){const t=s.totalProgress||0,i=(t===0||t===100)&&this.opts.hideAfterFinish;return r("div",{className:"uppy uppy-ProgressBar",style:{position:this.opts.fixed?"fixed":"initial"},"aria-hidden":i},r("div",{className:"uppy-ProgressBar-inner",style:{width:`${t}%`}}),r("div",{className:"uppy-ProgressBar-percentage"},t))}install(){const{target:s}=this.opts;s&&this.mount(s,this)}uninstall(){this.unmount()}};a.VERSION=o.version;export{a as P};
