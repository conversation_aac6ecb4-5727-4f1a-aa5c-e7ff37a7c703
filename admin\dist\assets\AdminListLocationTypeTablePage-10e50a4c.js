import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as t,b,r as g}from"./vendor-4f06b3f4.js";import{M as w,A,G as E,r as j,u as v}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{L as o}from"./index-6416aa2c.js";import{M as c}from"./index-d97c616d.js";import{M}from"./index-68c82eaa.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";new w;const y=[{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"name",accessor:"name",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Update At",accessor:"update_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],J=()=>{t.useContext(A);const{dispatch:p}=t.useContext(E),m=b(),[u,a]=t.useState(!1),[r,i]=t.useState(!1),[f,h]=t.useState(),x=g.useRef(null),[L,S]=t.useState([]),d=(s,l,n=[])=>{switch(s){case"add":a(l);break;case"edit":i(l),S(n),h(n[0]);break}};return t.useEffect(()=>{p({type:"SETPATH",payload:{path:"location_type"}})},[]),e.jsxs(e.Fragment,{children:[e.jsx(e.Fragment,{children:e.jsx("div",{className:"overflow-x-auto  rounded bg-white p-5 shadow",children:e.jsx(o,{children:e.jsx(M,{columns:y,tableRole:"admin",table:"nearby_type",actionId:"id",actions:{view:{show:!0,action:s=>m(`/admin/view-location_type/${s.id}`),multiple:!1},edit:{show:!0,multiple:!1,action:s=>d("edit",!0,s)},delete:{show:!0,action:null,multiple:!1},select:{show:!0,action:null,multiple:!1},add:{show:!0,action:()=>d("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!1,action:null,multiple:!0}},actionPostion:"ontable",refreshRef:x})})})}),e.jsx(o,{children:e.jsx(c,{isModalActive:u,closeModalFn:()=>a(!1),children:e.jsx(j,{setSidebar:a})})}),r&&e.jsx(o,{children:e.jsx(c,{isModalActive:r,closeModalFn:()=>i(!1),children:e.jsx(v,{activeId:f,setSidebar:i})})})]})};export{J as default};
