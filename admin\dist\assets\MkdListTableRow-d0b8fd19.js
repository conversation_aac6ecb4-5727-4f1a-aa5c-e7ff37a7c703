import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{h as _}from"./moment-a9aaa855.js";import{b as y}from"./vendor-4f06b3f4.js";const F=({i:M,row:a,columns:w,actions:r,actionPostion:N,actionId:d="id",handleTableCellChange:L,selectedIds:V=[],handleSelectRow:O,setDeleteId:v,table:C,tableRole:k})=>{const b=y();return e.jsx(e.Fragment,{children:e.jsx("tr",{children:w.map((s,t)=>{var c,p,n,h,l,o,x,m,f,u,g,j;return s.accessor.indexOf("image")>-1||s.accessor.indexOf("logo")>-1?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("img",{src:a[s.accessor],className:"h-[3.rem] w-[9.375rem]",alt:""})},t):s.accessor=="photo"?(console.log("iamges >> ",a),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:" flex gap-1 ",children:a.photo?a.photo.split(",").map(i=>e.jsx("a",{className:"text-blue-500",target:"_blank",href:i,rel:"noreferrer",children:"View"})):"N/A"})},t)):s.accessor=="task_pic"?(console.log("iamges >> ",a),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:" flex gap-1 ",children:a.image?a.image.split(",").map(i=>e.jsx("a",{className:"text-blue-500",target:"_blank",href:i,rel:"noreferrer",children:"View"})):"N/A"})},t)):s.accessor=="product_pics"?(console.log("iamges >> ",a),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:" flex gap-1 ",children:a.product_images?JSON.parse(a.product_images).map(i=>e.jsx("a",{className:"text-blue-500",target:"_blank",href:i,rel:"noreferrer",children:"View"})):"N/A"})},t)):s.accessor.indexOf("pdf")>-1||s.accessor.indexOf("doc")>-1||s.accessor.indexOf("file")>-1||s.accessor.indexOf("video")>-1?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsxs("a",{className:"text-blue-500",target:"_blank",href:a[s.accessor],rel:"noreferrer",children:[" ","View"]})},t):s.accessor=="service_id"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"",children:["ID: ",(c=a.service)==null?void 0:c.id]}),e.jsx("div",{className:"",children:(p=a.service)==null?void 0:p.name})]})},t):s.accessor=="product_id"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"",children:["ID: ",(n=a.product)==null?void 0:n.id]}),e.jsx("div",{className:"",children:(h=a.product)==null?void 0:h.name})]})},t):s.accessor=="rate"||s.accessor=="price"||s.accessor=="amount"?e.jsxs("td",{className:"whitespace-nowrap px-6 py-4",children:["$",a[s.accessor]]},t):s.accessor=="location_id"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:(l=a.location)==null?void 0:l.address},t):s.accessor===""?[(o=r==null?void 0:r.select)==null?void 0:o.show,(x=r==null?void 0:r.view)==null?void 0:x.show,(m=r==null?void 0:r.edit)==null?void 0:m.show,(f=r==null?void 0:r.delete)==null?void 0:f.show].includes(!0)?e.jsx("td",{className:"flex !w-full gap-2 whitespace-nowrap px-6 py-4",children:N==="ontable"&&e.jsxs(e.Fragment,{children:[((u=r==null?void 0:r.delete)==null?void 0:u.show)&&e.jsx("button",{className:"ml-2 inline-flex h-10 items-center justify-center gap-2 rounded-lg p-2.5 text-red-500 ",onClick:()=>{v(a[d])},children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M2.5 5H4.17M4.17 5H17.5M4.17 5V16.67C4.17 17.11 4.34 17.53 4.65 17.85C4.97 18.16 5.39 18.33 5.83 18.33H14.17C14.61 18.33 15.03 18.16 15.35 17.85C15.66 17.53 15.83 17.11 15.83 16.67V5H4.17ZM6.67 5V3.33C6.67 2.89 6.84 2.47 7.15 2.15C7.47 1.84 7.89 1.67 8.33 1.67H11.67C12.11 1.67 12.53 1.84 12.85 2.15C13.16 2.47 13.33 2.89 13.33 3.33V5M8.33 9.17V14.17M11.67 9.17V14.17",stroke:"#667085",strokeWidth:"1.67",strokeLinecap:"round",strokeLinejoin:"round"})})}),((g=r==null?void 0:r.edit)==null?void 0:g.show)&&e.jsx("button",{className:"inline-flex h-10 items-center justify-center gap-2 rounded-lg p-2.5 text-[#4F46E5] ",onClick:()=>{var i;(i=r==null?void 0:r.edit)!=null&&i.action&&r.edit.action([a[d]])},children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M14.1665 2.5C14.3854 2.28112 14.6452 2.1075 14.9312 1.98905C15.2171 1.8706 15.5236 1.80963 15.8332 1.80963C16.1427 1.80963 16.4492 1.8706 16.7352 1.98905C17.0211 2.1075 17.281 2.28112 17.4998 2.5C17.7187 2.71886 17.8923 2.97869 18.0108 3.26466C18.1292 3.55063 18.1902 3.85713 18.1902 4.16665C18.1902 4.47618 18.1292 4.78268 18.0108 5.06865C17.8923 5.35461 17.7187 5.61445 17.4998 5.83332L6.24984 17.0833L1.6665 18.3333L2.9165 13.75L14.1665 2.5Z",stroke:"#667085",strokeWidth:"1.66667",strokeLinecap:"round",strokeLinejoin:"round"})})}),((j=r==null?void 0:r.view)==null?void 0:j.show)&&e.jsx("button",{className:"inline-flex h-10 items-center justify-center gap-2 rounded-lg bg-[#42cbee]/5 p-2.5",onClick:()=>{var i;(i=r==null?void 0:r.view)!=null&&i.action?r.view.action(a):b(`/${k}/view-${C}/`+a[d],{state:a})},children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5.833 14.167L14.167 5.833M14.167 5.833H5.833M14.167 5.833V14.167",stroke:"#56CCF2",strokeWidth:"1.67",strokeLinecap:"round",strokeLinejoin:"round"})})})]})},t):null:s.mappingExist?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s.mappings[a[s.accessor]]},t):s.accessor=="start_date"||s.accessor=="start_datetime"||s.accessor=="end_date"||s.accessor=="end_at"||s.accessor=="create_at"||s.accessor=="update_at"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:a[s.accessor]?_(a[s.accessor]).format("DD/MM/YYYY"):"N/A"},t):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:a[s.accessor]},t)})})})};export{F as default};
