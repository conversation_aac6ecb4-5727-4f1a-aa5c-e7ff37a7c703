import{j as t}from"./@react-google-maps/api-afbf18d5.js";import{r as a}from"./vendor-f36d475e.js";const N=({services:u,onServiceSelect:x,className:h,isIcon:w=!1,selectedService:n,setSelectedService:r})=>{const[s,f]=a.useState(!1),[i,j]=a.useState(!1),m=a.useRef(null),b=o=>{r(o),f(!1),x&&x(o)},l=o=>{m.current&&!m.current.contains(o.target)&&f(!1)};return a.useEffect(()=>(document.addEventListener("mousedown",l),()=>{document.removeEventListener("mousedown",l)}),[]),a.useEffect(()=>{if(s){const o=m.current.getBoundingClientRect(),p=window.innerHeight-o.bottom,d=o.top;j(p<200&&d>p)}},[s]),console.log(" selectedService >> ",n),t.jsxs("div",{id:"drop-down",className:`relative w-full rounded-2xl bg-white ${h}`,ref:m,children:[t.jsxs("div",{className:"flex h-12 cursor-pointer items-center justify-between gap-2 px-4",onClick:()=>f(!s),children:[t.jsxs("div",{className:"flex flex-1 items-center justify-between font-['Poppins'] text-base font-normal text-black",children:[t.jsxs("div",{className:"flex",children:[(n==null?void 0:n.icon)&&t.jsx("span",{className:" mr-3 ",children:n==null?void 0:n.icon}),n==null?void 0:n.name]}),t.jsx("div",{className:"font-['Poppins'] text-sm font-normal text-black",children:(n==null?void 0:n.price)&&`Avg. Price ${n==null?void 0:n.price}`})]}),t.jsx("div",{className:"h-5 w-5",children:t.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M16.6 7.5L10.7 13.4C10.3 13.8 9.7 13.8 9.3 13.4L3.4 7.5",stroke:"#8181A4",strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),s&&t.jsx("div",{className:`absolute z-[9999999] mt-1 w-full overflow-hidden rounded-2xl bg-white shadow-lg ${i?"bottom-full mb-1":"top-full mt-1"}`,children:u.map((o,p)=>t.jsxs("div",{className:`flex h-12 cursor-pointer items-center justify-between px-4 hover:bg-gray-100 ${(n==null?void 0:n.name)==o.name?"bg-blue-100":""}`,onClick:()=>b(o),children:[t.jsxs("div",{className:"flex font-['Poppins'] text-base font-normal text-black",children:[(n==null?void 0:n.icon)&&w&&t.jsx("span",{className:" mr-3 ",children:n==null?void 0:n.icon}),o==null?void 0:o.name]}),t.jsx("div",{className:"font-['Poppins'] text-sm font-normal text-black",children:(o==null?void 0:o.price)&&`Avg. Price ${o==null?void 0:o.price}`})]},p))})]})};export{N as default};
