import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{h as R,r as n}from"./vendor-f36d475e.js";import"./index-5deedf4a.js";import{M as D,A as G,G as O,s as o}from"./index-cf5e6bc7.js";import{B as H,a as W}from"./index-895fa99b.js";import{M as j}from"./index-bf8d79cc.js";import{S as N}from"./index-65bc3378.js";import{u as F}from"./react-i18next-1e3e6bc5.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";const l=new D,pe=()=>{const{t:s}=F();R();const[c,w]=n.useState(""),[f,P]=n.useState([]),[t,_]=n.useState({}),[d,x]=n.useState(!1),[U,k]=n.useState(!1),[y,h]=n.useState(!1),[S,m]=n.useState(!1),[$,C]=n.useState(""),[v,L]=n.useState(0),[M,u]=n.useState(!0);n.useContext(G);const{state:q,dispatch:r}=n.useContext(O);n.useEffect(()=>{const i=async()=>{try{const a=await l.callRawAPI("/v3/api/custom/chumpchange/provider/list_recharge",{},"GET");l.setTable("setting");const I=await l.callRestAPI({},"PAGINATE"),{list:T}=I,B=T.reduce((g,b)=>(g[b.setting_key]=b.setting_value,g),{});_(B),a.error?o(r,a.message,4e3,"error"):P(a.data)}catch(a){console.log("Error",a),o(r,a.message||"Error fetching amounts",4e3,"error")}},p=async()=>{try{const a=await l.callRawAPI("/v3/api/custom/chumpchange/provider/data",{},"GET");a.error?o(r,a.message,4e3,"error"):L(a.data.listing_balance)}catch(a){console.log("Error",a),o(r,a.message||"Error fetching user info",4e3,"error")}};(async()=>(u(!0),await i(),await p(),u(!1)))()},[r]);const A=i=>{w(i)},E=async()=>{x(!0),m(!0),C("Loading...");try{const i=await l.callRawAPI("/v3/api/custom/chumpchange/provider/recharge/create",{recharge_id:c.id},"POST");i.error?o(r,i.message||"Error during recharge",4e3,"error"):k(!0),x(!1)}catch(i){console.log("Error",i),o(r,i.message||"Error during recharge",4e3,"error"),m(!1),x(!1)}};return console.log("amountList >> ",f),e.jsxs("div",{className:"p-5",children:[e.jsxs("div",{className:"relative flex w-full items-center justify-between ",children:[e.jsx("div",{className:" ",children:e.jsx(H,{})}),e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:s("provider.recharge.title")}),e.jsx("div",{className:" ",children:e.jsx("button",{onClick:()=>h(!0),className:"relative flex h-10 w-10 items-center justify-center rounded-[32px] bg-white/60 backdrop-blur-[20px]",children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M9.99984 18.3334C14.5832 18.3334 18.3332 14.5834 18.3332 10.0001C18.3332 5.41675 14.5832 1.66675 9.99984 1.66675C5.4165 1.66675 1.6665 5.41675 1.6665 10.0001C1.6665 14.5834 5.4165 18.3334 9.99984 18.3334Z",stroke:"#8181A4",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M10 11.9226V10.8052C10 9.88502 10.6326 9.39786 11.2651 9.00543C11.8826 8.62654 12.5 8.13942 12.5 7.24632C12.5 6.0014 11.3856 5 10 5C8.61444 5 7.5 6.0014 7.5 7.24632",stroke:"#8181A4",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M9.99381 14.3233H10.0074",stroke:"#8181A4",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})})})]}),M?e.jsx("div",{className:" mt-[100px] flex items-center justify-center ",children:e.jsx(N,{})}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:" mt-[7px] ",children:[e.jsx("div",{className:"text-center font-['Poppins'] text-base font-bold text-black",children:s("provider.recharge.status")}),e.jsxs("div",{className:" mt-[7px] text-center font-['Poppins'] text-[32px] font-medium text-[#56ccf2]",children:["$",v||0]})]}),e.jsxs("div",{className:"mt-[100px]",children:[e.jsx("div",{className:"font-['Poppins'] text-lg font-medium text-black",children:s("provider.recharge.s_amount")}),e.jsx("div",{className:"scrollbar-hide mt-5 flex w-full overflow-x-auto ",children:e.jsx("div",{className:"flex gap-[3px]",children:f.map((i,p)=>e.jsxs("div",{className:`flex h-[90px] w-[90px] flex-col items-center justify-center gap-1 rounded-[20px] border ${c.id===i.id?"border-[#9292b1] bg-[#79cef2]/40":"border-[#9292b1] bg-transparent"} cursor-pointer`,onClick:()=>A(i),children:[e.jsx("div",{className:"text-center font-['Poppins'] text-2xl font-bold text-black",children:i.amount}),e.jsx("div",{className:"text-center font-['Poppins'] text-sm font-medium text-[#8080a3]",children:"DOP"})]},p))})})]}),e.jsx("div",{className:"mt-[100px]",children:e.jsx(W,{onClick:E,disabled:!c,children:s("provider.recharge.re_btn")})})]}),S&&e.jsx(j,{showCloseButton:!d,closeModal:()=>m(!1),children:d?e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsxs("div",{className:"text-lg font-semibold",children:[s("loading.submitting"),"..."]}),e.jsx("div",{className:"mt-12",children:e.jsx(N,{})})]}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:" flex flex-col items-center justify-center px-5 pt-8 ",children:[e.jsx("div",{className:"w-[252px] text-center font-['Poppins'] text-xl font-medium leading-normal text-black",dangerouslySetInnerHTML:{__html:s("provider.activation.m_titel")}}),e.jsxs("div",{className:"mt-10 flex w-full items-center justify-between ",children:[e.jsxs("div",{className:"text-center font-['Poppins'] text-base font-semibold text-black",children:[s("provider.activation.request"),":"]}),e.jsx("div",{className:" text-right font-['Poppins'] text-base font-medium text-[#a2a8ae]",children:s("provider.recharge.balance_r")})]}),e.jsxs("div",{className:" flex w-full items-center justify-between ",children:[e.jsxs("div",{className:"text-center font-['Poppins'] text-base font-semibold text-black",children:[s("provider.activation.total_pay"),":"]}),e.jsxs("div",{className:" text-right font-['Poppins'] text-base font-medium text-[#a2a8ae]",children:["DOP ",c.amount]})]}),e.jsx("div",{className:"mt-10 w-full ",children:e.jsxs("div",{className:" font-['Poppins'] text-base font-light leading-tight text-black",children:[s("provider.recharge.to_complete"),":"]})}),e.jsxs("div",{className:"mt-1 flex w-full items-center justify-between ",children:[e.jsx("div",{className:" text-center font-['Poppins'] text-base font-semibold text-[#a2a8ae]",children:s("provider.activation.p_savings")}),e.jsxs("div",{className:" text-center font-['Poppins'] text-base font-semibold text-[#a2a8ae]",children:["# ",t!=null&&t.popular_savings?t==null?void 0:t.popular_savings:0]})]}),e.jsxs("div",{className:"mt-1 flex w-full items-center justify-between ",children:[e.jsx("div",{className:" text-center font-['Poppins'] text-base font-semibold text-[#a2a8ae]",children:s("provider.activation.r_savings")}),e.jsxs("div",{className:" text-center font-['Poppins'] text-base font-semibold text-[#a2a8ae]",children:["# ",t!=null&&t.reservas_savings?t==null?void 0:t.reservas_savings:0]})]}),e.jsx("div",{className:"w-full",children:e.jsxs("div",{className:"text-center font-['Poppins'] text-base font-semibold text-black",children:["RNC ",t!=null&&t.RNC?t==null?void 0:t.RNC:0]})}),e.jsx("div",{className:"mb-10 mt-10 w-full ",children:e.jsx("div",{className:" font-['Poppins'] text-base font-light leading-tight text-black",children:s("provider.activation.p_remember")})})]})})}),y?e.jsx(j,{showCloseButton:!d,closeModal:()=>h(!1),children:e.jsxs("div",{className:" flex w-full flex-col items-center justify-center px-5 pt-8 ",children:[e.jsx("div",{className:"w-[252px] text-center font-['Poppins'] text-base font-medium leading-tight text-black",dangerouslySetInnerHTML:{__html:s("provider.activation.info.title")}}),e.jsx("div",{className:" mt-[29px] font-['Poppins'] text-base font-light leading-tight text-black",children:s("provider.activation.info.s_title")}),e.jsxs("div",{className:" mt-[34px] w-full ",children:[e.jsx("div",{className:"font-['Poppins'] text-base font-semibold text-[#a2a8ae]",children:s("provider.activation.info.p_optoin_one")}),e.jsx("div",{className:" mt-[2px] font-['Poppins'] text-sm font-light leading-[16.80px] text-black",children:s("provider.activation.info.p_optoin_one_d")})]}),e.jsxs("div",{className:" mt-1 w-full ",children:[e.jsx("div",{className:"font-['Poppins'] text-base font-semibold text-[#a2a8ae]",children:s("provider.activation.info.p_optoin_two")}),e.jsx("div",{className:" mt-[2px] font-['Poppins'] text-sm font-light leading-[16.80px] text-black",children:s("provider.activation.info.p_optoin_two_d")}),e.jsxs("div",{className:"mt-1",children:[e.jsxs("div",{className:" flex w-full items-center justify-between ",children:[e.jsx("div",{className:"  text-center font-['Poppins'] text-base font-semibold text-[#a2a8ae]",children:s("provider.activation.info.popular_savings")}),e.jsxs("div",{className:" text-center font-['Poppins'] text-base font-semibold text-[#a2a8ae]",children:["# ",t!=null&&t.popular_savings?t==null?void 0:t.popular_savings:0]})]}),e.jsxs("div",{className:" flex w-full items-center justify-between ",children:[e.jsx("div",{className:"  text-center font-['Poppins'] text-base font-semibold text-[#a2a8ae]",children:s("provider.activation.info.reservas_savings")}),e.jsxs("div",{className:" text-center font-['Poppins'] text-base font-semibold text-[#a2a8ae]",children:["# ",t!=null&&t.reservas_savings?t==null?void 0:t.reservas_savings:0]})]})]}),e.jsx("div",{className:" mt-8 pb-10 font-['Poppins'] text-base font-light leading-tight text-black",children:s("provider.activation.info.disc")})]})]})}):""]})};export{pe as default};
