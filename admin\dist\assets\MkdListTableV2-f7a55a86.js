import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as i}from"./vendor-4f06b3f4.js";import{u as $e}from"./react-hook-form-f3d72793.js";import{o as Be}from"./yup-2324a46a.js";import{c as Oe}from"./yup-17027d7a.js";import{M as ze,A as qe,G as He,aa as ie,t as _,g as Ge}from"./index-06b5b6dd.js";import{P as Ke}from"./index-19801678.js";import{T as Ue}from"./index-68c82eaa.js";import{S as We}from"./index-2d8231e7.js";import{B as Ye,R as Je,A as Qe}from"./index.esm-1a4cea12.js";import{A as Xe}from"./AddButton-39426f55.js";import{L as T}from"./index-6416aa2c.js";import Ze from"./MkdListTableHead-3ce11554.js";import Ve from"./MkdListTableRow-d0b8fd19.js";import{M as et}from"./index-f6c8bd1f.js";import"./index-250f6b3d.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./moment-a9aaa855.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./react-icons-e5379072.js";import"./AddButton.module-98aac587.js";const tt=({onClick:b,className:d})=>e.jsx(e.Fragment,{children:e.jsxs("button",{onClick:b,className:"rounded-lgflex inline-flex h-10  items-center justify-center gap-2 rounded-lg border border-[#cfd4dc] bg-white px-4 py-2.5 font-['Poppins'] text-sm font-medium leading-tight text-[#344053] shadow",children:[e.jsx("svg",{width:"20",height:"18",viewBox:"0 0 20 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M13.3335 12.3334L10.0002 9.00003M10.0002 9.00003L6.66688 12.3334M10.0002 9.00003V16.5M16.9919 14.325C17.8047 13.8819 18.4467 13.1808 18.8168 12.3322C19.1868 11.4837 19.2637 10.5361 19.0354 9.63894C18.807 8.74182 18.2865 7.94629 17.5558 7.3779C16.8251 6.80951 15.9259 6.50064 15.0002 6.50003H13.9502C13.698 5.5244 13.2278 4.61864 12.5752 3.85085C11.9225 3.08307 11.1042 2.47324 10.182 2.0672C9.25967 1.66116 8.25734 1.46949 7.25031 1.5066C6.24328 1.5437 5.25777 1.80861 4.36786 2.28142C3.47795 2.75422 2.7068 3.42261 2.1124 4.23635C1.51799 5.05008 1.11579 5.98797 0.936028 6.97952C0.756269 7.97107 0.803632 8.99047 1.07456 9.96108C1.34548 10.9317 1.83291 11.8282 2.50021 12.5834",stroke:"#344054",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),"Export as CSV"]})});let m=new ze;const st=["create_at","update_at","start_datetime","end_date","start_date"],$t=({setResult:b,columns:d=[],actions:l={view:{show:!0,multiple:!0,action:null},edit:{show:!0,multiple:!0,action:null},delete:{show:!0,multiple:!0,action:null},select:{show:!0,multiple:!0,action:null},add:{show:!0,multiple:!0,action:null,showChildren:!0,children:"Add New"},export:{show:!0,multiple:!0,action:null}},actionPostion:D="ontable",actionId:$="id",table:h,tableRole:oe,tableTitle:k="",hasFilter:de=!0,schemaFields:rt=[],showPagination:at=!0,join:B="",refreshRef:O=null,defaultFilter:z=[]})=>{var se,re,ae,le;const{dispatch:P}=i.useContext(qe),[j,v]=i.useState([]),[c,q]=i.useState(10),[ce,ue]=i.useState(0),[F,me]=i.useState(0),[he,pe]=i.useState(!1),[fe,xe]=i.useState(!1),[ge,N]=i.useState(!1),[we,R]=i.useState(!1),[H,G]=i.useState(!1),[K,U]=i.useState(!1),[p,C]=i.useState([]),[je,S]=i.useState([]),[y,x]=i.useState([]),[W,ye]=i.useState("eq"),[Y,L]=i.useState(!0),I=i.useRef(null),be=Oe({}),[J,Q]=i.useState(null),[X,E]=i.useState(!1),[f,g]=i.useState([]),{state:ve,dispatch:Z}=i.useContext(He);function Ne(t){var n;const r=f;if((n=l==null?void 0:l.select)!=null&&n.multiple)if(r.includes(t)){const s=r.filter(a=>a!==t);g(()=>[...s]),x(s)}else{const s=[...r,t];g(()=>[...s]),x(s)}else if(r.includes(t)){const s=r.filter(a=>a!==t);g(()=>[...s]),x(s)}else{const s=[t];g(()=>[...s]),x(s)}}const Ce=()=>{if(E(t=>!t),X)g([]),x([]);else{const t=j.map(r=>r[$]);g(t),x(t)}},Se=async t=>{N(!0),Q(t)};i.useEffect(()=>{f.length<=0&&E(!1),f.length===j.length&&E(!0),f.length<j.length&&f.length>0&&E(!1)},[f,j]);const{handleSubmit:Ee,reset:lt}=$e({resolver:Be(be)});function Ae(t){d[t].isSorted?d[t].isSortedDesc=!d[t].isSortedDesc:(d.map(r=>r.isSorted=!1),d.map(r=>r.isSortedDesc=!1),d[t].isSorted=!0),async function(){await u(0,c)}()}function Te(){u(F-1,c)}function De(){u(F+1,c)}const V=(t,r,n)=>{const s=r==="eq"&&isNaN(n)?`'${n}'`:n,a=`${t},${r},${s}`;S(o=>[...o.filter(w=>!w.includes(t)),a])},ke=()=>{u(0,c,{},je)},ee=t=>{u(0,c,{},t)};async function u(t,r,n={},s=[]){L(!0);try{m.setTable(h);const a=await m.callRestAPI({payload:{...n},page:t,limit:r,filter:[...z,...s],join:B},"PAGINATE");a&&L(!1);const{list:o,total:ne,limit:w,num_pages:A,page:M}=a;v(o),q(w),ue(A),me(M),pe(M>1),xe(M+1<=A),b&&b(a)}catch(a){L(!1),console.log("ERROR",a),_(P,a.message)}}async function Pe(t,r,n={},s=[]){m.setTable(h);const a=await m.callRestAPI({payload:{...n},page:t,limit:r,filter:[...z,...s],join:B},"PAGINATE"),{list:o}=a;v(o),Z({type:"REFRESH_DATA",payload:{refreshData:!1}})}const Fe=async t=>{async function r(n){try{R(!0),m.setTable(h);const s=await m.callRestAPI({id:n},"DELETE");s!=null&&s.error||(v(a=>a.filter(o=>Number(o.id)!==Number(n))),R(!1),N(!1))}catch(s){throw R(!1),N(!1),_(P,s==null?void 0:s.message),new Error(s)}}typeof t=="object"?t.forEach(async n=>{await r(n)}):typeof t=="number"&&await r(t)},Re=async t=>{try{m.setTable(h);const r=await m.exportCSV()}catch(r){throw new Error(r)}},Le=t=>{const r=d.filter(n=>n.accessor).map(n=>{const s=Ge(t[n.accessor]);return s?`${n.accessor},cs,${s}`:null}).filter(Boolean);u(0,c,{},r)};async function Ie(t,r,n){try{m.setTable(h);const s=await m.callRestAPI({id:t,[r]:n},"PUT")}catch(s){console.log("ERROR",s),_(P,s.message)}}const Me=()=>{C([]),S([]),u(1,c)};async function _e(t,r,n,s){let a;r=isNaN(Number.parseInt(r))?r:Number.parseInt(r);try{clearTimeout(a),a=setTimeout(async()=>{await Ie(t,s,r)},200),v(o=>o.map((w,A)=>A===n?{...w,[s]:r}:w))}catch(o){console.error(o)}}i.useEffect(()=>{var t;(t=l==null?void 0:l.select)!=null&&t.action&&l.select.action()},[y.length]),i.useEffect(()=>{const r=setTimeout(async()=>{await u(1,c)},700);return()=>{clearTimeout(r)}},[]),i.useEffect(()=>{Z({type:"SHOW_BACKBUTTON",payload:{showBackButton:!1}})},[]),i.useEffect(()=>{Pe(1,c)},[ve.refreshData]);const te=t=>{I.current&&!I.current.contains(t.target)&&G(!1)};return i.useEffect(()=>(document.addEventListener("mousedown",te),()=>{document.removeEventListener("mousedown",te)}),[]),e.jsx("div",{className:"px-8 ",children:e.jsxs("div",{className:"rounded-lg border border-[#eaecf0] py-4 ",children:[O&&e.jsx("button",{ref:O,onClick:()=>u(1,c),className:"hidden"}),e.jsxs("div",{className:`flex gap-3 px-4 ${k?"flex-col":"h-fit items-center"}`,children:[de?e.jsx("div",{className:"flex w-auto items-center justify-between ",children:e.jsx("form",{className:"relative rounded bg-white",onSubmit:Ee(Le),children:e.jsx("div",{className:"flex items-center gap-4 text-nowrap text-gray-700",children:e.jsxs("div",{className:"relative",ref:I,children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-1",onClick:()=>G(!H),children:[e.jsx(Ye,{}),e.jsx("span",{children:"Filters"}),p.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:p.length})]}),H&&e.jsx("div",{className:"top-fill filter-form-holder absolute left-0 z-10 mt-4 w-[500px] min-w-[90%] rounded-md border border-gray-200 bg-white shadow-lg",children:e.jsxs("div",{className:"p-4",children:[p==null?void 0:p.map((t,r)=>{var n;return e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:" mb-3 w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",children:t}),e.jsxs("select",{className:"mb-3 w-[40%] rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:s=>{ye(s.target.value)},children:[e.jsx("option",{value:"eq",selected:!0,children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),t=="Select Status"?e.jsx(e.Fragment,{children:e.jsxs("select",{className:" mb-3 w-1/3 rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:s=>V("status",W,s.target.value),onKeyDown:s=>{s.key==="Enter"&&s.preventDefault()},children:[e.jsx("option",{value:"",children:"Select"}),Object.entries(((n=d.find(s=>s.accessor==="status"))==null?void 0:n.mappings)||{}).map(([s,a])=>e.jsx("option",{value:s,children:a},s))]})}):e.jsx("input",{type:st.includes(t)?"date":"text",placeholder:"Enter value...",className:" mb-3 w-1/3 rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:s=>V(t=="location_id"?"chumpchange_location.address":t=="service_id"?"chumpchange_service.name":t,W,s.target.value),onKeyDown:s=>{s.key==="Enter"&&s.preventDefault()}}),e.jsx("div",{className:"mt-[-10px] w-1/12",children:e.jsx(Je,{className:" cursor-pointer text-xl",onClick:()=>{t=="Select Status"?(C(s=>s.filter(a=>a!==t)),S(s=>{const a=s.filter(o=>!o.includes("status"));return ee(a),a})):(C(s=>s.filter(a=>a!==t)),S(s=>{const a=s.filter(o=>!o.includes(t));return ee(a),a}))}})})]},r)}),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out ",onClick:()=>{U(!K)},children:[e.jsx(Qe,{}),"Add filter"]}),K&&e.jsx("div",{className:"absolute top-11 z-10 bg-white px-5 py-3 text-gray-600 shadow-md",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:d.slice(0,-1).map(t=>!t.notfilter&&e.jsx("li",{className:`${p.includes(t.accessor)?"cursor-not-allowed text-gray-400":"cursor-pointer"}`,onClick:()=>{p.includes(t.accessor)||C(r=>[...r,t.mappingExist?"Select Status":t.accessor]),U(!1)},children:t.header},t.accessor))})}),p.length>0&&e.jsx("div",{onClick:Me,className:"inline-block cursor-pointer  rounded px-6  py-2.5 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out",children:"Clear all filter"})]}),e.jsx("button",{type:"button",onClick:ke,className:"mt-4 inline-block cursor-pointer rounded bg-blue-500 px-6 py-2.5 font-medium leading-tight text-white transition duration-150 ease-in-out",children:"Apply Filters"})]})})]})})})}):null,e.jsxs("div",{className:"flex h-fit w-full justify-between text-center",children:[e.jsx("h4",{className:"text-2xl font-medium capitalize",children:k||""}),e.jsxs("div",{className:"flex h-full gap-2",children:[y!=null&&y.length&&D==="abovetable"?e.jsx(T,{children:e.jsx(Ue,{actions:l,selectedItems:y})}):null,((se=l==null?void 0:l.export)==null?void 0:se.show)&&e.jsx(tt,{showText:!1,onClick:Re,className:"mx-1"}),((re=l==null?void 0:l.add)==null?void 0:re.show)&&e.jsx(Xe,{onClick:()=>{var t,r;(t=l==null?void 0:l.add)!=null&&t.action&&((r=l==null?void 0:l.add)==null||r.action())},showChildren:(ae=l==null?void 0:l.add)==null?void 0:ae.showChildren,children:(le=l==null?void 0:l.add)==null?void 0:le.children})]})]})]}),e.jsx("div",{className:"overflow-x-auto  rounded bg-white p-5 px-0",children:e.jsxs(e.Fragment,{children:[e.jsx("div",{className:`${Y?"":"overflow-x-auto"} border-b border-gray-200 shadow`,children:Y?e.jsx("div",{className:"flex max-h-fit min-h-fit min-w-fit max-w-full items-center justify-center  py-5",children:e.jsx(We,{})}):e.jsx(e.Fragment,{children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx(T,{children:e.jsx(Ze,{onSort:Ae,columns:d,actions:l,actionPostion:D,areAllRowsSelected:X,handleSelectAll:Ce})})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:j.map((t,r)=>e.jsx(T,{children:e.jsx(Ve,{i:r,row:t,columns:d,actions:l,actionPostion:D,actionId:$,handleTableCellChange:_e,handleSelectRow:Ne,selectedIds:f,setDeleteId:Se,table:h,tableRole:oe},r)},r))})]})})}),e.jsx(T,{children:e.jsx(et,{open:ge,actionHandler:()=>{Fe(J)},closeModalFunction:()=>{Q(null),N(!1)},title:`Delete ${ie(h)} `,message:`You are about to delete ${ie(h)} ${J}, note that this action is irreversible`,acceptText:"DELETE",rejectText:"CANCEL",loading:we})})]})}),e.jsx(Ke,{currentPage:F,pageCount:ce,pageSize:c,canPreviousPage:he,canNextPage:fe,updatePageSize:t=>{q(t),u(1,t)},previousPage:Te,nextPage:De})]})})};export{$t as default};
