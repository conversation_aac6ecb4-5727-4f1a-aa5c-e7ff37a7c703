import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as s,b as j}from"./vendor-4f06b3f4.js";import{u as A}from"./react-hook-form-f3d72793.js";import{o as E}from"./yup-2324a46a.js";import{c as w,a as m}from"./yup-17027d7a.js";import{G as I,A as P,M as N,s as R,t as k}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as n}from"./MkdInput-ff3aa862.js";import{I as D}from"./InteractiveButton-8f7d74ee.js";import"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";const de=({setSidebar:x})=>{const{dispatch:p}=s.useContext(I),g=w({provider_id:m(),plan_id:m(),status:m(),end_at:m()}).required(),{dispatch:y}=s.useContext(P),[f,T]=s.useState({}),[b,u]=s.useState(!1),v=j(),{register:i,handleSubmit:S,setError:h,setValue:C,formState:{errors:l}}=A({resolver:E(g)});s.useState([]);const _=async o=>{let c=new N;u(!0);try{for(let r in f){let a=new FormData;a.append("file",f[r].file);let d=await c.uploadImage(a);o[r]=d.url}c.setTable("user_plan");const e=await c.callRestAPI({provider_id:o.provider_id,plan_id:o.plan_id,status:o.status,end_at:o.end_at},"POST");if(!e.error)R(p,"Added"),v("/admin/user_plan"),x(!1),p({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(e.validation){const r=Object.keys(e.validation);for(let a=0;a<r.length;a++){const d=r[a];h(d,{type:"manual",message:e.validation[d]})}}u(!1)}catch(e){u(!1),console.log("Error",e),h("provider_id",{type:"manual",message:e.message}),k(y,e.message)}};return s.useEffect(()=>{p({type:"SETPATH",payload:{path:"active_subscriptions"}})},[]),t.jsxs("div",{className:" mx-auto rounded  p-5 shadow-md",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Add User Plan"}),t.jsxs("form",{className:" w-full max-w-lg",onSubmit:S(_),children:[t.jsx(n,{type:"number",page:"add",name:"provider_id",errors:l,label:"Provider Id",placeholder:"Provider Id",register:i,className:""}),t.jsx(n,{type:"number",page:"add",name:"plan_id",errors:l,label:"Plan Id",placeholder:"Plan Id",register:i,className:""}),t.jsx(n,{type:"text",page:"add",name:"status",errors:l,label:"Status",placeholder:"Status",register:i,className:""}),t.jsx(n,{type:"datetime-local",page:"add",name:"end_at",errors:l,label:"End At",placeholder:"End At",register:i,className:""}),t.jsx(D,{type:"submit",loading:b,disabled:b,className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",children:"Submit"})]})]})};export{de as default};
