import{j as r}from"./@react-google-maps/api-afbf18d5.js";import{r as e}from"./vendor-f36d475e.js";import{_ as o}from"./qr-scanner-cf010ec4.js";import{S as t}from"./index-65bc3378.js";e.lazy(()=>o(()=>import("./ProviderHeader-622dbc7b.js"),["assets/ProviderHeader-622dbc7b.js","assets/@react-google-maps/api-afbf18d5.js","assets/vendor-f36d475e.js","assets/react-icons-2eae9d1f.js","assets/index-cf5e6bc7.js","assets/react-confirm-alert-2487dba8.js","assets/moment-a9aaa855.js","assets/@react-pdf-viewer/core-9d395990.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-46b39f71.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-b3bb6c9d.js","assets/@fortawesome/react-fontawesome-eb6bfecd.js","assets/@fortawesome/fontawesome-svg-core-4fa3e289.js","assets/@fortawesome/free-solid-svg-icons-0a9c4907.js","assets/@fortawesome/free-regular-svg-icons-0a88e957.js","assets/@fortawesome/free-brands-svg-icons-fae0dcac.js","assets/i18next-7389dd8c.js","assets/react-i18next-1e3e6bc5.js","assets/index-d64010cc.css","assets/index.esm-03de46bd.js"]));e.lazy(()=>o(()=>import("./TopHeader-33c91bc7.js"),["assets/TopHeader-33c91bc7.js","assets/@react-google-maps/api-afbf18d5.js","assets/vendor-f36d475e.js","assets/index-cf5e6bc7.js","assets/react-confirm-alert-2487dba8.js","assets/moment-a9aaa855.js","assets/@react-pdf-viewer/core-9d395990.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-46b39f71.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-b3bb6c9d.js","assets/@fortawesome/react-fontawesome-eb6bfecd.js","assets/@fortawesome/fontawesome-svg-core-4fa3e289.js","assets/@fortawesome/free-solid-svg-icons-0a9c4907.js","assets/@fortawesome/free-regular-svg-icons-0a88e957.js","assets/@fortawesome/free-brands-svg-icons-fae0dcac.js","assets/i18next-7389dd8c.js","assets/react-i18next-1e3e6bc5.js","assets/index-d64010cc.css"]));const i=({children:s})=>r.jsx("div",{children:r.jsx(e.Suspense,{fallback:r.jsx("div",{className:"flex h-screen w-full items-center justify-center",children:r.jsx(t,{size:100,color:"#2CC9D5"})}),children:s})}),_=e.memo(i);export{_ as default};
