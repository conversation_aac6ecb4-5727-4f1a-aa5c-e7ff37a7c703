import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{R as s,d as x,r as S}from"./vendor-f36d475e.js";import{M as w,A as b,G as g,c as j,d as v}from"./index-cf5e6bc7.js";import{L as o}from"./index-5deedf4a.js";import{M as A,a as n}from"./index-6c24bd04.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./react-i18next-1e3e6bc5.js";new w;const E=[{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Product Id",accessor:"product_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"License Key Id",accessor:"license_key_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Update At",accessor:"update_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],H=()=>{s.useContext(b),s.useContext(g),x();const[p,t]=s.useState(!1),[r,a]=s.useState(!1),[m,u]=s.useState(),f=S.useRef(null),[P,h]=s.useState([]),d=(i,l,c=[])=>{switch(i){case"add":t(l);break;case"edit":a(l),h(c),u(c[0]);break}};return e.jsxs(e.Fragment,{children:[e.jsx(e.Fragment,{children:e.jsx("div",{className:"overflow-x-auto  rounded bg-white p-5 shadow",children:e.jsx(o,{children:e.jsx(A,{columns:E,tableRole:"provider",table:"license_product",actionId:"id",actions:{view:{show:!0,action:null,multiple:!1},edit:{show:!0,multiple:!1,action:i=>d("edit",!0,i)},delete:{show:!0,action:null,multiple:!1},select:{show:!0,action:null,multiple:!1},add:{show:!0,action:()=>d("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!1,action:null,multiple:!0}},actionPostion:"ontable",refreshRef:f})})})}),e.jsx(o,{children:e.jsx(n,{isModalActive:p,closeModalFn:()=>t(!1),children:e.jsx(j,{setSidebar:t})})}),r&&e.jsx(o,{children:e.jsx(n,{isModalActive:r,closeModalFn:()=>a(!1),children:e.jsx(v,{activeId:m,setSidebar:a})})})]})};export{H as default};
