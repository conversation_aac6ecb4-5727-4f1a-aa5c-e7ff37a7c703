import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{r as b,R as n,d as E,h as I}from"./vendor-f36d475e.js";import{B as T,R as Z,a as $}from"./index-895fa99b.js";import{a as F}from"./index-04e38e92.js";import{M as H,A as U,f as y}from"./index-cf5e6bc7.js";import{S as d}from"./index-65bc3378.js";import{t}from"./i18next-7389dd8c.js";import{M as P}from"./index-bf8d79cc.js";import{u as V}from"./react-i18next-1e3e6bc5.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const m=new H,pe=()=>{var p,f,h,v,u,j,C;const{state:i,dispatch:q}=b.useContext(U);n.useState(i.userDetails);const[s,g]=n.useState(),[L,c]=n.useState(!0),[_,a]=n.useState(!1),[k,l]=n.useState(!1),x=E();V();const{id:o}=I(),S=async()=>{try{c(!0),m.setTable("servicelisting");const r=await m.callRestAPI({id:o,filter:[`id,eq,${o}`],join:"service"},"PAGINATE");g(r.list[0]),c(!1)}catch(r){c(!1),console.log(r)}};b.useEffect(()=>{S()},[]);const M=async()=>{var r,N;try{a(!0);const w=new Date;new Date().setDate(w.getDate()+7);const W=A=>A.toISOString().split("T")[0],R=new URLSearchParams({service:(r=s==null?void 0:s.service)==null?void 0:r.name,price:(N=s==null?void 0:s.service)==null?void 0:N.price,service_id:s==null?void 0:s.service_id,rate:s==null?void 0:s.rate,description:s==null?void 0:s.description,city:s==null?void 0:s.city,republishId:o}).toString();x(`/provider/crate-listing/service?${R}`),a(!1)}catch{a(!1)}},B=async()=>{try{l(!0);const r=await m.callRawAPI(`/v3/api/custom/chumpchange/provider/listing/service/delete/${o}`,{},"DELETE");x("/provider/my-listings"),l(!1)}catch{l(!1)}};return console.log("listingData >>",s),e.jsxs("div",{className:"p-5",children:[_&&e.jsx(P,{showCloseButton:!1,children:e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsxs("div",{className:"text-lg font-semibold",children:[t("loading.updating"),"..."]}),e.jsx("div",{className:"mt-12",children:e.jsx(d,{})})]})}),k&&e.jsx(P,{showCloseButton:!1,children:e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsxs("div",{className:"text-lg font-semibold",children:[t("loading.deleting"),"..."]}),e.jsx("div",{className:"mt-12",children:e.jsx(d,{})})]})}),e.jsxs("div",{className:"relative flex w-full items-center justify-center ",children:[e.jsx("div",{className:" absolute left-0 top-0 ",children:e.jsx(T,{})}),e.jsxs("div",{className:"",children:[e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:t("provider.con_service.title")}),e.jsx("div",{className:"text-center font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:(p=s==null?void 0:s.service)!=null&&p.name?(f=s==null?void 0:s.service)==null?void 0:f.name:"N/A"})]})]}),e.jsx(F,{}),L?e.jsx("div",{className:"flex min-h-screen w-full items-center justify-center",children:e.jsx(d,{})}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:" mt-9 ",children:[e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:t("provider.con_service.offersHelpWith")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:(h=s==null?void 0:s.service)==null?void 0:h.name})]}),e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:t("provider.con_service.validFrom")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:y(s==null?void 0:s.create_at)})]}),e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:t("provider.con_service.validUntil")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:y(s==null?void 0:s.end_date)})]}),e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:t("provider.con_service.offerPrice")}),e.jsxs("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:["DOP ",s==null?void 0:s.rate]})]}),e.jsxs("div",{className:"flex justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsxs("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:[t("provider.con_service.description"),":"]}),e.jsx("div",{className:"w-[165px] text-right font-['Poppins'] text-sm font-light leading-[16.80px] text-black",children:s==null?void 0:s.description})]}),e.jsxs("div",{className:" border-t border-[#8181a4]/20 py-3 ",children:[e.jsxs("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:[t("provider.con_service.contactMe"),":"]}),e.jsxs("div",{className:" mt-[18px] flex justify-between gap-2",children:[e.jsxs("div",{className:"flex flex-col items-center justify-center gap-1 ",children:[e.jsx("div",{className:" h-7 w-7 ",children:e.jsx("svg",{width:"28",height:"28",viewBox:"0 0 28 28",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M12.25 7H15.75M19.0003 3.5H8.99967C7.896 3.5 7 4.44033 7 5.6V22.4C7 23.5597 7.896 24.5 8.99967 24.5H19.0003C20.1052 24.5 21 23.5597 21 22.4V5.6C21 4.44033 20.1052 3.5 19.0003 3.5Z",stroke:"#56CCF2",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:t("provider.con_service.mobile")}),e.jsx("a",{href:`tel:${(v=i.userDetails)==null?void 0:v.phone}`,className:"font-['Poppins'] text-xs font-medium text-black",children:(u=i.userDetails)==null?void 0:u.phone})]}),e.jsxs("div",{className:"flex flex-col items-center justify-center gap-1 ",children:[e.jsx("div",{className:"flex h-7 w-7 items-end justify-center ",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M15.7564 11.8265L12.6189 10.2578C12.5262 10.2118 12.423 10.1909 12.3198 10.1975C12.2165 10.204 12.1167 10.2377 12.0306 10.2951L10.4932 11.3196C9.71495 10.9212 9.08175 10.288 8.68326 9.5098L9.70884 7.97353C9.76625 7.88742 9.79992 7.7877 9.80647 7.68441C9.81301 7.58113 9.79218 7.47796 9.7461 7.38529L8.17733 4.24804C8.12855 4.14989 8.05332 4.06731 7.96012 4.00963C7.86692 3.95194 7.75945 3.92144 7.64983 3.92157C6.66169 3.92157 5.71401 4.31407 5.01528 5.01274C4.31656 5.7114 3.92402 6.659 3.92402 7.64706C3.92661 9.8824 4.81582 12.0254 6.39659 13.6061C7.97736 15.1867 10.1206 16.0758 12.3561 16.0784C13.3443 16.0784 14.292 15.6859 14.9907 14.9873C15.6894 14.2886 16.082 13.341 16.082 12.3529C16.082 12.2436 16.0515 12.1364 15.994 12.0434C15.9365 11.9504 15.8543 11.8753 15.7564 11.8265ZM12.3561 14.902C10.4326 14.8996 8.58844 14.1345 7.22827 12.7745C5.8681 11.4144 5.10292 9.57046 5.10059 7.64706C5.10065 7.03186 5.32321 6.43744 5.72722 5.97346C6.13123 5.50948 6.68943 5.20726 7.29882 5.12255L8.54011 7.6049L7.52139 9.13235C7.46787 9.21291 7.43503 9.30543 7.4258 9.4017C7.41656 9.49798 7.43121 9.59505 7.46845 9.68431C8.0094 10.9704 9.03255 11.9934 10.3187 12.5343C10.408 12.5715 10.505 12.5862 10.6013 12.577C10.6976 12.5677 10.7901 12.5349 10.8707 12.4814L12.3983 11.4627L14.8809 12.7039C14.7962 13.3133 14.4939 13.8714 14.0299 14.2754C13.5659 14.6794 12.9714 14.9019 12.3561 14.902ZM10.003 2.20408e-07C8.26929 -0.000363697 6.56528 0.449929 5.05812 1.3067C3.55097 2.16347 2.29245 3.39729 1.40603 4.88712C0.519614 6.37694 0.0357485 8.07158 0.00190601 9.8048C-0.0319365 11.538 0.385407 13.2502 1.21299 14.7735L0.0736749 18.1922C-0.00698272 18.434 -0.018688 18.6935 0.039871 18.9416C0.09843 19.1897 0.224939 19.4167 0.405219 19.5969C0.585499 19.7772 0.812424 19.9037 1.06056 19.9622C1.3087 20.0208 1.56824 20.0091 1.8101 19.9284L5.22903 18.7892C6.56737 19.5154 8.05388 19.9265 9.57522 19.991C11.0965 20.0555 12.6125 19.7719 14.0076 19.1616C15.4026 18.5513 16.6399 17.6306 17.625 16.4696C18.6101 15.3086 19.3171 13.938 19.6921 12.4623C20.067 10.9867 20.1 9.44484 19.7885 7.95447C19.4771 6.46409 18.8293 5.06451 17.8948 3.86245C16.9602 2.66039 15.7635 1.68758 14.3958 1.01821C13.0282 0.348845 11.5257 0.000589078 10.003 2.20408e-07ZM10.003 18.8235C8.4518 18.8239 6.92793 18.4154 5.58495 17.6392C5.4954 17.5878 5.39406 17.5604 5.2908 17.5598C5.2275 17.5602 5.16465 17.5704 5.10451 17.5902L1.4385 18.8118C1.40395 18.8233 1.36687 18.825 1.33142 18.8166C1.29598 18.8082 1.26356 18.7902 1.2378 18.7644C1.21205 18.7387 1.19398 18.7062 1.18561 18.6708C1.17725 18.6353 1.17892 18.5983 1.19044 18.5637L2.41212 14.902C2.43855 14.8228 2.44785 14.739 2.4394 14.656C2.43094 14.573 2.40493 14.4928 2.36309 14.4206C1.38973 12.7393 0.998479 10.7838 1.25004 8.8575C1.50161 6.93118 2.38193 5.14173 3.75442 3.76675C5.12691 2.39177 6.91486 1.50813 8.84086 1.25293C10.7669 0.997736 12.7233 1.38524 14.4065 2.35533C16.0898 3.32541 17.4059 4.82385 18.1505 6.61817C18.8951 8.41249 19.0268 10.4024 18.5249 12.2791C18.0231 14.1559 16.9159 15.8146 15.375 16.9978C13.8342 18.1811 11.9458 18.8229 10.003 18.8235Z",fill:"#7FD387"})})}),e.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:t("provider.con_service.whatsapp")}),e.jsx("a",{href:`https://wa.me/${(j=i.userDetails)==null?void 0:j.phone}`,target:"_blank",rel:"noopener noreferrer",className:"font-['Poppins'] text-xs font-medium text-black",children:(C=i.userDetails)==null?void 0:C.phone})]})]})]})]}),e.jsx("div",{className:"mt-10 flex items-center justify-center gap-3",children:s!=null&&s.end_date&&new Date(s.end_date)>new Date?e.jsx(Z,{className:"!w-[145px] uppercase",onClick:B,children:t("buttons.remove")}):e.jsx($,{className:"!w-[145px] uppercase ",onClick:M,children:t("provider.con_service.republish")})})]})]})};export{pe as default};
