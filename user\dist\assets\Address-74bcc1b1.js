import{j as e}from"./@react-google-maps/api-ee55a349.js";import{R as d}from"./index-d54cffea.js";import{M as l}from"./index-243c4859.js";import{t}from"./i18next-7389dd8c.js";import{r as o,L as c}from"./vendor-b16525a8.js";import"./qr-scanner-cf010ec4.js";const h=({item:s,handleRemove:a})=>{const[r,n]=o.useState();return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"relative bg-white",children:[e.jsxs("div",{className:"  w-ful flex items-center justify-between bg-[#f7f7f7] px-6 py-2",children:[e.jsx("div",{className:" font-['Poppins'] text-2xl font-medium text-black",children:s!=null&&s.name?s==null?void 0:s.name:"N/A"}),e.jsxs("div",{className:" flex gap-6 ",children:[e.jsx(c,{to:`/user/edit-address/${s.id}`,className:" bg-transparent  text-right font-['Poppins'] text-[17px] font-semibold leading-snug text-[#4ecbf0]",children:t("user.address.edit")}),e.jsx("button",{onClick:()=>n(!0),className:" bg-transparent  text-right font-['Poppins'] text-[17px] font-semibold leading-snug text-[#fd5d5d]",children:t("user.address.remove")})]})]}),e.jsxs("div",{className:" flex items-center justify-between px-6 py-3 ",children:[e.jsx("div",{className:" font-['Poppins'] text-lg font-normal text-black",children:t("user.address.address")}),e.jsx("div",{className:" text-right font-['Poppins'] text-base font-medium text-[#bcbbc1]",children:s!=null&&s.description?s==null?void 0:s.description:"N/A"})]}),e.jsxs("div",{className:" flex items-center justify-between px-6 py-3 ",children:[e.jsx("div",{className:" font-['Poppins'] text-lg font-normal text-black",children:t("user.address.city")}),e.jsx("div",{className:" text-right font-['Poppins'] text-base font-medium text-[#bcbbc1]",children:s!=null&&s.city?s==null?void 0:s.city:"N/A"})]})]}),r&&e.jsx(l,{closeModal:()=>n(!1),children:e.jsxs("div",{className:" mt-5 flex h-max flex-col items-center justify-center gap-8 ",children:[e.jsx("div",{className:"text-center font-['Poppins'] text-2xl font-medium leading-[31.20px] text-black",children:t("user.address.confirmed")}),e.jsx("div",{className:" text-center font-['Poppins'] text-2xl font-medium leading-[31.20px] text-black",children:t("user.address.yes_remove")}),e.jsx(d,{onClick:()=>{n(!1),a(s.id)},className:" !w-[163px]",children:t("buttons.remove")})]})})]})};export{h as default};
