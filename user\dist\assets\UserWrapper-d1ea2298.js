import{j as r}from"./@react-google-maps/api-ee55a349.js";import{r as e}from"./vendor-b16525a8.js";import{_ as s}from"./qr-scanner-cf010ec4.js";import{S as o}from"./index-5a645c18.js";e.lazy(()=>s(()=>import("./UserHeader-3d5c6a43.js"),["assets/UserHeader-3d5c6a43.js","assets/@react-google-maps/api-ee55a349.js","assets/vendor-b16525a8.js","assets/react-icons-488361f7.js","assets/index-09a1718e.js","assets/react-confirm-alert-c06b7fb4.js","assets/moment-a9aaa855.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-518241d3.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-20049f1e.js","assets/@fortawesome/react-fontawesome-88fe485e.js","assets/@fortawesome/fontawesome-svg-core-4fa3e289.js","assets/@fortawesome/free-solid-svg-icons-0a9c4907.js","assets/@fortawesome/free-regular-svg-icons-0a88e957.js","assets/@fortawesome/free-brands-svg-icons-fae0dcac.js","assets/i18next-7389dd8c.js","assets/react-i18next-4a61273e.js","assets/index-8ff8a2aa.css","assets/index.esm-f0ed8f65.js"]));e.lazy(()=>s(()=>import("./TopHeader-67417c18.js"),["assets/TopHeader-67417c18.js","assets/@react-google-maps/api-ee55a349.js","assets/vendor-b16525a8.js","assets/index-09a1718e.js","assets/react-confirm-alert-c06b7fb4.js","assets/moment-a9aaa855.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-518241d3.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-20049f1e.js","assets/@fortawesome/react-fontawesome-88fe485e.js","assets/@fortawesome/fontawesome-svg-core-4fa3e289.js","assets/@fortawesome/free-solid-svg-icons-0a9c4907.js","assets/@fortawesome/free-regular-svg-icons-0a88e957.js","assets/@fortawesome/free-brands-svg-icons-fae0dcac.js","assets/i18next-7389dd8c.js","assets/react-i18next-4a61273e.js","assets/index-8ff8a2aa.css"]));const i=({children:t})=>r.jsx("div",{children:r.jsx(e.Suspense,{fallback:r.jsx("div",{className:"flex h-screen w-full items-center justify-center",children:r.jsx(o,{size:100,color:"#2CC9D5"})}),children:t})}),_=e.memo(i);export{_ as default};
