import{j as t}from"./@react-google-maps/api-afbf18d5.js";import{R as d,d as T}from"./vendor-f36d475e.js";import{g as L}from"./index-cf5e6bc7.js";import{S as $}from"./index-905b6573.js";import{L as f}from"./index-5deedf4a.js";import k from"./MkdListTableRow-2b89275e.js";import z from"./MkdListTableHead-8c16ca40.js";import{a as C}from"./index-bf8d79cc.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./react-i18next-1e3e6bc5.js";const H={primary:"#0ea5e9",signup:"signup",add:"add",edit:"edit",search:"search",custom:"custom",secondary:"#F594C9",lightInfo:"#29282980"},me=({table:h,tableTitle:_,onSort:I,loading:u,columns:x,actions:a,actionPostion:g,tableRole:Y,deleteItem:N,deleteLoading:S,actionId:j="id",showDeleteModal:E,currentTableData:n,setShowDeleteModal:w,handleTableCellChange:M,setSelectedItems:o})=>{const[y,R]=d.useState(null),[q,c]=d.useState(!1),[b,m]=d.useState(!1),[r,i]=d.useState([]);function A(e){var v;c(!0);const s=r;if((v=a==null?void 0:a.select)!=null&&v.multiple)if(s.includes(e)){const l=s.filter(p=>p!==e);i(()=>[...l]),o(l)}else{const l=[...s,e];i(()=>[...l]),o(l)}else if(s.includes(e)){const l=s.filter(p=>p!==e);i(()=>[...l]),o(l)}else{const l=[e];i(()=>[...l]),o(l)}console.log(e)}const F=()=>{if(m(e=>!e),b)i([]),o([]);else{const e=n.map(s=>s[j]);i(e),o(e)}};T();const O=async e=>{console.log("id >>",e),w(!0),R(e)};return d.useEffect(()=>{r.length<=0&&(c(!1),m(!1)),r.length===n.length&&(m(!0),c(!0)),r.length<n.length&&r.length>0&&m(!1)},[r,n]),t.jsxs(t.Fragment,{children:[t.jsx("div",{className:`${u?"":"overflow-x-auto"} border-b border-gray-200 shadow`,children:u?t.jsx("div",{className:"flex max-h-fit min-h-fit min-w-fit max-w-full items-center justify-center  py-5",children:t.jsx($,{size:50,color:H.primary})}):t.jsx(t.Fragment,{children:t.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[t.jsx("thead",{className:"bg-gray-50",children:t.jsx(f,{children:t.jsx(z,{onSort:I,columns:x,actions:a,actionPostion:g,areAllRowsSelected:b,handleSelectAll:F})})}),t.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:n.map((e,s)=>t.jsx(f,{children:t.jsx(k,{i:s,row:e,columns:x,actions:a,actionPostion:g,actionId:j,handleTableCellChange:M,handleSelectRow:A,selectedIds:r,setDeleteId:O},s)},s))})]})})}),t.jsx(f,{children:t.jsx(C,{open:E,actionHandler:()=>{N(y)},closeModalFunction:()=>{R(null),w(!1)},title:`Delete ${L(h)} `,message:`You are about to delete ${L(h)} ${y}, note that this action is irreversible`,acceptText:"DELETE",rejectText:"CANCEL",loading:S})})]})};export{me as default};
