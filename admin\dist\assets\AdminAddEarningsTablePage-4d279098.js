import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as o,b as k}from"./vendor-4f06b3f4.js";import{u as A}from"./react-hook-form-f3d72793.js";import{o as E}from"./yup-2324a46a.js";import{c as w,a as u}from"./yup-17027d7a.js";import{G as I,A as _,M as N,s as T,t as R}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as c}from"./MkdInput-ff3aa862.js";import{I as D}from"./InteractiveButton-8f7d74ee.js";import"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";const ne=({setSidebar:h})=>{const{dispatch:m}=o.useContext(I),x=w({provider_id:u(),task_id:u(),amount:u()}).required(),{dispatch:y}=o.useContext(_),[f,P]=o.useState({}),[g,n]=o.useState(!1),v=k(),{register:l,handleSubmit:S,setError:b,setValue:C,formState:{errors:d}}=A({resolver:E(x)});o.useState([]);const j=async r=>{let p=new N;n(!0);try{for(let s in f){let a=new FormData;a.append("file",f[s].file);let i=await p.uploadImage(a);r[s]=i.url}p.setTable("earnings");const e=await p.callRestAPI({provider_id:r.provider_id,task_id:r.task_id,amount:r.amount},"POST");if(!e.error)T(m,"Added"),v("/admin/earnings"),h(!1),m({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(e.validation){const s=Object.keys(e.validation);for(let a=0;a<s.length;a++){const i=s[a];b(i,{type:"manual",message:e.validation[i]})}}n(!1)}catch(e){n(!1),console.log("Error",e),b("provider_id",{type:"manual",message:e.message}),R(y,e.message)}};return o.useEffect(()=>{m({type:"SETPATH",payload:{path:"earnings"}})},[]),t.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Add Earnings"}),t.jsxs("form",{className:" w-full max-w-lg",onSubmit:S(j),children:[t.jsx(c,{type:"number",page:"add",name:"provider_id",errors:d,label:"Provider Id",placeholder:"Provider Id",register:l,className:""}),t.jsx(c,{type:"number",page:"add",name:"task_id",errors:d,label:"Task Id",placeholder:"Task Id",register:l,className:""}),t.jsx(c,{type:"number",page:"add",name:"amount",errors:d,label:"Amount",placeholder:"Amount",register:l,className:""}),t.jsx(D,{type:"submit",loading:g,disabled:g,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{ne as default};
