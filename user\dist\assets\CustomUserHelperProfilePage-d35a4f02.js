import{j as o}from"./@react-google-maps/api-ee55a349.js";import{i as r,r as t,R as m}from"./vendor-b16525a8.js";import"./index-9ef1c042.js";import{M as e,A as p,G as i}from"./index-09a1718e.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./react-i18next-4a61273e.js";new e;const G=()=>(r(),t.useContext(p),t.useContext(i),m.useEffect(()=>{},[]),o.jsx("div",{className:" shadow-md rounded  mx-auto p-5"}));export{G as default};
