import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as s,b as A}from"./vendor-4f06b3f4.js";import{u as N}from"./react-hook-form-f3d72793.js";import{o as w}from"./yup-2324a46a.js";import{c as v,a as n}from"./yup-17027d7a.js";import{G as E,A as D,M as R,s as k,t as z}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as p}from"./MkdInput-ff3aa862.js";import{I}from"./InteractiveButton-8f7d74ee.js";import"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";const me=({setSidebar:h})=>{const{dispatch:d}=s.useContext(E),g=v({name:n(),amount:n(),description:n(),subsidized:n()}).required(),{dispatch:y}=s.useContext(D),[f,P]=s.useState({}),[b,u]=s.useState(!1);A();const{register:r,handleSubmit:S,setError:x,setValue:T,formState:{errors:m}}=N({resolver:w(g)});s.useState([]);const j=async o=>{let c=new R;u(!0);try{for(let i in f){let a=new FormData;a.append("file",f[i].file);let l=await c.uploadImage(a);o[i]=l.url}c.setTable("plan");const e=await c.callRestAPI({name:o.name,amount:o.amount,description:o.description,subsidized:o.subsidized},"POST");if(!e.error)k(d,"Added"),h(!1),d({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(e.validation){const i=Object.keys(e.validation);for(let a=0;a<i.length;a++){const l=i[a];x(l,{type:"manual",message:e.validation[l]})}}u(!1)}catch(e){u(!1),console.log("Error",e),x("name",{type:"manual",message:e.message}),z(y,e.message)}};return s.useEffect(()=>{d({type:"SETPATH",payload:{path:"plan"}})},[]),t.jsxs("div",{className:" mx-auto rounded  p-5 shadow-md",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Add Plan"}),t.jsxs("form",{className:" w-full max-w-lg",onSubmit:S(j),children:[t.jsx(p,{type:"text",page:"add",name:"name",errors:m,label:"Name",placeholder:"Name",register:r,className:""}),t.jsx(p,{type:"number",page:"add",name:"amount",errors:m,label:"Amount",placeholder:"Amount",register:r,className:""}),t.jsx(p,{type:"text",page:"add",name:"description",errors:m,label:"Description",placeholder:"Description",register:r,className:""}),t.jsx(p,{type:"dropdown",page:"add",name:"subsidized",errors:m,label:"Subsidized",placeholder:"Subsidized",register:r,className:"",options:[{value:0,name:"No"},{value:1,name:"Yes"}]}),t.jsx(I,{type:"submit",loading:b,disabled:b,className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",children:"Submit"})]})]})};export{me as default};
