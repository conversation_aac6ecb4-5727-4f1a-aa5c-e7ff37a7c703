import{j as s}from"./@react-google-maps/api-ac2f9d6f.js";import{R as r,h as x}from"./vendor-4f06b3f4.js";import"./yup-17027d7a.js";import{M as p,G as l,t as f}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{S as h}from"./index-2d8231e7.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";let c=new p;const I=()=>{const{dispatch:m}=r.useContext(l),{dispatch:o}=r.useContext(l),[e,d]=r.useState({}),[n,i]=r.useState(!0),a=x();return r.useEffect(function(){(async function(){try{i(!0),c.setTable("provider_skill");const t=await c.callRestAPI({id:Number(a==null?void 0:a.id),join:""},"GET");t.error||(d(t.model),i(!1))}catch(t){i(!1),console.log("error",t),f(o,t.message)}})()},[]),r.useEffect(()=>{m({type:"SHOW_BACKBUTTON",payload:{showBackButton:!0}})},[]),s.jsx("div",{className:" mx-auto rounded  p-5 shadow-md",children:n?s.jsx(h,{}):s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Service Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.service_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Provider Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.provider_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Price"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.price})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"mb-4 flex",children:[s.jsx("div",{className:"flex-1",children:"Status"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.status})]})})]})})};export{I as default};
