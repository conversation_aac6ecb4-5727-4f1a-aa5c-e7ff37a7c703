import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{r,h as B,d as F,R as S}from"./vendor-f36d475e.js";import"./index-5deedf4a.js";import{M as H,A as O,G as U,s as V}from"./index-cf5e6bc7.js";import{B as K,a as i}from"./index-895fa99b.js";import{M as h}from"./index-bf8d79cc.js";import{S as q}from"./index-65bc3378.js";import{_ as A}from"./qr-scanner-cf010ec4.js";import{P as J}from"./index-04e38e92.js";import{t as a}from"./i18next-7389dd8c.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-1e3e6bc5.js";r.lazy(()=>A(()=>import("./PickAddressFrom-53511362.js"),["assets/PickAddressFrom-53511362.js","assets/@react-google-maps/api-afbf18d5.js","assets/vendor-f36d475e.js","assets/@vis.gl/react-google-maps-e1ad9da7.js","assets/@googlemaps/markerclusterer-2f9a3a53.js","assets/index-895fa99b.js","assets/qr-scanner-cf010ec4.js","assets/index-cf5e6bc7.js","assets/react-confirm-alert-2487dba8.js","assets/moment-a9aaa855.js","assets/@react-pdf-viewer/core-9d395990.js","assets/@headlessui/react-46b39f71.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-b3bb6c9d.js","assets/@fortawesome/react-fontawesome-eb6bfecd.js","assets/@fortawesome/fontawesome-svg-core-4fa3e289.js","assets/@fortawesome/free-solid-svg-icons-0a9c4907.js","assets/@fortawesome/free-regular-svg-icons-0a88e957.js","assets/@fortawesome/free-brands-svg-icons-fae0dcac.js","assets/i18next-7389dd8c.js","assets/react-i18next-1e3e6bc5.js","assets/index-d64010cc.css","assets/index-65bc3378.js"]));const Q=r.lazy(()=>A(()=>import("./ShowAlertsMap-99168dcd.js"),["assets/ShowAlertsMap-99168dcd.js","assets/@react-google-maps/api-afbf18d5.js","assets/vendor-f36d475e.js","assets/@vis.gl/react-google-maps-e1ad9da7.js","assets/@googlemaps/markerclusterer-2f9a3a53.js","assets/index-cf5e6bc7.js","assets/react-confirm-alert-2487dba8.js","assets/moment-a9aaa855.js","assets/@react-pdf-viewer/core-9d395990.js","assets/qr-scanner-cf010ec4.js","assets/@headlessui/react-46b39f71.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-b3bb6c9d.js","assets/@fortawesome/react-fontawesome-eb6bfecd.js","assets/@fortawesome/fontawesome-svg-core-4fa3e289.js","assets/@fortawesome/free-solid-svg-icons-0a9c4907.js","assets/@fortawesome/free-regular-svg-icons-0a88e957.js","assets/@fortawesome/free-brands-svg-icons-fae0dcac.js","assets/i18next-7389dd8c.js","assets/react-i18next-1e3e6bc5.js","assets/index-d64010cc.css"])),g=new H,W={center:{lat:40.7,lng:-74},zoom:12},je=()=>{B(),r.useContext(O);const{state:X,dispatch:E}=r.useContext(U),[Y,n]=r.useState(!1),[v,d]=r.useState(!1);r.useState(!1),r.useState();const[_,m]=r.useState(!1),[j,x]=r.useState(!1),[N,p]=r.useState(!1),[k,w]=r.useState([]),[t,C]=r.useState(null),[f,D]=r.useState(0),[T,u]=r.useState(!0);r.useState(!1);const[o,y]=r.useState({lat:39.7827,lng:-89.6501}),[L,b]=r.useState(W),R=F(),$=()=>{navigator.geolocation?navigator.geolocation.getCurrentPosition(s=>{const l={lat:s.coords.latitude,lng:s.coords.longitude};b({center:l,zoom:15}),y(l)},s=>{console.log(s.message)}):console.error("Geolocation is not supported by this browser.")};console.log("selectedItem",t);const G=async()=>{try{u(!0);const s=await g.callRawAPI(`/v3/api/custom/chumpchange/provider/alert/active?lat=${o!=null&&o.lat?o==null?void 0:o.lat:0}&lng=${o!=null&&o.lng?o==null?void 0:o.lng:0}`,{},"GET");s.error||w(s==null?void 0:s.data),u(!1)}catch(s){u(!1),console.log("error >> ",s)}},P=async s=>{try{d(!0),n(!0),z(),(await g.callRawAPI(`/v3/api/custom/chumpchange/common/alert-ping/${t.id}?distance=${s}`,{},"GET")).error||(V(E,"Ping successful"),R("/provider/home"))}catch(l){n(!1),d(!1),console.log("error >> ",l)}},z=async()=>{try{const s=await g.callRawAPI("/v3/api/custom/chumpchange/common/alert-poll",{},"GET");s.error||(s.data.response==1?m(!0):s.data.response==2?x(!0):p(!0)),d(!1),n(!1)}catch(s){n(!1),console.error("Error fetching alert:",s)}};S.useEffect(()=>{$()},[]),S.useEffect(()=>{(async()=>G())()},[o]);const c=Math.floor(f),M=100;return console.log({distanceToUser:c}),e.jsxs("div",{className:"py-5",children:[e.jsxs("div",{className:"relative flex w-full items-center justify-center px-5 ",children:[e.jsx("div",{className:" absolute left-5 top-0 ",children:e.jsx(K,{})}),e.jsxs("div",{className:"",children:[e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:a("provider.my_alerts.title")}),e.jsx("div",{className:"text-center font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:t!=null&&t.service_name?t==null?void 0:t.service_name:a("provider.my_alerts.none")})]})]}),T?e.jsx("div",{className:"flex min-h-screen w-full items-center justify-center",children:e.jsx(q,{})}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:`${t?"block":"hidden"}`,children:e.jsx(J,{userData:{first_name:t==null?void 0:t.user_first_name,last_name:t==null?void 0:t.user_last_name,create_at:t==null?void 0:t.user_create_date,operating_city:t==null?void 0:t.user_operating_city,photo:t==null?void 0:t.user_photo}})}),e.jsxs("div",{className:" mt-9  px-5",children:[e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsxs("div",{className:"font-['Poppins'] text-lg font-medium text-[#8080a3]",children:[a("provider.my_alerts.alert_for"),":"]}),e.jsx("div",{className:"text-right font-['Poppins'] text-base font-medium text-black",children:t!=null&&t.service_name?t==null?void 0:t.service_name:"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between ",children:[e.jsxs("div",{className:"font-['Poppins'] text-lg font-medium text-[#8080a3]",children:[a("provider.my_alerts.distance"),":"]}),e.jsxs("div",{className:"text-right font-['Poppins'] text-base font-medium text-black",children:[f?f.toFixed(2):0," meters"]})]})]}),e.jsx("div",{className:"mt-5 h-[400px] w-full ",children:e.jsx(Q,{alertList:k,setselectedItem:C,selectedItem:t,setUserDistance:D,position:o,setPosition:y,cameraProps:L,setCameraProps:b})}),e.jsxs(e.Fragment,{children:[e.jsx("div",{className:" mt-9  px-5",children:e.jsxs("div",{className:" py-3 ",children:[e.jsxs("div",{className:"font-['Poppins'] text-lg font-medium text-[#8080a3]",children:[a("provider.my_alerts.message"),":"]}),e.jsx("div",{className:"mt-3 font-['Poppins'] text-base font-light leading-tight text-black",children:t!=null&&t.message?t==null?void 0:t.message:"N/A"})]})}),e.jsx("div",{className:`mt-10 px-5 ${t?" block ":" hidden "} `,children:e.jsxs(i,{onClick:()=>P(`${c}`),className:" disabled:bg-[#E1E2E3] disabled:text-[#A3A9AE]",disabled:v||c<M,children:[a("provider.my_alerts.ping_client")," - ",a("provider.my_alerts.i_am_nearby")]})}),e.jsx("div",{className:`mt-4 px-5 ${t?" block ":" hidden "} `,children:e.jsxs(i,{onClick:()=>P("is outside"),className:" disabled:bg-[#E1E2E3] disabled:text-[#A3A9AE]",disabled:v||!(c<M),children:[a("provider.my_alerts.ping_client")," - ",a("provider.my_alerts.i_am_outside")]})})]})]}),e.jsxs("div",{className:_?"block":"hidden",children:[" ",e.jsx(h,{closeModal:()=>m(!1),modal:_,children:e.jsxs("div",{className:" mt-5 flex h-max flex-col items-center justify-center gap-8 ",children:[e.jsx("div",{className:"text-center font-['Poppins'] text-2xl font-medium leading-[31.20px] text-black",children:a("provider.my_alerts.confirmed")}),e.jsx("div",{className:"text-center font-['Poppins'] text-2xl font-medium leading-[31.20px] text-black",dangerouslySetInnerHTML:{__html:a("provider.my_alerts.need_product")}}),e.jsx(i,{onClick:()=>m(!1),className:" !w-[163px]",children:a("buttons.done")})]})})]}),e.jsx("div",{className:N?"block":"hidden",children:e.jsx(h,{closeModal:()=>p(!1),modal:N,children:e.jsxs("div",{className:" mt-5 flex h-max flex-col items-center justify-center gap-8 ",children:[e.jsx("div",{className:" text-center font-['Poppins'] text-2xl font-medium leading-[31.20px] text-[#fd5d5d]",children:a("provider.my_alerts.declined")}),e.jsx("div",{className:"text-center font-['Poppins'] text-2xl font-medium leading-[31.20px] text-[#fd5d5d]",dangerouslySetInnerHTML:{__html:a("provider.my_alerts.no_longer")}}),e.jsx(i,{onClick:()=>p(!1),className:" !w-[163px]",children:a("buttons.done")})]})})}),e.jsx("div",{className:j?"block":"hidden",children:e.jsx(h,{closeModal:()=>x(!1),modal:j,children:e.jsxs("div",{className:" mt-5 flex h-max flex-col items-center justify-center gap-8 ",children:[e.jsx("div",{className:" text-center font-['Poppins'] text-2xl font-medium capitalize leading-[31.20px] text-[#fd5d5d] ",children:a("provider.my_alerts.offline")}),e.jsx("div",{className:"text-center font-['Poppins'] text-2xl font-medium leading-[31.20px] text-[#fd5d5d]",dangerouslySetInnerHTML:{__html:a("provider.my_alerts.offline_desc")}}),e.jsx(i,{onClick:()=>x(!1),className:" !w-[163px]",children:a("buttons.done")})]})})})]})};export{je as default};
