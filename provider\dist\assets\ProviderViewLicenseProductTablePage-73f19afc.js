import{j as s}from"./@react-google-maps/api-afbf18d5.js";import{R as a,h as o}from"./vendor-f36d475e.js";import"./yup-2f6e2476.js";import{M as x,G as c,t as f}from"./index-cf5e6bc7.js";import{S as h}from"./index-bec80226.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";import"./react-i18next-1e3e6bc5.js";let l=new x;const V=()=>{a.useContext(c);const{dispatch:m}=a.useContext(c),[e,d]=a.useState({}),[n,r]=a.useState(!0),i=o();return a.useEffect(function(){(async function(){try{r(!0),l.setTable("license_product");const t=await l.callRestAPI({id:Number(i==null?void 0:i.id),join:""},"GET");t.error||(d(t.model),r(!1))}catch(t){r(!1),console.log("error",t),f(m,t.message)}})()},[]),s.jsx("div",{className:" shadow-md rounded  mx-auto p-5",children:n?s.jsx(h,{}):s.jsxs(s.Fragment,{children:[s.jsx("h4",{className:"text-2xl font-medium",children:"View License Product"}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Product Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.product_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"License Key Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.license_key_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Create At"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.create_at})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Update At"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.update_at})]})})]})})};export{V as default};
