import{R as B}from"./vendor-4f06b3f4.js";var fe=e=>e.type==="checkbox",ee=e=>e instanceof Date,L=e=>e==null;const Ze=e=>typeof e=="object";var D=e=>!L(e)&&!Array.isArray(e)&&Ze(e)&&!ee(e),gr=e=>D(e)&&e.target?fe(e.target)?e.target.checked:e.target.value:e,hr=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,vr=(e,s)=>e.has(hr(s)),_r=e=>{const s=e.constructor&&e.constructor.prototype;return D(s)&&s.hasOwnProperty("isPrototypeOf")},Te=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function M(e){let s;const t=Array.isArray(e);if(e instanceof Date)s=new Date(e);else if(e instanceof Set)s=new Set(e);else if(!(Te&&(e instanceof Blob||e instanceof FileList))&&(t||D(e)))if(s=t?[]:{},!t&&!_r(e))s=e;else for(const l in e)e.hasOwnProperty(l)&&(s[l]=M(e[l]));else return e;return s}var Fe=e=>Array.isArray(e)?e.filter(Boolean):[],w=e=>e===void 0,d=(e,s,t)=>{if(!s||!D(e))return t;const l=Fe(s.split(/[,[\].]+?/)).reduce((n,u)=>L(n)?n:n[u],e);return w(l)||l===e?w(e[s])?t:e[s]:l},W=e=>typeof e=="boolean",Oe=e=>/^\w*$/.test(e),er=e=>Fe(e.replace(/["|']|\]/g,"").split(/\.|\[/)),V=(e,s,t)=>{let l=-1;const n=Oe(s)?[s]:er(s),u=n.length,y=u-1;for(;++l<u;){const h=n[l];let p=t;if(l!==y){const q=e[h];p=D(q)||Array.isArray(q)?q:isNaN(+n[l+1])?{}:[]}if(h==="__proto__")return;e[h]=p,e=e[h]}return e};const He={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},I={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},z={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"};B.createContext(null);var br=(e,s,t,l=!0)=>{const n={defaultValues:s._defaultValues};for(const u in e)Object.defineProperty(n,u,{get:()=>{const y=u;return s._proxyFormState[y]!==I.all&&(s._proxyFormState[y]=!l||I.all),t&&(t[y]=!0),e[y]}});return n},C=e=>D(e)&&!Object.keys(e).length,Fr=(e,s,t,l)=>{t(e);const{name:n,...u}=e;return C(u)||Object.keys(u).length>=Object.keys(s).length||Object.keys(u).find(y=>s[y]===(!l||I.all))},ge=e=>Array.isArray(e)?e:[e];function Ar(e){const s=B.useRef(e);s.current=e,B.useEffect(()=>{const t=!e.disabled&&s.current.subject&&s.current.subject.subscribe({next:s.current.next});return()=>{t&&t.unsubscribe()}},[e.disabled])}var $=e=>typeof e=="string",Vr=(e,s,t,l,n)=>$(e)?(l&&s.watch.add(e),d(t,e,n)):Array.isArray(e)?e.map(u=>(l&&s.watch.add(u),d(t,u))):(l&&(s.watchAll=!0),t),xr=(e,s,t,l,n)=>s?{...t[e],types:{...t[e]&&t[e].types?t[e].types:{},[l]:n||!0}}:{},$e=e=>({isOnSubmit:!e||e===I.onSubmit,isOnBlur:e===I.onBlur,isOnChange:e===I.onChange,isOnAll:e===I.all,isOnTouch:e===I.onTouched}),je=(e,s,t)=>!t&&(s.watchAll||s.watch.has(e)||[...s.watch].some(l=>e.startsWith(l)&&/^\.\w+/.test(e.slice(l.length))));const oe=(e,s,t,l)=>{for(const n of t||Object.keys(e)){const u=d(e,n);if(u){const{_f:y,...h}=u;if(y){if(y.refs&&y.refs[0]&&s(y.refs[0],n)&&!l)return!0;if(y.ref&&s(y.ref,y.name)&&!l)return!0;if(oe(h,s))break}else if(D(h)&&oe(h,s))break}}};var wr=(e,s,t)=>{const l=ge(d(e,t));return V(l,"root",s[t]),V(e,t,l),e},Le=e=>e.type==="file",H=e=>typeof e=="function",ve=e=>{if(!Te)return!1;const s=e?e.ownerDocument:0;return e instanceof(s&&s.defaultView?s.defaultView.HTMLElement:HTMLElement)},he=e=>$(e),Ce=e=>e.type==="radio",_e=e=>e instanceof RegExp;const Ke={value:!1,isValid:!1},Ye={value:!0,isValid:!0};var rr=e=>{if(Array.isArray(e)){if(e.length>1){const s=e.filter(t=>t&&t.checked&&!t.disabled).map(t=>t.value);return{value:s,isValid:!!s.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!w(e[0].attributes.value)?w(e[0].value)||e[0].value===""?Ye:{value:e[0].value,isValid:!0}:Ye:Ke}return Ke};const ze={isValid:!1,value:null};var tr=e=>Array.isArray(e)?e.reduce((s,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:s,ze):ze;function Ge(e,s,t="validate"){if(he(e)||Array.isArray(e)&&e.every(he)||W(e)&&!e)return{type:t,message:he(e)?e:"",ref:s}}var se=e=>D(e)&&!_e(e)?e:{value:e,message:""},Je=async(e,s,t,l,n)=>{const{ref:u,refs:y,required:h,maxLength:p,minLength:q,min:x,max:b,pattern:ce,validate:G,name:N,valueAsNumber:Ae,mount:j,disabled:J}=e._f,F=d(s,N);if(!j||J)return{};const K=y?y[0]:u,Y=_=>{l&&K.reportValidity&&(K.setCustomValidity(W(_)?"":_||""),K.reportValidity())},k={},re=Ce(u),de=fe(u),X=re||de,te=(Ae||Le(u))&&w(u.value)&&w(F)||ve(u)&&u.value===""||F===""||Array.isArray(F)&&!F.length,U=xr.bind(null,N,t,k),ye=(_,A,m,O=z.maxLength,P=z.minLength)=>{const R=_?A:m;k[N]={type:_?O:P,message:R,ref:u,...U(_?O:P,R)}};if(n?!Array.isArray(F)||!F.length:h&&(!X&&(te||L(F))||W(F)&&!F||de&&!rr(y).isValid||re&&!tr(y).isValid)){const{value:_,message:A}=he(h)?{value:!!h,message:h}:se(h);if(_&&(k[N]={type:z.required,message:A,ref:K,...U(z.required,A)},!t))return Y(A),k}if(!te&&(!L(x)||!L(b))){let _,A;const m=se(b),O=se(x);if(!L(F)&&!isNaN(F)){const P=u.valueAsNumber||F&&+F;L(m.value)||(_=P>m.value),L(O.value)||(A=P<O.value)}else{const P=u.valueAsDate||new Date(F),R=ue=>new Date(new Date().toDateString()+" "+ue),ie=u.type=="time",ae=u.type=="week";$(m.value)&&F&&(_=ie?R(F)>R(m.value):ae?F>m.value:P>new Date(m.value)),$(O.value)&&F&&(A=ie?R(F)<R(O.value):ae?F<O.value:P<new Date(O.value))}if((_||A)&&(ye(!!_,m.message,O.message,z.max,z.min),!t))return Y(k[N].message),k}if((p||q)&&!te&&($(F)||n&&Array.isArray(F))){const _=se(p),A=se(q),m=!L(_.value)&&F.length>+_.value,O=!L(A.value)&&F.length<+A.value;if((m||O)&&(ye(m,_.message,A.message),!t))return Y(k[N].message),k}if(ce&&!te&&$(F)){const{value:_,message:A}=se(ce);if(_e(_)&&!F.match(_)&&(k[N]={type:z.pattern,message:A,ref:u,...U(z.pattern,A)},!t))return Y(A),k}if(G){if(H(G)){const _=await G(F,s),A=Ge(_,K);if(A&&(k[N]={...A,...U(z.validate,A.message)},!t))return Y(A.message),k}else if(D(G)){let _={};for(const A in G){if(!C(_)&&!t)break;const m=Ge(await G[A](F,s),K,A);m&&(_={...m,...U(A,m.message)},Y(m.message),t&&(k[N]=_))}if(!C(_)&&(k[N]={ref:K,..._},!t))return k}}return Y(!0),k};function Dr(e,s){const t=s.slice(0,-1).length;let l=0;for(;l<t;)e=w(e)?l++:e[s[l++]];return e}function kr(e){for(const s in e)if(e.hasOwnProperty(s)&&!w(e[s]))return!1;return!0}function E(e,s){const t=Array.isArray(s)?s:Oe(s)?[s]:er(s),l=t.length===1?e:Dr(e,t),n=t.length-1,u=t[n];return l&&delete l[u],n!==0&&(D(l)&&C(l)||Array.isArray(l)&&kr(l))&&E(e,t.slice(0,-1)),e}var ke=()=>{let e=[];return{get observers(){return e},next:n=>{for(const u of e)u.next&&u.next(n)},subscribe:n=>(e.push(n),{unsubscribe:()=>{e=e.filter(u=>u!==n)}}),unsubscribe:()=>{e=[]}}},Se=e=>L(e)||!Ze(e);function Q(e,s){if(Se(e)||Se(s))return e===s;if(ee(e)&&ee(s))return e.getTime()===s.getTime();const t=Object.keys(e),l=Object.keys(s);if(t.length!==l.length)return!1;for(const n of t){const u=e[n];if(!l.includes(n))return!1;if(n!=="ref"){const y=s[n];if(ee(u)&&ee(y)||D(u)&&D(y)||Array.isArray(u)&&Array.isArray(y)?!Q(u,y):u!==y)return!1}}return!0}var sr=e=>e.type==="select-multiple",mr=e=>Ce(e)||fe(e),me=e=>ve(e)&&e.isConnected,ir=e=>{for(const s in e)if(H(e[s]))return!0;return!1};function be(e,s={}){const t=Array.isArray(e);if(D(e)||t)for(const l in e)Array.isArray(e[l])||D(e[l])&&!ir(e[l])?(s[l]=Array.isArray(e[l])?[]:{},be(e[l],s[l])):L(e[l])||(s[l]=!0);return s}function ar(e,s,t){const l=Array.isArray(e);if(D(e)||l)for(const n in e)Array.isArray(e[n])||D(e[n])&&!ir(e[n])?w(s)||Se(t[n])?t[n]=Array.isArray(e[n])?be(e[n],[]):{...be(e[n])}:ar(e[n],L(s)?{}:s[n],t[n]):t[n]=!Q(e[n],s[n]);return t}var le=(e,s)=>ar(e,s,be(s)),ur=(e,{valueAsNumber:s,valueAsDate:t,setValueAs:l})=>w(e)?e:s?e===""?NaN:e&&+e:t&&$(e)?new Date(e):l?l(e):e;function Ee(e){const s=e.ref;if(!(e.refs?e.refs.every(t=>t.disabled):s.disabled))return Le(s)?s.files:Ce(s)?tr(e.refs).value:sr(s)?[...s.selectedOptions].map(({value:t})=>t):fe(s)?rr(e.refs).value:ur(w(s.value)?e.ref.value:s.value,e)}var Er=(e,s,t,l)=>{const n={};for(const u of e){const y=d(s,u);y&&V(n,u,y._f)}return{criteriaMode:t,names:[...e],fields:n,shouldUseNativeValidation:l}},ne=e=>w(e)?e:_e(e)?e.source:D(e)?_e(e.value)?e.value.source:e.value:e;const Qe="AsyncFunction";var Sr=e=>(!e||!e.validate)&&!!(H(e.validate)&&e.validate.constructor.name===Qe||D(e.validate)&&Object.values(e.validate).find(s=>s.constructor.name===Qe)),Tr=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate);function Xe(e,s,t){const l=d(e,t);if(l||Oe(t))return{error:l,name:t};const n=t.split(".");for(;n.length;){const u=n.join("."),y=d(s,u),h=d(e,u);if(y&&!Array.isArray(y)&&t!==u)return{name:t};if(h&&h.type)return{name:u,error:h};n.pop()}return{name:t}}var Or=(e,s,t,l,n)=>n.isOnAll?!1:!t&&n.isOnTouch?!(s||e):(t?l.isOnBlur:n.isOnBlur)?!e:(t?l.isOnChange:n.isOnChange)?e:!0,Lr=(e,s)=>!Fe(d(e,s)).length&&E(e,s);const Cr={mode:I.onSubmit,reValidateMode:I.onChange,shouldFocusError:!0};function Nr(e={}){let s={...Cr,...e},t={submitCount:0,isDirty:!1,isLoading:H(s.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:s.errors||{},disabled:s.disabled||!1},l={},n=D(s.defaultValues)||D(s.values)?M(s.defaultValues||s.values)||{}:{},u=s.shouldUnregister?{}:M(n),y={action:!1,mount:!1,watch:!1},h={mount:new Set,unMount:new Set,array:new Set,watch:new Set},p,q=0;const x={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},b={values:ke(),array:ke(),state:ke()},ce=$e(s.mode),G=$e(s.reValidateMode),N=s.criteriaMode===I.all,Ae=r=>i=>{clearTimeout(q),q=setTimeout(r,i)},j=async r=>{if(!s.disabled&&(x.isValid||r)){const i=s.resolver?C((await X()).errors):await U(l,!0);i!==t.isValid&&b.state.next({isValid:i})}},J=(r,i)=>{!s.disabled&&(x.isValidating||x.validatingFields)&&((r||Array.from(h.mount)).forEach(a=>{a&&(i?V(t.validatingFields,a,i):E(t.validatingFields,a))}),b.state.next({validatingFields:t.validatingFields,isValidating:!C(t.validatingFields)}))},F=(r,i=[],a,c,f=!0,o=!0)=>{if(c&&a&&!s.disabled){if(y.action=!0,o&&Array.isArray(d(l,r))){const g=a(d(l,r),c.argA,c.argB);f&&V(l,r,g)}if(o&&Array.isArray(d(t.errors,r))){const g=a(d(t.errors,r),c.argA,c.argB);f&&V(t.errors,r,g),Lr(t.errors,r)}if(x.touchedFields&&o&&Array.isArray(d(t.touchedFields,r))){const g=a(d(t.touchedFields,r),c.argA,c.argB);f&&V(t.touchedFields,r,g)}x.dirtyFields&&(t.dirtyFields=le(n,u)),b.state.next({name:r,isDirty:_(r,i),dirtyFields:t.dirtyFields,errors:t.errors,isValid:t.isValid})}else V(u,r,i)},K=(r,i)=>{V(t.errors,r,i),b.state.next({errors:t.errors})},Y=r=>{t.errors=r,b.state.next({errors:t.errors,isValid:!1})},k=(r,i,a,c)=>{const f=d(l,r);if(f){const o=d(u,r,w(a)?d(n,r):a);w(o)||c&&c.defaultChecked||i?V(u,r,i?o:Ee(f._f)):O(r,o),y.mount&&j()}},re=(r,i,a,c,f)=>{let o=!1,g=!1;const v={name:r};if(!s.disabled){const S=!!(d(l,r)&&d(l,r)._f&&d(l,r)._f.disabled);if(!a||c){x.isDirty&&(g=t.isDirty,t.isDirty=v.isDirty=_(),o=g!==v.isDirty);const T=S||Q(d(n,r),i);g=!!(!S&&d(t.dirtyFields,r)),T||S?E(t.dirtyFields,r):V(t.dirtyFields,r,!0),v.dirtyFields=t.dirtyFields,o=o||x.dirtyFields&&g!==!T}if(a){const T=d(t.touchedFields,r);T||(V(t.touchedFields,r,a),v.touchedFields=t.touchedFields,o=o||x.touchedFields&&T!==a)}o&&f&&b.state.next(v)}return o?v:{}},de=(r,i,a,c)=>{const f=d(t.errors,r),o=x.isValid&&W(i)&&t.isValid!==i;if(e.delayError&&a?(p=Ae(()=>K(r,a)),p(e.delayError)):(clearTimeout(q),p=null,a?V(t.errors,r,a):E(t.errors,r)),(a?!Q(f,a):f)||!C(c)||o){const g={...c,...o&&W(i)?{isValid:i}:{},errors:t.errors,name:r};t={...t,...g},b.state.next(g)}},X=async r=>{J(r,!0);const i=await s.resolver(u,s.context,Er(r||h.mount,l,s.criteriaMode,s.shouldUseNativeValidation));return J(r),i},te=async r=>{const{errors:i}=await X(r);if(r)for(const a of r){const c=d(i,a);c?V(t.errors,a,c):E(t.errors,a)}else t.errors=i;return i},U=async(r,i,a={valid:!0})=>{for(const c in r){const f=r[c];if(f){const{_f:o,...g}=f;if(o){const v=h.array.has(o.name),S=f._f&&Sr(f._f);S&&x.validatingFields&&J([c],!0);const T=await Je(f,u,N,s.shouldUseNativeValidation&&!i,v);if(S&&x.validatingFields&&J([c]),T[o.name]&&(a.valid=!1,i))break;!i&&(d(T,o.name)?v?wr(t.errors,T,o.name):V(t.errors,o.name,T[o.name]):E(t.errors,o.name))}!C(g)&&await U(g,i,a)}}return a.valid},ye=()=>{for(const r of h.unMount){const i=d(l,r);i&&(i._f.refs?i._f.refs.every(a=>!me(a)):!me(i._f.ref))&&Ve(r)}h.unMount=new Set},_=(r,i)=>!s.disabled&&(r&&i&&V(u,r,i),!Q(Ne(),n)),A=(r,i,a)=>Vr(r,h,{...y.mount?u:w(i)?n:$(r)?{[r]:i}:i},a,i),m=r=>Fe(d(y.mount?u:n,r,e.shouldUnregister?d(n,r,[]):[])),O=(r,i,a={})=>{const c=d(l,r);let f=i;if(c){const o=c._f;o&&(!o.disabled&&V(u,r,ur(i,o)),f=ve(o.ref)&&L(i)?"":i,sr(o.ref)?[...o.ref.options].forEach(g=>g.selected=f.includes(g.value)):o.refs?fe(o.ref)?o.refs.length>1?o.refs.forEach(g=>(!g.defaultChecked||!g.disabled)&&(g.checked=Array.isArray(f)?!!f.find(v=>v===g.value):f===g.value)):o.refs[0]&&(o.refs[0].checked=!!f):o.refs.forEach(g=>g.checked=g.value===f):Le(o.ref)?o.ref.value="":(o.ref.value=f,o.ref.type||b.values.next({name:r,values:{...u}})))}(a.shouldDirty||a.shouldTouch)&&re(r,f,a.shouldTouch,a.shouldDirty,!0),a.shouldValidate&&ue(r)},P=(r,i,a)=>{for(const c in i){const f=i[c],o=`${r}.${c}`,g=d(l,o);(h.array.has(r)||D(f)||g&&!g._f)&&!ee(f)?P(o,f,a):O(o,f,a)}},R=(r,i,a={})=>{const c=d(l,r),f=h.array.has(r),o=M(i);V(u,r,o),f?(b.array.next({name:r,values:{...u}}),(x.isDirty||x.dirtyFields)&&a.shouldDirty&&b.state.next({name:r,dirtyFields:le(n,u),isDirty:_(r,o)})):c&&!c._f&&!L(o)?P(r,o,a):O(r,o,a),je(r,h)&&b.state.next({...t}),b.values.next({name:y.mount?r:void 0,values:{...u}})},ie=async r=>{y.mount=!0;const i=r.target;let a=i.name,c=!0;const f=d(l,a),o=()=>i.type?Ee(f._f):gr(r),g=v=>{c=Number.isNaN(v)||ee(v)&&isNaN(v.getTime())||Q(v,d(u,a,v))};if(f){let v,S;const T=o(),Z=r.type===He.BLUR||r.type===He.FOCUS_OUT,cr=!Tr(f._f)&&!s.resolver&&!d(t.errors,a)&&!f._f.deps||Or(Z,d(t.touchedFields,a),t.isSubmitted,G,ce),we=je(a,h,Z);V(u,a,T),Z?(f._f.onBlur&&f._f.onBlur(r),p&&p(0)):f._f.onChange&&f._f.onChange(r);const De=re(a,T,Z,!1),dr=!C(De)||we;if(!Z&&b.values.next({name:a,type:r.type,values:{...u}}),cr)return x.isValid&&(e.mode==="onBlur"?Z&&j():j()),dr&&b.state.next({name:a,...we?{}:De});if(!Z&&we&&b.state.next({...t}),s.resolver){const{errors:qe}=await X([a]);if(g(T),c){const yr=Xe(t.errors,l,a),We=Xe(qe,l,yr.name||a);v=We.error,a=We.name,S=C(qe)}}else J([a],!0),v=(await Je(f,u,N,s.shouldUseNativeValidation))[a],J([a]),g(T),c&&(v?S=!1:x.isValid&&(S=await U(l,!0)));c&&(f._f.deps&&ue(f._f.deps),de(a,S,v,De))}},ae=(r,i)=>{if(d(t.errors,i)&&r.focus)return r.focus(),1},ue=async(r,i={})=>{let a,c;const f=ge(r);if(s.resolver){const o=await te(w(r)?r:f);a=C(o),c=r?!f.some(g=>d(o,g)):a}else r?(c=(await Promise.all(f.map(async o=>{const g=d(l,o);return await U(g&&g._f?{[o]:g}:g)}))).every(Boolean),!(!c&&!t.isValid)&&j()):c=a=await U(l);return b.state.next({...!$(r)||x.isValid&&a!==t.isValid?{}:{name:r},...s.resolver||!r?{isValid:a}:{},errors:t.errors}),i.shouldFocus&&!c&&oe(l,ae,r?f:h.mount),c},Ne=r=>{const i={...y.mount?u:n};return w(r)?i:$(r)?d(i,r):r.map(a=>d(i,a))},Ue=(r,i)=>({invalid:!!d((i||t).errors,r),isDirty:!!d((i||t).dirtyFields,r),error:d((i||t).errors,r),isValidating:!!d(t.validatingFields,r),isTouched:!!d((i||t).touchedFields,r)}),lr=r=>{r&&ge(r).forEach(i=>E(t.errors,i)),b.state.next({errors:r?t.errors:{}})},Re=(r,i,a)=>{const c=(d(l,r,{_f:{}})._f||{}).ref,f=d(t.errors,r)||{},{ref:o,message:g,type:v,...S}=f;V(t.errors,r,{...S,...i,ref:c}),b.state.next({name:r,errors:t.errors,isValid:!1}),a&&a.shouldFocus&&c&&c.focus&&c.focus()},nr=(r,i)=>H(r)?b.values.subscribe({next:a=>r(A(void 0,i),a)}):A(r,i,!0),Ve=(r,i={})=>{for(const a of r?ge(r):h.mount)h.mount.delete(a),h.array.delete(a),i.keepValue||(E(l,a),E(u,a)),!i.keepError&&E(t.errors,a),!i.keepDirty&&E(t.dirtyFields,a),!i.keepTouched&&E(t.touchedFields,a),!i.keepIsValidating&&E(t.validatingFields,a),!s.shouldUnregister&&!i.keepDefaultValue&&E(n,a);b.values.next({values:{...u}}),b.state.next({...t,...i.keepDirty?{isDirty:_()}:{}}),!i.keepIsValid&&j()},Me=({disabled:r,name:i,field:a,fields:c,value:f})=>{if(W(r)&&y.mount||r){const o=r?void 0:w(f)?Ee(a?a._f:d(c,i)._f):f;V(u,i,o),re(i,o,!1,!1,!0)}},xe=(r,i={})=>{let a=d(l,r);const c=W(i.disabled)||W(s.disabled);return V(l,r,{...a||{},_f:{...a&&a._f?a._f:{ref:{name:r}},name:r,mount:!0,...i}}),h.mount.add(r),a?Me({field:a,disabled:W(i.disabled)?i.disabled:s.disabled,name:r,value:i.value}):k(r,!0,i.value),{...c?{disabled:i.disabled||s.disabled}:{},...s.progressive?{required:!!i.required,min:ne(i.min),max:ne(i.max),minLength:ne(i.minLength),maxLength:ne(i.maxLength),pattern:ne(i.pattern)}:{},name:r,onChange:ie,onBlur:ie,ref:f=>{if(f){xe(r,i),a=d(l,r);const o=w(f.value)&&f.querySelectorAll&&f.querySelectorAll("input,select,textarea")[0]||f,g=mr(o),v=a._f.refs||[];if(g?v.find(S=>S===o):o===a._f.ref)return;V(l,r,{_f:{...a._f,...g?{refs:[...v.filter(me),o,...Array.isArray(d(n,r))?[{}]:[]],ref:{type:o.type,name:r}}:{ref:o}}}),k(r,!1,void 0,o)}else a=d(l,r,{}),a._f&&(a._f.mount=!1),(s.shouldUnregister||i.shouldUnregister)&&!(vr(h.array,r)&&y.action)&&h.unMount.add(r)}}},Be=()=>s.shouldFocusError&&oe(l,ae,h.mount),or=r=>{W(r)&&(b.state.next({disabled:r}),oe(l,(i,a)=>{const c=d(l,a);c&&(i.disabled=c._f.disabled||r,Array.isArray(c._f.refs)&&c._f.refs.forEach(f=>{f.disabled=c._f.disabled||r}))},0,!1))},pe=(r,i)=>async a=>{let c;if(a&&(a.preventDefault&&a.preventDefault(),a.persist&&a.persist()),s.disabled){i&&await i({...t.errors},a);return}let f=M(u);if(b.state.next({isSubmitting:!0}),s.resolver){const{errors:o,values:g}=await X();t.errors=o,f=g}else await U(l);if(E(t.errors,"root"),C(t.errors)){b.state.next({errors:{}});try{await r(f,a)}catch(o){c=o}}else i&&await i({...t.errors},a),Be(),setTimeout(Be);if(b.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:C(t.errors)&&!c,submitCount:t.submitCount+1,errors:t.errors}),c)throw c},fr=(r,i={})=>{d(l,r)&&(w(i.defaultValue)?R(r,M(d(n,r))):(R(r,i.defaultValue),V(n,r,M(i.defaultValue))),i.keepTouched||E(t.touchedFields,r),i.keepDirty||(E(t.dirtyFields,r),t.isDirty=i.defaultValue?_(r,M(d(n,r))):_()),i.keepError||(E(t.errors,r),x.isValid&&j()),b.state.next({...t}))},Pe=(r,i={})=>{const a=r?M(r):n,c=M(a),f=C(r),o=f?n:c;if(i.keepDefaultValues||(n=a),!i.keepValues){if(i.keepDirtyValues){const g=new Set([...h.mount,...Object.keys(le(n,u))]);for(const v of Array.from(g))d(t.dirtyFields,v)?V(o,v,d(u,v)):R(v,d(o,v))}else{if(Te&&w(r))for(const g of h.mount){const v=d(l,g);if(v&&v._f){const S=Array.isArray(v._f.refs)?v._f.refs[0]:v._f.ref;if(ve(S)){const T=S.closest("form");if(T){T.reset();break}}}}l={}}u=e.shouldUnregister?i.keepDefaultValues?M(n):{}:M(o),b.array.next({values:{...o}}),b.values.next({values:{...o}})}h={mount:i.keepDirtyValues?h.mount:new Set,unMount:new Set,array:new Set,watch:new Set,watchAll:!1,focus:""},y.mount=!x.isValid||!!i.keepIsValid||!!i.keepDirtyValues,y.watch=!!e.shouldUnregister,b.state.next({submitCount:i.keepSubmitCount?t.submitCount:0,isDirty:f?!1:i.keepDirty?t.isDirty:!!(i.keepDefaultValues&&!Q(r,n)),isSubmitted:i.keepIsSubmitted?t.isSubmitted:!1,dirtyFields:f?{}:i.keepDirtyValues?i.keepDefaultValues&&u?le(n,u):t.dirtyFields:i.keepDefaultValues&&r?le(n,r):i.keepDirty?t.dirtyFields:{},touchedFields:i.keepTouched?t.touchedFields:{},errors:i.keepErrors?t.errors:{},isSubmitSuccessful:i.keepIsSubmitSuccessful?t.isSubmitSuccessful:!1,isSubmitting:!1})},Ie=(r,i)=>Pe(H(r)?r(u):r,i);return{control:{register:xe,unregister:Ve,getFieldState:Ue,handleSubmit:pe,setError:Re,_executeSchema:X,_getWatch:A,_getDirty:_,_updateValid:j,_removeUnmounted:ye,_updateFieldArray:F,_updateDisabledField:Me,_getFieldArray:m,_reset:Pe,_resetDefaultValues:()=>H(s.defaultValues)&&s.defaultValues().then(r=>{Ie(r,s.resetOptions),b.state.next({isLoading:!1})}),_updateFormState:r=>{t={...t,...r}},_disableForm:or,_subjects:b,_proxyFormState:x,_setErrors:Y,get _fields(){return l},get _formValues(){return u},get _state(){return y},set _state(r){y=r},get _defaultValues(){return n},get _names(){return h},set _names(r){h=r},get _formState(){return t},set _formState(r){t=r},get _options(){return s},set _options(r){s={...s,...r}}},trigger:ue,register:xe,handleSubmit:pe,watch:nr,setValue:R,getValues:Ne,reset:Ie,resetField:fr,clearErrors:lr,unregister:Ve,setError:Re,setFocus:(r,i={})=>{const a=d(l,r),c=a&&a._f;if(c){const f=c.refs?c.refs[0]:c.ref;f.focus&&(f.focus(),i.shouldSelect&&H(f.select)&&f.select())}},getFieldState:Ue}}function pr(e={}){const s=B.useRef(),t=B.useRef(),[l,n]=B.useState({isDirty:!1,isValidating:!1,isLoading:H(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,defaultValues:H(e.defaultValues)?void 0:e.defaultValues});s.current||(s.current={...Nr(e),formState:l});const u=s.current.control;return u._options=e,Ar({subject:u._subjects.state,next:y=>{Fr(y,u._proxyFormState,u._updateFormState,!0)&&n({...u._formState})}}),B.useEffect(()=>u._disableForm(e.disabled),[u,e.disabled]),B.useEffect(()=>{if(u._proxyFormState.isDirty){const y=u._getDirty();y!==l.isDirty&&u._subjects.state.next({isDirty:y})}},[u,l.isDirty]),B.useEffect(()=>{e.values&&!Q(e.values,t.current)?(u._reset(e.values,u._options.resetOptions),t.current=e.values,n(y=>({...y}))):u._resetDefaultValues()},[e.values,u]),B.useEffect(()=>{e.errors&&u._setErrors(e.errors)},[e.errors,u]),B.useEffect(()=>{u._state.mount||(u._updateValid(),u._state.mount=!0),u._state.watch&&(u._state.watch=!1,u._subjects.state.next({...u._formState})),u._removeUnmounted()}),B.useEffect(()=>{e.shouldUnregister&&u._subjects.values.next({values:u._getWatch()})},[e.shouldUnregister,u]),s.current.formState=br(l,u),s.current}export{xr as a,d as g,V as s,pr as u};
