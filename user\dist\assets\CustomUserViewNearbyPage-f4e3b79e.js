import{j as e}from"./@react-google-maps/api-ee55a349.js";import{i as d,r as a,R as p}from"./vendor-b16525a8.js";import"./index-9ef1c042.js";import{M as f,A as h,G as C,a as u}from"./index-09a1718e.js";import{B as j}from"./index-d54cffea.js";import"./index-49471902.js";import{S as b}from"./index-5a645c18.js";import{t as s}from"./i18next-7389dd8c.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";const N=new f,I=()=>{var r;const{id:m}=d();a.useContext(h),a.useContext(C);const[t,c]=a.useState(null),[o,n]=a.useState(!0),x=async()=>{try{n(!0);const i=await N.callRawAPI(`/v3/api/custom/chumpchange/user/nearby-location/${m}`,{},"GET");c(i==null?void 0:i.data),n(!1)}catch(i){n(!1),console.log("error >> ",i)}};return p.useEffect(()=>{(async()=>await x())()},[]),e.jsxs("div",{className:"p-5",children:[e.jsxs("div",{className:"relative flex w-full items-center justify-center ",children:[e.jsx("div",{className:" absolute left-0 top-0 ",children:e.jsx(j,{})}),e.jsxs("div",{className:"",children:[e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:s("user.view_nearby.title")}),e.jsx("div",{className:"text-center font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:t==null?void 0:t.business_name})]})]}),o?e.jsx("div",{className:"flex h-full w-full flex-1 items-center justify-center ",children:e.jsx(b,{width:"80"})}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:" mt-9 ",children:[e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:s("user.view_nearby.website")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:t!=null&&t.website?e.jsx("a",{href:t==null?void 0:t.website,className:" text-[#4fa7f9] underline ",target:"_blank",children:"View"}):"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:s("user.view_nearby.distance")}),e.jsxs("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:[u(53.9,-101.2,53.89338864876241,-101.16752137287746).toFixed(2)," ","M"]})]}),e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:s("user.view_nearby.get_directions")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:e.jsxs("button",{className:" text-[#4fa7f9] underline ",children:[" ",t!=null&&t.address?t==null?void 0:t.address:"N/A"]})})]}),e.jsxs("div",{className:"flex justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsxs("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:[s("user.view_nearby.description"),":"]}),e.jsx("div",{className:"w-[165px] text-right font-['Poppins'] text-sm font-light leading-[16.80px] text-black",children:t!=null&&t.description?t==null?void 0:t.description:"N/A"})]}),e.jsxs("div",{className:"mt-10",children:[e.jsxs("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:[s("user.view_nearby.contact"),":"]}),e.jsxs("div",{className:"mt-4 flex justify-between gap-4 ",children:[e.jsxs("div",{className:" flex flex-col items-center justify-center gap-1 ",children:[e.jsx("svg",{width:"28",height:"28",viewBox:"0 0 28 28",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M12.25 7H15.75M19 3.5H9C7.896 3.5 7 4.44033 7 5.6V22.4C7 23.5597 7.896 24.5 9 24.5H19C20.105 24.5 21 23.5597 21 22.4V5.6C21 4.44033 20.105 3.5 19 3.5Z",stroke:"#56CCF2",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:s("user.view_nearby.mobile")}),e.jsx("a",{href:`tel:${t==null?void 0:t.phone}`,className:"font-['Poppins'] text-xs font-medium text-black",children:t==null?void 0:t.phone})]}),e.jsxs("div",{className:" flex flex-col items-center justify-center gap-1 ",children:[e.jsx("svg",{width:"28",height:"28",viewBox:"0 0 20 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M15.7564 10.2878L12.6189 8.92325C12.5262 8.88317 12.423 8.86505 12.3198 8.87074C12.2165 8.87643 12.1167 8.90572 12.0306 8.95566L10.4932 9.84688C9.71495 9.50026 9.08175 8.94949 8.68326 8.27254L9.70884 6.93614C9.76625 6.86123 9.79992 6.77448 9.80647 6.68464C9.81301 6.59479 9.79218 6.50504 9.7461 6.42443L8.17733 3.69535C8.12855 3.60997 8.05332 3.53814 7.96012 3.48796C7.86692 3.43778 7.75945 3.41125 7.64983 3.41136C6.66169 3.41136 5.71401 3.75279 5.01528 4.36056C4.31656 4.96833 3.92402 5.79263 3.92402 6.65214C3.92661 8.59666 4.81582 10.4609 6.39659 11.8359C7.97736 13.2108 10.1206 13.9843 12.3561 13.9866C13.3443 13.9866 14.292 13.6451 14.9907 13.0374C15.6894 12.4296 16.082 11.6053 16.082 10.7458C16.082 10.6507 16.0515 10.5574 15.994 10.4765C15.9365 10.3956 15.8543 10.3303 15.7564 10.2878ZM12.3561 12.9631C10.4326 12.9611 8.58844 12.2956 7.22827 11.1125C5.8681 9.92935 5.10292 8.3253 5.10059 6.65214C5.10065 6.11698 5.32321 5.5999 5.72722 5.19628C6.13123 4.79267 6.68943 4.52977 7.29882 4.45608L8.54011 6.61547L7.52139 7.94419C7.46787 8.01427 7.43503 8.09475 7.4258 8.1785C7.41656 8.26225 7.43121 8.34669 7.46845 8.42434C8.0094 9.54306 9.03255 10.433 10.3187 10.9035C10.408 10.9359 10.505 10.9487 10.6013 10.9406C10.6976 10.9326 10.7901 10.904 10.8707 10.8575L12.3983 9.97139L14.8809 11.0511C14.7962 11.5811 14.4939 12.0667 14.0299 12.4181C13.5659 12.7695 12.9714 12.9631 12.3561 12.9631ZM10.003 1.91732e-07C8.26929 -0.000316379 6.56528 0.391391 5.05812 1.13669C3.55097 1.882 2.29245 2.95529 1.40603 4.25128C0.519614 5.54727 0.0357485 7.02143 0.00190601 8.52915C-0.0319365 10.0369 0.385407 11.5263 1.21299 12.8514L0.0736749 15.8253C-0.00698272 16.0357 -0.018688 16.2614 0.039871 16.4772C0.09843 16.6931 0.224939 16.8905 0.405219 17.0473C0.585499 17.2041 0.812424 17.3141 1.06056 17.3651C1.3087 17.416 1.56824 17.4058 1.8101 17.3357L5.22903 16.3447C6.56737 16.9764 8.05388 17.334 9.57522 17.3901C11.0965 17.4462 12.6125 17.1995 14.0076 16.6686C15.4026 16.1377 16.6399 15.3368 17.625 14.3269C18.6101 13.3169 19.3171 12.1246 19.6921 10.8409C20.067 9.55724 20.1 8.21602 19.7885 6.91955C19.4771 5.62308 18.8293 4.4056 17.8948 3.35993C16.9602 2.31426 15.7635 1.46802 14.3958 0.885739C13.0282 0.303459 11.5257 0.000512437 10.003 1.91732e-07ZM10.003 16.3745C8.4518 16.3748 6.92793 16.0195 5.58495 15.3443C5.4954 15.2995 5.39406 15.2757 5.2908 15.2752C5.2275 15.2755 5.16465 15.2844 5.10451 15.3016L1.4385 16.3643C1.40395 16.3743 1.36687 16.3757 1.33142 16.3685C1.29598 16.3612 1.26356 16.3455 1.2378 16.3231C1.21205 16.3007 1.19398 16.2725 1.18561 16.2416C1.17725 16.2108 1.17892 16.1786 1.19044 16.1485L2.41212 12.9631C2.43855 12.8943 2.44785 12.8214 2.4394 12.7492C2.43094 12.677 2.40493 12.6072 2.36309 12.5444C1.38973 11.0819 0.998479 9.3808 1.25004 7.7051C1.50161 6.02941 2.38193 4.47276 3.75442 3.27668C5.12691 2.08059 6.91486 1.31192 8.84086 1.08992C10.7669 0.867926 12.7233 1.20501 14.4065 2.04889C16.0898 2.89276 17.4059 4.19624 18.1505 5.75711C18.8951 7.31799 19.0268 9.04898 18.5249 10.6816C18.0231 12.3141 16.9159 13.757 15.375 14.7864C13.8342 15.8157 11.9458 16.3739 10.003 16.3745Z",fill:"#7FD387"})}),e.jsx("div",{className:"font-['Poppins'] text-xs font-medium text-[#8080a3]",children:s("user.view_nearby.whatsApp")}),e.jsx("a",{href:`https://wa.me/${t==null?void 0:t.phone}`,target:"_blank",rel:"noopener noreferrer",className:"font-['Poppins'] text-xs font-medium text-black",children:t==null?void 0:t.phone})]})]})]}),t!=null&&t.image?e.jsxs("div",{className:" mt-7 border-t border-[#8181a4]/20 py-3 pt-8 ",children:[e.jsx("div",{className:"font-['Poppins'] text-lg font-medium text-black",children:s("user.view_nearby.establishment_images")}),e.jsx("div",{className:"mt-5 flex gap-1",children:(r=t==null?void 0:t.image)==null?void 0:r.split(",").map((i,l)=>e.jsx("img",{className:"h-[127px] w-[120px] rounded-[20px] object-cover ",src:i},l))})]}):""]})})]})};export{I as default};
