import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as s,b,r as v}from"./vendor-4f06b3f4.js";import{M as g,A as w,G as j,a2 as A,a3 as E}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{L as r}from"./index-6416aa2c.js";import{M as n}from"./index-d97c616d.js";import{M}from"./index-68c82eaa.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";new g;const I=[{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"provider_id",accessor:"provider_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"service_id",accessor:"service_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"price",accessor:"price",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],Q=()=>{s.useContext(w),s.useContext(j),b();const[p,m]=s.useState({}),[f,t]=s.useState(!1),[o,a]=s.useState(!1),[x,h]=s.useState(),u=v.useRef(null),[P,S]=s.useState([]),d=(i,l,c=[])=>{switch(i){case"add":t(l);break;case"edit":a(l),S(c),h(c[0]);break}};return e.jsxs(e.Fragment,{children:[e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"flex flex-wrap gap-4 bg-white px-14 shadow ",children:e.jsxs("div",{className:"mt-4 inline-flex h-[154px] w-[261px] flex-col items-center justify-center gap-6 rounded-lg border border-[#e4e6eb] bg-white p-4 shadow",children:[e.jsx("div",{className:" font-['Inter'] text-3xl font-semibold leading-[38px] text-[#0f1728]",children:p.total}),e.jsx("div",{className:" font-['Poppins'] text-base font-medium leading-normal text-[#0f1728]",children:"Total products"})]})}),e.jsx("div",{className:"overflow-x-auto  rounded bg-white p-5 shadow",children:e.jsx(r,{children:e.jsx(M,{setResult:m,columns:I,tableRole:"admin",table:"provider_skill",actionId:"id",actions:{view:{show:!0,action:null,multiple:!1},edit:{show:!1,multiple:!1,action:i=>d("edit",!0,i)},delete:{show:!1,action:null,multiple:!1},select:{show:!1,action:null,multiple:!1},add:{show:!1,action:()=>d("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!0,action:null,multiple:!0}},actionPostion:"ontable",refreshRef:u,join:"service"})})})]}),e.jsx(r,{children:e.jsx(n,{isModalActive:f,closeModalFn:()=>t(!1),children:e.jsx(A,{setSidebar:t})})}),o&&e.jsx(r,{children:e.jsx(n,{isModalActive:o,closeModalFn:()=>a(!1),children:e.jsx(E,{activeId:x,setSidebar:a})})})]})};export{Q as default};
