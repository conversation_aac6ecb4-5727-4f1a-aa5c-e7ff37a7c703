import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{h as D,r as a,R as H}from"./vendor-f36d475e.js";import"./index-5deedf4a.js";import{M as W,A as $,G as z}from"./index-cf5e6bc7.js";import{B as q,a as F}from"./index-895fa99b.js";import{M as v}from"./index-bf8d79cc.js";import{S as w}from"./index-65bc3378.js";import{u as J}from"./react-i18next-1e3e6bc5.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";const r=new W,fe=()=>{const{t:s}=J();D();const[i,_]=a.useState(null),[d,P]=a.useState(null),[c,k]=a.useState(null),[o,y]=a.useState(null),[t,S]=a.useState({}),[C,u]=a.useState(!1),[A,M]=a.useState([]),[h,p]=a.useState(!0),[I,m]=a.useState(!1),[L,b]=a.useState(!1);a.useContext($),a.useContext(z);const R=async()=>{var n;try{p(!0);const f=await r.callRawAPI("/v3/api/custom/chumpchange/provider/list_plans",{},"GET"),j=await r.callRawAPI("/v3/api/custom/chumpchange/provider/user-plans",{},"GET"),g=j.data.filter(l=>l.status==="created"),x=j.data.filter(l=>l.status==="approved");y(g.length>0?g[0]:null),k(x.length>0?x[0]:null),x.length>0&&localStorage.setItem("plan_id",(n=x[0])==null?void 0:n.id),M(f.data),r.setTable("setting");const E=await r.callRestAPI({},"PAGINATE"),{list:G}=E,O=G.reduce((l,N)=>(l[N.setting_key]=N.setting_value,l),{});S(O),p(!1)}catch(f){p(!1),console.log("error >> ",f)}},B=async()=>{try{m(!0);const n=await r.callRawAPI("/v3/api/custom/chumpchange/provider/user-plans/create",{plan_id:i.id},"POST");u(!0),m(!1)}catch(n){m(!1),console.log("error >> ",n)}};H.useEffect(()=>{localStorage.getItem("plan_id")&&P(JSON.parse(localStorage.getItem("plan_id"))),(async()=>await R())()},[]);const T=n=>{_(n===i?null:n)};return console.log(" pendingPlan >> ",o),console.log(" approvePlan >> ",c),e.jsxs("div",{className:"p-5",children:[e.jsxs("div",{className:"relative flex w-full items-center justify-between ",children:[e.jsx("div",{className:" ",children:e.jsx(q,{})}),e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:s("provider.activation.title")}),e.jsx("div",{className:" ",children:e.jsx("button",{onClick:()=>b(!0),className:"relative flex h-10 w-10 items-center justify-center rounded-[32px] bg-white/60 backdrop-blur-[20px]",children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M9.99984 18.3334C14.5832 18.3334 18.3332 14.5834 18.3332 10.0001C18.3332 5.41675 14.5832 1.66675 9.99984 1.66675C5.4165 1.66675 1.6665 5.41675 1.6665 10.0001C1.6665 14.5834 5.4165 18.3334 9.99984 18.3334Z",stroke:"#8181A4",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M10 11.9226V10.8052C10 9.88502 10.6326 9.39786 11.2651 9.00543C11.8826 8.62654 12.5 8.13942 12.5 7.24632C12.5 6.0014 11.3856 5 10 5C8.61444 5 7.5 6.0014 7.5 7.24632",stroke:"#8181A4",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M9.99381 14.3233H10.0074",stroke:"#8181A4",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})})})]}),e.jsx("div",{className:" mt-[7px] ",children:e.jsx("div",{className:"text-center font-['Poppins'] text-base font-bold text-black",children:s("provider.activation.status")})}),h?e.jsx("div",{className:" flex items-center justify-center pt-10 ",children:e.jsx(w,{})}):e.jsxs(e.Fragment,{children:[o?e.jsxs("div",{className:"mt-4 text-center font-['Poppins'] text-2xl font-medium text-[#56ccf2]",children:[e.jsx("p",{className:" text-[#111] ",children:"Pending:"})," ",o==null?void 0:o.plan_name]}):e.jsx("div",{className:"mt-4 text-center font-['Poppins'] text-2xl font-medium text-[#56ccf2]",children:c?c==null?void 0:c.plan_name:s("provider.activation.select_plan")}),e.jsxs("div",{className:" mt-[70px] ",children:[e.jsx("div",{className:"font-['Poppins'] text-lg font-medium capitalize text-black ",children:s("provider.activation.select_p")}),e.jsx("div",{className:" mt-[27px] flex flex-col gap-6 ",children:A.map(n=>e.jsxs("div",{className:`relative flex h-[57px] w-full cursor-pointer flex-col items-center justify-center rounded-xl border  ${d===n.id?"border-[#bdbdbd] bg-[#bdbdbd] ":(i==null?void 0:i.id)===n.id?"border-[#56ccf2] bg-[#56ccf2]":"border-[#56ccf2] bg-white"}`,onClick:()=>T(n),children:[e.jsx("div",{className:`text-right font-['Poppins'] text-base font-black uppercase ${(i==null?void 0:i.id)===n.id||d===n.id?"text-white":"text-[#56ccf2]"}`,children:n.name}),n.subsidized?e.jsx("div",{className:" font-['Poppins'] text-sm font-light text-black",children:s("provider.activation.government_subsidy")}):e.jsxs("div",{className:" font-['Poppins'] text-sm font-light text-black",children:["DOP ",n.amount]})]},n.id))})]}),e.jsx("div",{className:" mt-10 ",children:e.jsx(F,{disabled:!i||i.id==d||o,className:`${!i||i.id==d?"bg-[#bdbdbd] !opacity-100 ":"bg-[#56ccf2]"}`,onClick:B,children:s("provider.activation.activate_plan")})})]}),C&&e.jsx(v,{closeModal:()=>u(!1),children:e.jsxs("div",{className:" flex flex-col items-center justify-center px-5 pt-8 ",children:[e.jsx("div",{className:"w-[252px] text-center font-['Poppins'] text-xl font-medium leading-normal text-black",dangerouslySetInnerHTML:{__html:s("provider.activation.m_titel")}}),e.jsxs("div",{className:"mt-10 flex w-full items-center justify-between ",children:[e.jsxs("div",{className:"text-center font-['Poppins'] text-base font-semibold text-black",children:[s("provider.activation.request"),":"]}),e.jsx("div",{className:" text-right font-['Poppins'] text-base font-medium text-[#a2a8ae]",children:i==null?void 0:i.name})]}),e.jsxs("div",{className:" flex w-full items-center justify-between ",children:[e.jsxs("div",{className:"text-center font-['Poppins'] text-base font-semibold text-black",children:[s("provider.activation.total_pay"),":"]}),e.jsxs("div",{className:" text-right font-['Poppins'] text-base font-medium text-[#a2a8ae]",children:["DOP ",i==null?void 0:i.amount]})]}),e.jsx("div",{className:"mt-10 w-full ",children:e.jsxs("div",{className:" font-['Poppins'] text-base font-light leading-tight text-black",children:[s("provider.activation.to_complete"),":"]})}),e.jsxs("div",{className:"mt-1 flex w-full items-center justify-between ",children:[e.jsx("div",{className:" text-center font-['Poppins'] text-base font-semibold text-[#a2a8ae]",children:s("provider.activation.p_savings")}),e.jsxs("div",{className:" text-center font-['Poppins'] text-base font-semibold text-[#a2a8ae]",children:["# ",t!=null&&t.popular_savings?t==null?void 0:t.popular_savings:0]})]}),e.jsxs("div",{className:"mt-1 flex w-full items-center justify-between ",children:[e.jsx("div",{className:" text-center font-['Poppins'] text-base font-semibold text-[#a2a8ae]",children:s("provider.activation.r_savings")}),e.jsxs("div",{className:" text-center font-['Poppins'] text-base font-semibold text-[#a2a8ae]",children:["# ",t!=null&&t.reservas_savings?t==null?void 0:t.reservas_savings:0]})]}),e.jsx("div",{className:"w-full",children:e.jsxs("div",{className:"text-center font-['Poppins'] text-base font-semibold text-black",children:["RNC ",t!=null&&t.RNC?t==null?void 0:t.RNC:0]})}),e.jsx("div",{className:"mb-10 mt-10 w-full ",children:e.jsx("div",{className:" font-['Poppins'] text-base font-light leading-tight text-black",children:s("provider.activation.p_remember")})})]})}),I&&e.jsx(v,{showCloseButton:!1,children:e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsxs("div",{className:"text-lg font-semibold",children:[s("loading.submitting"),"..."]}),e.jsx("div",{className:"mt-12",children:e.jsx(w,{})})]})}),L?e.jsx(v,{showCloseButton:!h,closeModal:()=>b(!1),children:e.jsxs("div",{className:" flex w-full flex-col items-center justify-center px-5 pt-8 ",children:[e.jsx("div",{className:"w-[252px] text-center font-['Poppins'] text-base font-medium leading-tight text-black",dangerouslySetInnerHTML:{__html:s("provider.activation.info.title")}}),e.jsx("div",{className:" mt-[29px] font-['Poppins'] text-base font-light leading-tight text-black",children:s("provider.activation.info.s_title")}),e.jsxs("div",{className:" mt-[34px] w-full ",children:[e.jsx("div",{className:"font-['Poppins'] text-base font-semibold text-[#a2a8ae]",children:s("provider.activation.info.p_optoin_one")}),e.jsx("div",{className:" mt-[2px] font-['Poppins'] text-sm font-light leading-[16.80px] text-black",children:s("provider.activation.info.p_optoin_one_d")})]}),e.jsxs("div",{className:" mt-1 w-full ",children:[e.jsx("div",{className:"font-['Poppins'] text-base font-semibold text-[#a2a8ae]",children:s("provider.activation.info.p_optoin_two")}),e.jsx("div",{className:" mt-[2px] font-['Poppins'] text-sm font-light leading-[16.80px] text-black",children:s("provider.activation.info.p_optoin_two_d")}),e.jsxs("div",{className:"mt-1",children:[e.jsxs("div",{className:" flex w-full items-center justify-between ",children:[e.jsx("div",{className:"  text-center font-['Poppins'] text-base font-semibold text-[#a2a8ae]",children:s("provider.activation.info.popular_savings")}),e.jsxs("div",{className:" text-center font-['Poppins'] text-base font-semibold text-[#a2a8ae]",children:["# ",t!=null&&t.popular_savings?t==null?void 0:t.popular_savings:0]})]}),e.jsxs("div",{className:" flex w-full items-center justify-between ",children:[e.jsx("div",{className:"  text-center font-['Poppins'] text-base font-semibold text-[#a2a8ae]",children:s("provider.activation.info.reservas_savings")}),e.jsxs("div",{className:" text-center font-['Poppins'] text-base font-semibold text-[#a2a8ae]",children:["# ",t!=null&&t.reservas_savings?t==null?void 0:t.reservas_savings:0]})]})]}),e.jsx("div",{className:" mt-8 pb-10 font-['Poppins'] text-base font-light leading-tight text-black",children:s("provider.activation.info.disc")})]})]})}):""]})};export{fe as default};
