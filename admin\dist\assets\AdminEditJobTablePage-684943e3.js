import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as d,b as H,r as o,h as J}from"./vendor-4f06b3f4.js";import{u as U}from"./react-hook-form-f3d72793.js";import{o as q}from"./yup-2324a46a.js";import{c as K,a as l}from"./yup-17027d7a.js";import{M as V,A as z,G as Q,t as W,s as X}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as c}from"./MkdInput-ff3aa862.js";import{I as Y}from"./InteractiveButton-8f7d74ee.js";import{S as Z}from"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";let g=new V;const Pe=u=>{var v,k,N,S,w,E;const{dispatch:I}=d.useContext(z),R=K({task:l(),arguments:l(),error_log:l(),identifier:l(),retries:l(),retry_count:l(),time_interval:l(),last_run:l(),status:l()}).required(),{dispatch:x}=d.useContext(Q),[y,ee]=d.useState({}),[j,b]=d.useState(!1),[T,h]=d.useState(!1),A=H(),[te,L]=o.useState(""),[se,C]=o.useState(""),[re,F]=o.useState(""),[ae,D]=o.useState(0),[oe,P]=o.useState(0),[le,$]=o.useState(""),[ie,M]=o.useState(""),[ne,G]=o.useState(""),{register:r,handleSubmit:O,setError:_,setValue:a,formState:{errors:s}}=U({resolver:q(R)}),n=J();o.useEffect(function(){(async function(){try{h(!0),g.setTable("job");const e=await g.callRestAPI({id:u.activeId?u.activeId:Number(n==null?void 0:n.id)},"GET");e.error||(a("task",e.model.task),a("arguments",e.model.arguments),a("error_log",e.model.error_log),a("identifier",e.model.identifier),a("retries",e.model.retries),a("retry_count",e.model.retry_count),a("time_interval",e.model.time_interval),a("last_run",e.model.last_run),a("status",e.model.status),L(e.model.task),C(e.model.error_log),F(e.model.identifier),D(e.model.retries),P(e.model.retry_count),$(e.model.time_interval),M(e.model.last_run),G(e.model.status),h(!1))}catch(e){h(!1),console.log("error",e),W(I,e.message)}})()},[]);const B=async e=>{b(!0);try{g.setTable("job");for(let p in y){let m=new FormData;m.append("file",y[p].file);let f=await g.uploadImage(m);e[p]=f.url}const i=await g.callRestAPI({id:u.activeId?u.activeId:Number(n==null?void 0:n.id),task:e.task,arguments:e.arguments,error_log:e.error_log,identifier:e.identifier,retries:e.retries,retry_count:e.retry_count,time_interval:e.time_interval,last_run:e.last_run,status:e.status},"PUT");if(!i.error)X(x,"Updated"),A("/admin/job"),x({type:"REFRESH_DATA",payload:{refreshData:!0}}),u.setSidebar(!1);else if(i.validation){const p=Object.keys(i.validation);for(let m=0;m<p.length;m++){const f=p[m];_(f,{type:"manual",message:i.validation[f]})}}b(!1)}catch(i){b(!1),console.log("Error",i),_("task",{type:"manual",message:i.message})}};return d.useEffect(()=>{x({type:"SETPATH",payload:{path:"job"}})},[]),t.jsxs("div",{className:" mx-auto rounded   p-5 shadow-md",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Job"}),T?t.jsx(Z,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:O(B),children:[t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"task",children:"Task"}),t.jsx("textarea",{placeholder:"Task",...r("task"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(v=s.task)!=null&&v.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-xs italic text-red-500",children:(k=s.task)==null?void 0:k.message})]}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"arguments",children:"Arguments"}),t.jsx("textarea",{placeholder:"Arguments",...r("arguments"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(N=s.arguments)!=null&&N.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-xs italic text-red-500",children:(S=s.arguments)==null?void 0:S.message})]}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"error_log",children:"Error Log"}),t.jsx("textarea",{placeholder:"Error Log",...r("error_log"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(w=s.error_log)!=null&&w.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-xs italic text-red-500",children:(E=s.error_log)==null?void 0:E.message})]}),t.jsx(c,{type:"text",page:"edit",name:"identifier",errors:s,label:"Identifier",placeholder:"Identifier",register:r,className:""}),t.jsx(c,{type:"number",page:"edit",name:"retries",errors:s,label:"Retries",placeholder:"Retries",register:r,className:""}),t.jsx(c,{type:"number",page:"edit",name:"retry_count",errors:s,label:"Retry Count",placeholder:"Retry Count",register:r,className:""}),t.jsx(c,{type:"text",page:"edit",name:"time_interval",errors:s,label:"Time Interval",placeholder:"Time Interval",register:r,className:""}),t.jsx(c,{type:"datetime-local",page:"edit",name:"last_run",errors:s,label:"Last Run",placeholder:"Last Run",register:r,className:""}),t.jsx(c,{type:"text",page:"edit",name:"status",errors:s,label:"Status",placeholder:"Status",register:r,className:""}),t.jsx(Y,{type:"submit",className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none",loading:j,disable:j,children:"Submit"})]})]})};export{Pe as default};
