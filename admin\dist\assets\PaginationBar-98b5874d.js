import{j as s}from"./@react-google-maps/api-ac2f9d6f.js";import"./vendor-4f06b3f4.js";const m=({currentPage:a,pageCount:t,pageSize:l,canPreviousPage:n,canNextPage:d,updatePageSize:i,previousPage:o,nextPage:r})=>s.jsx(s.Fragment,{children:s.jsxs("div",{className:"flex justify-between ",children:[s.jsxs("div",{className:"mt-2",children:[s.jsxs("span",{children:["Page"," ",s.jsxs("strong",{children:[+a," of ",t]})," "]}),s.jsx("select",{className:"mt-2 h-8 max-h-8 rounded-md !py-0",value:l,onChange:e=>{i(Number(e.target.value))},children:[5,10,20,30,40,50].map(e=>s.jsxs("option",{value:e,children:["Show ",e]},e))})]}),s.jsxs("div",{className:"mt-2",children:[s.jsx("button",{onClick:o,disabled:!n,className:"h-10 w-10 text-[25px] font-bold disabled:opacity-50 ",children:"←"})," ",s.jsx("button",{onClick:r,disabled:!d,className:"h-10 w-10 text-[25px] font-bold disabled:opacity-50 ",children:"→"})," "]})]})});export{m as default};
