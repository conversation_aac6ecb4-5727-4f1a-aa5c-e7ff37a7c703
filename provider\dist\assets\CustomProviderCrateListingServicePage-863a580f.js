import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{h as B,u as I,d as G,r as c,R as d}from"./vendor-f36d475e.js";import"./index-5deedf4a.js";import{M as V,A as W,G as T,t as U}from"./index-cf5e6bc7.js";import{B as $,R as F}from"./index-895fa99b.js";import j from"./DropDownSelection-d1fecb01.js";import{u as z}from"./react-hook-form-ff037c98.js";import{o as K}from"./yup-afe5cf51.js";import{c as Z,a as x}from"./yup-2f6e2476.js";import{S as J}from"./index-65bc3378.js";import{t as s}from"./i18next-7389dd8c.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-1e3e6bc5.js";import"./@hookform/resolvers-eb417cd0.js";const f=new V,ye=()=>{B();const u=I(),g=G(),{state:O,dispatch:b}=c.useContext(W);c.useContext(T),c.useState(!1);const[N,w]=d.useState([]),[_,m]=d.useState(!0),[y,k]=d.useState(),[r,p]=c.useState({name:s("provider.cl_service.select")}),[S,P]=c.useState({name:s("provider.cl_service.select")}),[D,v]=c.useState({name:"Select City"}),[L,C]=c.useState([]),A=Z({rate:x().required(),startDate:x().required(),endDate:x().required()}).required(),{register:l,handleSubmit:R,setError:Q,setValue:n,formState:{errors:i,isValid:X}}=z({resolver:K(A),mode:"onChange"});c.useEffect(()=>{const t=new URLSearchParams(u.search);k(t.get("republishId")||""),n("rate",t.get("rate")||""),n("startDate",t.get("startDate")||""),n("endDate",t.get("endDate")||""),n("days",t.get("days")||""),n("description",t.get("description")||""),n("city",t.get("city")||""),P({name:t.get("days")||""}),p({name:t.get("service")||"Select",price:t.get("price")||"",id:t.get("service_id")||0})},[u.search,n]);const E=t=>{p(t),console.log("service",t),n("service",t.name),n("price",t.price)},M=async t=>{console.log("data",t);const a=new URLSearchParams({service:r.name,price:r==null?void 0:r.price,service_id:r.id,days:t.days,startDate:t.startDate,endDate:t.endDate,rate:t.rate,description:t.description,city:t.city,republishId:y}).toString();g(`/provider/crate-listing/service/confirm?${a}`)},q=async()=>{var t;try{m(!0);const a=await f.callRawAPI("/v3/api/custom/chumpchange/users/services",{},"GET");if(console.log("services",a),!a.error){let h=[{name:s("provider.cl_service.select")}];(t=a==null?void 0:a.data)==null||t.map(o=>{h.push({name:o==null?void 0:o.name,price:o==null?void 0:o.price,id:o.id})}),w(h),f.setTable("city");const H=await f.callRestAPI({},"GETALL");C(H.list),m(!1)}}catch(a){m(!1),console.log("error",a),U(b,a.message)}};return d.useEffect(()=>{(async()=>q())()},[]),console.log("selectedDays",S),e.jsxs("form",{onSubmit:R(M),className:"p-5",children:[e.jsxs("div",{className:"relative flex w-full items-center justify-center ",children:[e.jsx("div",{className:" absolute left-0 top-0 ",children:e.jsx($,{})}),e.jsxs("div",{className:"",children:[e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:s("provider.cl_service.title")}),e.jsx("div",{className:"text-center font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:r!=null&&r.name?r==null?void 0:r.name:"N/A"})]})]}),_?e.jsx("div",{className:"flex min-h-screen w-full items-center justify-center",children:e.jsx(J,{})}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mt-7",children:[(i==null?void 0:i.description)&&e.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:e.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:s("provider.cl_service.missing_description")})}),(i==null?void 0:i.days)&&e.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:e.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:s("provider.cl_service.missing_days")})}),(i==null?void 0:i.startDate)&&e.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:e.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:s("provider.cl_service.missing_start_date")})}),(i==null?void 0:i.endDate)&&e.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:e.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:s("provider.cl_service.missing_end_date")})}),(i==null?void 0:i.rate)&&e.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:e.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:s("provider.cl_service.missing_rate")})}),e.jsx("div",{className:"max-w-[321px] font-['Poppins'] text-base font-normal tracking-tight text-black",children:s("provider.cl_service.info")}),e.jsx("div",{className:"mt-5 px-2 font-['Poppins'] text-[22px] font-medium text-black",children:s("provider.cl_service.service_offer")}),e.jsxs("div",{className:"",children:[e.jsx("div",{className:"px-2 font-['Poppins'] text-base font-medium text-[#8080a3]",children:s("provider.cl_service.select_service")}),e.jsx(j,{services:N,onServiceSelect:E,selectedService:r,setSelectedService:p})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("div",{className:" px-2 font-['Poppins'] text-[22px] font-bold tracking-wide text-black",children:s("provider.cl_service.what_rate")}),e.jsx("input",{type:"text",placeholder:s("provider.cl_service.p_what_rate"),...l("rate"),className:"relative mt-3 h-12 w-full rounded-2xl border-none bg-white text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("div",{className:" px-2 font-['Poppins'] text-[22px] font-bold tracking-wide text-black",children:s("provider.s_listing.city_name")}),e.jsx(j,{services:L,onServiceSelect:t=>{v(t),n("city",t.name)},selectedService:D,setSelectedService:v})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("div",{className:" px-2 font-['Poppins'] text-[22px] font-bold tracking-wide text-black",children:s("provider.cl_service.descripe")}),e.jsxs("div",{className:"relative mt-3 h-[104px] w-full overflow-hidden rounded-2xl bg-white",children:[e.jsx("span",{className:"absolute left-[14px] top-[14px] z-[99] h-5 w-5 ",children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M7.08366 15.8332H6.66699C3.33366 15.8332 1.66699 14.9998 1.66699 10.8332V6.6665C1.66699 3.33317 3.33366 1.6665 6.66699 1.6665H13.3337C16.667 1.6665 18.3337 3.33317 18.3337 6.6665V10.8332C18.3337 14.1665 16.667 15.8332 13.3337 15.8332H12.917C12.6587 15.8332 12.4087 15.9582 12.2503 16.1665L11.0003 17.8332C10.4503 18.5665 9.55033 18.5665 9.00033 17.8332L7.75033 16.1665C7.61699 15.9832 7.30866 15.8332 7.08366 15.8332Z",stroke:"#8181A4",strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M13.3301 9.16667H13.3375",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M9.99607 9.16667H10.0036",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M6.66209 9.16667H6.66957",stroke:"#8181A4",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),e.jsx("textarea",{...l("description"),type:"text",placeholder:s("provider.cl_service.detailed"),className:" absolute left-0 top-0 h-full w-full border-none bg-white  pl-[40px] pt-[14px] text-sm font-medium text-black outline-none focus:outline-none focus:ring-0 "})]})]}),e.jsxs("div",{className:"mt-[35px]",children:[e.jsx("div",{className:" px-2 font-['Poppins'] text-[22px] font-bold tracking-wide text-black",children:s("provider.cl_service.available")}),e.jsxs("div",{className:" mt-3 flex justify-between ",children:[e.jsxs("div",{className:"",children:[e.jsx("input",{type:"date",...l("startDate"),className:" h-12 w-full rounded-2xl border-none bg-white outline-none focus:border-none focus:outline-none focus:ring-0 "}),e.jsx("div",{className:"mt-[10px] text-center font-['Poppins'] text-base font-medium text-[#8080a3] ",children:s("provider.cl_service.start_date")})]}),e.jsxs("div",{className:"",children:[e.jsx("input",{type:"date",...l("endDate"),className:" h-12 w-full rounded-2xl border-none bg-white outline-none focus:border-none focus:outline-none focus:ring-0 "}),e.jsx("div",{className:"mt-[10px] text-center font-['Poppins'] text-base font-medium text-[#8080a3] ",children:s("provider.cl_service.end_date")})]})]})]}),e.jsx("div",{className:"mt-[26px] font-['Poppins'] text-base font-normal tracking-tight text-black",children:s("provider.cl_service.interested")})]}),e.jsx("div",{className:"mt-[58px]",children:e.jsx(F,{type:"submit",className:"uppercase",children:s("buttons.next")})})]})]})};export{ye as default};
