import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as j,r as E,u as _,b as A,L as $}from"./vendor-4f06b3f4.js";import{u as F}from"./react-hook-form-f3d72793.js";import{o as G}from"./yup-2324a46a.js";import{c as I,a as y}from"./yup-17027d7a.js";import{M as q,A as D,G as z,s as N}from"./index-06b5b6dd.js";import{I as B}from"./InteractiveButton-8f7d74ee.js";import"./moment-a9aaa855.js";import{L as M}from"./login-new-bg-eb709951.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./MoonLoader-16bed42a.js";let O=new q;const pe=()=>{var m,c,d,x;const v=I({email:y().email().required(),password:y().required()}).required(),{dispatch:k}=j.useContext(D),{dispatch:r}=j.useContext(z),[i,a]=E.useState(!1),S=_(),L=new URLSearchParams(S.search).get("redirect_uri"),P=A(),{register:l,handleSubmit:R,setError:n,formState:{errors:t}}=F({resolver:G(v)}),C=async p=>{var u,h,g,f;try{a(!0);const s=await O.register(p.email,p.password,"admin");if(!s.error)k({type:"LOGIN",payload:s}),N(r,"Succesfully Registered",4e3,"success"),P(L??"/admin/dashboard");else if(a(!1),s.validation){const b=Object.keys(s.validation);for(let o=0;o<b.length;o++){const w=b[o];n(w,{type:"manual",message:s.validation[w]})}}}catch(s){a(!1),console.log("Error",s),N(r,s==null?void 0:s.message,4e3,"error"),n("email",{type:"manual",message:(h=(u=s==null?void 0:s.response)==null?void 0:u.data)!=null&&h.message?(f=(g=s==null?void 0:s.response)==null?void 0:g.data)==null?void 0:f.message:s==null?void 0:s.message})}};return e.jsx("div",{className:"h-screen m-auto max-h-screen min-h-screen",children:e.jsxs("div",{className:"min-h-full flex justify-center w-full max-h-full h-full",children:[e.jsxs("section",{className:"md:w-1/2 w-full flex flex-col items-center justify-center bg-white",children:[e.jsxs("form",{onSubmit:R(C),className:"flex flex-col max-w-md w-full px-6 mt-[9.375rem]",children:[e.jsx("h1",{className:"md:font-bold font-semibold md:text-5xl text-3xl text-center mb-8",children:"Register"}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"email",children:"Email"}),e.jsx("input",{type:"email",autoComplete:"off",placeholder:"Email",...l("email"),className:`resize-none border-2 p-2 px-4 bg-transparent mb-3 active:outline-none shadow appearance-none rounded w-full py-2  text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(m=t.email)!=null&&m.message?"border-red-500":""}`}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(c=t.email)==null?void 0:c.message})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"password",children:"Password"}),e.jsx("input",{autoComplete:"off",type:"password",name:"password",placeholder:"Password",...l("password"),className:`border-2 focus:outline-none active:outline-none flex-grow p-2 px-4 shadow appearance-none rounded w-full py-2 text-gray-700 mb-3 leading-tight focus:shadow-outline ${(d=t.password)!=null&&d.message?"border-red-500":""}`}),e.jsx("button",{type:"button",className:"absolute right-1 top-[20%]",children:e.jsx("img",{src:"/invisible.png",alt:"",className:"w-6 mr-2"})}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(x=t.password)==null?void 0:x.message})]}),e.jsx(B,{type:"submit",className:"flex justify-center items-center bg-gradient-to-l from-[#33d4b7_9.11%] to-[#0d9895_69.45%] text-white tracking-wide outline-none focus:outline-none rounded  py-2",loading:i,disabled:i,children:e.jsx("span",{children:"Register"})})]}),e.jsx("div",{className:"oauth flex flex-col gap-4 max-w-md w-full px-6 text-[#344054] grow",children:e.jsx("div",{children:e.jsxs("h3",{className:"mt-5 text-center text-sm text-gray-800 normal-case",children:["Already have an account? ",e.jsx($,{className:"self-end mb-8 my-text-gradient font-semibold text-sm",to:"/admin/login",children:"Login "})," "]})})}),e.jsxs("p",{className:"text-center text-gray-500 text-xs h-10 my-5",children:["© ",new Date().getFullYear()," manaknightdigital inc. All rights reserved."]})]}),e.jsx("section",{className:"md:block hidden w-1/2",style:{backgroundImage:`url(${M})`,backgroundSize:"cover",backgroundPosition:"center center"}})]})})};export{pe as default};
