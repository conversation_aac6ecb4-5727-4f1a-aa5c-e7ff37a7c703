import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as a,b as F}from"./vendor-4f06b3f4.js";import{u as I}from"./react-hook-form-f3d72793.js";import{o as P}from"./yup-2324a46a.js";import{c as q,a as o}from"./yup-17027d7a.js";import{A as D,G as L,M,s as R,t as V}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{D as $}from"./index-3e43e6ef.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";const ne=({setSidebar:n})=>{var g,h,f;const j=q({page:o().required(),key:o().required(),type:o().required(),value:o()}).required(),i=[{key:"text",value:"Text"},{key:"image",value:"Image"},{key:"number",value:"Number"},{key:"kvp",value:"Key-Value Pair"},{key:"image-list",value:"Image List"},{key:"captioned-image-list",value:"Captioned Image List"},{key:"team-list",value:"Team List"}],{dispatch:C}=a.useContext(D),{dispatch:m}=a.useContext(L),[N,w]=a.useState((g=i[0])==null?void 0:g.key),[E,S]=a.useState(""),[c,d]=a.useState(!1),T=F(),{register:r,handleSubmit:p,setError:u,formState:{errors:x},reset:A}=I({resolver:P(j)}),y=async t=>{let b=new M;d(!0),console.log(t);try{b.setTable("cms");const s=await b.cmsAdd(t.page,t.key,t.type,E);if(!s.error)T("/admin/cms"),R(m,"Added"),A();else if(s.validation){const v=Object.keys(s.validation);for(let l=0;l<v.length;l++){const k=v[l];u(k,{type:"manual",message:s.validation[k]})}}}catch(s){console.log("Error",s),u("page",{type:"manual",message:s.message}),V(C,s.message)}d(!1)};return a.useEffect(()=>{m({type:"SETPATH",payload:{path:"cms"}})},[]),e.jsxs("div",{className:"rounded mx-auto",children:[e.jsxs("div",{className:"flex items-center p-3 gap-4 border-b border-b-[#E0E0E0] justify-between",children:[e.jsx("div",{className:"flex items-center gap-3",children:e.jsx("span",{className:"text-lg font-semibold",children:"Add CMS Content"})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center py-2 px-3 border border-[#C6C6C6] rounded-md shadow-sm hover:bg-[#F4F4F4]",onClick:()=>n(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center py-2 px-3 text-white bg-[#4F46E5] rounded-md shadow-sm",onClick:async()=>{await p(y)(),n(!1)},disabled:c,children:c?"Saving...":"Save"})]})]}),e.jsxs("form",{className:"w-full p-4 text-left",onSubmit:p(y),children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"page",children:"Page"}),e.jsx("input",{type:"text",placeholder:"Page",...r("page"),className:`shadow appearance-none border rounded w-full mb-3 py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline
}`})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"key",children:"Content Identifier"}),e.jsx("input",{type:"text",placeholder:"Content Identifier",...r("key"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(h=x.key)!=null&&h.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(f=x.key)==null?void 0:f.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Content Type"}),e.jsx("select",{name:"type",id:"type",className:"shadow border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",...r("type",{onChange:t=>w(t.target.value)}),children:i.map(t=>e.jsx("option",{name:t.name,value:t.key,children:t.value},t.key))})]}),e.jsx($,{contentType:N,setContentValue:S})]})]})};export{ne as default};
