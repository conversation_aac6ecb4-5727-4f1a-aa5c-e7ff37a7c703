import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as m,b as C}from"./vendor-4f06b3f4.js";import{u as k}from"./react-hook-form-f3d72793.js";import{o as A}from"./yup-2324a46a.js";import{c as S,a as j}from"./yup-17027d7a.js";import{A as R,G as F,M as U,s as N,t as D}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";const Z=({setSidebar:t})=>{var f,h,b,g;const v=S({email:j().email().required(),password:j().required()}).required(),{dispatch:E}=m.useContext(R),{dispatch:r}=m.useContext(F),[d,n]=m.useState(!1);C();const{register:c,handleSubmit:p,setError:x,formState:{errors:a}}=k({resolver:A(v)}),u=async w=>{let o=new U;n(!0);try{const s=await o.register(w.email,w.password,"government");if(s.error){if(s.validation){const l=Object.keys(s.validation);for(let i=0;i<l.length;i++){const y=l[i];x(y,{type:"manual",message:s.validation[y]})}}}else{o.setTable("user");const l=await o.callRestAPI({id:s.user_id,verify:1},"PUT");N(r,"Added"),t(!1),r({type:"REFRESH_DATA",payload:{refreshData:!0}})}}catch(s){N(r,s.message,5e3,"error"),console.log("Error",s),x("email",{type:"manual",message:s.message}),D(E,s.message)}n(!1)};return e.jsxs("div",{className:"mx-auto  rounded",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[e.jsx("div",{className:"flex items-center gap-3",children:e.jsx("span",{className:"text-lg font-semibold",children:"Add User"})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 shadow-sm hover:bg-[#f4f4f4]",onClick:()=>t(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center rounded-md bg-[#4F46E5] px-3 py-2 text-white shadow-sm",onClick:async()=>{await p(u)(),t(!1)},disabled:d,children:d?"Saving...":"Save"})]})]}),e.jsxs("form",{className:" w-full p-4 text-left",onSubmit:p(u),children:[e.jsxs("div",{className:"mb-4 ",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"email",children:"Email"}),e.jsx("input",{type:"email",placeholder:"Email",...c("email"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(f=a.email)!=null&&f.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(h=a.email)==null?void 0:h.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"password",children:"Password"}),e.jsx("input",{type:"password",placeholder:"******************",...c("password"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(b=a.password)!=null&&b.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(g=a.password)==null?void 0:g.message})]})]})]})};export{Z as default};
