import{j as t}from"./@react-google-maps/api-ac2f9d6f.js";import{R as a,b as k,r as s,h as C}from"./vendor-4f06b3f4.js";import{u as F}from"./react-hook-form-f3d72793.js";import{o as L}from"./yup-2324a46a.js";import{c as M,a as p}from"./yup-17027d7a.js";import{M as G,A as J,G as O,t as B,s as H}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as u}from"./MkdInput-ff3aa862.js";import{I as U}from"./InteractiveButton-8f7d74ee.js";import{S as $}from"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";let f=new G;const Se=d=>{const{dispatch:y}=a.useContext(J),I=M({rating:p(),description:p(),client_id:p(),provider_id:p(),job_id:p()}).required(),{dispatch:b}=a.useContext(O),[h,q]=a.useState({}),[j,v]=a.useState(!1),[S,x]=a.useState(!1),w=k(),[K,E]=s.useState(0),[V,R]=s.useState(""),[z,N]=s.useState(""),[Q,D]=s.useState(""),[W,P]=s.useState(""),{register:l,handleSubmit:A,setError:_,setValue:n,formState:{errors:m}}=F({resolver:L(I)}),o=C();s.useEffect(function(){(async function(){try{x(!0),f.setTable("review");const e=await f.callRestAPI({id:d.activeId?d.activeId:Number(o==null?void 0:o.id)},"GET");e.error||(n("rating",e.model.rating),n("description",e.model.description),n("client_id",e.model.client_id),n("provider_id",e.model.provider_id),n("job_id",e.model.job_id),E(e.model.rating),R(e.model.description),N(e.model.client_id),D(e.model.provider_id),P(e.model.job_id),x(!1))}catch(e){x(!1),console.log("error",e),B(y,e.message)}})()},[]);const T=async e=>{v(!0);try{f.setTable("review");for(let c in h){let r=new FormData;r.append("file",h[c].file);let g=await f.uploadImage(r);e[c]=g.url}const i=await f.callRestAPI({id:d.activeId?d.activeId:Number(o==null?void 0:o.id),rating:e.rating,description:e.description,client_id:e.client_id,provider_id:e.provider_id,job_id:e.job_id},"PUT");if(!i.error)H(b,"Updated"),w("/admin/review"),b({type:"REFRESH_DATA",payload:{refreshData:!0}}),d.setSidebar(!1);else if(i.validation){const c=Object.keys(i.validation);for(let r=0;r<c.length;r++){const g=c[r];_(g,{type:"manual",message:i.validation[g]})}}v(!1)}catch(i){v(!1),console.log("Error",i),_("rating",{type:"manual",message:i.message})}};return a.useEffect(()=>{b({type:"SETPATH",payload:{path:"review"}})},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Review"}),S?t.jsx($,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:A(T),children:[t.jsx(u,{type:"number",page:"edit",name:"rating",errors:m,label:"Rating",placeholder:"Rating",register:l,className:""}),t.jsx(u,{type:"text",page:"edit",name:"description",errors:m,label:"Description",placeholder:"Description",register:l,className:""}),t.jsx(u,{type:"text",page:"edit",name:"client_id",errors:m,label:"Client Id",placeholder:"Client Id",register:l,className:""}),t.jsx(u,{type:"text",page:"edit",name:"provider_id",errors:m,label:"Provider Id",placeholder:"Provider Id",register:l,className:""}),t.jsx(u,{type:"text",page:"edit",name:"job_id",errors:m,label:"Job Id",placeholder:"Job Id",register:l,className:""}),t.jsx(U,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:j,disable:j,children:"Submit"})]})]})};export{Se as default};
