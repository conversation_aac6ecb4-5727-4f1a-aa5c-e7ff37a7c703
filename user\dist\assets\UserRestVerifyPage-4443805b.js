import{j as t}from"./@react-google-maps/api-ee55a349.js";import{B as S,a as R}from"./index-d54cffea.js";import{A as _,a as k}from"./index-dd254604.js";import{S as B}from"./index-5a645c18.js";import{M as C}from"./index-243c4859.js";import{M as D,G as L,s as h}from"./index-09a1718e.js";import{t as r}from"./i18next-7389dd8c.js";import{r as c,R as M,u as O,i as A,d as E}from"./vendor-b16525a8.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";let T=new D;const ce=()=>{const[n,f]=c.useState(["","","",""]),a=c.useRef([]),[x,G]=c.useState(!1),[m,K]=c.useState(!1),{dispatch:u}=M.useContext(L),g=O(),p=new URLSearchParams(g.search).get("email"),[j,i]=c.useState(!1);A();const b=(s,e)=>{if(isNaN(s))return;const o=[...n];o[e]=s,f(o),s&&e<3?a.current[e+1].focus():!s&&e>0&&a.current[e-1].focus()},N=s=>{const e=s.clipboardData.getData("text").split("").filter(l=>!isNaN(l)),o=[...n];e.slice(0,4).forEach((l,d)=>{o[d]=l,a.current[d].value=l}),f(o),e.length>=4?a.current[3].focus():e.length===3?a.current[2].focus():e.length===2?a.current[1].focus():e.length===1&&a.current[0].focus()},y=(s,e)=>{s.key==="Backspace"&&!n[e]&&e>0&&a.current[e-1].focus()},v=E(),w=async s=>{v(`/user/reset/${n.join("")}?phone=${p}`)},P=async()=>{console.log("Resend OTP");try{i(!0);const s=await T.callRawAPI("/v3/api/custom/chumpchange/user/resend-code",{email:p},"POST");h(u,"Sent successfully"),i(!1)}catch(s){i(!1),console.log("error >> ",s),h(u,s.message,5e3,"error")}};return console.log("otp >> ",n.join("")),t.jsxs(_,{children:[j&&t.jsx(C,{showCloseButton:!1,children:t.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[t.jsxs("div",{className:"text-lg font-semibold",children:[r("loading.sending"),"..."]}),t.jsx("div",{className:"mt-12",children:t.jsx(B,{})})]})}),t.jsx(S,{}),t.jsx(k,{className:"-mt-6 leading-none ",children:r("auth_pages.reset.title")}),t.jsxs("div",{className:"flex flex-col gap-4",children:[t.jsx("div",{className:" flex h-[70px] flex-col items-center justify-start",children:m?t.jsx("div",{className:" flex h-10 w-full items-center justify-center bg-[#f3e6eb]",children:t.jsx("p",{className:" text-center font-['Poppins'] text-sm font-medium text-[#f95050]",children:r("auth_pages.reset.invalid_code")})}):t.jsx("div",{className:"font-['Poppins'] text-sm font-semibold text-[#8080a3]",children:r("auth_pages.reset.v_sub_title")})}),t.jsx("div",{className:"flex w-full items-center justify-between gap-2 ",children:n.map((s,e)=>t.jsx("input",{type:"text",maxLength:"1",value:s,onChange:o=>b(o.target.value,e),onPaste:N,onKeyDown:o=>y(o,e),ref:o=>a.current[e]=o,className:`h-[83px] w-[72px] rounded-2xl border-2  text-center font-['Poppins'] text-2xl  font-medium text-black outline-none focus:outline-none focus:ring-0 ${m?"border-[#ff0000]/5 bg-[#ff0000]/5 focus:border-[#ff0000]/5":"border-[#fff] bg-white focus:border-[#fff] "} `},e))}),t.jsxs("div",{className:" mt-0 flex gap-4 ",children:[t.jsx("p",{className:"text-center font-['Poppins'] text-sm font-medium leading-[18.20px] text-[#8080a3]",children:r("auth_pages.reset.dont_get")}),t.jsx("p",{onClick:P,className:"text-center font-['Poppins'] text-sm font-medium leading-[18.20px] text-[#56ccf2]",children:r("auth_pages.reset.resend")})]}),t.jsx("div",{className:" mt-10 ",children:t.jsx(R,{loading:x,disabled:n.join("").length!==4,type:"submit",onClick:w,children:r("buttons.next")})})]})]})};export{ce as default};
