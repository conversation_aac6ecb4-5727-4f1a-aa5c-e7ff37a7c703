import React from "react";
import { CaretLeft } from "Assets/svgs";

import { NavLink } from "react-router-dom";

const BackButton = ({ text = "Back", link }) => {
  return (
    <div>
      <NavLink className="flex items-center gap-1 py-1 px-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition duration-200" to={link ? link : -1}>
        <CaretLeft className="text-white" />
        <span className="font-semibold">{text}</span>
      </NavLink>
    </div>
  );
};

export default BackButton;
