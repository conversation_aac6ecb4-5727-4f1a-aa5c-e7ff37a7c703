import{j as e}from"./@react-google-maps/api-ee55a349.js";import{r as m,R as n,i as b,d as g}from"./vendor-b16525a8.js";import"./index-9ef1c042.js";import{M as w,A as P,G as y,d as L}from"./index-09a1718e.js";import{B as _}from"./index-d54cffea.js";import{u as k}from"./user-a875fff3.js";import{C as M}from"./index-49471902.js";import{S}from"./index-5a645c18.js";import{t as l}from"./i18next-7389dd8c.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";const H=new w,D=()=>{m.useContext(P),m.useContext(y);const[s,f]=n.useState({}),[a,p]=n.useState([]),[r,h]=n.useState([]),[c,j]=n.useState([]),[v,o]=n.useState(!1),{id:u,provider:N}=b();g();const C=async()=>{var t,x,d;try{const i=await H.callRawAPI(`/v3/api/custom/chumpchange/user/${u}`,{},"GET");i.error||(f(i==null?void 0:i.data),p((t=i==null?void 0:i.data)==null?void 0:t.products),h((x=i==null?void 0:i.data)==null?void 0:x.skills),j((d=i==null?void 0:i.data)==null?void 0:d.paymentMethods))}catch(i){console.log(i)}};return n.useEffect(()=>{(async()=>(o(!0),await C(),o(!1)))()},[]),e.jsxs("div",{className:"p-5",children:[e.jsxs("div",{className:"relative flex w-full items-center justify-between ",children:[e.jsx("div",{className:" ",children:e.jsx(_,{})}),e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:l("user.provider_profile.title")}),e.jsx("div",{className:" "})]}),v?e.jsx("div",{className:" flex h-full w-full items-center justify-center",children:e.jsx(S,{})}):e.jsxs(e.Fragment,{children:[" ",e.jsx(M,{user:k,userData:s}),e.jsxs("div",{className:" mt-9 ",children:[e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:l("user.provider_profile.phone")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:s!=null&&s.phone?s==null?void 0:s.phone:"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:l("user.provider_profile.email")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:e.jsx("a",{href:`mailto:${s==null?void 0:s.email}`,className:" text-[#4fa7f9] underline ",children:s!=null&&s.email?s==null?void 0:s.email:"N/A"})})]}),e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:l("user.provider_profile.account_status")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:e.jsxs("button",{className:" text-[#4fa7f9] ",children:[l("user.provider_profile.active_on")," ",s!=null&&s.create_at?L(s==null?void 0:s.create_at):"N/A"]})})]}),N!=="false"&&e.jsxs("div",{className:"flex items-center justify-between border-t border-[#8181a4]/20 py-3 ",children:[e.jsx("div",{className:"font-['Poppins'] text-sm font-medium text-[#8080a3]",children:l("user.provider_profile.listing_balance")}),e.jsx("div",{className:"text-right font-['Poppins'] text-sm font-medium text-black",children:e.jsxs("button",{className:" text-[#4fa7f9]  ",children:["DOP"," ",s!=null&&s.listing_balance?s==null?void 0:s.listing_balance:"0"]})})]})]}),e.jsx("div",{className:" mt-[50px] ",children:e.jsxs("div",{className:"",children:[e.jsx("div",{className:"font-['Poppins'] text-lg font-medium text-black",children:l("user.provider_profile.help_with")}),e.jsx("div",{className:"scrollbar-hide mt-5 flex w-full overflow-x-auto ",children:e.jsx("div",{className:"flex gap-[3px]",children:e.jsx(e.Fragment,{children:r==null?void 0:r.map(t=>e.jsxs("div",{className:"flex h-[127px] w-[120px] cursor-pointer flex-col items-center justify-center rounded-[20px] bg-[#56ccf2]",children:[e.jsx("div",{className:" flex h-12 w-12 items-center justify-center overflow-hidden rounded-full bg-white backdrop-blur-[20px]",children:e.jsx("img",{src:t==null?void 0:t.logo,alt:"",className:" h-full w-full object-cover "})}),e.jsx("div",{className:" mt-2 text-center font-['Poppins'] text-xs font-medium text-black",children:t==null?void 0:t.name}),e.jsx("div",{className:" inline-flex items-center justify-start gap-1",children:e.jsxs("div",{className:"text-center font-['Poppins'] text-xs font-medium text-[#8080a3]",children:["$",t==null?void 0:t.price]})})]}))})})})]})}),e.jsx("div",{className:" mt-[50px] ",children:e.jsxs("div",{className:"",children:[e.jsx("div",{className:"font-['Poppins'] text-lg font-medium text-black",children:l("user.provider_profile.ambulantly")}),e.jsx("div",{className:"scrollbar-hide mt-5 flex w-full overflow-x-auto  ",children:e.jsx("div",{className:" flex gap-[3px] ",children:e.jsx(e.Fragment,{children:a==null?void 0:a.map(t=>e.jsxs("div",{className:"flex h-[127px] w-[120px] cursor-pointer flex-col items-center justify-center rounded-[20px] bg-[#56ccf2]",children:[e.jsx("div",{className:" flex h-12 w-12 items-center justify-center overflow-hidden rounded-full bg-white backdrop-blur-[20px]",children:e.jsx("img",{src:t==null?void 0:t.logo,alt:"",className:" h-full w-full object-cover "})}),e.jsx("div",{className:" mt-2 text-center font-['Poppins'] text-xs font-medium text-black",children:t==null?void 0:t.name}),e.jsx("div",{className:" inline-flex items-center justify-start gap-1",children:e.jsxs("div",{className:"text-center font-['Poppins'] text-xs font-medium text-[#8080a3]",children:["$",(t==null?void 0:t.amount)||0]})})]}))})})})]})}),e.jsxs("div",{className:" mt-[55px] ",children:[e.jsx("div",{className:"font-['Poppins'] text-lg font-medium text-black",children:l("user.provider_profile.payments")}),e.jsx(e.Fragment,{children:c==null?void 0:c.map(t=>e.jsxs("div",{className:"mt-[11px] flex w-full items-center gap-4 rounded-[20px] bg-[#56ccf2] p-[6px]",children:[e.jsx("div",{className:" flex h-12 w-12 items-center justify-center rounded-[32px] bg-white backdrop-blur-[20px] ",children:e.jsx("svg",{width:"32",height:"31",viewBox:"0 0 32 31",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M24.0381 0.884277L17.5385 4.42274L6.76922 10.3083L0.269531 13.8849L3.07692 18.9618V15C3.07692 14.5103 3.27142 14.0408 3.61764 13.6945C3.96386 13.3483 4.43344 13.1538 4.92307 13.1538H5.92369L5.96061 13L20.2314 5.19197L21.3083 5.49966C21.3378 5.59812 21.3698 5.71259 21.4228 5.80735C21.8461 6.58274 22.8012 6.88551 23.5766 6.46212C23.6701 6.41043 23.7305 6.29843 23.808 6.23074L24.9231 6.57659L27.3846 11.0775L27.0769 12.1926C26.9797 12.2221 26.864 12.2172 26.7692 12.2689C26.4086 12.4658 26.1661 12.7957 26.0381 13.1538H30.7692L27.5766 7.34582L24.0381 0.884277ZM22.6929 8.96182C22.3324 9.03893 22.0094 9.23772 21.7781 9.5248C21.5469 9.81188 21.4214 10.1698 21.4228 10.5384C21.4228 11.4197 22.1181 12.1544 23.0006 12.1544C23.8818 12.1544 24.6154 11.4197 24.6154 10.5384C24.6154 9.6572 23.8818 8.96182 22.9994 8.96182C22.8898 8.96182 22.7975 8.93966 22.6929 8.96182ZM16.6929 10.0387C15.7529 10.0111 14.8228 10.2369 14 10.6923C12.928 11.2781 12.1255 12.1741 11.6923 13.1538H20.6154C20.5437 12.777 20.4137 12.4137 20.2301 12.0769C19.5335 10.8043 18.1834 10.083 16.6929 10.0387ZM4.92307 15V31H32V15H4.92307ZM10.3458 16.9618H26.5772L27.3846 17.7692C27.3637 17.8714 27.3071 17.9686 27.3071 18.0769C27.3071 18.9594 28.0406 19.6929 28.9231 19.6929C29.0314 19.6929 29.1286 19.635 29.2308 19.6154L30.0381 20.4227V25.5772L29.2308 26.3846C29.1286 26.3637 29.0314 26.307 28.9231 26.307C28.0406 26.307 27.3071 27.0406 27.3071 27.923C27.3071 28.0314 27.3637 28.1286 27.3846 28.2307L26.5772 29.0381H10.3458L9.53845 28.2307C9.55938 28.1286 9.61599 28.0314 9.61599 27.923C9.61599 27.0406 8.88245 26.307 7.99999 26.307C7.89169 26.307 7.79445 26.3649 7.6923 26.3846L6.88492 25.5772V20.4227L7.6923 19.6154C7.79445 19.6363 7.89169 19.6929 7.99999 19.6929C8.88245 19.6929 9.61599 18.9594 9.61599 18.0769C9.61599 17.9686 9.55938 17.8714 9.53845 17.7692L10.3458 16.9618ZM18.4615 18.808C15.7932 18.808 13.6148 20.6787 13.6148 23C13.6148 25.3212 15.7932 27.192 18.4615 27.192C21.1298 27.192 23.3083 25.3212 23.3083 23C23.3083 20.6787 21.1298 18.808 18.4615 18.808ZM10.4615 21.384C9.5803 21.384 8.84553 22.1187 8.84553 23C8.84553 23.8812 9.5803 24.616 10.4615 24.616C11.3428 24.616 12.0775 23.8812 12.0775 23C12.0775 22.1187 11.3428 21.384 10.4615 21.384ZM26.4615 21.384C25.5791 21.384 24.8455 22.1175 24.8455 23C24.8455 23.8824 25.5791 24.616 26.4615 24.616C27.344 24.616 28.0775 23.8824 28.0775 23C28.0775 22.1175 27.344 21.384 26.4615 21.384Z",fill:"#56CCF2",stroke:"#56CCF2"})})}),e.jsxs("div",{className:"flex flex-col gap-[6px] py-3 ",children:[e.jsxs("div",{className:" font-['Poppins'] text-xs font-medium text-black",children:[t.method_type," - ",t.account_type]}),e.jsxs("div",{className:" font-['Poppins'] text-xs font-medium text-[#8080a3] ",children:["Acc.# ",t.account_number]}),e.jsxs("div",{className:" font-['Poppins'] text-xs font-medium text-[#8080a3] ",children:["RNC/Cedula# ",t.cedula_rnc]})]})]}))})]})]})]})};export{D as default};
