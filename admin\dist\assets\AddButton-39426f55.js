import{j as m}from"./@react-google-maps/api-ac2f9d6f.js";import{r as d}from"./vendor-4f06b3f4.js";import{c as l}from"./AddButton.module-98aac587.js";const p=({onClick:e,children:n="Add New",showPlus:r=!0,className:o,showChildren:i=!0})=>{const[s,t]=d.useState(!1),a=()=>{e&&e(),t(!0)};return m.jsxs("button",{onAnimationEnd:()=>t(!1),onClick:a,className:`${s&&"animate-wiggle"} ${l.button} relative flex h-10 w-fit min-w-fit items-center justify-center overflow-hidden rounded-md border border-primaryBlue bg-indigo-600 px-[.6125rem] py-[.5625rem] text-sm font-medium leading-none text-white shadow-md shadow-indigo-600  ${o}`,children:[r?"+":null," ",i?n:null]})};export{p as A};
