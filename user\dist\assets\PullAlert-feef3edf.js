import{j as r}from"./@react-google-maps/api-ee55a349.js";import{a as m}from"./index-d54cffea.js";import{M as f}from"./index-243c4859.js";import{t as o}from"./i18next-7389dd8c.js";import{r as s}from"./vendor-b16525a8.js";import{M as u}from"./index-09a1718e.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";let n=new u;const B=()=>{const[p,a]=s.useState(!1),[t,x]=s.useState({}),l=async()=>{if(localStorage.getItem("token"))try{const e=await n.callRawAPI("/v3/api/custom/chumpchange/common/alert-poll",{},"GET");e.error||x(e.data)}catch(e){console.error("Error fetching alert:",e)}finally{l()}};s.useEffect(()=>{l()},[]);const c=async e=>{try{a(!1);const i=await n.callRawAPI(`/v3/api/custom/chumpchange/common/ping-response/${t==null?void 0:t.ping_id}`,{response:e},"POST")}catch(i){console.error("Error fetching alert:",i)}};return r.jsx("div",{children:p&&r.jsxs(f,{closeModal:()=>a(!1),children:[r.jsxs("div",{className:"mx-auto mt-10 max-w-[230px] text-center font-['Poppins'] text-2xl font-medium leading-[31.20px] text-black",children:[t==null?void 0:t.service_name," ",o("user.get_alert.desc")]}),r.jsxs("div",{className:"mt-10 flex justify-center gap-5 ",children:[r.jsx(m,{onClick:()=>c(0),className:"max-w-[163px] !bg-[#8181a4]/20 !text-[#8080a3]",children:o("user.get_alert.not")}),r.jsx(m,{onClick:()=>c(1),className:"max-w-[163px]",children:o("user.get_alert.yes")})]})]})})};export{B as default};
