import{j as e}from"./@react-google-maps/api-ee55a349.js";import{_ as h}from"./qr-scanner-cf010ec4.js";import{r as t,L as u}from"./vendor-b16525a8.js";import{B as j}from"./index-d54cffea.js";import{S as c}from"./index-5a645c18.js";import{M as g}from"./index-243c4859.js";import{M as v,A as w,G as C}from"./index-09a1718e.js";import{t as E}from"./i18next-7389dd8c.js";import"./react-confirm-alert-c06b7fb4.js";import"./moment-a9aaa855.js";import"./@headlessui/react-518241d3.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-20049f1e.js";import"./@fortawesome/react-fontawesome-88fe485e.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-i18next-4a61273e.js";const N=t.lazy(()=>h(()=>import("./Address-74bcc1b1.js"),["assets/Address-74bcc1b1.js","assets/@react-google-maps/api-ee55a349.js","assets/vendor-b16525a8.js","assets/index-d54cffea.js","assets/qr-scanner-cf010ec4.js","assets/index-243c4859.js","assets/i18next-7389dd8c.js"])),n=new v,z=()=>{t.useContext(w);const{state:y,dispatch:d}=t.useContext(C),[a,m]=t.useState([]),[x,r]=t.useState(!0),[p,l]=t.useState(!1),o=async()=>{try{r(!0);const s=await n.callRawAPI("/v3/api/custom/chumpchange/users/addresses",{},"GET");console.log(s),s.error||m(s.data),r(!1)}catch{r(!1)}};t.useEffect(()=>{(async()=>await o())()},[]);const f=async s=>{try{l(!0);const i=await n.callRawAPI(`/v3/api/custom/chumpchange/user/address/delete/${s}`,{},"DELETE");await o(),showToast(d,"Deleted"),l(!1)}catch{l(!1)}};return e.jsxs("div",{className:"p-5",children:[p&&e.jsx(g,{showCloseButton:!1,children:e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center ",children:[e.jsx("div",{className:"text-lg font-semibold",children:"Deleting..."}),e.jsx("div",{className:"mt-12",children:e.jsx(c,{})})]})}),e.jsxs("div",{className:"relative flex w-full items-center justify-between ",children:[e.jsx("div",{className:" ",children:e.jsx(j,{link:"/user/profile"})}),e.jsx("div",{className:"text-center font-['Poppins'] text-lg font-medium text-black",children:E("user.address.title")}),e.jsx("div",{className:" ",children:e.jsx(u,{to:"/user/add-address",className:"",children:e.jsxs("svg",{width:"43",height:"43",viewBox:"0 0 43 43",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{width:"43",height:"43",rx:"21.5",fill:"#E1E2E3"}),e.jsx("path",{d:"M20.1667 14.3333C20.1667 13.597 20.7636 13 21.5 13C22.2364 13 22.8333 13.597 22.8333 14.3333V27.6667C22.8333 28.403 22.2364 29 21.5 29C20.7636 29 20.1667 28.403 20.1667 27.6667V14.3333Z",fill:"#56CCF2"}),e.jsx("path",{d:"M14.8333 22.3333C14.097 22.3333 13.5 21.7364 13.5 21C13.5 20.2636 14.097 19.6667 14.8333 19.6667H28.1667C28.903 19.6667 29.5 20.2636 29.5 21C29.5 21.7364 28.903 22.3333 28.1667 22.3333H14.8333Z",fill:"#56CCF2"})]})})})]}),e.jsx("div",{className:" mt-[55px] flex flex-col gap-7 ",children:x?e.jsx("div",{className:"flex h-full w-full items-center justify-center pt-10 ",children:e.jsx(c,{})}):a==null?void 0:a.map((s,i)=>e.jsx(N,{item:s,handleRemove:f},i))})]})};export{z as default};
