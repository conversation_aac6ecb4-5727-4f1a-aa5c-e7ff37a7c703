import{j as t}from"./@react-google-maps/api-afbf18d5.js";import{h as _,r as s,R as S,L as Y}from"./vendor-f36d475e.js";import"./index-5deedf4a.js";import{M as T,A as M,G as C}from"./index-cf5e6bc7.js";import{P as L}from"./index-55e4d382.js";import{E as f}from"./index-010bc024.js";import{S as R}from"./index-65bc3378.js";import{h as u}from"./moment-a9aaa855.js";import{u as D}from"./react-i18next-1e3e6bc5.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-2487dba8.js";import"./@react-pdf-viewer/core-9d395990.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";const h=new T,ot=()=>{var p,x;const{t:a}=D();_();const[g,l]=s.useState(!1);s.useContext(M);const{state:G,dispatch:v}=s.useContext(C),[r,m]=s.useState("active"),[o,y]=s.useState([]),[j,b]=s.useState(0),[d,w]=s.useState([]),[k,N]=s.useState(0),E=async()=>{try{const e=await h.callRawAPI("/v3/api/custom/chumpchange/provider/earnings/monthly",{},"GET");if(!e.error){y(e.data);const n=e.data.reduce((i,c)=>i+c.provider_task_amount,0);b(n)}}catch(e){console.error("Error fetching active tasks:",e)}},P=async()=>{try{const e=await h.callRawAPI("/v3/api/custom/chumpchange/provider/earnings/ytd",{},"GET");if(!e.error){w(e.data);const n=e.data.reduce((i,c)=>i+c.provider_task_amount,0);N(n)}}catch(e){return console.error("Error fetching expired tasks:",e),[]}};return S.useEffect(()=>{v({type:"SETPATH",payload:{path:"earnings"}}),(async()=>(l(!0),await E(),await P(),l(!1)))()},[]),t.jsxs(L,{className:"",children:[t.jsxs("div",{className:" mt-2 flex items-center justify-between px-10 ",children:[t.jsx("div",{className:"font-['Poppins'] text-lg font-bold text-black",children:a("provider.earnings.title")}),t.jsx(Y,{to:"/provider/transaction-history",className:"relative flex h-10 w-[74px] items-center justify-center rounded-[100px] bg-[#56ccf2] font-['Poppins'] text-sm font-semibold text-black ",children:a("provider.earnings.more")})]}),t.jsxs("div",{className:" mt-14 px-5 ",children:[t.jsxs("div",{className:" grid h-12 w-full grid-cols-2 items-center rounded-2xl bg-[#8181a4]/20 px-1",children:[t.jsx("button",{className:`flex h-10 items-center justify-center rounded-2xl font-['Poppins'] text-sm font-medium ${r==="active"?"bg-black text-white":"text-black"}`,onClick:()=>m("active"),children:a("provider.earnings.this_month")}),t.jsx("button",{className:`flex h-10 items-center justify-center rounded-2xl font-['Poppins'] text-sm font-medium ${r==="expired"?"bg-black text-white":"text-black"}`,onClick:()=>m("expired"),children:a("provider.earnings.year_to_date")})]}),t.jsxs("div",{className:" mt-10 flex flex-col items-center justify-center",children:[t.jsxs("div",{className:"text-center font-['Poppins'] text-[42px] font-medium text-[#56ccf2]",children:["$",r==="active"?j:k]}),t.jsx("div",{className:"text-center font-['Poppins'] text-base font-normal text-black",children:(o==null?void 0:o.length)>0&&(r==="active"?u((p=o[0])==null?void 0:p.create_at).format("MMMM YYYY"):a("provider.earnings.total_amount")+" "+u((x=d[0])==null?void 0:x.create_at).format("YYYY"))})]})]}),t.jsxs("div",{className:" relative left-0 mt-6 flex w-full flex-1 flex-col items-center justify-start overflow-hidden rounded-tl-[32px] rounded-tr-[32px] bg-white",children:[t.jsx("div",{className:" flex h-7 w-full max-w-[335px] items-center justify-center border-b bg-white ",children:t.jsx("div",{className:" h-1 w-[50px] rounded bg-[#f2f2f7]"})}),g?t.jsx("div",{className:"flex h-full w-full items-center justify-center pt-10 ",children:t.jsx(R,{})}):r==="active"?t.jsx(f,{taskData:o}):t.jsx(f,{taskData:d})]})]})};export{ot as default};
