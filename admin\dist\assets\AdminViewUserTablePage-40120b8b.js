import{j as r}from"./@react-google-maps/api-ac2f9d6f.js";import{S as _}from"./index-2d8231e7.js";import{M as g,G as p}from"./index-06b5b6dd.js";import{h as i}from"./moment-a9aaa855.js";import{R as n,h as y}from"./vendor-4f06b3f4.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-525c3702.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";let u=new g;const l=()=>{const{dispatch:f}=n.useContext(p),{dispatch:h}=n.useContext(p),[t,e]=n.useState({}),[x,m]=n.useState(!0),c=y();n.useEffect(function(){(async function(){try{m(!0),u.setTable("user");const s=await u.callRestAPI({id:Number(c==null?void 0:c.id),join:""},"GET");s.error||(e(s.model),m(!1))}catch(s){m(!1),console.log("error",s),tokenExpireError(h,s.message)}})()},[]);const a=(s,o)=>r.jsx("div",{className:"mb-4 mt-4",children:r.jsxs("div",{className:"mb-4 flex",children:[r.jsx("div",{className:"flex-1",children:s}),r.jsx("div",{className:"flex-1",children:typeof o=="object"?JSON.stringify(o):o})]})});return n.useEffect(()=>{f({type:"SHOW_BACKBUTTON",payload:{showBackButton:!0}})},[]),r.jsx("div",{children:r.jsx("div",{className:"mx-auto rounded p-5 shadow-md",children:x?r.jsx(_,{}):r.jsxs(r.Fragment,{children:[a("Id",t==null?void 0:t.id),a("Oauth",t==null?void 0:t.oauth),a("Role",t==null?void 0:t.role),a("First Name",t==null?void 0:t.first_name),a("Last Name",t==null?void 0:t.last_name),a("Email",t==null?void 0:t.email),a("Password",t==null?void 0:t.password),a("Type",t==null?void 0:t.type),a("Verify",t==null?void 0:t.verify),a("Phone",t==null?void 0:t.phone),a("Photo",t==null?void 0:t.photo),a("Operating City",t==null?void 0:t.operating_city),a("Refer",t==null?void 0:t.refer),a("Stripe Uid",t==null?void 0:t.stripe_uid),a("Paypal Uid",t==null?void 0:t.paypal_uid),a("Listing Balance",t==null?void 0:t.listing_balance),a("Confirmation Code",t==null?void 0:t.confirmation_code),a("Two Factor Authentication",t==null?void 0:t.two_factor_authentication),a("Cedula Number",t==null?void 0:t.cedula_number),a("Cedula Image Link",t==null?void 0:t.cedula_image_link),a("Qr Link",t==null?void 0:t.qr_link),a("Status",t==null?void 0:t.status),a("Create At",i(t==null?void 0:t.create_at).format("DD/MM/YYYY")),a("Update At",i(t==null?void 0:t.update_at).format("DD/MM/YYYY"))]})})})};export{l as default};
