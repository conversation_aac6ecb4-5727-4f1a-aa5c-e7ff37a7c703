import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as l,b as v}from"./vendor-4f06b3f4.js";import{u as A}from"./react-hook-form-f3d72793.js";import{o as E}from"./yup-2324a46a.js";import{c as k,a as o}from"./yup-17027d7a.js";import{G as D,A as T,M as R,s as C,t as F}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import"./react-quill-f727e59f.js";/* empty css                   */import{M as i}from"./MkdInput-ff3aa862.js";import{I as q}from"./InteractiveButton-8f7d74ee.js";import"./index-2d8231e7.js";import"./@hookform/resolvers-1aa18522.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";import"./@craftjs/core-4ea9888f.js";import"./MoonLoader-16bed42a.js";const pe=({setSidebar:N})=>{var h,f,y,j;const{dispatch:c}=l.useContext(D),_=k({product_name:o(),product_type_id:o(),user_id:o(),price:o(),quantity:o(),item_condition:o(),description:o(),product_images:o(),status:o()}).required(),{dispatch:w}=l.useContext(T),[x,M]=l.useState({}),[g,p]=l.useState(!1),S=v(),{register:s,handleSubmit:I,setError:b,setValue:O,formState:{errors:t}}=A({resolver:E(_)});l.useState([]);const P=async a=>{let u=new R;p(!0);try{for(let m in x){let d=new FormData;d.append("file",x[m].file);let n=await u.uploadImage(d);a[m]=n.url}u.setTable("productlisting");const r=await u.callRestAPI({product_name:a.product_name,product_type_id:a.product_type_id,user_id:a.user_id,price:a.price,quantity:a.quantity,item_condition:a.item_condition,description:a.description,product_images:a.product_images,status:a.status},"POST");if(!r.error)C(c,"Added"),S("/admin/productlisting"),N(!1),c({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(r.validation){const m=Object.keys(r.validation);for(let d=0;d<m.length;d++){const n=m[d];b(n,{type:"manual",message:r.validation[n]})}}p(!1)}catch(r){p(!1),console.log("Error",r),b("product_name",{type:"manual",message:r.message}),F(w,r.message)}};return l.useEffect(()=>{c({type:"SETPATH",payload:{path:"productlisting"}})},[]),e.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Productlisting"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:I(P),children:[e.jsx(i,{type:"text",page:"add",name:"product_name",errors:t,label:"Product Name",placeholder:"Product Name",register:s,className:""}),e.jsx(i,{type:"number",page:"add",name:"product_type_id",errors:t,label:"Product Type Id",placeholder:"Product Type Id",register:s,className:""}),e.jsx(i,{type:"number",page:"add",name:"user_id",errors:t,label:"User Id",placeholder:"User Id",register:s,className:""}),e.jsx(i,{type:"number",page:"add",name:"price",errors:t,label:"Price",placeholder:"Price",register:s,className:""}),e.jsx(i,{type:"number",page:"add",name:"quantity",errors:t,label:"Quantity",placeholder:"Quantity",register:s,className:""}),e.jsx(i,{type:"text",page:"add",name:"item_condition",errors:t,label:"Item Condition",placeholder:"Item Condition",register:s,className:""}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"description",children:"Description"}),e.jsx("textarea",{placeholder:"Description",...s("description"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(h=t.description)!=null&&h.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(f=t.description)==null?void 0:f.message})]}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"product_images",children:"Product Images"}),e.jsx("textarea",{placeholder:"Product Images",...s("product_images"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(y=t.product_images)!=null&&y.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(j=t.product_images)==null?void 0:j.message})]}),e.jsx(i,{type:"text",page:"add",name:"status",errors:t,label:"Status",placeholder:"Status",register:s,className:""}),e.jsx(q,{type:"submit",loading:g,disabled:g,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{pe as default};
