import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as a,L as x,i as c}from"./vendor-4f06b3f4.js";import{P as m}from"./index.esm-e652e625.js";import{M as d,G as p,A as f,t as h}from"./index-06b5b6dd.js";import{M as C}from"./index.esm-8e8a99ba.js";import"./moment-a9aaa855.js";import{i as u}from"./admin-ad25220a.js";import"./react-icons-e5379072.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";let v=new d;const g=[{to:"/government/dashboard",text:"Dashboard",icon:e.jsx(C,{className:"text-xl text-[currentColor]"}),value:"government"},{to:"/government/user",text:"Workers",icon:e.jsx("svg",{width:"23",height:"22",viewBox:"0 0 23 22",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M22.8725 9.0245L22.1225 7.7255L20 8.951V6.5H18.5V8.951L16.3775 7.7255L15.6275 9.0245L17.75 10.25L15.6275 11.4755L16.3775 12.7745L18.5 11.549V14H20V11.549L22.1225 12.7745L22.8725 11.4755L20.75 10.25L22.8725 9.0245ZM15.5 21.5H14V17.75C13.9988 16.7558 13.6033 15.8027 12.9003 15.0997C12.1973 14.3967 11.2442 14.0012 10.25 14H5.75C4.7558 14.0012 3.80267 14.3967 3.09966 15.0997C2.39666 15.8027 2.00119 16.7558 2 17.75V21.5H0.5V17.75C0.501588 16.3581 1.05522 15.0237 2.03944 14.0394C3.02367 13.0552 4.3581 12.5016 5.75 12.5H10.25C11.6419 12.5016 12.9763 13.0552 13.9606 14.0394C14.9448 15.0237 15.4984 16.3581 15.5 17.75V21.5ZM8 2C8.99456 2 9.94839 2.39509 10.6517 3.09835C11.3549 3.80161 11.75 4.75544 11.75 5.75C11.75 6.74456 11.3549 7.69839 10.6517 8.40165C9.94839 9.10491 8.99456 9.5 8 9.5C7.00544 9.5 6.05161 9.10491 5.34835 8.40165C4.64509 7.69839 4.25 6.74456 4.25 5.75C4.25 4.75544 4.64509 3.80161 5.34835 3.09835C6.05161 2.39509 7.00544 2 8 2ZM8 0.5C6.60761 0.5 5.27226 1.05312 4.28769 2.03769C3.30312 3.02226 2.75 4.35761 2.75 5.75C2.75 7.14239 3.30312 8.47774 4.28769 9.46231C5.27226 10.4469 6.60761 11 8 11C9.39239 11 10.7277 10.4469 11.7123 9.46231C12.6969 8.47774 13.25 7.14239 13.25 5.75C13.25 4.35761 12.6969 3.02226 11.7123 2.03769C10.7277 1.05312 9.39239 0.5 8 0.5Z",fill:"currentColor"})}),value:"user"},{to:"/government/analytics",text:"Analytics",value:"analytics"},{to:"/government/profile",text:"Profile",icon:e.jsx(m,{className:"text-xl text-[currentColor]"}),value:"profile"}],$=()=>{const{state:{isOpen:r,path:o},dispatch:i}=a.useContext(p),{state:j,dispatch:l}=a.useContext(f);a.useState(!1),a.useState(!1);let n=t=>{i({type:"OPEN_SIDEBAR",payload:{isOpen:t}})};return a.useEffect(()=>{async function t(){try{const s=await v.getProfile();l({type:"UPDATE_PROFILE",payload:s})}catch(s){console.log("Error",s),h(l,s.response.data.message?s.response.data.message:s.message)}}t()},[]),e.jsx(e.Fragment,{children:e.jsx(e.Fragment,{children:e.jsxs("div",{className:`z-50 flex max-h-screen flex-1 flex-col overflow-y-auto border border-[#E0E0E0] bg-white py-4 text-[#A8A8A8] transition-all ${r?"fixed h-screen w-[15rem] min-w-[15rem] max-w-[15rem] md:relative":"relative min-h-screen w-[4.2rem] min-w-[4.2rem] max-w-[4.2rem] bg-black text-white"} `,children:[e.jsxs("div",{className:`text-[#393939] ${r?"flex w-full":"flex items-center justify-center"} `,children:[e.jsx("div",{}),r&&e.jsx("div",{className:"flex w-full items-center justify-center text-2xl font-bold ",children:e.jsx(x,{to:"/",children:e.jsxs("h4",{className:"inline-flex h-[46px] w-[114px] items-center justify-between rounded-[100px] bg-[#42cbee]/20 pl-2 pr-2",children:[e.jsx("img",{src:u,alt:""}),e.jsx("div",{className:" font-['Poppins'] text-xs font-semibold leading-[18px] text-black",children:"CHIRIPERO"})]})})})]}),e.jsx("div",{className:"mt-4 h-fit w-auto flex-1 ",children:e.jsx("div",{className:"sidebar-list w-auto",children:e.jsx("ul",{className:"flex flex-wrap px-2 text-sm",children:g.map((t,s)=>e.jsx("li",{className:"block w-full list-none",children:e.jsx(c,{to:t.to,className:` font-['Poppins'] text-base font-normal leading-normal ${o==t.value?"active-nav !bg-[#56ccf2] !text-[#fff] ":" !text-[#8181A4] "} `,children:e.jsxs("div",{className:"flex items-center gap-3",children:[t.icon,r&&e.jsx("span",{children:t.text})]})})},s))})})}),e.jsx("div",{className:"flex justify-end",children:e.jsx("div",{className:"mr-3 cursor-pointer rounded-lg border border-[#E0E0E0] bg-white p-2 text-2xl text-gray-400",children:e.jsx("span",{onClick:()=>n(!r),children:e.jsx("svg",{className:`transition-transform ${r?"":"rotate-180"}`,xmlns:"http:www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12ZM10.4142 11L11.7071 9.70711C12.0976 9.31658 12.0976 8.68342 11.7071 8.29289C11.3166 7.90237 10.6834 7.90237 10.2929 8.29289L7.82322 10.7626C7.13981 11.446 7.13981 12.554 7.82322 13.2374L10.2929 15.7071C10.6834 16.0976 11.3166 16.0976 11.7071 15.7071C12.0976 15.3166 12.0976 14.6834 11.7071 14.2929L10.4142 13H16C16.5523 13 17 12.5523 17 12C17 11.4477 16.5523 11 16 11H10.4142Z",fill:"#A8A8A8"})})})})})]})})})};export{$ as GovernmentHeader,$ as default};
