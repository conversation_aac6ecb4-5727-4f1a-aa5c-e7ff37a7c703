import{j as e}from"./@react-google-maps/api-ac2f9d6f.js";import{R as s,b as x,r as g}from"./vendor-4f06b3f4.js";import{M as w,A as b,G as A,K as E,L as v}from"./index-06b5b6dd.js";import"./moment-a9aaa855.js";import{L as o}from"./index-6416aa2c.js";import{M as c}from"./index-d97c616d.js";import{M as j}from"./index-68c82eaa.js";import"./react-confirm-alert-525c3702.js";import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-d39d893a.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-d923fcf0.js";import"./@fortawesome/react-fontawesome-6b681b2b.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-ec2f391c.js";import"./react-i18next-c78f8e57.js";new w;const M=[{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Provider Id",accessor:"provider_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Plan Id",accessor:"plan_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Update At",accessor:"update_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"End At",accessor:"end_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],H=()=>{s.useContext(b),s.useContext(A);const p=x(),[m,a]=s.useState(!1),[r,i]=s.useState(!1),[f,u]=s.useState(),h=g.useRef(null),[I,S]=s.useState([]),d=(t,l,n=[])=>{switch(t){case"add":a(l);break;case"edit":i(l),S(n),u(n[0]);break}};return e.jsxs(e.Fragment,{children:[e.jsx(e.Fragment,{children:e.jsx("div",{className:"overflow-x-auto  rounded bg-white p-5 shadow",children:e.jsx(o,{children:e.jsx(j,{columns:M,tableRole:"admin",table:"user_plan",actionId:"id",actions:{view:{show:!0,action:t=>{p(`/admin/view-activation_requests/${t.id}`)},multiple:!1},edit:{show:!0,multiple:!1,action:t=>d("edit",!0,t)},delete:{show:!0,action:null,multiple:!1},select:{show:!1,action:null,multiple:!1},add:{show:!1,action:()=>d("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!0,action:null,multiple:!0}},actionPostion:"ontable",refreshRef:h,defaultFilter:["status,eq,'created'"]})})})}),e.jsx(o,{children:e.jsx(c,{isModalActive:m,closeModalFn:()=>a(!1),children:e.jsx(E,{setSidebar:a})})}),r&&e.jsx(o,{children:e.jsx(c,{isModalActive:r,closeModalFn:()=>i(!1),children:e.jsx(v,{activeId:f,setSidebar:i})})})]})};export{H as default};
