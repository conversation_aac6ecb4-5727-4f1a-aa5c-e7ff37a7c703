import{j as e}from"./@react-google-maps/api-afbf18d5.js";import{h as H,r as i,R as p,L as c}from"./vendor-f36d475e.js";import"./index-5deedf4a.js";import{M as S,A as B,G as k,t as F}from"./index-cf5e6bc7.js";import{P}from"./index-55e4d382.js";import{u as D}from"./user-a875fff3.js";import{H as V}from"./index-010bc024.js";import{S as u}from"./index-65bc3378.js";import{u as M}from"./react-i18next-1e3e6bc5.js";import"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-2487dba8.js";import"./moment-a9aaa855.js";import"./@react-pdf-viewer/core-9d395990.js";import"./@headlessui/react-46b39f71.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-b3bb6c9d.js";import"./@fortawesome/react-fontawesome-eb6bfecd.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./i18next-7389dd8c.js";const h=new S,l1=()=>{var m;H();const{state:l,dispatch:C}=i.useContext(B),{state:G,dispatch:g}=i.useContext(k),[j,d]=i.useState(!1),[v,o]=i.useState(!1),[w,b]=i.useState([]),[f,x]=p.useState([]),{t:a}=M(),y=async()=>{try{o(!0);const t=await h.callRawAPI("/v3/api/custom/chumpchange/users/services",{},"GET");if(!t.error){const s=t==null?void 0:t.data.map(r=>({...r,active:!1}));x(s),o(!1)}}catch(t){o(!1),console.log("error",t),F(C,t.message)}},n=async t=>{console.log("serviceType >>",t);try{d(!0);let s="/v3/api/custom/chumpchange/provider/services/home_all";t&&(s=`/v3/api/custom/chumpchange/provider/services/home_filter/${t}`);const r=await h.callRawAPI(s,{},"GET");r.error||b(r.data)}catch(s){console.log("error >>",s)}finally{d(!1)}};p.useEffect(()=>{g({type:"SETPATH",payload:{path:"providers"}}),y(),n()},[]);const _=t=>{const s=f.map((r,N)=>({...r,active:N===t?!r.active:!1}));x(s),s[t].active?(console.log("newData[index].id >>",s[t]),n(s[t].id)):n()};return console.log("state",l.userDetails),e.jsxs(P,{className:" relative ",children:[e.jsxs("div",{className:" relative h-full w-full px-3 py-4 ",children:[e.jsxs("div",{className:"mb-[31px] flex items-center justify-between px-2 py-5",children:[e.jsxs("div",{className:"font-['Poppins'] text-2xl font-bold text-black",children:["Chiripero ",l.userDetails.first_name]}),e.jsx(c,{to:"/provider/profile",className:" relative z-10 ",children:e.jsx("img",{className:"h-[45px] w-[45px] rounded-full object-cover",src:l.userDetails.photo||D})})]}),e.jsxs("svg",{className:"absolute right-0 top-0 ",width:"216",height:"336",viewBox:"0 0 216 336",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("g",{filter:"url(#filter0_f_6956_1462)",children:e.jsx("path",{d:"M244.642 213.42C239.945 245.036 244.414 225.866 217.471 235.334L184.308 226.203C170.469 214.856 140.907 189.848 133.376 180.593C125.845 171.337 108.053 146.927 100.098 135.879C121.861 137.315 174.008 141.627 208.497 147.392C251.608 154.597 227.544 111.441 220.686 98.9126C213.827 86.3842 204.838 90.8648 174.206 84.7494C143.574 78.634 173.079 73.0441 192.697 47.7829C212.314 22.5217 254.265 38.9644 273.483 35.485C292.702 32.0056 396.542 29.6422 410.728 39.4793C424.915 49.3164 429.209 16.1607 441.092 10.5358C452.975 4.91087 454.858 27.6442 465.409 37.2712C475.96 46.8982 267.131 47.6414 256.853 96.3949C246.575 145.148 322.57 121.033 340.748 122.084C358.926 123.134 348.874 117.635 369.576 116.865C390.278 116.095 371.958 143.725 374.454 164.363C376.95 185.001 363.618 170.295 339.577 183.455C315.536 196.616 330.424 202.107 325.941 211.603C321.457 221.098 302.8 188.624 281.15 173.191C259.499 157.758 264.543 172.6 249.324 172.13C234.104 171.66 249.338 181.804 244.642 213.42Z",fill:"#50A8F9","fill-opacity":"0.6"})}),e.jsx("defs",{children:e.jsxs("filter",{id:"filter0_f_6956_1462",x:"0.0976562",y:"-90.3467",width:"565.698",height:"425.681",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),e.jsx("feGaussianBlur",{stdDeviation:"50",result:"effect1_foregroundBlur_6956_1462"})]})})]}),e.jsxs("svg",{className:"absolute left-0 top-[50%]",width:"180",height:"401",viewBox:"0 0 180 401",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("g",{filter:"url(#filter0_f_6956_1463)",children:e.jsx("path",{d:"M-42.0347 119.41C-29.7579 89.899 -38.7737 107.396 -10.3346 104.796L19.5937 121.75C30.2424 136.134 52.8004 167.605 57.8427 178.42C62.8849 189.235 74.1761 217.251 79.1915 229.908C58.4385 223.2 8.9236 206.281 -23.1126 192.267C-63.1579 174.749 -50.3635 222.476 -46.7731 236.3C-43.1828 250.124 -33.3715 247.975 -5.16075 261.387C23.05 274.799 -6.92722 273.013 -32.1205 292.718C-57.3138 312.422 -93.9775 286.231 -113.464 284.911C-132.951 283.59 -234.222 260.519 -245.576 247.515C-256.93 234.511 -269.193 265.613 -282.09 268.165C-294.987 270.717 -291.26 248.213 -299.14 236.3C-307.02 224.388 -104.335 274.674 -82.4603 229.908C-60.5854 185.141 -140.168 189.964 -157.539 184.506C-174.91 179.047 -166.506 186.834 -186.769 182.525C-207.032 178.215 -182.518 155.896 -179.897 135.273C-177.277 114.651 -167.941 132.168 -141.414 125.279C-114.887 118.389 -127.982 109.427 -121.315 101.314C-114.648 93.2017 -104.488 129.249 -87.2631 149.503C-70.0381 169.757 -71.3042 154.132 -56.6602 158.306C-42.0162 162.479 -54.3115 148.921 -42.0347 119.41Z",fill:"#00B3FF","fill-opacity":"0.6"})}),e.jsx("defs",{children:e.jsxs("filter",{id:"filter0_f_6956_1463",x:"-399.364",y:"0.145264",width:"578.556",height:"399.889",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),e.jsx("feGaussianBlur",{stdDeviation:"50",result:"effect1_foregroundBlur_6956_1463"})]})})]}),e.jsxs("div",{className:" w-full rounded-[32px] border-4 border-white bg-white/50 px-5 py-5 pb-6 backdrop-blur-2xl",children:[e.jsx("div",{className:" text-center font-['Poppins'] text-xs font-semibold uppercase text-[#000030]/50",children:a("provider.home.f_title")}),v?e.jsx("div",{className:"mt-4 flex w-full items-center justify-center ",children:e.jsx(u,{})}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"mt-4 grid w-full grid-cols-3 justify-items-center gap-x-1 gap-y-4 xs:grid-cols-4 ",children:[f.map((t,s)=>e.jsxs("button",{onClick:()=>_(s),className:"flex w-full flex-col items-center justify-start ",children:[e.jsx("div",{className:`flex h-14 w-14  items-center justify-center overflow-hidden rounded-full border-2 ${t.active?"border-[#fd5d5d] bg-[#fd5d5d]/10 ":"border-[#50a8f9]/10 bg-[#50a8f9]/10"}`,children:e.jsx("img",{src:t.logo,alt:"",className:"h-full w-full object-cover "})}),e.jsx("div",{className:"text-center font-['Poppins'] text-xs font-medium text-[#000030]/50",children:t.name.length>8?`${t.name.substring(0,8)}...`:t.name})]},s)),e.jsxs(c,{to:"/provider/my-alert",className:"flex w-full flex-col items-center justify-center ",children:[e.jsx("div",{className:"flex h-14 w-14 items-center justify-center rounded-full border-2 border-[#50a8f9]/10 bg-[#50a8f9]/10",children:e.jsxs("svg",{width:"28",height:"28",viewBox:"0 0 28 28",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M7.58301 19.8333C8.54951 19.8333 9.33301 19.0498 9.33301 18.0833C9.33301 17.1168 8.54951 16.3333 7.58301 16.3333C6.61651 16.3333 5.83301 17.1168 5.83301 18.0833C5.83301 19.0498 6.61651 19.8333 7.58301 19.8333Z",fill:"#56CCF2"}),e.jsx("path",{d:"M15.75 19.8333C16.7165 19.8333 17.5 19.0498 17.5 18.0833C17.5 17.1168 16.7165 16.3333 15.75 16.3333C14.7835 16.3333 14 17.1168 14 18.0833C14 19.0498 14.7835 19.8333 15.75 19.8333Z",fill:"#56CCF2"}),e.jsx("path",{d:"M4.66634 12.8332V9.33324H13.1713C12.9497 8.59824 12.833 7.81657 12.833 6.99991H5.16801C6.13634 6.17157 8.64467 5.72824 12.9263 5.85657C13.043 5.03991 13.2763 4.25824 13.6147 3.53491C3.46467 3.11491 2.33301 5.85657 2.33301 8.16657V19.2499C2.33301 20.3582 2.77634 21.3616 3.49967 22.0966V24.4999C3.49967 25.1416 4.02467 25.6666 4.66634 25.6666H5.83301C6.47467 25.6666 6.99967 25.1416 6.99967 24.4999V23.3332H16.333V24.4999C16.333 25.1416 16.858 25.6666 17.4997 25.6666H18.6663C19.308 25.6666 19.833 25.1416 19.833 24.4999V22.0966C20.5563 21.3616 20.9997 20.3582 20.9997 19.2499V15.1666C18.7713 15.1666 16.7647 14.2799 15.2947 12.8332H4.66634ZM18.6663 18.6666C18.6663 19.9499 17.6163 20.9999 16.333 20.9999H6.99967C5.71634 20.9999 4.66634 19.9499 4.66634 18.6666V15.1666H18.6663V18.6666Z",fill:"#56CCF2"}),e.jsx("path",{d:"M21.0003 1.16675C17.7803 1.16675 15.167 3.78008 15.167 7.00008C15.167 10.2201 17.7803 12.8334 21.0003 12.8334C24.2203 12.8334 26.8337 10.2201 26.8337 7.00008C26.8337 3.78008 24.2203 1.16675 21.0003 1.16675ZM21.5837 10.5001H20.417V9.33341H21.5837V10.5001ZM21.5837 8.16675H20.417V3.50008H21.5837V8.16675Z",fill:"#56CCF2"})]})}),e.jsx("div",{className:"text-center font-['Poppins'] text-xs font-medium text-[#000030]/50",children:a("provider.home.my_alerts")})]})]})})]})]}),e.jsxs("div",{className:" relative left-0 mt-6 flex w-full flex-1 flex-col items-center justify-start overflow-hidden rounded-tl-[32px] rounded-tr-[32px] bg-white",children:[e.jsx("div",{className:" flex h-7 w-full max-w-[335px] items-center justify-center border-b bg-white ",children:e.jsx("div",{className:" h-1 w-[50px] rounded bg-[#f2f2f7]"})}),(m=l==null?void 0:l.userDetails)!=null&&m.plan_id?j?e.jsx("div",{className:"flex h-full w-full items-center justify-center pt-10 ",children:e.jsx(u,{})}):e.jsx(V,{taskData:w}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"mt-5 w-[300px] font-['Poppins'] text-2xl font-medium text-[#b4b4b4]",dangerouslySetInnerHTML:{__html:a("provider.home.activate")}}),e.jsx(c,{to:"/provider/account-activation",className:"mt-10 text-right font-['Poppins'] text-base font-black uppercase text-[#56ccf2]",children:a("provider.home.btn_account")})]})]})]})};export{l1 as default};
